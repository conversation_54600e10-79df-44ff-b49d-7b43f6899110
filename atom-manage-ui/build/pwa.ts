import { VitePWA } from "vite-plugin-pwa";

export const pwaSupport = (command: string, VITE_BASE_URL: string) =>
  VitePWA({
    strategies: "generateSW",
    registerType: "autoUpdate",
    includeAssets: [
      "favicon.ico",
      "apple-touch-icon.png",
      "web-app-manifest-192x192.png",
      "web-app-manifest-512x512.png"
    ],
    selfDestroying: true,
    manifest: {
      name: "中铁二十四局集团数字会议系统",
      short_name: "数字会议系统",
      description: "中铁二十四局集团数字会议系统",
      theme_color: "#ffffff",
      background_color: "#ffffff",
      display: "standalone",
      scope: "/",
      lang: "zh-CN",
      icons: [
        {
          src: "/favicon.ico",
          sizes: "16x16 32x32",
          type: "image/x-icon"
        },
        {
          src: "/web-app-manifest-192x192.png",
          sizes: "192x192",
          type: "image/png",
          purpose: "any maskable"
        },
        {
          src: "/web-app-manifest-512x512.png",
          sizes: "512x512",
          type: "image/png",
          purpose: "any maskable"
        },
        {
          src: "/apple-touch-icon.png",
          sizes: "180x180",
          type: "image/png"
        }
      ]
    },
    workbox: {
      maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5MB
      globPatterns: [
        "**/*.{js,css,html}", // 核心文件
        "**/*.{png,jpg,jpeg,gif,svg,ico}", // 图片资源
        "**/*.{woff,woff2,ttf,eot}" // 字体文件
      ],
      globIgnores: ["pdfjs-*/**"],
      navigateFallback: "/index.html",
      navigateFallbackDenylist: [
        // API 请求
        new RegExp(`^${VITE_BASE_URL}/`),
        /\/(oauth2|login|sso|auth)\//,
        /^\/pdfjs-.*\//
      ],
      // 添加运行时缓存策略，解决OAuth2登录异常问题
      runtimeCaching: [
        {
          // API 请求：完全不缓存
          urlPattern: new RegExp(`^${VITE_BASE_URL}/`),
          handler: "NetworkOnly"
        },
        {
          // 认证相关：完全不缓存
          urlPattern: /\/(oauth2|login|sso|auth)\//,
          handler: "NetworkOnly"
        },
        {
          urlPattern: /^\/pdfjs-.*\//,
          handler: "NetworkOnly"
        }
      ]
    },
    devOptions: {
      enabled: command === "serve",
      type: "module",
      // 开发模式下禁用workbox文件扫描，避免警告
      suppressWarnings: true
    }
  });
