<script setup lang="ts">
import { uniqueId } from "lodash";
import { onMounted, ref, onBeforeUnmount } from "vue";
import Player from "xgplayer";
import volume from "xgplayer/es/plugins/volume";
import { deviceDetection } from "@pureadmin/utils";
import screenShot from "xgplayer/es/plugins/screenShot";
import playbackRate from "xgplayer/es/plugins/playbackRate";
import "xgplayer/dist/index.min.css";

import { useElementBounding } from "@vueuse/core";
import { IPlayerOptions, Events } from "xgplayer/es/player";

defineOptions({
  name: "VideoPreview"
});

const id = uniqueId("mse");
const videoRef = ref(null);
const videoDetailPlayer = ref();
const props = defineProps({
  url: {
    require: true,
    type: String
  },
  poster: {
    require: false,
    type: String
  },
  width: {
    require: false,
    default: 600
  },
  height: {
    require: false,
    default: 337.5
  },
  cssFullscreen: {
    type: Boolean,
    default: true
  },
  style: {
    type: [Object, String],
    required: false
  },
  fluid: {
    type: Boolean,
    default: false
  },
  visible:{
    type:Boolean,
    default:true
  }

});

const { width: videoWidth } = useElementBounding(videoRef);

onMounted(() => {
  const fluid = props.fluid || deviceDetection();
  const config = {
    id: id,
    // 默认静音
    volume: 0.4,

    screenShot: true,
    url: props.url,
    poster: props.poster,
    maxWidth: "100%",
    download: true,
    cssFullscreen: props.cssFullscreen,
    controlPlugins: [volume, playbackRate, screenShot],
    lang: "zh",

    //传入倍速可选数组
    playbackRate: [0.5, 0.75, 1, 1.5, 2]
  } as IPlayerOptions;
  if (fluid) {
    config.fluid = true;
  } else {
    config.width =
      videoWidth.value > props.width ? videoWidth.value : props.width;
    config.height = props.height;
  }

  videoDetailPlayer.value = new Player(config);

});
// 抛出暂停视频的方法
const handlemyClose=()=>{

  videoDetailPlayer.value.pause();
}
defineExpose({
  handlemyClose
});
onBeforeUnmount(() => {
  videoDetailPlayer.value.destroy();

});
</script>

<template>

  <div ref="videoRef" :id="id" :style="style" />
</template>

<style scoped>
#mse {
  flex: auto;
}
</style>
