<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { message } from "@/utils/message";
import { calculateFileMD5 } from "@/utils/file";
import { ref, watch, reactive } from "vue";
import { FileUploadResult as File, UploadStatus } from "@/types/system/sys-oss";
import { find, remove } from "lodash";
import OssUtils from "@/utils/oss";
import Delete from "@iconify-icons/ep/delete";
import Close from "@iconify-icons/ep/close";
import ZoomIn from "@iconify-icons/ep/zoom-in";
import VideoPreview from "@/components/VideoPreview";
defineOptions({
  name: "UploadVideo"
});

const emit = defineEmits<{
  (e: "change", fileList: File[]): void;
  (e: "update:modelValue", value: File[]): void;
  (e: "upload", value: UploadStatus): void;
}>();

// const

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 2048
  },
  fileType: {
    type: Array,
    default: () => ["mov", "mp4", "avi", "wmv", "mpeg", "mkv"]
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showTip: {
    type: Boolean,
    default: true
  }
});

const uploadFileUrl = import.meta.env.VITE_BASE_URL + "/system/oss/upload";
const fileList = ref<File[]>([]);
const uploadRef = ref(null);
const previewUrl = ref("");
const visibleDialog = ref(false);

type Parcent = {
  [key: string]: number;
};
const percents = reactive<Parcent>({});
watch(
  () => props.modelValue,
  async val => {
    if (val) {
      const list = Array.isArray(val)
        ? val
        : (props.modelValue as string).split(",");
      fileList.value = list;
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// methods
const handleRemove = (file: any, list: any[]) => {
  const findex = list.map(f => f.ossId).indexOf(file.ossId);
  if (findex > -1) {
    list.splice(findex, 1);
    fileList.value = list;
    emit("update:modelValue", list);
    emit("change", list);
  }
};

// 如果上传成功的文件的ossId在fileList中存在，则阻止上传
function fileRejecWithUploadSuccessHas(file: any, error: string) {
  uploadRef.value.abort(file);
  remove(fileList.value, { uid: file.uid });
  message(error, { type: "error" });
  closeLoading();
  return;
}

function handleSuccessUploadFile(foundFile: any, data: File) {
  if (!foundFile) {
    foundFile = data;
    fileList.value.push(data);
  }
  foundFile.status = "success";
  foundFile.id = data.id;
  foundFile.url = data.url;
  foundFile.fileSize = foundFile.size;
  foundFile.ossId = data.ossId;
  foundFile.md5 = data.md5;
  emit("update:modelValue", fileList.value);
  emit("change", fileList.value);
}

const closeLoading = () => {
  emit("upload", { status: "over" });
};

// 覆盖默认上传
function handleHttpRequest(files): any {
  const { file } = files;
  percents[file.uid] = 0;
  const foundFile: any = find(fileList.value, { uid: file.uid });
  if (file.status === "exist") {
    handleSuccessUploadFile(foundFile, file);
    closeLoading();
    return;
  }
  const fileName = file.name;

  OssUtils.upload({
    file: file,
    filename: fileName,
    serverUploadUrl: uploadFileUrl,
    onProgress: ({ percent }) => {
      percents[file.uid] = percent;
      files.onProgress({ percent });
    }
  })
    .then(res => {
      // 根据file.uid查询fileList中的文件
      if (res.code === 200) {
        // 如果上传成功的文件的ossId在fileList中存在，则阻止上传
        if (find(fileList.value, { ossId: res.data.ossId })) {
          fileRejecWithUploadSuccessHas(file, "文件已存在");
          return;
        }

        const data = res.data;
        data.md5 = file.md5;
        handleSuccessUploadFile(foundFile, data);
        closeLoading();
      } else {
        fileRejecWithUploadSuccessHas(file, res.msg);
      }
    })
    .catch(msg => {
      message(msg, { type: "error" });
    })
    .finally(() => {
      closeLoading();
    });
}

const handleBeforeUpload = async file => {
  let isVideo = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name
        .slice(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
    }
    isVideo = props.fileType.some((type: string) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isVideo = file.type.indexOf("video") > -1;
  }

  if (!isVideo) {
    message(`文件格式不正确, 请上传${props.fileType.join("/")}视频格式文件!`, {
      type: "error"
    });

    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      message(`上传视频大小不能超过 ${props.fileSize} MB!`, { type: "error" });
      return false;
    }
  }

  try {
    const md5 = await calculateFileMD5(file);
    const isExist = await checkFileExist(md5, file);
    if (isExist) {
      closeLoading();
      message("该视频已存在", { type: "error" });
      return false;
    }
  } catch {
    closeLoading();
  }
};

const checkFileExist = async (md5: string, file: any) => {
  const md5File = fileList.value.find(item => item.md5 === md5);
  if (md5File) return true;
  file.md5 = md5;
  return false;
};
// 文件个数超出
const handleExceed = () => {
  message("上传视频数量超出限制", { type: "error" });
};

const handlePreview = file => {
  previewUrl.value = file.url;
  visibleDialog.value = true;
};
</script>

<template>
  <div class="upload-video" :class="{ limit: fileList.length >= limit }">
    <el-upload
      ref="uploadRef"
      multiple
      :action="'#'"
      list-type="picture-card"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      name="file"
      :show-file-list="true"
      v-model:file-list="fileList"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :http-request="handleHttpRequest"
      :class="{
        hide: fileList.length >= limit
      }"
      :disabled="disabled"
    >
      <template v-slot:default>
        <!--<i class="el-icon-film"></i>-->
        <el-button
          :icon="useRenderIcon('ep:video-camera')"
          circle
          style="border: none"
        />
      </template>

      <template v-slot:file="{ file }">
        <div class="video-box">
          <div
            class="el-upload-list__item-info"
            v-if="file.status === 'uploading'"
          >
            <el-progress
              type="circle"
              :percentage="percents[file.uid]"
              indeterminate
            />
          </div>

          <template v-else>
            <video
              tabindex="2"
              mediatype="video"
              crossorigin="anonymous"
              :src="file.url"
              width="252"
              height="142"
              style="z-index: -9; width: 252px; height: 142px"
            />

            <label class="el-upload-list__item-status-label"
              ><i class="el-icon el-icon--upload-success el-icon--check"
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 1024 1024"
                >
                  <path
                    fill="currentColor"
                    d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"
                  /></svg></i
            ></label>

            <i class="el-icon el-icon--close">
              <iconify-icon-offline :icon="Close" />
            </i>
            <span class="el-upload-list__item-actions"
              ><span class="el-upload-list__item-preview"
                ><i class="el-icon el-icon--zoom-in">
                  <iconify-icon-offline
                    :icon="ZoomIn"
                    @click="handlePreview(file)"
                  /> </i></span
              ><span class="el-upload-list__item-delete"
                ><i class="el-icon el-icon--delete">
                  <iconify-icon-offline
                    :icon="Delete"
                    @click="handleRemove(file, fileList)"
                  /> </i></span
            ></span>
          </template>
        </div>
      </template>
    </el-upload>
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>

    <el-dialog v-model="visibleDialog" title="预览" width="800">
      <VideoPreview :url="previewUrl" :width="700" />
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.upload-video {
  & :deep(.el-upload-list__item) {
    width: 214px;
    height: 142px;
  }

  & :deep(.el-upload.el-upload--picture-card) {
    width: 214px;
    height: 142px;
    line-height: 156px;
  }
}
.upload-video.limit {
  & :deep(.el-upload.el-upload--picture-card) {
    display: none;
  }
}
.video-box {
  video {
    height: 142px !important;
  }
}
</style>
