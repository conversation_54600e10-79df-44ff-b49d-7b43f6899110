<script setup lang="ts">
import {
  listOrgChildren,
  listUserByPosition,
  searchUser
} from "@/api/system/org";
import { computed, onMounted, reactive, ref, watch } from "vue";
import Search from "@iconify-icons/ep/search";
import OfficeBuilding from "@iconify-icons/ep/office-building";
import { useToggle } from "@vueuse/core";
import { SearchUser } from "@/types/system/sys-user";
import { debounce, remove, unionBy } from "lodash";
import { SysOrg } from "@/types/system/sys-org";
import type { TreeNode } from "element-plus/es/components/tree-v2/src/types";
import type { SelectTreeNode, UserSelectTreeProps } from "./type.ts";
import { getUserInfo, getUserInfoByOrgIds } from "@/api/system/user";
import BtnLoading from "@/assets/lottie/btn-loading.json";

const props = withDefaults(defineProps<UserSelectTreeProps>(), {
  importType: null,
  multiple: true,
  disabled: false
});

const selectValue = ref<number[]>();
const searchValue = ref<number[]>();
const modelValue = defineModel<number[]>();
const treeRef = ref(null);
const rootOrg = ref(0);
const userList = ref<SearchUser[]>([]);
const [isSearch] = useToggle(true);

const userOptions = computed(() => {
  return unionBy(cacheData.value, userList.value, "id");
});
const selectRef = ref(null);

// 将dynamicTags初始化为对象数组
const dynamicTags = ref([]);

const emit = defineEmits(["update:modelValue"]);

// 树的懒加载方法
async function loadNode(node: TreeNode, resolve: (res: any) => void) {
  const { data } = node;
  try {
    if (data.type === 3 && props.importType == "user") {
      const response = await listUserByPosition(data.id ?? 0);
      const result = response.data;
      const userNodes = result.map((item: SearchUser) => ({
        id: item.id,
        code: "",
        name: item.name,
        parentId: data.id,
        parentName: data.name,
        type: 4,
        isLeaf: response.data.length > 0
      }));
      resolve(userNodes);
      // emit("currentUser", userNodes);
    } else {
      const response = await listOrgChildren(data.id ?? 0);
      let result: SysOrg[];
      if (props.importType == "user") {
        result = response.data ?? [];
      } else {
        // 如果是1、2 过滤第三层
        result = response.data.filter(i => i.type !== 3);
      }

      const orgNodes = result.map((item: SysOrg) => ({
        id: item?.id,
        code: item?.code,
        name: item?.name,
        parentId: data?.id,
        type: item?.type,
        disabled:
          props.importType == "user"
            ? props.importType == "user"
            : item?.virtual || item?.id == 1 || data?.id == 178851,
        isLeaf: props.importType === "org" ? item.type === 3 : false,
        virtual: item?.virtual
      }));
      resolve(orgNodes);
    }
  } catch (error) {
    console.error("Error fetching org data:", error);
    resolve([]);
  }
}

// 通过树选择用户
function handleCheck(select: SelectTreeNode, checkObj: any) {
  // 查看cacheData中是否存在当前记录，不存在则添加
  const isCheck = checkObj.checkedKeys.includes(select.id);
  if (isCheck) {
    cacheData.value.push({
      id: select.id,
      name: select.name,
      orgId: select.parentId,
      orgName: select.parentName,
      position: select.position
    });
  }
}

// 多选，删除tag触发事件
function handleRemoveTag(val: number) {
  remove(selectValue.value, val);
}

// 切换树和搜索
function toggleSearch() {
  isSearch.value = !isSearch.value;
  // 检索切到树的时候需要修改下
  // if (props.importType !== "user") {
  //   selectValue.value = [];
  // }
}

const cacheData = ref<SearchUser[]>([]);
const cacheLoading = ref(false);

// 搜索用户
const searchLoading = ref(false);
const handleSearchUser = debounce(
  (query: string) => {
    if (!query) {
      userList.value = [];
      return;
    }
    searchLoading.value = true;
    searchUser(rootOrg.value, query)
      .then(resp => {
        userList.value = resp.data;
      })
      .catch(console.error)
      .finally(() => {
        searchLoading.value = false;
      });
  },
  500,
  { leading: false, trailing: true }
);

function handleUserList(list: any[]) {
  console.log(list);
  console.log(dynamicTags.value);
  list.forEach(user => {
    const existingIndex = dynamicTags.value.findIndex(
      (tag: any) => tag.id == (user.userId ?? user.id)
    );
    console.log(existingIndex);
    if (existingIndex === -1) {
      // 如果dynamicTags中不存在相同id的元素，添加新元素
      dynamicTags.value.push({
        id: user.userId ?? user.id,
        name: user.nickName ?? user.name
      });
    }
  });
  emit(
    "update:modelValue",
    dynamicTags.value.map(tag => tag.id)
  );
}

watch(
  () => searchValue.value,
  val => {
    if (props.importType == "user") {
      if (val && val.length > 0) {
        cacheLoading.value = true;
        getUserInfo(val)
          .then(resp => {
            cacheData.value = resp.data;
          })
          .finally(() => {
            cacheLoading.value = false;
          });
      }
    } else {
      if (val && val.length > 0) {
        getUserInfo(val)
          .then(resp => {
            cacheData.value = resp.data;
            const list = resp.data;

            handleUserList(list);
          })
          .finally(() => {
            cacheLoading.value = false;
          });
      } else {
        if (dynamicTags.value?.length > 0 && selectValue.value?.length == 0) {
          dynamicTags.value = [];
          emit(
            "update:modelValue",
            dynamicTags.value.map(tag => tag.id)
          );
        }
      }
    }
  }
);

watch(
  () => selectValue.value,
  val => {
    if (props.importType == "user") {
      if (val && val.length > 0) {
        cacheLoading.value = true;
        getUserInfo(val)
          .then(resp => {
            cacheData.value = resp.data;
          })
          .finally(() => {
            cacheLoading.value = false;
          });
      }
    } else {
      if (val && val.length > 0) {
        cacheLoading.value = true;
        if (isSearch.value) {
          getUserInfo(val)
            .then(resp => {
              cacheData.value = resp.data;

              handleUserList(resp.data);
            })
            .finally(() => {
              cacheLoading.value = false;
            });
        } else {
          // 查询当前节点下的所有人员
          getUserInfoByOrgIds(val)
            .then(resp => {
              handleUserList(resp.data);
            })
            .finally(() => {
              cacheLoading.value = false;
            });
        }
      } else {
        if (dynamicTags.value?.length > 0 && searchValue.value?.length == 0) {
          dynamicTags.value = [];
          emit(
            "update:modelValue",
            dynamicTags.value.map(tag => tag.id)
          );
        }
      }
    }
  },
  { deep: true, immediate: true }
);

// 通过id移除标签
const handleClose = (id: number) => {
  const index = dynamicTags.value.findIndex(tag => tag.id === id);
  if (index !== -1) {
    dynamicTags.value.splice(index, 1);
  }
  emit(
    "update:modelValue",
    dynamicTags.value.map(tag => tag.id)
  );
};

async function getUserList(value) {
  return new Promise(resolve => {
    getUserInfo(value).then(resp => {
      const data = resp.data;
      resolve(data);
    });
  });
}

// 组件初始化时立即执行的匿名函数
// 用于初始化组件的状态和数据
(() => {
  // 清空选择值和搜索值
  selectValue.value = [];
  searchValue.value = [];

  // 如果有传入的modelValue值,则根据这些id获取用户信息并添加到dynamicTags中
  if (modelValue.value && modelValue.value.length > 0) {
    getUserInfo(modelValue.value)
      .then(resp => {
        resp.data.forEach(item => {
          dynamicTags.value.push({
            id: item.id,
            name: item.name
          });
        });
      })
      .finally(() => {
        cacheLoading.value = false;
      });
  } else {
    // 如果没有modelValue,则清空dynamicTags
    dynamicTags.value = [];
  }
})();

// watch(modelValue, async newValue => {
//   if (newValue && newValue.length > 0) {
//     cacheLoading.value = true;
//     if (props.importType == "user") {
//       getUserInfo(newValue)
//         .then(resp => {
//           cacheData.value = resp.data;
//         })
//         .finally(() => {
//           cacheLoading.value = false;
//         });
//     } else {
//       const data = await getUserList(modelValue.value);
//       data.forEach(item => {
//         dynamicTags.value.push({
//           id: item.id,
//           name: item.name
//         });
//       });
//       cacheLoading.value = false;
//     }
//   }
// });
</script>

<template>
  <!--  <div style="display: flex; justify-content: space-between;" class="w-full">-->
  <section class="relative w-full">
    <el-tree-select
      lazy
      show-checkbox
      ref="treeRef"
      v-model="selectValue"
      v-show="!isSearch"
      node-key="id"
      :disabled="props.disabled"
      class="w-auto min-w-full"
      popper-class="popper-class"
      highlight-current
      suffix-icon="none"
      expand-on-click-node
      :key="props.importType"
      :collapse-tags="true"
      :max-collapse-tags="10"
      collapse-tags-tooltip
      :load="loadNode"
      :cache-data="cacheData"
      :multiple="props.multiple"
      check-strictly
      :props="{
        children: 'children',
        label: 'name',
        value: 'id',
        isLeaf: 'isLeaf',
        type: 'type'
      }"
      @check="handleCheck"
      @remove-tag="handleRemoveTag"
    />
    <el-select
      ref="selectRef"
      remote
      filterable
      v-model="searchValue"
      v-show="isSearch"
      class="w-full"
      value-key="id"
      :disabled="props.disabled"
      :loading="searchLoading"
      loading-text="搜索中..."
      placeholder="请搜索用户"
      suffix-icon="none"
      :collapse-tags="true"
      collapse-tags-tooltip
      :max-collapse-tags="10"
      :reserve-keyword="false"
      :multiple="props.multiple"
      :remote-method="handleSearchUser"
    >
      <el-option
        v-for="item in userOptions"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <span style="float: left">{{ item.orgName }}</span>
        <span style="float: right; color: var(--el-text-color-secondary)">{{
          item.name
        }}</span>
      </el-option>
    </el-select>

    <div
      class="absolute right-4 top-1/2 -translate-y-1/2"
      :title="
        cacheLoading
          ? '加载用户信息中...'
          : isSearch
          ? '切换成组织架构模式'
          : '切换成搜索模式'
      "
    >
      <lottie
        v-if="cacheLoading"
        class="absolute-center"
        :animationData="BtnLoading"
        :width="45"
        :height="45"
      />
      <template v-else>
        <IconifyIconOffline
          class="absolute-center cursor-alias text-[#aaa] hover:text-black"
          :icon="Search"
          v-show="!isSearch"
          @click="toggleSearch"
        />
        <IconifyIconOffline
          class="absolute-center cursor-alias text-[#aaa] hover:text-black"
          :icon="OfficeBuilding"
          v-show="isSearch"
          @click="toggleSearch"
        />
      </template>
    </div>
  </section>
  <div
    class="w-full rounded-md"
    style="
      border-width: 1px;
      border-color: #dcdfe6;
      height: 80px;
      overflow-y: auto;
    "
  >
    <el-tag
      v-for="tag in dynamicTags"
      :key="tag.id"
      closable
      :disable-transitions="false"
      @close="handleClose(tag.id)"
    >
      {{ tag.name }}
    </el-tag>
  </div>
  <!--  </div>-->
</template>

<style>
.popper-class .el-checkbox.is-disabled {
  display: none;
}
.popper-class .is-disabled {
  cursor: pointer;
  color: #606266;
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
<style lang="scss" scoped>
:deep(.el-select__wrapper .el-input__suffix) {
  display: none;
}
</style>
