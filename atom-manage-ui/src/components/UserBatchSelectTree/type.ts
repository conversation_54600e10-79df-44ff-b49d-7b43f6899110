export interface UserSelectTreeProps {
  importType: "org" | "user";
  modelValue: SelectNode[] | SelectNode | null;
  multiple?: boolean;
  disabled?: boolean;
}

export interface SelectTreeNode {
  code: string;
  disabled: boolean;
  id: number;
  isLeaf: boolean;
  label: string;
  name: string;
  parentId: number;
  parentName: string;
  type: number;
}

export interface SelectNode {
  id: number;
  name: string;
  type: "org" | "user";
}
