<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { message } from "@/utils/message";
import { ref, watch } from "vue";
import { calculateFileMD5 } from "@/utils/file";
import { FileUploadResult, UploadStatus } from "@/types/system/sys-oss";
import { find, remove } from "lodash";
import OssUtils from "@/utils/oss";

defineOptions({
  name: "ImageUpload"
});

const emit = defineEmits<{
  (e: "change", fileList: FileUploadResult[]): void;
  (e: "update:modelValue", value: FileUploadResult[]): void;
  (e: "upload", value: UploadStatus): void;
}>();
// const emit = defineEmits(["input", "change"]);

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 20
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg", "gif", "bmp", "webp", "heic"]
  },

  disabled: {
    type: Boolean,
    default: false
  },
  showTip: {
    type: Boolean,
    default: true
  }
});

const uploadFileUrl = import.meta.env.VITE_BASE_URL + "/system/oss/upload";
const fileList = ref<FileUploadResult[]>([]);
const uploadRef = ref(null);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);

watch(
  () => props.modelValue,
  async val => {
    if (val) {
      const list = Array.isArray(val)
        ? val
        : (props.modelValue as string).split(",");
      fileList.value = list;
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// methods
const handleRemove = (file: any, fileList: any[]) => {
  const findex = fileList.map(f => f.ossId).indexOf(file.ossId);
  if (findex > -1) {
    fileList.splice(findex, 1);
    emit("update:modelValue", fileList);
    emit("change", fileList);
  }
};

// 覆盖默认上传
function handleHttpRequest(files): any {
  const { file } = files;
  const foundFile: any = find(fileList.value, { uid: file.uid });
  const fileName = file.name;
  if (file.status === "exist") {
    file.originalName = fileName;
    handleSuccessUploadFile(foundFile, file);
    closeLoading();
    return;
  }

  OssUtils.upload({
    file: file,
    filename: fileName,
    serverUploadUrl: uploadFileUrl,
    params: {
      mediaId: props.mediaId
    },
    onProgress: ({ percent }) => {
      files.onProgress({ percent });
    }
  })
    .then(res => {
      // 根据file.uid查询fileList中的文件
      if (res.code === 200) {
        // 如果上传成功的文件的ossId在fileList中存在，则阻止上传
        if (find(fileList.value, { ossId: res.data.ossId })) {
          fileRejecWithUploadSuccessHas(file, "文件已存在");
          return;
        }

        const data = res.data;
        data.md5 = file.md5;
        handleSuccessUploadFile(foundFile, data);
        closeLoading();
      } else {
        fileRejecWithUploadSuccessHas(file, res.msg);
      }
    })
    .catch(msg => {
      message(msg, { type: "error" });
    })
    .finally(() => {
      closeLoading();
    });
}

// 如果上传成功的文件的ossId在fileList中存在，则阻止上传
function fileRejecWithUploadSuccessHas(file: any, error: string) {
  uploadRef.value.abort(file);
  remove(fileList.value, { uid: file.uid });
  message(error, { type: "error" });
  closeLoading();
  return;
}

function handleSuccessUploadFile(foundFile: any, data: FileUploadResult) {
  // 如果未找到fileList中的文件，则添加
  if (!foundFile) {
    foundFile = data;
    fileList.value.push(data);
  }

  foundFile.status = "success";
  foundFile.id = data.id;
  foundFile.url = data.url;
  foundFile.fileSize = foundFile.size;
  foundFile.ossId = data.ossId;
  foundFile.md5 = data.md5;
  emit("update:modelValue", fileList.value);
  emit("change", fileList.value);
}

const closeLoading = () => {
  emit("upload", { status: "over" });
};

// 上传前loading加载
const handleBeforeUpload = async file => {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name
        .slice(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
    }
    isImg = props.fileType.some((type: string) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }

  if (!isImg) {
    message(`文件格式不正确, 请上传${props.fileType.join("/")}图片格式文件!`, {
      type: "error"
    });

    return false;
  }

  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      message(`上传头像图片大小不能超过 ${props.fileSize} MB!`, {
        type: "error"
      });
      return false;
    }
  }

  try {
    const md5 = await calculateFileMD5(file);
    const isExist = await checkFileExist(md5, file);
    if (isExist) {
      closeLoading();
      message("该文件已存在", { type: "error" });
      return false;
    }
  } catch {
    closeLoading();
  }
};

const checkFileExist = async (md5: string, file: any) => {
  const md5File = fileList.value.find(item => item.md5 === md5);
  if (md5File) return true;
  file.md5 = md5;
  return false;
};
// 文件个数超出
const handleExceed = () => {
  message(`上传文件数量不能超过 ${props.limit} 个!`, { type: "error" });
};

// 预览
const handlePictureCardPreview = file => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};
</script>
<template>
  <div class="upload-image" :class="{ limit: fileList.length >= limit }">
    <el-upload
      ref="uploadRef"
      multiple
      :action="'#'"
      list-type="picture-card"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      name="file"
      :show-file-list="true"
      v-model:file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :http-request="handleHttpRequest"
      :disabled="disabled"
    >
      <template #default>
        <el-button
          :icon="useRenderIcon(disabled ? 'ep:picture' : 'ep:plus')"
          circle
          style="border: none"
        />
      </template>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <el-dialog v-model="dialogVisible" title="预览" width="800">
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.upload-image.limit {
  & :deep(.el-upload.el-upload--picture-card) {
    display: none;
  }
}
</style>
