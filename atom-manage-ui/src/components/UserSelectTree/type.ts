export interface UserSelectTreeProps {
  importType: "org" | "user";
  multiple?: boolean;
  disabled?: boolean;
  placeholder?: string;
  root?: number;
  isSearch?: boolean;
  clearable?: boolean;
  enableSort?: boolean; // 是否启用拖拽排序功能，仅在 importType === 'user' 且 multiple === true 时生效
}

export interface SelectTreeNode {
  code: string;
  disabled: boolean;
  id: number;
  isLeaf: boolean;
  label: string;
  name: string;
  parentId: number;
  parentName: string;
  type: number;
  position?: string;
}

export interface SelectNode {
  id: number;
  name: string;
  type: "org" | "user";
}
