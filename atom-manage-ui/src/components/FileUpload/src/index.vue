<script lang="ts" setup>
import { getToken } from "@/utils/auth";
import { message } from "@/utils/message";
import { computed, ref, watch } from "vue";
import { calculateFileMD5 } from "@/utils/file";
import { FileUploadResult, UploadStatus } from "@/types/system/sys-oss";
import download from "@/plugins/download";
import { find, remove } from "lodash";
import OssUtils from "@/utils/oss";
import VuePdfEmbed from "vue-pdf-embed";
import "vue-pdf-embed/dist/styles/annotationLayer.css";
import "vue-pdf-embed/dist/styles/textLayer.css";

const emit = defineEmits<{
  (e: "change", fileList: FileUploadResult[]): void;
  (e: "update:modelValue", value: FileUploadResult[]): void;
  (e: "upload", value: UploadStatus): void;
}>();

const props = defineProps({
  // 值
  modelValue: [String, Object, Array],
  disabled: {
    type: Boolean,
    default: false
  },
  // 数量限制
  limit: {
    type: Number,
    default: 5
  },
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 50
  },
  showFileList: {
    type: Boolean,
    default: true
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "xlsx", "docx", "ppt", "pptx", "txt", "pdf"]
  },
  horizontal: {
    type: Boolean,
    default: false
  },
  desc: {
    type: String,
    default: "选取文件"
  }
});

const uploadFileUrl = import.meta.env.VITE_BASE_URL + "/system/oss/upload";
const fileList = ref<FileUploadResult[]>([]);
const headers = { Authorization: "Bearer " + getToken() };
const uploadRef = ref(null);
const previewUrl = ref(null);
const previewVisible = ref(false);
const loading = ref(false);

watch(
  () => props.modelValue,
  async val => {
    if (val) {
      const list = Array.isArray(val)
        ? val
        : (props.modelValue as string).split(",");
      fileList.value = list;
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

const showTip = computed(() => {
  return props.isShowTip && (props.fileType || props.fileSize);
});

async function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType) {
    const fileName = file.name.split(".");
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.includes(fileExt);
    if (!isTypeOk) {
      message(`文件格式不正确, 请上传${props.fileType.join("/")}格式的文件`, {
        type: "error"
      });
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isSizeOk = file.size / 1024 / 1024 < props.fileSize;
    if (!isSizeOk) {
      message(`文件大小超过${props.fileSize}MB, 请重新上传`, { type: "error" });
      return false;
    }
  }

  emit("upload", { status: "md5" });
  try {
    const md5 = await calculateFileMD5(file);
    const isExist = await checkFileExist(md5, file);
    if (isExist) {
      closeLoading();
      message("该文件已存在", { type: "error" });
      return false;
    }
  } catch {
    closeLoading();
  }
}

const checkFileExist = async (md5: string, file: any) => {
  const md5File = fileList.value.find(item => item.md5 === md5);
  if (md5File) return true;
  file.md5 = md5;
  return false;
};

function handleExceed() {
  message("超出文件数量限制", { type: "warning" });
}

function handleUploadError() {
  message("上传失败", { type: "error" });
}

function downloadFile(file: any) {
  download.oss(file.ossId, file.name);
}

function handlePreview(file: any) {
  previewVisible.value = true;
  loading.value = true;
  previewUrl.value = file.url;
  loading.value = false;
}

function handleClose() {
  previewVisible.value = false;
  previewUrl.value = null;
}

const handleRemove = (file: any, fileList: any[]) => {
  const findex = fileList.map(f => f.ossId).indexOf(file.ossId);
  if (findex > -1) {
    fileList.splice(findex, 1);
    emit("update:modelValue", fileList);
    emit("change", fileList);
  }
};

// 覆盖默认上传
function handleHttpRequest(files): any {
  const { file } = files;
  const foundFile: any = find(fileList.value, { uid: file.uid });
  const fileName = file.name;
  if (file.status === "exist") {
    file.originalName = fileName;
    handleSuccessUploadFile(foundFile, file);
    closeLoading();
    return;
  }

  OssUtils.upload({
    file: file,
    filename: fileName,
    serverUploadUrl: uploadFileUrl,
    onProgress: ({ percent }) => {
      files.onProgress({ percent: percent });
    }
  })
    .then(res => {
      // 根据file.uid查询fileList中的文件
      if (res.code === 200) {
        // 如果上传成功的文件的ossId在fileList中存在，则阻止上传
        if (find(fileList.value, { ossId: res.data.ossId })) {
          fileRejecWithUploadSuccessHas(file, "该文件已存在");
          return;
        }

        const data = res.data;
        data.md5 = file.md5;
        handleSuccessUploadFile(foundFile, data);
        closeLoading();
      } else {
        fileRejecWithUploadSuccessHas(file, res.msg);
      }
    })
    .catch(msg => {
      message(msg, { type: "error" });
    })
    .finally(() => {
      closeLoading();
    });
}

// 如果上传成功的文件的ossId在fileList中存在，则阻止上传
function fileRejecWithUploadSuccessHas(file: any, error: string) {
  uploadRef.value.abort(file);
  remove(fileList.value, { uid: file.uid });
  message(error, { type: "error" });
  closeLoading();
  return;
}

function handleSuccessUploadFile(foundFile: any, data: FileUploadResult) {
  // 如果未找到fileList中的文件，则添加
  if (!foundFile) {
    foundFile = data;
    fileList.value.push(data);
  }

  foundFile.status = "success";
  foundFile.url = data.url;
  foundFile.fileSize = foundFile.size;
  foundFile.ossId = data.ossId;
  foundFile.md5 = data.md5;
  emit("update:modelValue", fileList.value);
  emit("change", fileList.value);
}

const closeLoading = () => {
  emit("upload", { status: "over" });
};

const accept = computed(() => {
  return props.fileType.map(item => `.${item}`).join(",");
});
</script>

<template>
  <el-upload
    class="upload-file"
    multiple
    :accept="accept"
    :action="'#'"
    :before-upload="handleBeforeUpload"
    :limit="limit"
    :show-file-list="true"
    v-model:file-list="fileList"
    :headers="headers"
    :on-error="handleUploadError"
    :on-exceed="handleExceed"
    :on-remove="handleRemove"
    :on-preview="downloadFile"
    :http-request="handleHttpRequest"
    ref="uploadRef"
    :disabled="disabled"
  >
    <!-- 上传按钮 -->
    <slot name="title">
      <el-button size="default" type="primary" v-if="!disabled">
        {{ props.desc }}
      </el-button>
    </slot>
    <template v-slot:file="{ file }">
      <div
        class="el-upload-list__item-info"
        v-if="file.status === 'uploading' || file.status === 'ready'"
      >
        <div class="flex justify-between">
          <div>
            {{ file.originalName ?? file.name }}
          </div>
          <div class="">
            {{
              file.percentage != 100
                ? `读取文件.${file.percentage}%`
                : "等待完成.."
            }}
          </div>
        </div>
      </div>

      <li v-else class="el-upload-list__item is-success" tabindex="0">
        <!--v-if-->
        <div class="el-upload-list__item-info">
          <a class="el-upload-list__item-name">
            <i
              class="el-icon el-icon--document--preview"
              @click="handlePreview(file)"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                <path
                  fill="currentColor"
                  d="M576 992H128a32 32 0 0 1-32-32V64A32 32 0 0 1 128 32h768a32 32 0 0 1 32 32v576a32 32 0 0 1-64 0V96h-704v832H576a32 32 0 0 1 0 64z"
                />
                <path
                  fill="currentColor"
                  d="M768 288H256a32 32 0 0 1 0-64h512a32 32 0 0 1 0 64zM448 544H256a32 32 0 0 1 0-64h192a32 32 0 0 1 0 64zM384 800H256a32 32 0 0 1 0-64h128a32 32 0 0 1 0 64zM640 896a192 192 0 1 1 192-192 192 192 0 0 1-192 192z m0-320a128 128 0 1 0 128 128 128 128 0 0 0-128-128z"
                />
                <path
                  fill="currentColor"
                  d="M896 992a32 32 0 0 1-21.76-8.32l-138.24-128a32.64 32.64 0 0 1 44.16-47.36l137.6 128a32.64 32.64 0 0 1 0 45.44 32 32 0 0 1-21.76 10.24z"
                />
              </svg>
            </i>
            <span class="el-upload-list__item-file-name">{{
              file.originalName ?? file.name
            }}</span></a
          ><!--v-if-->
        </div>
        <div v-if="!disabled">
          <label class="el-upload-list__item-status-label"
            ><i class="el-icon el-icon--upload-success el-icon--circle-check"
              ><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                <path
                  fill="currentColor"
                  d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"
                />
                <path
                  fill="currentColor"
                  d="M745.344 361.344a32 32 0 0 1 45.312 45.312l-288 288a32 32 0 0 1-45.312 0l-160-160a32 32 0 1 1 45.312-45.312L480 626.752l265.344-265.408z"
                /></svg></i></label
          ><i
            class="el-icon el-icon--close"
            @click="handleRemove(file, fileList)"
            ><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
              /></svg
          ></i>
        </div>
        <label style="white-space: nowrap; cursor: pointer" v-else>
          <i class="el-icon el-icon--download" @click="downloadFile(file)">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M802 664v146c0 7.7-6.3 14-14 14H236c-7.7 0-14-6.3-14-14V664c0-5.5-4.5-10-10-10h-50c-5.5 0-10 4.5-10 10v170c0 33.1 26.9 60 60 60h600c33.1 0 60-26.9 60-60V664c0-5.5-4.5-10-10-10h-50c-5.5 0-10 4.5-10 10z"
              />
              <path
                fill="currentColor"
                d="M547 163v449.5l173.6-173.6c13.7-13.7 35.8-13.7 49.5 0 13.7 13.7 13.7 35.8 0 49.5L536.8 721.7c-0.4 0.4-0.8 0.8-1.3 1.2-0.2 0.2-0.4 0.4-0.6 0.5-0.2 0.2-0.4 0.4-0.7 0.6-0.3 0.2-0.5 0.4-0.8 0.6-0.2 0.1-0.4 0.3-0.5 0.4l-0.9 0.6c-0.2 0.1-0.3 0.2-0.5 0.3-0.3 0.2-0.6 0.4-1 0.6-0.2 0.1-0.3 0.2-0.5 0.3-0.3 0.2-0.6 0.4-1 0.5-0.2 0.1-0.4 0.2-0.5 0.3-0.3 0.2-0.6 0.3-0.9 0.5l-0.6 0.3c-0.3 0.1-0.6 0.3-0.8 0.4-0.2 0.1-0.5 0.2-0.7 0.3-0.3 0.1-0.5 0.2-0.8 0.3l-0.9 0.3c-0.2 0.1-0.4 0.2-0.7 0.2-0.3 0.1-0.6 0.2-1 0.3-0.2 0.1-0.4 0.1-0.6 0.2-0.4 0.1-0.7 0.2-1.1 0.3-0.2 0-0.4 0.1-0.6 0.1-0.4 0.1-0.7 0.2-1.1 0.2-0.2 0-0.4 0.1-0.6 0.1-0.4 0.1-0.7 0.1-1.1 0.2-0.2 0-0.4 0.1-0.7 0.1-0.3 0-0.7 0.1-1 0.1-0.3 0-0.6 0-0.9 0.1-0.3 0-0.5 0-0.8 0.1-1.2 0.1-2.3 0.1-3.5 0-0.3 0-0.5 0-0.8-0.1-0.3 0-0.6 0-0.9-0.1-0.3 0-0.7-0.1-1-0.1-0.2 0-0.4-0.1-0.7-0.1-0.4-0.1-0.7-0.1-1.1-0.2-0.2 0-0.4-0.1-0.6-0.1-0.4-0.1-0.7-0.2-1.1-0.2-0.2 0-0.4-0.1-0.6-0.1-0.4-0.1-0.7-0.2-1.1-0.3-0.2-0.1-0.4-0.1-0.6-0.2-0.3-0.1-0.6-0.2-1-0.3-0.2-0.1-0.5-0.1-0.7-0.2l-0.9-0.3c-0.3-0.1-0.5-0.2-0.8-0.3-0.2-0.1-0.5-0.2-0.7-0.3-0.3-0.1-0.6-0.3-0.8-0.4l-0.6-0.3c-0.3-0.2-0.6-0.3-0.9-0.5-0.2-0.1-0.4-0.2-0.5-0.3-0.3-0.2-0.6-0.4-1-0.6-0.2-0.1-0.3-0.2-0.5-0.3-0.3-0.2-0.6-0.4-1-0.6-0.2-0.1-0.3-0.2-0.5-0.3-0.3-0.2-0.6-0.4-0.9-0.7-0.2-0.1-0.3-0.3-0.5-0.4-0.3-0.2-0.5-0.4-0.8-0.6-0.2-0.2-0.4-0.4-0.7-0.6-0.2-0.2-0.4-0.4-0.6-0.5l-1.2-1.2-233.1-233.1c-13.7-13.7-13.7-35.8 0-49.5 13.7-13.7 35.8-13.7 49.5 0L477 612.5V163c0-19.3 15.7-35 35-35s35 15.7 35 35z"
              />
            </svg>
          </i>
        </label>
      </li>
    </template>
    <!-- 上传提示 -->
    <template #tip>
      <div class="el-upload__tip" v-if="showTip && fileList.length < 1">
        <slot name="tips">
          请上传
          <template v-if="fileSize">
            大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
          </template>
          <template v-if="fileType">
            格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
          </template>
          的文件
        </slot>
      </div>
    </template>
  </el-upload>

  <el-dialog
    title="预览附件"
    v-model="previewVisible"
    width="700px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
    @close="handleClose"
  >
    <div
      v-if="previewUrl"
      v-loading="loading"
      style="max-height: 80vh; overflow-y: auto"
    >
      <VuePdfEmbed :source="previewUrl" />
    </div>
  </el-dialog>
</template>
<style scoped lang="scss">
.upload-file {
  width: 100%;
}
.el-upload-list__item-file-name {
  word-wrap: break-word;
  max-width: 100%;
  display: inline-flex;
}
</style>
