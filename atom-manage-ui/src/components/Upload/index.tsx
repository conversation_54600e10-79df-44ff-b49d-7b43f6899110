import { ref, watch } from "vue";
import { FileUploadResult } from "@/types/system/sys-oss";
import { find, remove } from "lodash";
import OssUtils from "@/utils/oss";
import { message, confirm } from "@/utils/message";
import { calculateFileMD5 } from "@/utils/file";
import { getToken } from "@/utils/auth";

interface Emit {
  (event: "update:modelValue", value: any): void;
  (event: "change", value: any): void;
  (event: "success", value: any): void;
}
type _Emit = Emit | undefined | null;
export function useUpload(props, emit: _Emit, successCallback: any) {
  const uploadFileUrl =
    (import.meta as any).env.VITE_BASE_URL + "/system/oss/upload";

  const headers = { Authorization: "Bearer " + getToken() };

  const fileList = ref<FileUploadResult[]>([]);

  const cancelController = ref({});

  watch(
    () => props.modelValue,
    async val => {
      if (val) {
        const list = Array.isArray(val)
          ? val
          : String(props.modelValue).split(",");
        fileList.value = list;
      } else {
        fileList.value = [];
      }
    },
    { deep: true, immediate: true }
  );

  const handleHttpRequest = async files => {
    const { file } = files;
    const foundFile: any = find(fileList.value, { uid: file.uid });
    if (file.status === "exist") {
      handleSuccessUploadFile(foundFile, file);
      return;
    }

    const fileName = file.name;

    try {
      const res = await OssUtils.upload({
        file: file,
        filename: fileName,
        serverUploadUrl: uploadFileUrl,
        cancel: f => {
          cancelController.value[foundFile.uid] = f;
        },
        onProgress: ({ percent }) => {
          files.onProgress({ percent });
        }
      });

      if (res.code === 200) {
        if (find(fileList.value, { ossId: res.data.ossId })) {
          message("文件已存在", { type: "error" });
          return;
        }

        const data = res.data;
        data.fileSize = file.size;
        data.uid = file.uid;
        handleSuccessUploadFile(foundFile, res.data);
      } else {
        message(res.msg, { type: "error" });
      }
    } catch (msg) {
      message(msg, { type: "error" });
    }
  };

  const handleBeforeUpload = async file => {
    let isValidType = false;
    let isValidSize = false;

    // 检查文件类型
    if (props.fileType && props.fileType.length) {
      const fileType = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
      isValidType = props.fileType.includes(fileType);
      if (!isValidType) {
        message(`文件格式不正确, 请上传${props.fileType.join("/")}格式的文件`, {
          type: "error"
        });
        return false;
      }
    }

    // 检查文件大小
    if (props.fileSize) {
      const fileSizeInMB = file.size / 1024 / 1024;
      isValidSize = fileSizeInMB <= props.fileSize;
      if (!isValidSize) {
        message(`文件大小不能超过${props.fileSize}MB`, { type: "error" });
        return false;
      }
    }
    file.fileSize = file.size;

    // 计算文件的MD5值，检查文件是否已存在
    try {
      const md5 = await calculateFileMD5(file);
      const isExist = await checkFileExist(md5, file);
      if (isExist) {
        message("该文件已存在", { type: "error" });
        return false;
      }
    } catch (error) {
      message("文件校验失败", { type: "error" });
      return false;
    }

    // 如果文件类型和大小都有效，则返回true，表示文件可以上传
  };

  const checkFileExist = async (md5: string, _) => {
    const md5File = fileList.value.find(item => item.md5 === md5);
    return !!md5File;
  };

  const handleSuccessUploadFile = (foundFile: any, data: FileUploadResult) => {
    if (!foundFile) {
      fileList.value.push(data);
    }
    if (typeof successCallback === "function") {
      successCallback(data, foundFile);
    }
    emitChange(data);
  };

  const handleRemove = (file: any) => {
    remove(fileList.value, { uid: file.uid });
    emitChange(file);
  };

  function handleUploadError() {
    message("上传失败", { type: "error" });
  }

  function emitChange(data) {
    if (emit) {
      emit("update:modelValue", fileList.value);
      emit("success", data);
    }
  }

  const handleCloseUpload = async file => {
    await confirm("取消上传");
    closeUpload(file.uid);
  };

  const closeUpload = uid => {
    if (cancelController.value[uid]) {
      cancelController.value[uid].abort();
      delete cancelController.value[uid];
      fileList.value = fileList.value.filter(item => item.uid != uid);
    }
  };

  return {
    fileList,
    handleHttpRequest,
    handleBeforeUpload,
    handleSuccessUploadFile,
    handleRemove,
    handleUploadError,
    handleCloseUpload,
    headers
  };
}
