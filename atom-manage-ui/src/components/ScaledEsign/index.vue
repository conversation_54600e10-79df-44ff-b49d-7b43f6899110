<template>
  <div ref="container" class="scaled-esign-container">
    <canvas
      ref="canvas"
      @mousedown="mouseDown"
      @mousemove="mouseMove"
      @mouseup="mouseUp"
      @touchstart="touchStart"
      @touchmove="touchMove"
      @touchend="touchEnd"
      class="esign-canvas"
    />
  </div>
</template>

<script>
export default {
  name: "ScaledEsign",
  emits: ["image-imported"],
  props: {
    width: {
      type: Number,
      default: 800
    },
    height: {
      type: Number,
      default: 300
    },
    lineWidth: {
      type: Number,
      default: 4
    },
    lineColor: {
      type: String,
      default: "#000000"
    },
    bgColor: {
      type: String,
      default: ""
    },
    isCrop: {
      type: Boolean,
      default: false
    },
    format: {
      type: String,
      default: "image/png"
    },
    quality: {
      type: Number,
      default: 1
    },
    // 笔触效果相关属性
    brushEffect: {
      type: Boolean,
      default: true
    },
    pressureSensitive: {
      type: Boolean,
      default: true
    },
    minLineWidth: {
      type: Number,
      default: 1
    },
    maxLineWidth: {
      type: Number,
      default: 8
    }
  },
  data() {
    return {
      hasDrew: false,
      resultImg: "",
      points: [],
      canvasTxt: null,
      canvasRect: null,
      isMouseDown: false,
      lastPoint: null,
      myBg: this.bgColor ? this.bgColor : "rgba(255, 255, 255, 0)",
      sratio: 1,
      pageScale: 1, // 存储页面缩放比例
      resizeObserver: null, // ResizeObserver 实例
      // 笔触效果相关数据
      strokePoints: [], // 当前笔画的点集合
      lastTimestamp: 0, // 上次绘制时间戳
      lastVelocity: 0, // 上次绘制速度
      smoothedPoints: [] // 平滑处理后的点
    };
  },
  computed: {
    ratio() {
      return this.height / this.width;
    },
    stageInfo() {
      return this.$refs.canvas.getBoundingClientRect();
    }
  },
  watch: {
    myBg: function (newVal) {
      if (this.$refs.canvas) {
        this.$refs.canvas.style.background = newVal;
      }
    }
  },
  beforeMount() {
    window.addEventListener("resize", this.$_resizeHandler);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.$_resizeHandler);
    this.disconnectResizeObserver();
  },
  mounted() {
    this.initCanvas();
    this.getPageScale();
    this.initResizeObserver();
  },
  methods: {
    // 初始化画布
    initCanvas() {
      const canvas = this.$refs.canvas;
      const container = this.$refs.container;

      if (!canvas || !container) return;

      // 设置画布尺寸
      canvas.height = this.height;
      canvas.width = this.width;
      canvas.style.width = this.width + "px";
      canvas.style.height = this.height + "px";
      canvas.style.background = this.myBg;

      // 确保容器尺寸与画布匹配
      container.style.width = this.width + "px";
      container.style.height = this.height + "px";

      // 初始化画布上下文
      this.canvasTxt = canvas.getContext("2d");

      // 应用初始布局
      this.$_resizeHandler();
    },

    // 初始化 ResizeObserver
    initResizeObserver() {
      if (typeof ResizeObserver !== "undefined") {
        this.disconnectResizeObserver();

        this.resizeObserver = new ResizeObserver(entries => {
          this.$_resizeHandler();
        });

        if (this.$refs.container) {
          this.resizeObserver.observe(this.$refs.container);
        }

        // 同时观察父元素以捕获容器变化
        const parentElement = this.$refs.container?.parentElement;
        if (parentElement) {
          this.resizeObserver.observe(parentElement);
        }
      }
    },

    // 断开 ResizeObserver
    disconnectResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },

    // 获取页面缩放比例 - 从CSS变量获取
    getPageScale() {
      const computedStyle = getComputedStyle(document.documentElement);
      const scaleStr = computedStyle.getPropertyValue("--page-scale").trim();

      if (scaleStr && !isNaN(parseFloat(scaleStr))) {
        this.pageScale = parseFloat(scaleStr);
      } else {
        this.pageScale = 1;
      }
    },

    $_resizeHandler() {
      const canvas = this.$refs.canvas;
      const container = this.$refs.container;

      if (!canvas || !container) return;

      // 获取容器的实际宽度
      const containerWidth = container.clientWidth;

      // 计算缩放比例，保持宽高比
      const scale = containerWidth / this.width;

      // 设置画布样式尺寸（视觉尺寸）
      canvas.style.width = this.width * scale + "px";
      canvas.style.height = this.height * scale + "px";

      // 更新容器高度以匹配画布高度
      container.style.height = canvas.style.height;

      // 更新绘图上下文的缩放
      this.sratio = scale;

      // 重新获取页面缩放比例
      this.getPageScale();
    },

    // 计算实际坐标，考虑页面缩放
    calculateRealCoordinates(clientX, clientY) {
      const rect = this.$refs.canvas.getBoundingClientRect();
      // 应用页面缩放比例的逆运算来获取真实坐标
      const x = (clientX - rect.left) / this.pageScale / this.sratio;
      const y = (clientY - rect.top) / this.pageScale / this.sratio;
      return { x, y };
    },

    // PC 事件处理
    mouseDown(e) {
      e = e || event;
      e.preventDefault();
      this.isMouseDown = true;
      this.hasDrew = true;

      const coords = this.calculateRealCoordinates(e.clientX, e.clientY);
      this.lastPoint = coords;
      this.drawStart(coords);
    },

    mouseMove(e) {
      e = e || event;
      e.preventDefault();
      if (this.isMouseDown) {
        const coords = this.calculateRealCoordinates(e.clientX, e.clientY);
        this.drawMove(coords);
      }
    },

    mouseUp(e) {
      e = e || event;
      e.preventDefault();
      const coords = this.calculateRealCoordinates(e.clientX, e.clientY);
      this.drawEnd(coords);
      this.isMouseDown = false;
      this.lastPoint = null;
    },

    // 移动设备事件处理
    touchStart(e) {
      e = e || event;
      e.preventDefault();
      this.hasDrew = true;

      if (e.touches.length === 1) {
        const touch = e.touches[0];
        const coords = this.calculateRealCoordinates(
          touch.clientX,
          touch.clientY
        );
        this.lastPoint = coords;
        this.drawStart(coords);
      }
    },

    touchMove(e) {
      e = e || event;
      e.preventDefault();

      if (e.touches.length === 1) {
        const touch = e.touches[0];
        const coords = this.calculateRealCoordinates(
          touch.clientX,
          touch.clientY
        );
        this.drawMove(coords);
      }
    },

    touchEnd(e) {
      e = e || event;
      e.preventDefault();

      if (e.changedTouches.length === 1) {
        const touch = e.changedTouches[0];
        const coords = this.calculateRealCoordinates(
          touch.clientX,
          touch.clientY
        );
        this.drawEnd(coords);
        this.lastPoint = null;
      }
    },

    // 绘制开始
    drawStart(point) {
      const timestamp = Date.now();
      this.lastTimestamp = timestamp;
      this.lastVelocity = 0;
      this.strokePoints = [{ ...point, timestamp, pressure: 1 }];
      this.smoothedPoints = [];

      if (this.brushEffect) {
        // 使用笔触效果绘制起始点
        this.drawBrushPoint(point, this.lineWidth);
      } else {
        // 传统绘制方式
        const ctx = this.canvasTxt;
        ctx.beginPath();
        ctx.moveTo(point.x, point.y);
        ctx.lineTo(point.x, point.y);
        ctx.lineCap = "round";
        ctx.lineJoin = "round";
        ctx.lineWidth = this.lineWidth;
        ctx.strokeStyle = this.lineColor;
        ctx.stroke();
      }

      this.points.push({ x: point.x, y: point.y });
    },

    // 绘制移动
    drawMove(point) {
      if (!this.lastPoint) return;

      const timestamp = Date.now();
      const timeDelta = timestamp - this.lastTimestamp;

      if (timeDelta < 16) return; // 限制绘制频率，约60fps

      if (this.brushEffect) {
        // 计算速度和压感
        const distance = this.getDistance(this.lastPoint, point);
        const velocity = timeDelta > 0 ? distance / timeDelta : 0;
        const pressure = this.calculatePressure(velocity);

        // 添加到笔画点集合
        this.strokePoints.push({ ...point, timestamp, pressure });

        // 平滑处理并绘制
        this.drawSmoothStroke();
      } else {
        // 传统绘制方式
        const ctx = this.canvasTxt;
        ctx.beginPath();
        ctx.moveTo(this.lastPoint.x, this.lastPoint.y);
        ctx.lineTo(point.x, point.y);
        ctx.lineCap = "round";
        ctx.lineJoin = "round";
        ctx.lineWidth = this.lineWidth;
        ctx.strokeStyle = this.lineColor;
        ctx.stroke();
      }

      this.lastPoint = point;
      this.lastTimestamp = timestamp;
      this.points.push({ x: point.x, y: point.y });
    },

    // 绘制结束
    drawEnd(point) {
      if (this.brushEffect && this.strokePoints.length > 0) {
        // 完成当前笔画的绘制
        this.finishStroke();
      }

      this.strokePoints = [];
      this.smoothedPoints = [];
      this.points.push({ x: -1, y: -1 });
    },

    // 重置画布
    reset() {
      this.canvasTxt.clearRect(0, 0, this.width, this.height);
      this.hasDrew = false;
      this.resultImg = "";
      this.points = [];
    },

    // 生成签名图片
    generate() {
      if (!this.hasDrew) {
        return Promise.reject(new Error("请先签名"));
      }

      return new Promise((resolve, reject) => {
        try {
          const canvas = this.$refs.canvas;
          let dataUrl;

          if (this.isCrop) {
            // 裁剪签名区域
            const { minX, minY, maxX, maxY } = this.getSignatureRect();
            if (minX === Infinity) {
              reject(new Error("找不到签名"));
              return;
            }

            // 添加一些边距
            const padding = 10;
            const width = maxX - minX + padding * 2;
            const height = maxY - minY + padding * 2;

            // 创建临时画布进行裁剪
            const tempCanvas = document.createElement("canvas");
            tempCanvas.width = width;
            tempCanvas.height = height;
            const tempCtx = tempCanvas.getContext("2d");

            // 绘制裁剪后的图像
            tempCtx.drawImage(
              canvas,
              minX - padding,
              minY - padding,
              width,
              height,
              0,
              0,
              width,
              height
            );

            dataUrl = tempCanvas.toDataURL(this.format, this.quality);
          } else {
            // 使用整个画布
            dataUrl = canvas.toDataURL(this.format, this.quality);
          }

          this.resultImg = dataUrl;
          resolve(dataUrl);
        } catch (error) {
          reject(error);
        }
      });
    },

    // 获取签名的边界矩形
    getSignatureRect() {
      let minX = Infinity;
      let minY = Infinity;
      let maxX = -Infinity;
      let maxY = -Infinity;

      for (const point of this.points) {
        if (point.x === -1 && point.y === -1) continue; // 跳过分隔标记

        minX = Math.min(minX, point.x);
        minY = Math.min(minY, point.y);
        maxX = Math.max(maxX, point.x);
        maxY = Math.max(maxY, point.y);
      }

      return { minX, minY, maxX, maxY };
    },

    // 笔触效果相关方法

    // 计算两点间距离
    getDistance(p1, p2) {
      return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
    },

    // 根据速度计算压感
    calculatePressure(velocity) {
      if (!this.pressureSensitive) return 1;

      // 速度越快，压感越小（线条越细）
      const maxVelocity = 2; // 最大速度阈值
      const minPressure = 0.3; // 最小压感
      const maxPressure = 1; // 最大压感

      const normalizedVelocity = Math.min(velocity / maxVelocity, 1);
      return maxPressure - normalizedVelocity * (maxPressure - minPressure);
    },

    // 绘制单个笔触点
    drawBrushPoint(point, width) {
      const ctx = this.canvasTxt;
      ctx.beginPath();
      ctx.arc(point.x, point.y, width / 2, 0, 2 * Math.PI);
      ctx.fillStyle = this.lineColor;
      ctx.fill();
    },

    // 平滑笔画并绘制
    drawSmoothStroke() {
      if (this.strokePoints.length < 2) return;

      const ctx = this.canvasTxt;
      const points = this.strokePoints;
      const len = points.length;

      // 使用贝塞尔曲线平滑绘制
      for (let i = 1; i < len; i++) {
        const p1 = points[i - 1];
        const p2 = points[i];

        // 计算线条宽度（基于压感）
        const width1 = this.calculateLineWidth(p1.pressure);
        const width2 = this.calculateLineWidth(p2.pressure);

        this.drawSmoothLine(p1, p2, width1, width2);
      }
    },

    // 计算实际线条宽度
    calculateLineWidth(pressure) {
      const baseWidth = this.lineWidth;
      const minWidth = this.minLineWidth;
      const maxWidth = this.maxLineWidth;

      // 根据压感调整线条宽度
      const pressureWidth = minWidth + (maxWidth - minWidth) * pressure;
      return Math.min(Math.max(pressureWidth, minWidth), maxWidth);
    },

    // 绘制平滑线条
    drawSmoothLine(p1, p2, width1, width2) {
      const ctx = this.canvasTxt;

      // 使用渐变宽度绘制线条
      const distance = this.getDistance(p1, p2);
      const steps = Math.max(Math.floor(distance / 2), 1);

      for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const x = p1.x + (p2.x - p1.x) * t;
        const y = p1.y + (p2.y - p1.y) * t;
        const width = width1 + (width2 - width1) * t;

        ctx.beginPath();
        ctx.arc(x, y, width / 2, 0, 2 * Math.PI);
        ctx.fillStyle = this.lineColor;
        ctx.fill();
      }
    },

    // 完成笔画绘制
    finishStroke() {
      // 可以在这里添加笔画结束时的特殊效果
      // 比如笔触渐变等
    },

    // 导入外部 base64 图片到 canvas
    importBase64Image(base64Data, options = {}) {
      return new Promise((resolve, reject) => {
        try {
          // 验证 base64 数据格式
          if (!base64Data || typeof base64Data !== "string") {
            reject(new Error("无效的 base64 数据"));
            return;
          }

          // 确保 base64 数据包含正确的前缀
          let imageData = base64Data;
          if (!base64Data.startsWith("data:image/")) {
            // 如果没有前缀，默认添加 PNG 前缀
            imageData = `data:image/png;base64,${base64Data}`;
          }

          // 创建图片对象
          const img = new Image();

          img.onload = () => {
            try {
              const canvas = this.$refs.canvas;
              const ctx = this.canvasTxt;

              if (!canvas || !ctx) {
                reject(new Error("Canvas 未初始化"));
                return;
              }

              // 解析选项参数
              const {
                x = 0, // 图片在 canvas 中的 x 坐标
                y = 0, // 图片在 canvas 中的 y 坐标
                width = img.width, // 图片显示宽度，默认为原始宽度
                height = img.height, // 图片显示高度，默认为原始高度
                clearCanvas = false, // 是否清空画布
                maintainAspectRatio = true, // 是否保持宽高比
                fitToCanvas = false // 是否适应画布大小
              } = options;

              // 如果需要清空画布
              if (clearCanvas) {
                this.reset();
              }

              let drawWidth = width;
              let drawHeight = height;
              let drawX = x;
              let drawY = y;

              // 如果需要适应画布大小
              if (fitToCanvas) {
                const canvasWidth = this.width;
                const canvasHeight = this.height;
                const imgAspectRatio = img.width / img.height;
                const canvasAspectRatio = canvasWidth / canvasHeight;

                if (maintainAspectRatio) {
                  if (imgAspectRatio > canvasAspectRatio) {
                    // 图片更宽，以宽度为准
                    drawWidth = canvasWidth;
                    drawHeight = canvasWidth / imgAspectRatio;
                    drawX = 0;
                    drawY = (canvasHeight - drawHeight) / 2;
                  } else {
                    // 图片更高，以高度为准
                    drawHeight = canvasHeight;
                    drawWidth = canvasHeight * imgAspectRatio;
                    drawX = (canvasWidth - drawWidth) / 2;
                    drawY = 0;
                  }
                } else {
                  drawWidth = canvasWidth;
                  drawHeight = canvasHeight;
                  drawX = 0;
                  drawY = 0;
                }
              } else if (
                maintainAspectRatio &&
                (width !== img.width || height !== img.height)
              ) {
                // 如果指定了尺寸且需要保持宽高比
                const aspectRatio = img.width / img.height;
                const targetAspectRatio = width / height;

                if (aspectRatio > targetAspectRatio) {
                  // 原图更宽，以宽度为准
                  drawWidth = width;
                  drawHeight = width / aspectRatio;
                } else {
                  // 原图更高，以高度为准
                  drawHeight = height;
                  drawWidth = height * aspectRatio;
                }
              }

              // 绘制图片到 canvas
              ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

              // 标记已经绘制过内容
              this.hasDrew = true;

              // 触发更新事件
              this.$emit("image-imported", {
                originalWidth: img.width,
                originalHeight: img.height,
                drawWidth,
                drawHeight,
                drawX,
                drawY
              });

              resolve({
                success: true,
                imageInfo: {
                  originalWidth: img.width,
                  originalHeight: img.height,
                  drawWidth,
                  drawHeight,
                  drawX,
                  drawY
                }
              });
            } catch (error) {
              reject(new Error(`绘制图片失败: ${error.message}`));
            }
          };

          img.onerror = () => {
            reject(new Error("图片加载失败，请检查 base64 数据是否正确"));
          };

          // 开始加载图片
          img.src = imageData;
        } catch (error) {
          reject(new Error(`导入图片失败: ${error.message}`));
        }
      });
    },

    // 导入图片并自动适应画布（便捷方法）
    importImageFitCanvas(base64Data, clearCanvas = true) {
      return this.importBase64Image(base64Data, {
        fitToCanvas: true,
        maintainAspectRatio: true,
        clearCanvas
      });
    },

    // 导入图片到指定位置（便捷方法）
    importImageAtPosition(
      base64Data,
      x,
      y,
      width,
      height,
      clearCanvas = false
    ) {
      return this.importBase64Image(base64Data, {
        x,
        y,
        width,
        height,
        clearCanvas,
        maintainAspectRatio: false
      });
    }
  }
};
</script>

<style scoped>
.scaled-esign-container {
  position: relative;
  overflow: hidden;
}

.esign-canvas {
  display: block;
  touch-action: none;
}
</style>
