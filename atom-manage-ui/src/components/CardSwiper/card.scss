@mixin absolute-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 图片加载 */
:deep(.el-image__placeholder) {
  background: url('@/assets/images/loading.gif') no-repeat 50% 50% !important;
  background-size: 20% !important;
}

.card-container {
  width: 100%;
  aspect-ratio: 5 / 3;
  perspective: 800px;
  perspective-origin: center;
  transform-style: preserve-3d;
  position: relative;

  .card,
  .bg-card {
    width: 100%;
    aspect-ratio: 16 / 9;
    border-radius: 5px;
    position: absolute;
    bottom: 0;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
  }

  .card {
    background-color: white;
    &:not(.placeholder) {
      cursor: pointer;
    }
  }

  .bg-card {
    transform: translateZ(-100px) translateY(-25px);
    background-color: #dbe5f6;
  }

  .icon-play {
    width: 40px;
    height: 40px;
    transform-origin: center;
  }

  .card-title {
    position: absolute;
    padding: 5px 10px;
    left: 0;
    bottom: 0;
    width: 100%;
    color: white;
    font-size: 12px;
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.4);
    display: grid;
    grid-template-columns: 4fr 1fr 2fr;

    .author,
    .time {
      text-align: right;
    }
  }

  :deep(.el-image), .icon-play {
    @include absolute-center;
    transition: all 0.6s ease;
  }

  & .card:not(.placeholder):hover {
    :deep(.el-image) {
      transform: translate(-50%, -50%) scale(1.1);
    }

    .card-title {
      grid-template-columns:1fr;
      background-color: rgba(0, 0, 0, 0.6);

      .title {
        text-align: center;
      }

      .author,
      .time {
        display: none;
      }
    }
  }
}
