<script setup lang="ts">
import resource_error from "@/assets/images/resource_error.jpg";
import image_none from "@/assets/images/image_none.png";
import file from "@/assets/images/file.png";
import play from "@/assets/images/play.png";
import { VueFlip } from "vue-flip";
import { CSSProperties } from "vue";

interface Props {
  url?: string;
  type: "image" | "video" | "file" | "placeholder";
  flipping?: boolean;
  showBgCard?: boolean;
  cardStyle?: CSSProperties;
}

const props = withDefaults(defineProps<Props>(), {
  showBgCard: true
});
</script>

<template>
  <section class="card-container" :class="[showBgCard ? '' : 'h-full']">
    <vue-flip width="100%" height="100%" :model-value="flipping">
      <template #front>
        <div
          class="card"
          :style="cardStyle"
          :class="[
            type === 'placeholder' ? 'placeholder' : '',
            showBgCard ? '' : 'h-full'
          ]"
        >
          <div class="card-content">
            <slot>
              <!--图片-->
              <el-image
                class="w-full h-full"
                v-if="type === 'image'"
                fit="cover"
                :src="url"
              >
                <template #error>
                  <img
                    class="w-full h-full object-cover"
                    :src="resource_error"
                    alt="图片加载失败"
                  />
                </template>
              </el-image>
              <!--=视频-->
              <el-image
                class="w-full h-full"
                fit="cover"
                v-if="type === 'video'"
                :src="url"
              >
                <template #error>
                  <img
                    class="w-full h-full object-cover"
                    :src="resource_error"
                    alt="图片加载失败"
                  />
                </template>
              </el-image>
              <img
                class="icon-play"
                v-if="type === 'video'"
                :src="play"
                alt=""
              />
              <!--文件-->
              <img
                class="w-1/3 absolute inset-x-1/2 -translate-x-1/2 inset-y-1/2 -translate-y-1/2"
                v-else-if="type === 'file'"
                :src="file"
                alt="图片加载失败"
              />
              <!--占位符-->
              <el-image
                class="w-full h-full"
                v-if="type === 'placeholder'"
                fit="cover"
                :src="image_none"
              />
            </slot>
          </div>
          <div class="card-title" v-if="type !== 'placeholder'">
            <div class="title truncate">
              <slot name="title" />
            </div>
            <div class="author truncate">
              <slot name="author" />
            </div>
            <div class="time truncate">
              <slot name="time" />
            </div>
          </div>
        </div>
      </template>
    </vue-flip>
    <div class="bg-card" v-if="showBgCard" />
  </section>
</template>

<style lang="scss" scoped>
@import "card";
</style>
