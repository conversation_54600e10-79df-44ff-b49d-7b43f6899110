<script setup lang="ts">
import {defineEmits} from 'vue'
import useDrag from "@/components/CardSwiper/useDrag.ts";
import resource_error from "@/assets/images/resource_error.jpg";
import image_none from "@/assets/images/image_none.png";
import file from "@/assets/images/file.png";
import play from "@/assets/images/play.png";

const {handleStart, handleMove, handleEnd, cardRefs, loop} = useDrag()

const props = defineProps<{ url?: string, type: 'image' | 'video'| 'file' | 'placeholder' }>()

function handleDrag(e: DragEvent) {
  e.preventDefault()
}

const emit = defineEmits(['handleNext', 'isDrag', 'dragStart'])
</script>

<template>

  <section class="card-container card-swiper z-[99]"
           @mousedown="e => handleStart(e, emit)"
           @mousemove="handleMove"
           @mouseup="e => handleEnd(e,emit)"
           @dragstart="handleDrag">
    <template v-for="(v,i) in loop" :key="i">
      <div class="card" :style="`--i:${i}`"
           :class="[url?'':'placeholder']"
           :ref="el => cardRefs[i] = el">
        <template v-if="i === 0 && url">
          <!--图片-->
          <el-image class="w-full h-full" v-if="type === 'image'" fit="cover" :src="url">
            <template #error>
              <img class="w-full h-full object-cover" :src="resource_error" alt="图片加载失败"/>
            </template>
          </el-image>
          <!--=视频-->
          <el-image class="w-full h-full" fit="cover" v-if="type === 'video'" :src="url">
            <template #error>
              <img class="w-full h-full object-cover" :src="resource_error" alt="图片加载失败"/>
            </template>
          </el-image>
          <img class="icon-play" v-if="type === 'video'" :src="play" alt="">
          <!--文件-->
          <img
              class="w-1/3 absolute inset-x-1/2 -translate-x-1/2 inset-y-1/2 -translate-y-1/2"
              v-if="type === 'file'" :src="file" alt="图片加载失败"/>

          <div class="card-title" v-if="type !== 'placeholder'">
            <div class="title truncate">
              <slot name="title"/>
            </div>
            <div class="author truncate">
              <slot name="author"/>
            </div>
            <div class="time truncate">
              <slot name="time"/>
            </div>
          </div>
        </template>
        <template v-else>
          <!--占位符-->
          <el-image class="w-full h-full placeholder" fit="cover" :src="image_none"/>
        </template>
      </div>
    </template>
  </section>
</template>

<style lang="scss" scoped>
@import "./index.scss";
</style>
