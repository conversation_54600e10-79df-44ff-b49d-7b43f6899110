@import './card.scss';

.card-container.card-swiper {
  aspect-ratio: unset;
  .card {
    aspect-ratio: 500/297;
    transform: translateZ(calc(-100px * var(--i))) translateY(calc(-35px * var(--i)));
    filter: drop-shadow(2px 2px 10px rgba(0, 0, 0, 0.2));

    &:not(:first-child) {
      background-color: #dbe5f6;
    }

    .icon-play {
      width: 60px;
      height: 60px;
    }

    .card-title {
      grid-template-columns: 6fr 1fr 1fr;
    }
  }
}


