{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.0.2", "a": "", "k": "", "d": "", "tc": ""}, "fr": 25, "ip": 0, "op": 60, "w": 296, "h": 274, "nm": "Option 2", "ddd": 0, "assets": [{"id": "image_0", "w": 385, "h": 375, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149, 160, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [105, 105, 100]}, {"t": 29, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.5, -3.75], [28.25, -35.25]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.266666666667, 0.541176470588, 1, 1], "ix": 3}, "o": {"a": 0, "k": 99, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [0]}, {"t": 40, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"t": 24, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 60, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149, 160, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [105, 105, 100]}, {"t": 29, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-28.75, -4.75], [2.25, 33], [56.5, -33]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.266666666667, 0.541176470588, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [0]}, {"t": 40, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 24, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 60, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149, 160, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [105, 105, 100]}, {"t": 29, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-58.647, -4.629], [-28.23, 32.403]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.266666666667, 0.541176470588, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.25, -0.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [0]}, {"t": 38, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 22, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 60, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "Fly", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [100]}, {"t": 22, "s": [0], "h": 1}, {"t": 40, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [-180]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [-330]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [-330]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [-540]}, {"t": 60, "s": [-690]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [187, 185, 0], "to": [108.667, -112, 0], "ti": [31.796, -102.292, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [58, 111, 0], "to": [-13.306, 42.807, 0], "ti": [-42, 11.833, 0]}, {"t": 24, "s": [142, 168, 0], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [142, 168, 0], "to": [179.877, -48.718, 0], "ti": [55.679, -95.081, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [66.265, 94.694, 0], "to": [-47.998, 80.331, 0], "ti": [-36.511, 42.665, 0]}, {"t": 60, "s": [187, 185, 0]}], "ix": 2}, "a": {"a": 0, "k": [266.733, 153.364, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [30, 30, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 36, "s": [0, 0, 100]}, {"t": 60, "s": [30, 30, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 60, "st": 0, "bm": 0}], "markers": []}