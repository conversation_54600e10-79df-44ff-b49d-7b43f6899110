@keyframes schedule-in-width {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

@keyframes schedule-out-width {
  from {
    width: 100%;
  }

  to {
    width: 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes close {
  from {
    transform: translate(-50%, -50%);
  }

  to {
    transform: translate(0, -50%);
  }
}

.tags-view {
  width: 100%;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: var(--el-text-color-primary);
  background: #fff;
  position: relative;
  box-shadow: 0 0 1px #888;

  .scroll-item {
    border-radius: 3px 3px 0 0;
    padding: 0 6px;
    box-shadow: 0 0 1px #888;
    position: relative;
    margin-right: 4px;
    height: 28px;
    display: inline-block;
    line-height: 28px;
    transition: all 0.4s;
    cursor: pointer;

    .el-icon-close {
      font-size: 10px;
      color: var(--el-color-primary);
      cursor: pointer;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
      transition: font-size 0.2s;

      &:hover {
        border-radius: 50%;
        color: #fff;
        background: #b4bccc;
        font-size: 13px;
      }
    }

    &.is-closable:not(:first-child) {
      &:hover {
        padding-right: 18px;

        &:not(.is-active) {
          .el-icon-close {
            animation: close 200ms ease-in forwards;
          }
        }
      }
    }
  }

  .tag-title {
    text-decoration: none;
    color: var(--el-text-color-primary);
    padding: 0 4px;
  }

  .scroll-container {
    flex: 1;
    overflow: hidden;
    padding: 5px 0;
    white-space: nowrap;
    position: relative;

    .tab {
      position: relative;
      float: left;
      list-style: none;
      overflow: visible;
      white-space: nowrap;
      transition: transform 0.5s ease-in-out;

      .scroll-item {
        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        &:nth-child(1) {
          margin-left: 5px;
        }
      }
    }
  }

  /* 右键菜单 */
  .contextmenu {
    margin: 0;
    background: #fff;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    color: var(--el-text-color-primary);
    font-weight: normal;
    font-size: 13px;
    white-space: nowrap;
    outline: 0;
    box-shadow: 0 2px 8px rgb(0 0 0 / 15%);

    li {
      width: 100%;
      margin: 0;
      padding: 7px 12px;
      cursor: pointer;
      display: flex;
      align-items: center;

      &:hover {
        // background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }

      svg {
        display: block;
        margin-right: 0.5em;
      }
    }
  }
}

.el-dropdown-menu {
  li {
    width: 100%;
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;

    svg {
      display: block;
      margin-right: 0.5em;
    }
  }
}

.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #606266;
  background: #f0f0f0;
}

:deep(.el-dropdown-menu__item) i {
  margin-right: 10px;
}

:deep(.el-dropdown-menu__item--divided) {
  margin: 1px 0;
}
.el-dropdown-menu__item--divided::before {
  margin: 0;
}

.el-dropdown-menu__item.is-disabled {
  cursor: not-allowed;
}

.scroll-item.is-active {
  // background-color: var(--el-color-primary-light-9);
  position: relative;
  color: #fff;

  &:not(:first-child) {
    padding-right: 18px;
  }

  .el-icon-close {
    transform: translate(0, -50%);
  }

  .tag-title {
    color: var(--el-color-primary) !important;
  }
}

.arrow-left,
.arrow-right,
.arrow-down {
  width: 40px;
  height: 38px;
  color: var(--el-text-color-primary);
  position: relative;

  svg {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 50%);
  }
}

.arrow-left {
  box-shadow: 5px 0 5px -6px #ccc;

  &:hover {
    cursor: w-resize;
  }
}

.arrow-right {
  box-shadow: -5px 0 5px -6px #ccc;
  border-right: 0.5px solid #ccc;

  &:hover {
    cursor: e-resize;
  }
}

/* 卡片模式下鼠标移入显示蓝色边框 */
.card-in {
  color: var(--el-color-primary);

  .tag-title {
    color: var(--el-color-primary);
  }
}

/* 卡片模式下鼠标移出隐藏蓝色边框 */
.card-out {
  border: none;
  color: #666;

  .tag-title {
    color: #666;
  }
}

/* 灵动模式 */
.schedule-active {
  width: 100%;
  height: 2px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: var(--el-color-primary);
}

/* 灵动模式下鼠标移入显示蓝色进度条 */
.schedule-in {
  width: 100%;
  height: 2px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: var(--el-color-primary);
  animation: schedule-in-width 200ms ease-in;
}

/* 灵动模式下鼠标移出隐藏蓝色进度条 */
.schedule-out {
  width: 0;
  height: 2px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: var(--el-color-primary);
  animation: schedule-out-width 200ms ease-in;
}
