<script setup lang="ts">
import { nextTick, PropType, ref } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import Message from "@iconify-icons/ep/message";
import { MessageRow } from "@/types/message/messageManage";
import { Clock } from "@element-plus/icons-vue";

const props = defineProps({
  noticeItem: {
    type: Object as PropType<MessageRow>,
    default: () => {}
  },
  isHandle: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["handle", "ignore"]);
const titleRef = ref(null);
const titleTooltip = ref(false);
const { tooltipEffect } = useNav();

function hoverTitle() {
  nextTick(() => {
    titleRef.value?.scrollWidth > titleRef.value?.clientWidth
      ? (titleTooltip.value = true)
      : (titleTooltip.value = false);
  });
}

function handleHandleMessage() {
  emit("handle", props.noticeItem);
}
function handleIgnoreMessage() {
  emit("ignore", props.noticeItem);
}
</script>

<template>
  <div class="notice-container">
    <div class="notice-container-text" @click="handleHandleMessage">
      <div class="notice-text-title">
        <!-- <el-tooltip
          popper-class="notice-title-popper"
          :effect="tooltipEffect"
          :disabled="!titleTooltip"
          :content="noticeItem.title"
          placement="top-start"
          :enterable="!isMobile"
        >
          <div
            ref="titleRef"
            class="notice-title-content"
            @mouseover="hoverTitle"
          >
            {{ noticeItem.title }}
          </div>
        </el-tooltip> -->
        <div
          ref="titleRef"
          class="notice-title-content"
          @mouseover="hoverTitle"
        >
          {{ noticeItem.title }}
        </div>
        <el-tag
          v-if="isHandle"
          round
          effect="light"
          :type="noticeItem.completed ? 'success' : 'danger'"
          size="large"
        >
          {{ noticeItem.completed ? "已完成" : "未完成" }}
        </el-tag>
      </div>

      <div class="dcdbList">
        <div class="peritem">
          <div class="peritem-tr2">
            <div class="peritem-tr2-left">
              <el-icon class="custom-icon" :size="18">
                <Clock />
              </el-icon>
              {{ noticeItem.datetime }}
            </div>
            <!--            <div class="peritem-tr2-right">-->
            <!--              {{ noticeItem.source == "1" ? "来自会议" : "新建任务" }}-->
            <!--            </div>-->
          </div>
        </div>
      </div>

      <!--      <div class="flex justify-between items-center">-->
      <!--        <div class="notice-text-datetime text-[#00000073] dark:text-white">-->
      <!--          {{ noticeItem.datetime }}-->
      <!--        </div>-->

      <!--        <div>-->
      <!--          <span-->
      <!--            class="text-gray-400 cursor-pointer"-->
      <!--            @click="handleIgnoreMessage"-->
      <!--            >忽略</span-->
      <!--          >-->
      <!--          <span-->
      <!--            v-if="isHandle"-->
      <!--            class="ml-2 text-blue-500 cursor-pointer"-->
      <!--            @click="handleHandleMessage"-->
      <!--            >处理</span-->
      <!--          >-->
      <!--        </div>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<style>
.notice-title-popper {
  max-width: 238px;
}
</style>
<style scoped lang="scss">
.notice-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0.625rem 0;
  border-bottom: 0.0625rem solid #d4dcea;
  font-size: 1.125rem;

  .notice-container-avatar {
    margin-right: 1rem;
    background: #fff;
  }

  .notice-container-text {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;

    .notice-text-title {
      display: flex;
      margin-bottom: 0.3125rem;
      font-weight: 400;
      cursor: pointer;
      align-items: center;

      .notice-title-content {
        flex: 1;
        width: 200px;
        overflow: hidden;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
      }

      .notice-title-extra {
        float: right;
        margin-top: -1.5px;
        font-weight: 400;
      }
    }

    .notice-text-description,
    .notice-text-datetime {
      font-size: 16px;
      line-height: 1.5715;
    }

    .notice-text-description {
      display: -webkit-box;
      text-overflow: ellipsis;
      overflow: hidden;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .notice-text-datetime {
      margin-top: 4px;
    }
  }
}

.dcdbList .peritem {
  padding: 0.3125rem;
  color: #606266;
  font-size: 1.125rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  .peritem-tr1,
  .peritem-tr2 {
    display: flex;
    justify-content: space-between;
    .peritem-tr1-left,
    .peritem-tr2-left,
    .peritem-tr1-right,
    .peritem-tr2-right {
      max-width: 98%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .peritem-tr2-left {
      font-style: italic;
    }
    .peritem-tr1-left,
    .peritem-tr2-left {
      flex: 1;
    }
    .peritem-tr1-right,
    .peritem-tr2-right {
      flex-basis: 5rem;
      text-align: right;
    }
  }
}
</style>
