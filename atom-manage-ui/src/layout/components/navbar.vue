<script setup lang="ts">
import mixNav from "./sidebar/mixNav.vue";
import { useNav } from "@/layout/hooks/useNav";
import Breadcrumb from "./sidebar/breadCrumb.vue";
import topCollapse from "./sidebar/topCollapse.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
const { layout, device, pureApp, toggleSideBar, username, avatarsStyle } =
  useNav();
import { assetPath } from "@/utils/atom";
import { computed } from "vue";
import { isEmpty } from "@pureadmin/utils";
import { useUserStoreHook } from "@/store/modules/user";
import Profile from "./profile/index.vue";

const avatar = computed(() => {
  return isEmpty(useUserStoreHook()?.avatar)
    ? "default.svg"
    : useUserStoreHook()?.avatar;
});

const avatarUrl = computed(() => {
  return assetPath("avatar/" + avatar.value);
});

const { VITE_PUBLIC_PATH } = import.meta.env;

// 去移动端首页
const goUserHome = async () => {
  const scheme = window.location.protocol;
  const host = window.location.host;
  window.location.href = `${scheme}//${host}${VITE_PUBLIC_PATH}user/home/<USER>
};
</script>

<template>
  <div class="navbar bg-transparent pl-[10px]">
    <topCollapse
      v-if="device === 'mobile'"
      class="hamburger-container"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />

    <Breadcrumb
      v-if="layout !== 'mix' && device !== 'mobile'"
      class="breadcrumb-container"
    />

    <mixNav v-if="layout === 'mix'" />

    <div v-if="layout === 'vertical'" class="vertical-header-right">
      <!-- 菜单搜索 -->
      <!--<Search />-->
      <!-- 通知 -->
      <!--<Notice id="header-notice" />-->
      <!-- 退出登录 -->
      <!--      <Profile />-->

      <!--<span
              class="set-icon navbar-bg-hover"
              title="打开项目配置"
              @click="onPanel"
            >
              <IconifyIconOffline :icon="Setting" />
            </span>-->
      <div class="flex items-center text-sm mr-4">
        <img
          :src="avatarUrl"
          :style="avatarsStyle"
          class="rounded-full w-[22px] h-[22px]"
        />
        <p v-if="username" class="mr-2">{{ username }}</p>
        <el-link
          class="mr-2"
          type="primary"
          v-auth="['system:user:home']"
          @click="goUserHome"
          :icon="useRenderIcon('ri:arrow-right-circle-line')"
          :underline="false"
        >
          前往会议端
        </el-link>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 48px;
  overflow: hidden;

  .hamburger-container {
    line-height: 48px;
    height: 100%;
    float: left;
    cursor: pointer;
  }

  .vertical-header-right {
    display: flex;
    min-width: 280px;
    height: 48px;
    align-items: center;
    color: #000000d9;
    justify-content: flex-end;

    .el-dropdown-link {
      height: 48px;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      cursor: pointer;
      color: #000000d9;

      p {
        font-size: 14px;
      }

      img {
        width: 22px;
        height: 22px;
        border-radius: 50%;
      }
    }
  }

  .breadcrumb-container {
    float: left;
    margin-left: 16px;
  }
}
</style>
