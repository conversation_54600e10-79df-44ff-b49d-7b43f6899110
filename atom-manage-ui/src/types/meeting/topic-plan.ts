import { BaseEntity } from "@/types";

export class TopicPlan extends BaseEntity {
  // * 主键
  id?: number;
  // * 主题
  title: string;

  planList: any[] = [];
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TopicPlanQuery {
  // * 主题
  title?: string;
}

export class TopicPlanDetail extends BaseEntity {
  // * 主键
  id?: number;
  // * 议题编号
  topicId: number;
  // * 开始时间
  beginTime: Date;
  // * 结束时间
  endTime: Date;
  // * 当前计划的类型名称
  typeName: string;
  // * 关联编号
  typeId: number;
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TopicPlanDetailQuery {
  // * 议题编号
  topicId?: number;
  // * 开始时间
  beginTime?: Date;
  // * 结束时间
  endTime?: Date;
  // * 当前计划的类型名称
  typeName?: string;
  // * 关联编号
  typeId?: number;
}
