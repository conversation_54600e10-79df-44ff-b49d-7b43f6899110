import { BaseEntity } from "@/types";

export class MeetingJoiner extends BaseEntity {
  // * 主键id
  id?: number;
  // * 1：有效  0：删除
  isActive?: string;
  // * 会议主键id
  meetingId: number;
  // * 参会用户编号
  userId: number;
  // * 参会用户姓名
  userName: string;
  // 是否同意
  isAgree: string;
  // 类型
  userType: string;
  // 参会人员排序
  joinerOrder?: number;
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class MeetingJoinerQuery {
  // * 会议主键id
  meetingId?: number;
  // * 参会用户编号
  userId?: number;
}
