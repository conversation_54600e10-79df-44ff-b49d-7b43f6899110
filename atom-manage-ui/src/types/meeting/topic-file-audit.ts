import { BaseEntity } from "@/types";

export class TopicFileAudit extends BaseEntity {
  // * 主键
  id?: number;
  // * 是否有效
  isActive?: string;
  // * 审核用户编号
  userId: number;
  // * 审核状态
  status: string;
  // * 备注
  remarks: string;
  // * 主题编号
  topicId: number;
  // * 文件编号
  fileId: number;
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TopicFileAuditQuery {
  // * 审核用户编号
  userId?: number;
  // * 审核状态
  status?: string;
  // * 备注
  remarks?: string;
  // * 主题编号
  topicId?: number;
  // * 文件编号
  fileId?: number;
}

export class TopicFileAuditRow {
  // * 议题编号
  topicId: number;

  // * 议题名称
  topicName: string;
}

export class TopicUpload {
  // * 议题id
  topicId?: number;
  // * 类型id
  typeId: string;
  // * 文件列表
  fileList: any[] = [];
  // * 审核人员
  userId: string;
}
