import { BaseEntity } from "@/types";
import { TopicMeetingPlan } from "@/types/meeting/topicMeetingPlan";

export class TopicMeeting extends BaseEntity {
  // * 主键
  id?: number;
  // * 议题标题
  topicTitle: string;
  // * 预召开会议id（过时）
  planId: number;
  // * 会议类型（过时）
  meetType: string;
  // * 事项编码
  noticeCode: string;
  // * 补充说明
  remark: string;
  // * 议题排序
  topicOrder: number;
  // * 有效位
  isActive?: string;
  // * 汇报人
  reportUserId?: number;
  // * 汇报人姓名
  reportUserName?: string;
  // * 部门领导
  departUserId?: number;
  departUserName?: string;
  // * 分管领导
  leaderUserId?: number;
  leaderUserName?: string;
  // * 上传人单位
  creatorDept?: string;
  // * 议题正文
  mainFile?: any;
  // * 议题附件
  topicFileList?: any;
  // * 关联的预召开会议是否过期（过时）
  delayPlan?: boolean;

  // * 关联的多个预会议
  planList?: TopicMeetingPlan[];
  // * 关联的多个预会议id (表单编辑提交用)
  planIds?: number[];
  // * 议题正文
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
    this.planList = [];
    this.planIds = [];
  }
}

export class TopicMeetingLinkEdit {
  id?: number;
  topicTitle: string;
  reportUserId: number;
}

export class TopicMeetingLinkOrder {
  id?: number;
  topicOrder: number;
}

export class TopicMeetingQuery {
  // * 议题标题
  topicTitle?: string;
  // * 会议类型
  meetType?: number;
  // * 事项编码
  dateRange?: any;
  // * 预召开会议id
  planId?: number;
}

export class FileComment {
  id?: number;
  linkId: number;
  linkFileId: number;
  fileId: number;
  url: string;
  originalName: string;
  createTime: Date;
}

export class FileCommentName {
  id?: number;
  fileName: string;
  suffix: string;
}
