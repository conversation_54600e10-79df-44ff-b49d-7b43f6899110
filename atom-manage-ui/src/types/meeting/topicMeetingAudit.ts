import { BaseEntity } from "@/types";
import { TopicMeetingPlan } from "./topicMeetingPlan";
export class TopicMeetingAuditInfo extends BaseEntity {
  // * 主键
  id?: number;
  // * 审批状态
  status: string;
  // * 备注
  remark: string;
  // * 关联的议题收集id
  topicId: number;
  // * 有效位
  isActive?: string;
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TopicMeetingAudit {
  // * 议题收集id
  topicId?: number;
  // * 议题标题
  topicTitle?: string;
  // * 会议类型
  meetType?: string;
  // * 议题上传时间
  createTime?: Date;
  // * 议题审批状态
  status?: string;
  // * 上传单位
  creatorDept?: string;
  // * 汇报人姓名
  reportUserName?: string;
  // * 关联议题的预召开会议
  planList?: TopicMeetingPlan[];
}

export class TopicMeetingLinkRecall {
  id?: number;
  remark: string;
}

export class TopicMeetingAuditQuery {
  // * 用户类型 1 部门领导 2 分管领导
  userType?: string;
  // * 用户id
  userId?: number;
  // * 审批状态
  status?: string;
  // * 关联的议题收集id
  topicMeetId?: number;
  // * 议题标题
  topicTitle?: string;
  // * 会议类型
  meetType?: string;
  // * 日期范围
  dateRange?: any;
}
