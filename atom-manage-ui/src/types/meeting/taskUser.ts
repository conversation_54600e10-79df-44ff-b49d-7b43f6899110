import { BaseEntity } from "@/types";

export class TaskUser extends BaseEntity {
  // * 主键
  id?: number;
  // * NAME
  taskName: string;
  // * 用户id
  userId: number;
  // * 督办主表id
  taskId: number;
  // * 用户名字
  userName: string;
  // * 是否删除
  isActive?: string;
  // userIds: number;

  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TaskUserQuery {
  // * NAME
  taskName?: string;
  // * 用户id
  userId?: number;
  // * 督办主表id
  taskId?: number;
  // * 用户名字
  userName?: string;
}
