import { BaseEntity } from "@/types";

export class MeetingMemo extends BaseEntity {
    // * 主键
    id?: number;
    // * 便签内容
    memoWord: string;
    // * 会议id
    meetingId: number;
    // * 是否删除
    isActive?: string;
    // * 姓名
    userName?: string;
    constructor() {
        super();
        //  * 根据自身业务需要的初始化值修改
    }
}

export class MeetingMemoQuery {
    // * 便签内容
    memoWord?: string;
    // * 会议id
    meetingId?: number;
    // * 创建人
    creatorId?: number;
}
