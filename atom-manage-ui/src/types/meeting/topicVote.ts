import { BaseEntity } from "@/types";

export class TopicVote extends BaseEntity {
  // * 主键
  id?: number;
  // * 会议id
  meetingId: number;
  // * 用户id
  userId: number;
  // * 议题id
  topicId: number;
  // * 表决意见，1 同意；2 原则同意；3 不同意；4 保留意见
  status: string;
  // * 是否删除
  isActive?: string;
  // * 用户名字
  userName?: string;
  // * 备注
  remark?: string;
  // * 用户类型
  userType?: string;
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TopicVoteQuery {
  // * 会议id
  meetingId?: number;
  // * 用户id
  userId?: number;
  // * 议题id
  topicId?: number;
  // * 表决意见，1 同意；2 原则同意；3 不同意；4 保留意见
  status?: string;
}

export class TopicVoteAndMeetingLink {
  // 会议id
  meetingId?: number;
  // 会议标题
  meetingTitle?: string;
  // 会议类型
  meetingType?: string;
  // 预会议id
  planId?: number;
  // 预会议名称
  planTitle?: string;
  // 投票明细
  votes: TopicVote[];
  // link
  link: MeetingLinkVote;
}

export class MeetingLinkVote {
  // * 关联id
  id?: number;
  // * 预会议id (特殊议题为空)
  planId?: number;
  // * 议题名称
  topicTitle?: string;
  // * 表决意见，1 同意；2 原则同意；3 不同意；4 保留意见
  status?: string;
}
