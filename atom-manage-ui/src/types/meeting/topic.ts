import { BaseEntity } from "@/types";

export class Topic extends BaseEntity {
  // * 主键
  id?: number;
  // * 标题
  title: string;
  // * 状态
  status: string;
  // * $column.columnComment
  content: string;

  // * 文件列表
  fileList: any[] = [];

  // 计划列表
  planList: any[] = [];

  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class TopicQuery {
  // * 标题
  title?: string;
  // * 开始时间
  beginTime?: Date;
  // * 结束时间
  endTime?: Date;
  // * 状态
  status?: string;
  // * $column.columnComment
  content?: string;
}
