import { BaseEntity } from "@/types";

export class MeetingUserSign extends BaseEntity {
  // * 主键
  id?: number;
  // * 用户id
  userId: number;
  // * 签名base64码
  signPic: string;
  // * 会议id
  meetingId: string;
  // * 是否删除
  isActive?: string;
  constructor() {
    super();
    //  * 根据自身业务需要的初始化值修改
  }
}

export class MeetingUserSignQuery {
  // * 用户id
  userId?: number;
  // * 签名base64码
  signPic?: string;
  // * 会议id
  meetingId?: number;
}

export class MeetingUserList {
  id?: number;

  meetingId?: number;

  userId?: number;

  attendanceStatus?: string;

  userType?: string;

  userName?: string;

  updateTime?: Date;
}
