export class BaseEntity {
  // * 创建人
  creatorId?: string;

  // * 更新人
  updaterId?: string;

  // * 创建时间
  createTime?: Date;

  // * 更新时间
  updateTime?: Date;
}

/** {isAsc:"asc",orderByColumn:"id"} order by id asc
 * {isAsc:"asc",orderByColumn:"id,createTime"} order by id asc,create_time asc
 * {isAsc:"desc",orderByColumn:"id,createTime"} order by id desc,create_time desc
 * {isAsc:"asc,desc",orderByColumn:"id,createTime"} order by id asc,create_time desc
 */
export class BaseQuery {
  orderByColumn?: string;

  isAsc?: string;
}
