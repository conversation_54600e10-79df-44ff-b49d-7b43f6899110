import { BaseEntity } from "@/types";

export class SysDictType extends BaseEntity {
  // * 字典主键
  dictId: string;

  // * 字典名称
  dictName: string;

  // * 字典类型
  dictType: string;

  // * 状态（0正常 1停用）
  status: string;

  // * 备注
  remark: string;

  // * 父级字典
  parentId: string;

  constructor() {
    super();
    this.dictId = undefined;
    this.dictName = undefined;
    this.dictType = undefined;
    this.status = "0";
    this.remark = undefined;
  }
}

export class SysDictTypeQuery {
  // * 字典名称
  dictName: string;

  // * 字典类型
  dictType: string;

  // * 状态（0正常 1停用）
  status: string;

  dateRange?: any;

  // * 额外参数
  get params(): any {
    return this.dateRange
      ? {
          beginTime: this.dateRange[0],
          endTime: this.dateRange[1]
        }
      : undefined;
  }
}
