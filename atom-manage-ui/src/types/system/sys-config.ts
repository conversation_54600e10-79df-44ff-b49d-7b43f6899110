import { BaseEntity } from "@/types";

export class SysConfig extends BaseEntity {
  // * 主键
  id: string;

  // * 参数名称
  configName: string;

  // * 参数键名
  configKey: string;

  // * 参数键值
  configValue: string;

  // * 系统内置（Y是 N否）
  isSys: string;

  // * 备注
  remark: string;

  constructor() {
    super();
    this.id = undefined;
    this.configName = undefined;
    this.configKey = undefined;
    this.configValue = undefined;
    this.isSys = "Y";
    this.remark = undefined;
  }
}

export class SysConfigQuery {
  // * 参数名称
  configName?: string;

  // * 参数键名
  configKey?: string;

  // * 系统内置（Y是 N否）
  isSys?: string;

  // * 时间范围
  dateRange?: any;

  // * 额外参数
  get params(): any {
    return this.dateRange
      ? {
          beginTime: this.dateRange[0],
          endTime: this.dateRange[1]
        }
      : undefined;
  }
}
