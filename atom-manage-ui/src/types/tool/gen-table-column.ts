import { BaseEntity } from "@/types";

export class GenTableColumn extends BaseEntity {
  // * 主键
  id: string;

  // * 归属表编号
  tableId: string;

  // * 列名称
  columnName: string;

  // * 列描述
  columnComment: string;

  // * 列类型
  columnType: string;

  // * JAVA类型
  javaType: string;

  // * JAVA字段名
  javaField: string;

  // * 是否主键（1是）
  isPk: string;

  // * 是否自增（1是）
  isIncrement: string;

  // * 是否必填（1是）
  isRequired: string;

  // * 是否为插入字段（1是）
  isInsert: string;

  // * 是否编辑字段（1是）
  isEdit: string;

  // * 是否列表字段（1是）
  isList: string;

  // * 是否查询字段（1是）
  isQuery: string;

  // * 查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）
  queryType: string;

  // * 显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）
  htmlType: string;

  // * 字典类型
  dictType: string;

  // * 排序
  sort: number;
}
