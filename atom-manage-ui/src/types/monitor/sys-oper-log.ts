export class SysOperLog {
  // * 日志主键
  operId: number;

  // * 操作模块
  title: string;

  // * 业务类型（0其它 1新增 2修改 3删除）
  businessType: number;

  // * 业务类型数组
  businessTypes: any;

  // * 业务描述
  description: string;

  // * 请求方法
  method: string;

  // * 请求方式
  requestMethod: string;

  // * 操作类别（0其它 1后台用户 2手机端用户）
  operatorType: number;

  // * 操作人员
  operName: string;

  // * 机构名称
  orgName: string;

  // * 请求url
  operUrl: string;

  // * 操作地址
  operIp: string;

  // * 操作地点
  operLocation: string;

  // * 请求参数
  operParam: string;

  // * 返回参数
  jsonResult: string;

  // * 操作状态（0正常 1异常）
  status: number;

  // * 错误消息
  errorMsg: string;

  // * 操作时间
  operTime: Date;

  // * 消耗时间
  costTime: number;
}

export class SysOperLogQuery {
  // 系统模块
  title: string;

  // 操作人员
  operName: string;

  // 类型
  businessType: string;

  // 状态
  status: string;

  // 操作时间
  createTime: string;

  // * 时间范围
  dateRange?: any;

  // * 额外参数
  get params(): any {
    return this.dateRange
      ? {
          beginTime: this.dateRange[0],
          endTime: this.dateRange[1]
        }
      : undefined;
  }
}
