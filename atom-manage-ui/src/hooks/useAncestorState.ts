import { createInjectionState } from "@vueuse/core";
import { computed, shallowRef } from "vue";

// 声明父组件名称
const [useProvidedAncestorState, useAncestorState] = createInjectionState(
  (componentName: string) => {
    return shallowRef(componentName);
  }
);

function useAncestorDetection(targetComponentName: string) {
  const ancestorState = useAncestorState();

  const isInsideTarget = computed(() => {
    return ancestorState.value === targetComponentName;
  });

  return {
    isInsideTarget,
    ancestorState
  };
}

export { useProvidedAncestorState, useAncestorDetection };
