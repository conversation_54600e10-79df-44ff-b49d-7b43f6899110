import { Ref } from "vue";

function getPageScale() {
  const computedStyle = getComputedStyle(document.documentElement);
  const scaleStr = computedStyle.getPropertyValue("--page-scale").trim();

  if (scaleStr && !isNaN(parseFloat(scaleStr))) {
    return parseFloat(scaleStr);
  } else {
    return 1;
  }
}

export function usePureTableMaxHeight(
  containerRef: Ref<HTMLElement>,
  tableRef: Ref<any>
) {
  if (containerRef.value && tableRef.value?.$el) {
    const containerRect = containerRef.value.getBoundingClientRect();
    const tableRect = tableRef.value?.$el.getBoundingClientRect();
    return `${
      (containerRect.height - (tableRect.top - containerRect.top)) /
        getPageScale() -
      80
    }px`;
  }
  return "unset";
}
