@import "./mixin.scss";
@import "./transition.scss";
@import "./element-plus.scss";
@import "./sidebar.scss";
@import "./dark.scss";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;
}

.special-page {
  height: 100%;
  --el-font-size-base: 18px !important;

  .pure-table {
    .el-table--default {
      font-size: 18px !important;
    }
  }

  .el-select__placeholder {
    font-size: 17px !important;
  }

  .el-pagination {
    font-size: 16px;
    --el-pagination-font-size: 16px;
    --el-pagination-bg-color: rgba(255, 255, 255, 0.3);
    --el-pagination-button-disabled-bg-color: rgba(255, 255, 255, 0.3);
    --el-pagination-button-bg-color: rgba(255, 255, 255, 0.3);

    .el-select__wrapper,
    .el-input__wrapper {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  .el-table {
    --el-table-bg-color: rgba(255, 255, 255, 0.2);
    --el-table-tr-bg-color: rgba(255, 255, 255, 0.2);
    --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.2);
  }

  .bg-bg_color {
    background-color: transparent;
  }

  .el-form {

    .el-select__wrapper,
    .el-input__wrapper {
      background-color: rgba(255, 255, 255, 0.4);
    }
  }

  .el-input__wrapper {
    background-color: transparent;
  }

  .el-tree {
    background-color: transparent;
    --el-tree-node-content-height: 45px;

    .text-xs {
      font-size: 1rem;
    }
  }
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgb(225, 225, 225);
}

::-webkit-scrollbar-track {
  width: 5px;
}

//整体样式优化
.logotitle {
  transform: scaleY(1.15);
  letter-spacing: 5px;
}

.fontSize26.iconfont {
  font-size: 1.625rem;
}

.fontSize24.iconfont {
  font-size: 1.5rem;
}

.fontSize18.iconfont {
  font-size: 1.125rem;
}

.iconfont.pt3 {
  display: inline-block;
  position: relative;
  top: 0.1875rem;
}

.iconfont.pt2 {
  display: inline-block;
  position: relative;
  top: 0.125rem;
}

.iconfont.redDot {
  position: relative;
}

.iconfont.redDot::after {
  content: "";
  display: inline-block;
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background-color: #f56c6c;
  position: absolute;
  top: 0.125rem;
  left: 90%;
}

.table-bar-title {
  margin-right: 18px;
}

// .el-select-dropdown {
//   min-width: 100px !important;
// }

.selectitem {
  display: flex;
  justify-content: space-between;
  gap: 1.25rem;
}

.fileView {
  position: relative;
  top: 0.3125rem;
}

.glass-filter {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
