<template>
  <div class="cardtitle">
    <span class="titletxt">督查督办</span>
    <!-- <span class="more">
      <el-button :icon="MoreFilled" />
    </span> -->
  </div>
  <div class="dcdbList">
    <div class="peritem" v-for="item in dcdbData" :key="item.id">
      <div class="peritem-tr1">
        <div class="peritem-tr1-left">{{ item.title }}</div>
        <div class="peritem-tr1-right">
          <el-tag type="danger" size="large" round effect="light">待办</el-tag>
        </div>
      </div>
      <div class="peritem-tr2">
        <div class="peritem-tr2-left">
          <el-icon class="custom-icon" :size="20">
            <Clock />
          </el-icon>
          {{ item.datetime }} ~ {{ item.deadLine }}
        </div>
        <div class="peritem-tr2-right">
          {{ item.source == "1" ? "来自会议" : "新建任务" }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  contentListMeeting,
  getMeeting,
  getLately
} from "@/api/meeting/meeting";
defineOptions({
  name: "dcdb"
});
import { ref, onMounted, onBeforeUnmount } from "vue";
import type { CalendarDateType, CalendarInstance } from "element-plus";
import {
  ArrowRight,
  MoreFilled,
  Clock,
  Timer,
  ArrowLeftBold,
  ArrowRightBold
} from "@element-plus/icons-vue";
import { useGlobal } from "@pureadmin/utils";
// import { selectDictLabel } from "@/utils/atom";
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, meeting_place } = $useDict(
  "meeting_type",
  "meeting_place"
);

const dcdbData = ref([
  {
    id: "1815646295958282875", // 主键
    title: "元旦组织党员活动议事督查督办", //待办名称
    deadLine: "11-20", // 截止时间
    source: "1" // 来源 1 来自会议 2 新建任务
  },
  {
    id: "1815645295958282805", // 主键
    title: "对近期挂网招标项目做好标前策划、标书编制、评审等工作督查督办", //待办名称
    deadLine: "11-30", // 截止时间
    source: "2" // 来源 1 来自会议 2 新建任务 字典名称：TASK_TYPE
  },
  {
    id: "1815645295958282855", // 主键
    title: "对近期挂网招标项目做好标前策划、标书编制、评审等工作督查督办", //待办名称
    deadLine: "11-30", // 截止时间
    source: "2" // 来源 1 来自会议 2 新建任务 字典名称：TASK_TYPE
  }
]);

const handleCurrentChange = val => {
  console.log(val);
};
</script>

<style scoped lang="scss">
.cardtitle {
  font-size: 1.5rem;
  letter-spacing: 0.1875rem;
  transform: rotateX(18deg);
  padding-bottom: 0.625rem;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-left: 1.125rem;
}
.cardtitle::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 10%;
  width: 0.3125rem;
  height: 50%;
  background: #357ff3;
  border-radius: 0.3125rem;
}
.dcdbList {
  max-height: 31.25rem;
  overflow: hidden;
  overflow-y: auto;
}
.dcdbList .peritem {
  border-bottom: 0.0625rem solid #d4dcea;
  padding: 0.9375rem;
  color: #606266;
  font-size: 1.125rem;
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  .peritem-tr1,
  .peritem-tr2 {
    display: flex;
    justify-content: space-between;
    .peritem-tr1-left,
    .peritem-tr2-left,
    .peritem-tr1-right,
    .peritem-tr2-right {
      max-width: 98%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .peritem-tr2-left {
      font-style: italic;
    }
    .peritem-tr1-left,
    .peritem-tr2-left {
      flex: 1;
    }
    .peritem-tr1-right,
    .peritem-tr2-right {
      flex-basis: 5rem;
      text-align: right;
    }
  }
}
.dcdbList .peritem:first-of-type {
  border-top: 0.0625rem solid #d4dcea;
}
</style>
