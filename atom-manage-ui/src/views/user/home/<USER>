<template>
  <div
    class="component-root"
    v-loading="loading"
    element-loading-background="rgba(255, 255, 255, 0.1)"
  >
    <div class="notifytr1">
      <template v-if="notifyInfo.nofitytr1data.flag">
        <span style="font-size: 25px">
          <p class="titleTips">
            {{ notifyInfo.nofitytr1data.title }}
          </p>
          <p>尊敬的{{ notifyInfo.nofitytr1data.name }}：</p>
          <p
            class="info"
            style="
              text-indent: 53px;
              letter-spacing: 1px;
              line-height: 1.8;
              font-size: 26px;
            "
          >
            {{ notifyInfo.nofitytr1data.content }}
          </p>
          <p style="text-align: right">
            {{
              selectDictLabel(
                meeting_place,
                notifyInfo.nofitytr1data.meetingDept
              )
            }}
          </p>
          <p style="text-align: right">
            {{
              dayjs(notifyInfo.nofitytr1data.meetingDate).format("YYYY-MM-DD")
            }}
          </p>
        </span>
      </template>
      <template v-else>
        <!--      <el-empty :image="emptypng" style="margin-top: 50px"> </el-empty>-->
        <div class="centered-content">
          {{ notifyInfo.nofitytr1data.title }}
        </div>
      </template>
    </div>
    <div class="notifytr2">
      <div
        class="perCount"
        v-for="item in notifyInfo.nofitytr2data"
        :key="item.name"
      >
        <span class="titlename">{{ item.name }}：</span>
        <span class="count">{{ item.count }}</span>
      </div>
    </div>
    <div class="notifytr3">
      <div
        class="perYt"
        v-for="(item, index) in notifyInfo.titleList"
        :key="index"
        @click="changeTitle(item.name)"
        @mouseover.stop="changeTitle(item.name)"
        :class="{ active: notifyInfo.curTitleIndex == item.name }"
      >
        <div class="titlename">{{ item.name }}</div>
        <div class="icon">
          <!-- <i class="iconImg" :class="item.icon"></i> -->
          <span class="icon iconfont" :class="item.icon" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// import { bannerData } from "@/views/user/home/<USER>";

defineOptions({
  name: "notify"
});
import { ref, onMounted, watch } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { getMySelfTopic, getNotify } from "@/api/meeting/meeting";
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, meeting_place } = $useDict(
  "meeting_type",
  "meeting_place"
);
import { selectDictLabel } from "@/utils/atom";
import dayjs from "dayjs";
import { useUserStoreHook } from "@/store/modules/user";
const emit = defineEmits(["titlechange"]);
const roles = useUserStoreHook().roles;
const loading = ref(false);
const notifyInfo = ref({
  nofitytr1data: {
    title: "", // 通知标题
    name: "", // 接收人姓名（即尊敬的 何佐耀）
    content: "", // 通知内容（纯文字，非富文本）
    meetingDate: "", // 通知时间
    meetingDept: "", // 通知人部门
    flag: false // 是否有通知
  },
  nofitytr2data: {
    topicNum: {
      name: "议题数",
      count: 0
    }, // 议题数量
    documentNum: {
      name: "参考资料数",
      count: 0
    }, // 参考资料数
    attendUserNum: {
      name: "参会人数",
      count: 0
    }, // 参会人数
    readUserNum: {
      name: "列席人数",
      count: 0
    } //列席人员
  },
  titleList: [
    // {
    //   name: "议题资料",
    //   icon: "icon-bianjiwenjian",
    //   herf: "#"
    // },
    // {
    //   name: "会议签到",
    //   icon: "icon-bi",
    //   herf: "#"
    // },
    // {
    //   name: "我的议题",
    //   icon: "icon-ziliao1",
    //   herf: "#"
    // },
    // {
    //   name: "会议服务",
    //   icon: "icon-toupiao",
    //   herf: "#"
    // }
  ],
  curTitleIndex: "议题资料"
});

const props = defineProps({
  meetingId: {
    type: Number,
    required: false
  }
});

const changeTitle = (title: string) => {
  notifyInfo.value.curTitleIndex = title;
  emit("titlechange", title);
};

// 监听 meetingId 的变化
watch(
  () => props.meetingId,
  newVal => {
    if (newVal !== undefined && newVal !== null) {
      getNotifyInfo(newVal);
      initNotifyArea(newVal);
    }
  }
);

const getNotifyInfo = meetingId => {
  if (meetingId !== undefined && meetingId !== null) {
    getNotify(props.meetingId).then(res => {
      notifyInfo.value.nofitytr1data = res.data.notify;
      notifyInfo.value.nofitytr2data = res.data.countNum;
    });
  }
};

const initNotifyArea = async meetingId => {
  if (meetingId !== undefined && meetingId !== null) {
    loading.value = true;
    // 会议服务人员只看会议服务
    if (roles.some(item => item === "meeting_serve")) {
      changeTitle("会议服务");
      setTimeout(() => {
        loading.value = false;
      }, 300);
      return;
    } else {
      const areaList = [
        {
          name: "议题资料",
          icon: "icon-bianjiwenjian",
          herf: "#"
        },
        {
          name: "会议签到",
          icon: "icon-bi",
          herf: "#"
        },
        {
          name: "会议服务",
          icon: "icon-toupiao",
          herf: "#"
        }
      ];
      try {
        const { data } = await getMySelfTopic(meetingId);
        if (data.length !== 0) {
          areaList.splice(2, 0, {
            name: "我的议题",
            icon: "icon-ziliao1",
            herf: "#"
          });
        }
        notifyInfo.value.titleList = areaList;
        changeTitle("议题资料");
      } catch (error) {
        console.error(error);
      } finally {
        setTimeout(() => {
          loading.value = false;
        }, 300);
      }
    }
  }
};

onMounted(() => {
  getNotifyInfo(props.meetingId);
  initNotifyArea(props.meetingId);
});

defineExpose({
  curTitleIndex: notifyInfo.value.curTitleIndex
});
</script>

<style scoped lang="scss">
/* 添加根容器样式 */
.component-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notifytr1 {
  font-size: 1.375rem;
  letter-spacing: 2px;
  flex: 1;
  text-shadow: 0 0 20px #ffffff;
  .titleTips {
    margin: auto;
    max-width: 60%;
    text-align: center;
    font-size: 35px;
    letter-spacing: 0.1875rem;
    font-weight: bold;
    transform: rotateX(18deg);
    padding-bottom: 0.1875rem;
  }
  .centered-content {
    margin: auto;
    max-width: 60%;
    text-align: center;
    font-size: 40px;
    letter-spacing: 0.1875rem;
    font-weight: bold;
    transform: rotateX(18deg);
    padding-bottom: 0.1875rem;
    margin-top: 90px;
  }
  p {
    margin-bottom: 0.625rem;
    line-height: 2rem;
    color: #606266;
  }
  p.info {
    max-height: 5.625rem;
    height: 5.625rem;
    overflow: hidden;
    overflow-y: auto;
  }
}
.notifytr2 {
  font-size: 1.125rem;
  display: flex;
  gap: 0.625rem;
  justify-content: space-between;
  padding: 0.625rem 0 1.25rem 0;
  .perCount .count {
    font-size: 1.75rem;
    color: #357ff3;
    font-weight: bold;
    font-family: cursive, "Arial Black", Arial, Helvetica, sans-serif;
    position: relative;
    top: 0.1875rem;
  }
}
.notifytr3 {
  font-size: 1.125rem;
  display: flex;
  gap: 0.625rem;
  justify-content: space-between;
  .perYt {
    padding: 1.25rem;
    border-radius: 0.625rem;
    flex: 1;
    position: relative;
    cursor: pointer;
    background-image: url(@/assets/images/bg3.png);
    background-size: cover;
    background-repeat: no-repeat;
    .icon {
      text-align: right;
      opacity: 0.8;
      font-size: 1.5rem;
    }
    .titlename {
      position: relative;
      letter-spacing: 0.125rem;
      margin-bottom: 0.625rem;
    }
  }
  .perYt.active {
    background-image: url(@/assets/images/bg4.png);
    background-size: cover;
    background-repeat: no-repeat;
    color: #fff;
    .titlename::after {
      content: "";
      position: absolute;
      bottom: -0.375rem;
      left: 0;
      width: 13%;
      height: 0.1875rem;
      background-color: #fff;
      border-radius: 0.3125rem;
    }
  }
}
</style>
