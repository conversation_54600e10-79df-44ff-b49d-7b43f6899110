<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import ScaledEsign from "@/components/ScaledEsign/index.vue";

import { useUserStoreHook } from "@/store/modules/user";
import {
  addMeetingMemo,
  listMeetingMemo,
  getMeetingMemo
} from "@/api/meeting/meetingMemo";
import { MeetingMemo, MeetingMemoQuery } from "@/types/meeting/meetingMemo";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "MemoDialog"
});

const visible = defineModel<boolean>("visible", { default: false });
const userId = useUserStoreHook().userId;
const props = defineProps({
  meetingId: {
    type: Number,
    required: true
  }
});

// 表单信息
const form = ref<MeetingMemo>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);
const lineWidth = ref(6);
const lineColor = ref("#000000");
const brushEffect = ref(true);
const pressureSensitive = ref(true);
const minLineWidth = ref(2);
const maxLineWidth = ref(12);
const bgColor = ref("");
const isCrop = ref(false);
const esignRef = ref(null);
const tableData = ref<MeetingMemo[]>();
const queryParams = reactive<MeetingMemoQuery>(new MeetingMemoQuery());
const signImg = ref("");
const isFlag = ref(true);

const title = computed(() => {
  return "便签";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange()
);

function handlePropsDataChange() {
  cancel();
  getList();
}
/** 提交按钮 */
function submitForm() {
  loading.value = true;
  esignRef.value
    .generate()
    .then(res => {
      form.value = {
        memoWord: res,
        meetingId: props.meetingId
      };
      addMeetingMemo(form.value)
        .then(() => {
          message("签名成功", { type: "success" });
          visible.value = false;
          esignRef.value.reset();
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
          esignRef.value.reset();
        });
    })
    .catch(() => {
      loading.value = false;
      return;
    });
}

function cancel() {
  if (esignRef.value) {
    esignRef.value.reset(); // 确保组件有这个方法
  }
}

function getList() {
  queryParams.meetingId = props.meetingId;
  queryParams.creatorId = userId;
  listMeetingMemo(queryParams).then(res => {
    tableData.value = res.data;
  });
}

function handleClick(value) {
  signImg.value = value.memoWord;
  isFlag.value = false;
}

function returnEdit() {
  isFlag.value = true;
}
</script>

<template>
  <!-- 添加或修改签名对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="75%"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-row :gutter="20">
      <el-col :span="10">
        <el-table
          border
          :data="tableData"
          style="width: 100%"
          @row-click="handleClick"
          height="550px"
        >
          <el-table-column
            align="center"
            prop="userName"
            label="姓名"
            width="100"
          />
          <el-table-column
            align="center"
            prop="createTime"
            label="时间"
            width="250"
          />
          <el-table-column align="center" prop="memoWord" label="内容">
            <template #default="scope">
              <el-image
                style="width: 30%; height: 30%"
                :src="scope.row.memoWord"
                fit="cover"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="14">
        <div v-if="isFlag">
          <scaled-esign
            ref="esignRef"
            style="border: 1px dashed #1c1a1a"
            :height="600"
            :isCrop="isCrop"
            :lineWidth="lineWidth"
            :lineColor="lineColor"
            :brushEffect="brushEffect"
            :pressureSensitive="pressureSensitive"
            :minLineWidth="minLineWidth"
            :maxLineWidth="maxLineWidth"
          />
        </div>
        <div v-else>
          <el-image
            v-if="signImg"
            :src="signImg"
            alt=""
            style="height: 100%; width: 100%"
            fit="cover"
          />
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <div v-if="isFlag">
          <el-button :loading="loading" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">清 空</el-button>
        </div>
        <div v-else>
          <el-button @click="returnEdit">返回编辑</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
