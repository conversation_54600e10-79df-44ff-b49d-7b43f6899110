<template>
  <div class="home">
    <div class="home_left">
      <div v-if="!!meetingId" class="cardGroup">
        <div class="customcard tr1">
          <notify
            ref="notifyRef"
            :meetingId="meetingId"
            @titlechange="changeNotifyTitle"
          />
        </div>
        <div class="customcard tr2">
          <transition name="fade-transform" mode="out-in">
            <component
              v-if="getCurrentComponent"
              :is="getCurrentComponent"
              :meetingId="meetingId"
            />
            <!-- 自定义空状态样式 -->
            <div v-else class="empty-container">
              <div class="empty-content">
                <i class="iconfont icon-ziliao1" />
                <span>暂无相关内容</span>
              </div>
            </div>
          </transition>
        </div>
      </div>
      <div v-else class="no-meeting-container">
        <div class="no-meeting-content">
          <i class="iconfont icon-ziliao1" />
          <span>您当前暂无可参加会议</span>
        </div>
      </div>
    </div>
    <div class="home_right">
      <div class="customcard tr2">
        <jhhy :meetingId="meetingId" @handleClick="handleChildClick" />
      </div>
      <div class="customcard tr1" v-if="!roles.includes('meeting_serve')">
        <Notice />
      </div>
      <div class="customcard tr1" v-if="roles.includes('meeting_serve')">
        <hyfw :meetingId="meetingId" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: "UserHome"
});
import { ref, computed } from "vue";
import notify from "./notify.vue";
import ytzl from "./ytzl.vue";
import jhhy from "./jhhy.vue";
import hyfw from "./hyfw.vue";
import hyjd from "./hyjd.vue";
import selfytzl from "./selfytzl.vue";
import hyfwTable from "./hyfwTable.vue";
import Notice from "@/layout/components/notice/index.vue";
import { useUserStoreHook } from "@/store/modules/user";

const notifyRef = ref<InstanceType<typeof notify>>();
const activeNotifyTitle = ref("");
const meetingId = ref();
const roles = useUserStoreHook().roles;
const handleChildClick = (mId: number) => {
  meetingId.value = mId;
};

const changeNotifyTitle = (title: string) => {
  activeNotifyTitle.value = title;
};

// 添加计算属性来处理组件切换
const getCurrentComponent = computed(() => {
  switch (activeNotifyTitle.value) {
    case "我的议题":
      return selfytzl;
    case "议题资料":
      return ytzl;
    case "会议签到":
      return hyjd;
    case "会议服务":
      return hyfwTable;
    default:
      return null;
  }
});
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  height: 100%;
  display: flex;
  font-size: 1.125rem;
  color: #535151;
  .home_left {
    flex: 1;
    border-right: 0.0625rem solid #d4dcea;
    padding: 1.25rem;
    width: calc(100% - 38%);
    .cardGroup {
      background: rgba(202, 216, 245, 0.53);
      width: 100%;
      height: 100%;
      border-radius: 0.3125rem;
      padding: 1.25rem;
      display: flex;
      flex-direction: column;
      gap: 1.25rem;
      .customcard.tr1 {
        flex-basis: 50%;
        flex-grow: 0;
        width: 100%;
        // background:linear-gradient(to bottom, #bbcbeb99 0%, #bbcbeb33  100%);
        background-image: url("@/assets/images/bg2.png");
        background-size: cover;
        border-radius: 0.3125rem;
        padding: 1.25rem;
        display: flex;
        flex-direction: column;
        position: relative;
      }
      .customcard.tr2 {
        flex: 1;
        background: rgb(243 246 252 / 0.53);
        border-radius: 0.3125rem;
        padding: 1.25rem;
        max-width: 100%;
      }
    }
  }
  .home_right {
    width: calc(38%);
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    padding: 1.25rem;
    .customcard.tr1 {
      display: flex;
      flex-direction: column;
      flex-basis: 50%;
      border-radius: 0.3125rem;
      padding: 1.25rem;
      max-width: 100%;
      background-image: url("@/assets/images/bg2.png");
      background-size: cover;
    }
    .customcard.tr2 {
      display: flex;
      flex-direction: column;
      flex: 1;
      flex-basis: 30%;
      border-radius: 0.3125rem;
      padding: 1.25rem;
      max-width: 100%;
      background-image: url("@/assets/images/bg2.png");
      background-size: cover;
    }
    .customcard.tr3 {
      flex-basis: 20%;
      border-radius: 0.3125rem;
      padding: 1.25rem;
      max-width: 100%;
      background-image: url("@/assets/images/bg2.png");
      background-size: cover;
    }
  }
}

/* 修改过渡动画样式 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease-out;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 组件包装器样式 */
.component-wrapper {
  width: 100%;
  height: 100%;
}

/* 确保容器有相对定位 */
.customcard.tr2 {
  position: relative;
  min-height: 200px;
}

/* 空状态容器样式 */
.empty-container {
  width: 100%;
  height: 22rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(243 246 252 / 0.53);
  border-radius: 0.3125rem;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #909399;

    .iconfont {
      font-size: 3rem;
      opacity: 0.8;
    }

    span {
      font-size: 1.125rem;
      letter-spacing: 0.125rem;
    }
  }
}

/* 无会议状态容器样式 */
.no-meeting-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(202, 216, 245, 0.53);
  border-radius: 0.3125rem;
  padding: 1.25rem;
  min-height: calc(100vh);

  .no-meeting-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.25rem;
    color: #6e7c99;

    .iconfont {
      font-size: 3.5rem;
      opacity: 0.9;
      color: #8fa3ce;
    }

    span {
      font-size: 1.25rem;
      letter-spacing: 0.125rem;
      font-weight: 500;
    }
  }
}
</style>
