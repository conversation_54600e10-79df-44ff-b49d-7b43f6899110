<template>
  <div class="component-root">
    <div class="cardtitle">
      <span class="titletxt">会议服务</span>
      <span class="more">
        <el-button
          v-if="canAddServe"
          type="primary"
          :icon="Plus"
          size="large"
          @click="handleAdd"
          >添加</el-button
        >
      </span>
    </div>
    <div class="hyfwTableList">
      <el-table
        class="ghost-table"
        ref="singleTableRef"
        :data="hyfwTableData"
        highlight-current-row
        :show-header="false"
        style="width: 100%"
        size="large"
        @current-change="handleCurrentChange"
      >
        <el-table-column type="index" width="50" />
        <el-table-column property="title" label="title" show-overflow-tooltip />
        <el-table-column
          property="createTime"
          label="createTime"
          width="250"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag type="danger" disable-transitions>{{ row.timeAgo }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          property="creatorName"
          label="creatorName"
          width="180"
          show-overflow-tooltip
        />
        <!-- <el-table-column fixed="right" label="Operations" width="58">
        <template #default="scope">
          <el-button link type="primary" size="large">
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </template>
      </el-table-column> -->
      </el-table>
    </div>
    <hyfwAdd ref="hyfwAddRef" :meetingId="props.meetingId" @ok="refreshTable" />
  </div>
</template>
<script setup lang="ts">
import { getMeeting } from "@/api/meeting/meeting";
defineOptions({
  name: "hyfwTable"
});
import {
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  reactive,
  computed
} from "vue";
import { Plus } from "@element-plus/icons-vue";
import hyfwAdd from "./hyfwAdd.vue";
import { listMeetingServe } from "@/api/meeting/meetingServe";
import { MeetingServeQuery } from "@/types/meeting/meetingServe";
import { MeetingForm } from "@/types/meeting/meeting";
const hyfwAddRef = ref<InstanceType<typeof hyfwAdd>>();
const queryParams = reactive<MeetingServeQuery>(new MeetingServeQuery());
const hyfwTableData = ref([]);
const meetingInfo = ref<MeetingForm>(new MeetingForm());

import { useUserStoreHook } from "@/store/modules/user";
const roles = useUserStoreHook().roles;

const props = defineProps({
  meetingId: {
    type: Number,
    required: false
  }
});

const handleCurrentChange = val => {
  console.log(val);
};

const handleAdd = () => {
  hyfwAddRef.value.open();
};

const getListHyfw = (mId: number) => {
  if (mId !== undefined && mId !== null) {
    queryParams.meetingId = mId;
    listMeetingServe(queryParams).then(res => {
      hyfwTableData.value = res.data;
    });
  }
};

// 根据会议时间控制服务按钮能否显示
const now = ref(new Date());
const timer = setInterval(() => {
  now.value = new Date();
  getListHyfw(props.meetingId);
}, 1000 * 10); // 每10s更新当前时间

const canAddServe = computed(() => {
  if (
    !meetingInfo.value.meetingDate ||
    !meetingInfo.value.meetingTime ||
    meetingInfo.value.meetingTime.length === 0 ||
    // 会议已结束
    meetingInfo.value.meetingStatus === "9"
  ) {
    return false;
  }
  const meetingStart = new Date(
    meetingInfo.value.meetingDate + " " + meetingInfo.value.meetingTime[0]
  );
  const meetingEnd = new Date(
    meetingInfo.value.meetingDate + " " + meetingInfo.value.meetingTime[1]
  );
  return (
    now.value > meetingStart &&
    now.value < meetingEnd &&
    // 会议服务人员不需要显示添加服务按钮
    !roles.some(item => item === "meeting_serve")
  );
});

//  会议基本信息
const getMeetingInfo = (mId: number) => {
  if (mId !== undefined && mId !== null) {
    getMeeting(mId).then(res => {
      meetingInfo.value = res.data;
    });
  }
};

// 监听 meetingId 的变化
watch(
  () => props.meetingId,
  meetingId => {
    if (meetingId !== undefined && meetingId !== null) {
      getMeetingInfo(meetingId);
      getListHyfw(meetingId);
    }
  },
  { immediate: true }
);

onMounted(() => {
  // getMeetingInfo(props.meetingId);
  // getListHyfw(props.meetingId);
});
onBeforeUnmount(() => {
  clearInterval(timer);
});

const refreshTable = () => {
  getListHyfw(props.meetingId);
};
</script>

<style scoped lang="scss">
.cardtitle {
  font-size: 1.5rem;
  letter-spacing: 0.1875rem;
  padding-bottom: 0.625rem;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-left: 1.125rem;
  .titletxt {
    transform: rotateX(18deg);
  }
}
.cardtitle::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 10%;
  width: 0.3125rem;
  height: 50%;
  background: #357ff3;
  border-radius: 0.3125rem;
}
.hyfwTableList {
  height: 22.6875rem;
  overflow: hidden;
  overflow-y: auto;
}
</style>
