<template>
  <div class="component-root">
    <div class="cardtitle">
      <span class="titletxt">议题资料</span>
      <span class="more">
        <el-button
          v-if="props.meetingId"
          type="primary"
          :icon="EditPen"
          size="large"
          @click="handleMemo"
          >便签</el-button
        >
        <!-- <el-button :icon="MoreFilled" /> -->
      </span>
    </div>
    <div class="ytList">
      <el-table
        class="ghost-table"
        ref="singleTableRef"
        :data="ytzlData"
        highlight-current-row
        :show-header="false"
        style="width: 100%"
        size="large"
        row-class-name="ytzl-row"
        :tooltip-options="{ disabled: true }"
      >
        <el-table-column type="index" width="50" />
        <el-table-column
          property="topicTitle"
          label="topicTitle"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="text-blue-700 cursor-pointer"
              @click="handleCurrentChange(scope.row)"
              >{{ scope.row.topicTitle }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          property="creatorDept"
          label="creatorDept"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          property="reportUserName"
          label="reportUserName"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column label="Operations" width="150">
          <template #default="{ row }">
            <el-button
              v-if="userId === registerId"
              type="success"
              @click="handleVote(row)"
              >{{ row.status ? "表决结果" : "记录表决" }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-model="dialogVisible" title="议题预览" fullscreen>
      <topic-info
        ref="topicInfoRef"
        :id="topicId"
        :commentVisible="true"
        :show-go-back="false"
        :height="1000"
      />
      <template #footer>
        <div class="text-center" ref="topicInfoFooterRef">
          <el-button @click="dialogVisible = false" size="large"
            >关 闭</el-button
          >
        </div>
      </template>
    </el-dialog>
    <div>
      <MemoDialog :meetingId="props.meetingId" v-model:visible="visibleMemo" />
      <meeting-vote-dialog
        v-model:visible="visibleVote"
        :meetingLink="meetingLink"
        @confirm="getMeetingVo"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: "ytzl"
});
import { ref, onMounted, watch } from "vue";
import { EditPen } from "@element-plus/icons-vue";
import TopicInfo from "@/views/meeting/topicMeeting/info/topicInfo.vue";
import MemoDialog from "@/views/user/home/<USER>";
import MeetingVoteDialog from "@/views/meeting/register/components/MeetingVoteDialog.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { getMeeting } from "@/api/meeting/meeting";
import { MeetingLinkVote } from "@/types/meeting/topicVote";
// import { selectDictLabel } from "@/utils/atom";
const meetingLink = ref<MeetingLinkVote>(new MeetingLinkVote());
const dialogVisible = ref(false);
const visibleMemo = ref(false);
const visibleVote = ref(false);
// 文件浏览id
const topicId = ref();
const userId = useUserStoreHook().userId;
const registerId = ref();
const ytzlData = ref([]);

const props = defineProps({
  meetingId: {
    type: Number,
    required: false
  }
});

const handleMemo = () => {
  visibleMemo.value = true;
};

const handleVote = item => {
  meetingLink.value = item;
  visibleVote.value = true;
};

const getMeetingVo = () => {
  if (props.meetingId !== undefined && props.meetingId !== null) {
    getMeeting(props.meetingId).then(res => {
      registerId.value = res.data.registerId;
      ytzlData.value = res.data.topicMeeting;
    });
  }
};
// 监听 meetingId 的变化
watch(
  () => props.meetingId,
  meetingId => {
    if (meetingId !== undefined && meetingId !== null) {
      // getListTopic();
      getMeetingVo();
    }
  },
  { immediate: true }
);

onMounted(() => {
  // getListTopic();
  // getMeetingVo();
});

const handleCurrentChange = item => {
  // 议题收集的议题
  if (item && item.planId) {
    topicId.value = item.topicId;
  } else {
    // 会议资料上传的特殊议题
    topicId.value = item.id;
  }
  dialogVisible.value = true;
};
</script>

<style scoped lang="scss">
.component-root {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}
.cardtitle {
  flex: 0 0 auto;
  font-size: 1.5rem;
  letter-spacing: 0.1875rem;
  transform: rotateX(18deg);
  padding-bottom: 0.625rem;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-left: 1.125rem;
}
.cardtitle::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 10%;
  width: 0.3125rem;
  height: 50%;
  background: #357ff3;
  border-radius: 0.3125rem;
}
.ytList {
  overflow: hidden;
  overflow-y: auto;
}
</style>
