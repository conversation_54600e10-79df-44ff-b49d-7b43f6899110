<template>
  <div class="component-root">
    <div class="cardtitle">
      <span class="titletxt">我的议题</span>
    </div>
    <div class="ytList">
      <el-table
        class="ghost-table"
        ref="singleTableRef"
        :data="ytzlData"
        highlight-current-row
        :show-header="false"
        style="width: 100%"
        size="large"
        row-class-name="ytzl-row"
        @current-change="handleCurrentChange"
        :tooltip-options="{ disabled: true }"
      >
        <el-table-column type="index" width="50" />
        <el-table-column
          property="topicTitle"
          label="topicTitle"
          show-overflow-tooltip
        />
        <el-table-column
          property="creatorDept"
          label="creatorDept"
          width="280"
          show-overflow-tooltip
        />
        <el-table-column
          property="reportUserName"
          label="reportUserName"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column fixed="right" label="Operations" width="58">
          <template #default>
            <el-button link type="primary" size="large">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-model="dialogVisible" title="议题预览" fullscreen>
      <topic-info
        ref="topicInfoRef"
        :id="topicId"
        :commentVisible="true"
        :show-go-back="false"
        :height="1000"
      />
      <template #footer>
        <div class="text-center" ref="topicInfoFooterRef">
          <el-button @click="dialogVisible = false" size="large"
            >关 闭</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { getMySelfTopic } from "@/api/meeting/meeting";
defineOptions({
  name: "ytzl"
});
import { ref, onMounted, watch } from "vue";
import { ArrowRight } from "@element-plus/icons-vue";
import TopicInfo from "@/views/meeting/topicMeeting/info/topicInfo.vue";
const dialogVisible = ref(false);
const topicId = ref();
const ytzlData = ref([]);

const props = defineProps({
  meetingId: {
    type: Number,
    required: false
  }
});

const getListTopic = () => {
  if (props.meetingId !== undefined && props.meetingId !== null) {
    getMySelfTopic(props.meetingId).then(res => {
      const list = res.data;
      if (list) {
        ytzlData.value = list;
      }
    });
  }
};

// 监听 meetingId 的变化
watch(
  () => props.meetingId,
  meetingId => {
    if (meetingId !== undefined && meetingId !== null) {
      getListTopic();
    }
  },
  { immediate: true }
);

onMounted(() => {
  // getListTopic();
});

const handleCurrentChange = item => {
  if (item && item.planId) {
    topicId.value = item.topicId;
  } else {
    topicId.value = item.id;
  }
  dialogVisible.value = true;
};
</script>

<style scoped lang="scss">
.component-root {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}
.cardtitle {
  flex: 0 0 auto;
  font-size: 1.5rem;
  letter-spacing: 0.1875rem;
  transform: rotateX(18deg);
  padding-bottom: 0.625rem;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-left: 1.125rem;
}
.cardtitle::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 10%;
  width: 0.3125rem;
  height: 50%;
  background: #357ff3;
  border-radius: 0.3125rem;
}
.ytList {
  overflow: hidden;
  overflow-y: auto;
}
</style>
