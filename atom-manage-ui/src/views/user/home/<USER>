<script setup lang="ts">
import { computed, ref, watch, onUnmounted } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import ScaledEsign from "@/components/ScaledEsign/index.vue";

import { useUserStoreHook } from "@/store/modules/user";
import { getSignMeetingIdAndUserId, signMeeting } from "@/api/meeting/meeting";
import { MeetingUserSign } from "@/types/meeting/meetingUserSign";
import vueQr from "vue-qr/src/packages/vue-qr.vue";
import zgtj from "@/assets/images/zgtj.jpg";
import { getUserSign } from "@/api/system/userSign";
import { InfoFilled } from "@element-plus/icons-vue";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "SignDialog"
});

const visible = defineModel<boolean>("visible", { default: false });
const userId = useUserStoreHook().userId;
const props = defineProps({
  meetingId: {
    type: Number,
    required: true
  }
});

// 表单信息
const form = ref<MeetingUserSign>(null);
const loading = ref(false);
const lineWidth = ref(6);
const lineColor = ref("#000000");
const bgColor = ref("");
const isCrop = ref(false);
const esignRef = ref(null);
const messageTimer = ref(null);

// 笔触效果相关配置
const brushEffect = ref(true);
const pressureSensitive = ref(true);
const minLineWidth = ref(2);
const maxLineWidth = ref(12);

const { VITE_MOBILE_MEETING_SIGN_URL, VITE_PUBLIC_PATH, DEV } = import.meta.env;
// 开发环境访问当前app的签名地址，否则访问配置的签名地址
const eSignUrl = DEV
  ? `${window.location.protocol}//${window.location.host}${VITE_PUBLIC_PATH}meetingSign?userId=${userId}&meetingId=${props.meetingId}`
  : `${VITE_MOBILE_MEETING_SIGN_URL}/${userId}/${props.meetingId}`;

const title = computed(() => {
  return "会议签到";
});

// 用户系统签名
const userSignTemplate = ref();

// 当前会议签名
const userMeetingSign = ref();
// 获得系统签名
function getUserSignTemplate() {
  getUserSign(userId)
    .then(res => {
      if (res.data) {
        userSignTemplate.value = res.data.signPic;
      } else {
        userSignTemplate.value = null;
      }
    })
    .catch(() => {
      userSignTemplate.value = null;
    });
}

// 加载当前会议签名
function loadUserMeetingSign() {
  getSignMeetingIdAndUserId(props.meetingId, userId)
    .then(res => {
      if (res.data) {
        userMeetingSign.value = res.data.signPic;
        esignRef.value.importImageFitCanvas(res.data.signPic, true);
      } else {
        userMeetingSign.value = null;
      }
    })
    .catch(() => {
      userMeetingSign.value = null;
    });
}

// 引用系统签名
function quoteSign() {
  if (userSignTemplate.value) {
    esignRef.value.importImageFitCanvas(userSignTemplate.value, true);
  }
}

/** 提交按钮 */
function submitForm() {
  loading.value = true;
  esignRef.value
    .generate()
    .then(res => {
      form.value = {
        signPic: res,
        userId: userId,
        meetingId: props.meetingId
      };
      signMeeting(form.value)
        .then(() => {
          message("签到成功", { type: "success" });
          onClosed();
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(e => {
      message(e.message, { type: "warning" });
      loading.value = false;
      return;
    });
}

function clearSign() {
  if (esignRef.value) {
    esignRef.value.reset(); // 确保组件有这个方法
  }
}

// 检查移动端是否签名
function pollApi() {
  if (props.meetingId !== undefined && props.meetingId !== null) {
    getSignMeetingIdAndUserId(props.meetingId, userId).then(res => {
      if (res.data) {
        if (userMeetingSign.value !== res.data.signPic) {
          userMeetingSign.value = res.data.signPic;
          message("签到成功", { type: "success" });
          onClosed();
          emit("confirm");
        }
      }
    });
  }
}

function onOpen() {
  loadUserMeetingSign();
  getUserSignTemplate();
  startPolling();
}

function onClosed() {
  stopPolling();
  clearSign();
  visible.value = false;
  userSignTemplate.value = null;
  userMeetingSign.value = null;
}

function startPolling() {
  messageTimer.value = setInterval(pollApi, 3000);
}

function stopPolling() {
  if (messageTimer.value) {
    clearInterval(messageTimer.value);
    messageTimer.value = null; // 清除定时器ID
  }
}
</script>

<template>
  <!-- 添加或修改签名对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    :close-on-click-modal="false"
    style="width: 1748px"
    align-center
    @open="onOpen"
    @closed="onClosed"
  >
    <el-row :gutter="24">
      <el-col :span="6">
        <div class="flex flex-col items-center justify-center h-full p-5 gap-3">
          <div class="bg-white p-3 rounded-lg shadow-md">
            <vue-qr :logoSrc="zgtj" :text="eSignUrl" :size="200" />
          </div>
          <div class="text-gray-600 text-base">微信扫码签到</div>
        </div>
      </el-col>
      <el-col :span="18">
        <scaled-esign
          ref="esignRef"
          style="border: 1px dashed #1c1a1a"
          :width="1280"
          :height="768"
          :isCrop="isCrop"
          :lineWidth="lineWidth"
          :lineColor="lineColor"
          :brushEffect="brushEffect"
          :pressureSensitive="pressureSensitive"
          :minLineWidth="minLineWidth"
          :maxLineWidth="maxLineWidth"
          v-model:bgColor="bgColor"
        />
        <div class="flex justify-between mt-2">
          <div class="flex items-center gap-1">
            <el-button
              :loading="loading"
              :disabled="!userSignTemplate"
              @click="quoteSign"
              size="large"
              type="primary"
              class="mt-2 px-6 py-3 font-medium"
            >
              引用签名
            </el-button>
            <span
              v-if="!userSignTemplate"
              class="flex items-center gap-2 text-gray-500 mt-2"
            >
              <el-icon size="18px"><InfoFilled /></el-icon>
              <span>请在签名配置设置后，再引用签名</span>
            </span>
          </div>
          <div>
            <el-button
              :loading="loading"
              type="primary"
              size="large"
              @click="submitForm"
              class="mt-2 px-6 py-3 font-medium"
              >签 到
            </el-button>
            <el-button
              :loading="loading"
              @click="clearSign"
              size="large"
              class="mt-2 px-6 py-3 font-medium"
              >清 空</el-button
            >
          </div>
        </div>
      </el-col>
    </el-row>
  </el-dialog>
</template>
