<template>
  <div class="flex flex-col h-full component-root">
    <div class="cardtitle">
      <span>会议签到</span>
      <el-button
        v-if="registerId === userId || userId == 1"
        type="primary"
        :icon="useRenderIcon('ix:export')"
        size="large"
        @click="exportSignPdf(meetingId)"
        >导出签到表</el-button
      >
    </div>

    <div class="overflow-hidden flex-1 rounded-lg">
      <div class="flex gap-8 items-center h-full">
        <div
          v-if="isJoiner"
          class="flex items-center justify-center bg-transparent w-[400px] animate-fade-in-scale"
        >
          <el-image
            v-if="signImg"
            :src="signImg"
            alt=""
            class="object-contain w-full h-full"
            fit="contain"
          />
          <div
            v-if="!signImg && !canSign"
            class="flex flex-col justify-center items-center w-full h-full"
          >
            <el-tag type="info" size="large" class="px-4 py-2">
              <el-icon class="mr-2"><InfoFilled /></el-icon>
              当前不可签到
            </el-tag>
          </div>
          <div
            v-else-if="!signImg && canSign"
            class="flex flex-col justify-center items-center w-full h-full"
          >
            <el-button
              type="primary"
              size="large"
              class="px-8 py-4 text-lg border-2 shadow-md border-primary/20"
              @click="startSign()"
            >
              <el-icon class="mr-2"><Edit /></el-icon>
              开始签到
            </el-button>
          </div>
        </div>
        <div
          class="flex flex-col flex-1 h-full transition-all duration-100"
          :class="{ 'animate-slide-right': isJoiner }"
        >
          <div class="flex mb-3 w-full">
            <div class="flex gap-1 items-center w-1/2">
              <span class="pr-1 font-semibold">主持人</span>
              <el-icon class="mr-2 text-blue-500" :size="24"><User /></el-icon
              >{{ hyjdData.compereName }}
            </div>
            <div class="flex gap-1 items-center w-1/2">
              <span class="pr-1 font-semibold">记录人</span>
              <el-icon class="mr-2 text-blue-500" :size="24"><User /></el-icon
              >{{ hyjdData.recorderName }}
            </div>
          </div>
          <el-tabs
            v-model="activeName"
            class="flex flex-col flex-1 custom-tabs"
            @tab-click="handleTabClick"
          >
            <el-tab-pane label="参会人员" name="2" class="tab-pane-content">
              <div
                class="scrollable-list"
                v-if="hyjdData.joinerList.length > 0"
              >
                <div
                  v-for="item in hyjdData.joinerList"
                  :key="item.userId"
                  class="flex justify-between items-center pb-2 mb-2 border-b border-gray-200/50"
                >
                  <span class="flex gap-2 items-center">
                    <el-icon :size="24"><User /></el-icon>
                    <span> {{ item.userName }}</span>
                  </span>
                  <span>
                    <el-tag
                      :type="item.beginTime ? 'primary' : 'danger'"
                      size="large"
                      round
                      effect="light"
                      disable-transitions
                    >
                      {{ item.beginTime ? item.beginTime : "未签到" }}
                    </el-tag>
                  </span>
                </div>
              </div>
              <div v-else class="empty-container">
                <el-empty :image-size="100" :image="emptypng" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="列席人员" name="1" class="tab-pane-content">
              <div
                class="scrollable-list"
                v-if="hyjdData.bystanderList.length > 0"
              >
                <div
                  v-for="item in hyjdData.bystanderList"
                  :key="item.userId"
                  class="flex justify-between items-center pb-2 mb-2 border-b border-gray-200/50"
                >
                  <span class="flex gap-2 items-center">
                    <el-icon :size="24"><User /></el-icon>
                    <span> {{ item.userName }}</span>
                  </span>
                  <span>
                    <el-tag
                      :type="item.beginTime ? 'primary' : 'danger'"
                      size="large"
                      round
                      effect="light"
                      disable-transitions
                    >
                      {{ item.beginTime ? item.beginTime : "未签到" }}
                    </el-tag>
                  </span>
                </div>
              </div>
              <div v-else class="empty-container">
                <el-empty :image="emptypng" :image-size="100" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <SignDialog
      :meetingId="props.meetingId"
      v-model:visible="visibleSign"
      @confirm="fetchAll"
    />
  </div>
</template>
<script setup lang="ts">
import {
  getMeeting,
  getJoinerList,
  getSignMeetingIdAndUserId
} from "@/api/meeting/meeting";
defineOptions({
  name: "hyjd"
});
import { ref, watch, onBeforeUnmount, computed, nextTick } from "vue";

import { User, InfoFilled, Edit } from "@element-plus/icons-vue";
import { useUserStoreHook } from "@/store/modules/user";
import SignDialog from "@/views/user/home/<USER>";
import emptypng from "@/assets/images/empty.png";
import { MeetingForm } from "@/types/meeting/meeting";
import { download } from "@/plugins/download";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
const props = defineProps({
  meetingId: {
    type: Number,
    required: false
  }
});
const meetingInfo = ref<MeetingForm>(new MeetingForm());
const activeName = ref("2");
const signImg = ref();
const signDate = ref();
const visibleSign = ref(false);
const userId = useUserStoreHook().userId;
const hyjdData = ref({
  attendNum: 25, // 应参加会议人数
  signNum: 15, // 签到人数
  // 主持人
  compereName: "",
  // 记录人
  recorderName: "",
  //参会人员
  joinerList: [],
  //列席人员
  bystanderList: []
});

// 根据会议时间控制签到按钮能否显示
const now = ref(new Date());
const timer = setInterval(() => {
  now.value = new Date();
}, 1000 * 5); // 每5s更新当前时间

// 是否参会或列席人员
const isJoiner = computed(() => {
  return hyjdData.value.joinerList
    .concat(hyjdData.value.bystanderList)
    .some(item => item.userId === userId);
});
// 是否可以签到
const canSign = computed(() => {
  if (
    !meetingInfo.value.meetingDate ||
    !meetingInfo.value.meetingTime ||
    meetingInfo.value.meetingTime.length === 0
  ) {
    return false;
  }
  const meetingStart = new Date(
    meetingInfo.value.meetingDate + " " + meetingInfo.value.meetingTime[0]
  );
  const meetingEnd = new Date(
    meetingInfo.value.meetingDate + " " + meetingInfo.value.meetingTime[1]
  );
  // 会议进行中 是参会列席人员 没有签到过
  return (
    now.value > meetingStart &&
    now.value < meetingEnd &&
    isJoiner.value &&
    !signImg.value
  );
});

const startSign = () => {
  visibleSign.value = true;
};

// 处理 tab 点击事件，确保 tab 切换的稳定性
const handleTabClick = tab => {
  // 确保 activeName 与点击的 tab 保持一致
  activeName.value = tab.props.name;
};
const registerId = ref();
//  会议基本信息
const getMeetingInfo = (mId: number) => {
  if (mId !== undefined && mId !== null) {
    getMeeting(mId).then(res => {
      registerId.value = res.data.registerId;
      meetingInfo.value = res.data;
    });
  }
};

const getSign = (mId: number) => {
  if (mId !== undefined && mId !== null) {
    getSignMeetingIdAndUserId(mId, userId).then(res => {
      if (res.data) {
        signImg.value = res.data.signPic;
        signDate.value = res.data.createTime;
      } else {
        signImg.value = null;
        signDate.value = null;
      }
    });
  }
};

const getListJoin = (mId: number) => {
  if (mId !== undefined && mId !== null) {
    // 保存当前的 tab 状态
    const currentTab = activeName.value;

    getJoinerList(mId).then(res => {
      const list = res.data.joiner;
      hyjdData.value.compereName = res.data.compere;
      hyjdData.value.recorderName = res.data.recorder;
      if (list) {
        hyjdData.value.bystanderList = list
          .filter(item => item.userType === "1")
          .sort((a, b) => a.joinerOrder - b.joinerOrder); // 列席
        hyjdData.value.joinerList = list
          .filter(item => item.userType === "2")
          .sort((a, b) => a.joinerOrder - b.joinerOrder); // 参会
      }

      // 确保在数据更新后保持当前的 tab 状态
      nextTick(() => {
        activeName.value = currentTab;
      });
    });
  }
};

const exportSignPdf = (mId: number) => {
  if (mId !== undefined && mId !== null) {
    download(
      `/meeting/manager/signTable/pdf/${mId}`,
      {},
      `${meetingInfo.value.meetingTitle}签到表.pdf`
    );
  }
};

const fetchAll = () => {
  getMeetingInfo(props.meetingId);
  getListJoin(props.meetingId);
  getSign(props.meetingId);
};
// 监听 meetingId 的变化
watch(
  () => props.meetingId,
  () => {
    fetchAll();
  },
  { immediate: true }
);
onBeforeUnmount(() => {
  clearInterval(timer);
});
</script>

<style lang="scss" scoped>
.cardtitle {
  font-size: 1.5rem;
  letter-spacing: 0.1875rem;
  padding-bottom: 0.625rem;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-left: 1.125rem;
}
.cardtitle::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 10%;
  width: 0.3125rem;
  height: 50%;
  background: #357ff3;
  border-radius: 0.3125rem;
}

@keyframes fade-in-scale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  70% {
    opacity: 1;
    transform: scale(1.03);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes slide-right {
  0% {
    transform: translateX(-400px);
  }
  80% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-slide-right {
  animation: slide-right 0.25s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 自定义 tabs 样式，确保高度正确传递 */
.custom-tabs {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.custom-tabs :deep(.el-tabs__content) {
  flex: 1;
  height: 0;
  min-height: 0;
  overflow: hidden;
}

.custom-tabs :deep(.el-tab-pane) {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* tab-pane 内容区域样式 */
.tab-pane-content {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 可滚动列表容器 */
.scrollable-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 0;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent;
}

.scrollable-list::-webkit-scrollbar {
  width: 6px;
}

.scrollable-list::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-list::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
  transition: background-color 0.3s;
}

.scrollable-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

/* 空状态容器 */
.empty-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.component-root {
  height: 100%;
  min-height: 0;
}
.rounded-lg {
  min-height: 0;
}
.flex-1 {
  min-height: 0;
}
</style>
