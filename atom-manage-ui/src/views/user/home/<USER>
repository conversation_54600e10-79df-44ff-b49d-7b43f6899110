<template>
  <div class="cardtitle">
    <div class="tab-container">
      <span
        class="tab-item"
        :class="{ active: activeTab === 'planning' }"
        @click="switchTab('planning')"
      >
        计划中会议
      </span>
      <span
        class="tab-item"
        :class="{ active: activeTab === 'completed' }"
        @click="switchTab('completed')"
      >
        已完成会议
      </span>
    </div>
    <span class="moreBtn">
      <el-button
        title="刷新"
        text
        size="large"
        :icon="Refresh"
        @click="refreshList"
        >刷新</el-button
      >
      <!--          <el-button type="primary" :icon="Timer" size="large">历史会议</el-button>-->
    </span>
  </div>
  <div
    v-if="jhhyData.length > 0"
    class="jhhyList"
    element-loading-background="rgba(255, 255, 255, 0.1)"
    v-loading="loading"
    @scroll="handleScroll"
  >
    <div
      class="peritem"
      v-for="item in jhhyData"
      :key="item.id"
      :class="{ active: item.id === props.meetingId }"
      @click="handleClick(item)"
    >
      <div class="peritem-tr1">
        <div class="peritem-tr1-left txt-italic">
          <el-icon class="custom-icon" :size="20">
            <Timer />
          </el-icon>
          <span v-if="item.meetingTime">
            {{ item.meetingDate }}
            {{ formatMeetingTime(item.meetingTime[0]) }} ~
            {{ formatMeetingTime(item.meetingTime[1]) }}
          </span>
        </div>
        <div class="peritem-tr1-right">{{ item.meetingTitle }}</div>
      </div>
      <div class="peritem-tr2">
        <div class="peritem-tr2-left">
          <el-icon class="custom-icon" :size="20">
            <LocationInformation />
          </el-icon>
          {{ selectDictLabel(meeting_place, item.meetingLocation) }}
        </div>
        <div class="peritem-tr2-right">
          <el-tag type="primary" size="small" round effect="light">
            {{ selectDictLabel(meeting_type, item.meetingType) }}
          </el-tag>
          <el-button
            v-if="activeTab === 'planning' && canEndMeeting(item)"
            class="end-meeting-btn"
            type="danger"
            size="small"
            round
            @click.stop="meetingEnd(item)"
          >
            结束会议
          </el-button>
        </div>
      </div>
    </div>
  </div>
  <el-empty v-else :image="emptypng" style="margin-top: 1.5rem" />
</template>
<script setup lang="ts">
import { BaseQuery } from "@/types";

defineOptions({
  name: "jhhy"
});
import { computed, onMounted, reactive, ref } from "vue";
import { listMyMeeting, endMeeting } from "@/api/meeting/meeting";

import { LocationInformation, Refresh, Timer } from "@element-plus/icons-vue";
import { useGlobal } from "@pureadmin/utils";
import { MeetingQuery } from "@/types/meeting/meeting";
import { PaginationProps } from "@pureadmin/table";
import { selectDictLabel } from "@/utils/atom";
import emptypng from "@/assets/images/empty.png";
import { useUserStoreHook } from "@/store/modules/user";
import { ElMessageBox, ElLoading, ElMessage } from "element-plus";

const emit = defineEmits(["handleClick"]);

// eslint-disable-next-line no-unused-vars
const props = defineProps({
  meetingId: {
    type: Number,
    required: false
  },
  ready: {
    type: Boolean,
    required: false
  }
});

const handleScroll = event => {
  const { scrollTop, scrollHeight, clientHeight } = event.target;
  const isBottom = scrollHeight - (scrollTop + clientHeight) < 1;
  if (isBottom) {
    if (jhhyData.value.length < total.value) {
      console.log("加载更多数据");
      pagination.currentPage = pagination.currentPage + 1;
      getList(true);
    }
  }
};
const userId = useUserStoreHook().userId;

// 添加已完成会议和计划中会议切换
const activeTab = ref("planning");

// 参数
const queryParams = reactive<MeetingQuery>(new MeetingQuery());
// 默认显示计划中会议
queryParams.meetingStatusIn = "0,1";
//分页参数
const pagination = reactive<PaginationProps & BaseQuery>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  orderByColumn: "meetingBeginTime",
  isAsc: "asc"
});
const total = ref(0);
const loading = ref(false);
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, meeting_place } = $useDict(
  "meeting_type",
  "meeting_place"
);

const jhhyData = ref([]);

const formatMeetingTime = (time: string) => {
  if (time) {
    return time.substring(0, 5);
  }
  return "";
};

const getList = (isAppend = false) => {
  loading.value = true;
  listMyMeeting(queryParams, pagination)
    .then(res => {
      if (isAppend) {
        jhhyData.value.push(...res.data);
      } else {
        jhhyData.value = res.data;
      }
      total.value = res.total;
      // 默认选中第一条
      if (jhhyData.value && jhhyData.value.length !== 0) {
        handleClick(jhhyData.value[0]);
      } else {
        handleClick(null);
      }
      loading.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleClick = item => {
  emit("handleClick", item?.id);
};

onMounted(() => {
  refreshList();
});

const switchTab = tab => {
  if (activeTab.value !== tab) {
    activeTab.value = tab;
    // 切换会议类型查询条件
    queryParams.meetingStatusIn = tab === "planning" ? "0,1" : "9";
    // 切换排序方式
    pagination.orderByColumn = "meetingBeginTime";
    pagination.isAsc = tab === "planning" ? "asc" : "desc";
    refreshList();
  }
};

// 判断是否可以结束会议
const canEndMeeting = computed(() => {
  return item => {
    return item.meetingStatusIn !== "9" && item.registerId == userId;
  };
});

// 结束会议按钮点击处理
const meetingEnd = item => {
  ElMessageBox.confirm(`确定要结束会议 "${item.meetingTitle}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: "结束会议中..."
      });
      endMeeting(item.id)
        .then(() => {
          ElMessage.success("会议已结束");
          refreshList();
        })
        .finally(() => {
          loadingInstance.close();
        });
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 刷新列表
const refreshList = () => {
  pagination.currentPage = 1;
  jhhyData.value = [];
  getList(false);
};
</script>

<style scoped lang="scss">
.cardtitle {
  font-size: 1.5rem;
  letter-spacing: 0.1875rem;
  padding-bottom: 0.625rem;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-left: 1.125rem;
  .titletxt {
    transform: rotateX(18deg);
  }

  .tab-container {
    display: flex;
    gap: 1.5rem;

    .tab-item {
      cursor: pointer;
      padding-bottom: 0.3125rem;
      position: relative;
      transform: rotateX(18deg);

      &.active {
        font-weight: bold;
        color: #357ff3;
      }

      &:hover {
        color: #357ff3;
      }
    }
  }
}
.cardtitle::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 10%;
  width: 0.3125rem;
  height: 50%;
  background: #357ff3;
  border-radius: 0.3125rem;
}
.jhhyList {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}
.jhhyList .peritem {
  cursor: pointer;
  border-bottom: 0.0625rem solid #d4dcea;
  padding: 0.9375rem;
  color: #606266;
  font-size: 1.125rem;
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  &.active {
    background-color: #357ff310;
  }
  .peritem-tr1,
  .peritem-tr2 {
    display: flex;
    justify-content: space-between;
    .peritem-tr1-left,
    .peritem-tr2-left,
    .peritem-tr1-right,
    .peritem-tr2-right {
      max-width: 98%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .peritem-tr1-left,
    .peritem-tr2-left {
      flex-basis: 50%;
    }
    .peritem-tr1-right,
    .peritem-tr2-right {
      flex: 1;
      text-align: right;
    }
  }
}
.jhhyList .peritem:hover {
  background-color: #357ff310;
}
.jhhyList .peritem:first-of-type {
  border-top: 0.0625rem solid #d4dcea;
}

.peritem-tr2 {
  .peritem-tr2-right {
    flex: 1;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 0.625rem;

    .end-meeting-btn {
      margin-left: 0.625rem;
    }
  }
}
</style>
