<script setup lang="ts">
import UserHome from "@/views/user/home/<USER>";
import Meeting from "@/views/meeting/manager/index.vue";
import File from "@/views/meeting/file/index.vue";
import Sign from "@/views/system/sign/index.vue";
import { ref, watch, computed, shallowRef } from "vue";
import { useUserStoreHook } from "@/store/modules/user";
import { hasAuth } from "@/router/utils";
import TopicManager from "@/views/meeting/topicManager/index.vue";
import { message } from "@/utils/message";
import icon_build from "@/assets/images/building.png";
import TaskManage from "@/views/meeting/taskManage/index.vue";
import Display from "@/views/meeting/dataDisplay/index.vue";
import MeetingGroup from "@/views/meeting/meetingGroup/index.vue";
import { usePageScale } from "@/hooks/usePageScale";
import { useDeviceResultStoreHook } from "@/store/modules/deviceResult";
import { useProvidedAncestorState } from "@/hooks/useAncestorState";

defineOptions({
  name: "UserHomeLayout"
});

const menuList = ref([
  {
    name: "首页",
    icon: "icon-shouye",
    url: "/user/home",
    perm: "system:user:home",
    component: UserHome
  },
  {
    name: "议题收集",
    icon: "icon-a-yijianfankuijibiji",
    url: "/topic/manager/center",
    perm: "meeting:topicMeeting:manager",
    component: TopicManager
  },
  {
    name: "会议预约",
    icon: "icon-rili",
    url: "/meeting/manager",
    perm: "meeting:manager:query",
    component: Meeting
  },
  {
    name: "会议分组",
    icon: "icon-tixing-tianchong",
    url: "/meeting/meetingGroup",
    perm: "meeting:meetingGroup:query",
    component: MeetingGroup
  },
  {
    name: "会议资料",
    icon: "icon-ziliao",
    url: "/meeting/file",
    perm: "meeting:file:query",
    component: File
  },
  {
    name: "督查督办",
    icon: "icon-dangjianAPP_duchaduban",
    url: "/meeting/task/manage",
    perm: "meeting:task:manage",
    component: TaskManage
  },
  // {
  //   name: "数据展示",
  //   icon: "icon-shuju",
  //   url: "/data-index",
  //   perm: "meeting:data:display"
  // },
  {
    name: "签名配置",
    icon: "icon-bi",
    url: "/sign",
    perm: "system:userSign:query",
    component: Sign
  },
  {
    name: "后台管理",
    icon: "icon-shezhi",
    url: "/welcome",
    perm: "system:manage:home"
  }
]);
// 根据权限过滤菜单
menuList.value = menuList.value.filter(item => hasAuth(item.perm));
const device = useDeviceResultStoreHook().getDevice;

// 是否是平板设备
const isTablet = computed(() => {
  return device.is("tablet");
});

if (!isTablet.value) {
  // 使用页面缩放 composable
  usePageScale({
    width: 1920,
    height: 1200,
    useVisualViewport: false
  });
}
const showMenu = ref(true);
const username = useUserStoreHook().username;
const menuUrl = ref<string>(menuList.value[0]?.url || "");
const currentComponent = shallowRef(menuList.value[0]?.component || null);

const { VITE_PUBLIC_PATH } = import.meta.env;
const dept = useUserStoreHook().dept;
const handleLink = (item: any | undefined) => {
  if (item == undefined) return;
  if (item.url === "/welcome") {
    goManageHome();
    return;
  }
  menuUrl.value = item.url;
  currentComponent.value = item.component;
};
// 前往管理端
const goManageHome = async () => {
  const scheme = window.location.protocol;
  const host = window.location.host;
  window.location.href = `${scheme}//${host}${VITE_PUBLIC_PATH}welcome`;
};
watch(
  () => menuUrl.value,
  newValue => {
    if (["/monitor", "/screen", "/manage"].includes(newValue)) {
      message("正在开发中", { type: "warning" });
    }
  }
);
// 提供祖先状态
useProvidedAncestorState("UserHomeLayout");
</script>

<template>
  <div class="userContent" :class="{ scaled: !isTablet }">
    <div class="whole-content" :class="{ scaled: !isTablet }">
      <div class="customBodyPage">
        <header class="h-[100px] flex justify-between py-[15px]">
          <div class="flex">
            <Transition name="slide">
              <div
                class="w-150 flex flex-col items-center py-[20px]"
                v-show="showMenu"
              >
                <img src="/logo.svg" alt="logo" width="80px" />
              </div>
            </Transition>
            <span
              class="logotitle text-3xl ml-[20px] leading-[100px] text-[#373e75]"
              >中铁二十四局集团数字会议系统</span
            >
          </div>
          <div class="mr-[30px] text-lg">
            <!--            <span-->
            <!--              class="fontSize26 pt3 iconfont text-xl mr-[20px] text-slate-500"-->
            <!--              :class="{ redDot: isShowRedDot }"-->
            <!--              >&#xe716;</span-->
            <!--            >-->
            <span class="fontSize18 iconfont text-xl mr-[5px] text-slate-500"
              >&#xe639;</span
            >{{ dept }} &nbsp;&nbsp;<span class="text-[#357ff3]">{{
              username
            }}</span>

            <span
              class="fontSize24 pt2 iconfont text-xl ml-[15px]"
              style="cursor: pointer"
              @click="useUserStoreHook().logOut()"
              >&#xe629;</span
            >
          </div>
        </header>

        <main class="flex flex-1 relative min-h-[420px]">
          <span
            class="absolute left-[151px] showMenu iconfont icon-zuojiantou-03"
            @click="showMenu = false"
            title="隐藏侧边栏"
            v-if="showMenu"
          />
          <span
            class="absolute iconfont showMenu icon-youjiantou-03"
            @click="showMenu = true"
            title="展开侧边栏"
            v-else
          />
          <Transition name="slide">
          <div class="flex flex-col w-150 glass-filter" v-if="showMenu">
            <div
              v-for="(item, index) in menuList"
              :key="index"
              class="flex flex-col text-center cursor-pointer mt-[10px] mx-[20px] p-[15px]"
              :class="item.url === menuUrl ? 'menu-active' : 'menu'"
              @click="handleLink(item)"
            >
              <span class="iconfont iconMenu" :class="item.icon" />
              <span class="text-lg">{{ item.name }}</span>
            </div>
          </div>
          </Transition>
          <div class="flex-1 contentHeight">
            <component
              v-if="currentComponent"
              :is="currentComponent"
              class="h-full bg-transparent special-page"
            />
            <el-empty
              class="special-page"
              style="height: 120vh"
              description="正在开发中..."
              :image="icon_build"
              :image-size="200"
              v-else
            />
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.main {
  font-size: 20px !important;
}
.userContent {
  position: relative;
  width: 100%;
  height: 100vh;
  &.scaled {
    overflow: hidden;
  }

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url(@/assets/images/bg.png) center center no-repeat;
    background-size: cover;
    z-index: -1;
  }
}
.whole-content {
  width: 1920px;
  height: 1200px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: transform 0.3s ease;
  &.scaled {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(var(--page-scale, 1));
    transform-origin: center center;
  }
}

.customBodyPage {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.showMenu {
  background: rgb(255 255 255 / 50%);
  border-radius: 20%;
  color: #5a5a5a;
  display: inline-block;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  font-size: 20px;
  top: 1px;
  z-index: 100;
  cursor: pointer;
}
.iconMenu {
  font-size: 32px;
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

/* html,
body {
  height: 100%;
  box-sizing: border-box;
  color: #2c2c2c;
} */
.contentHeight {
  height: 100%;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
}

h1 {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  color: #1d2460;
  font-weight: bolder;
  font-size: 30px;
  display: flex;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid 1px #cbd2e0;
}
header .logo {
  border-right: solid 1px #cbd2e0;
}
.w-150 {
  border-right: solid 1px #d4dcea;
  width: 150px;
}

.menu:hover {
  background-color: rgba(53, 127, 243, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 10px;
  color: #357ff3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(53, 127, 243, 0.1);
}

.menu:hover .iconMenu {
  color: #357ff3;
}

.menu-active {
  background-color: rgba(53, 127, 243, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.menu-active .iconMenu {
  color: #fff;
}

.menu-active::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(53, 127, 243, 0.6);
  border-radius: 10px;
  z-index: -1;
}

.menu {
  transition: background-color 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
  border: 1px solid transparent;
  position: relative;
  z-index: 1;
}
.dis-play {
  margin-top: 130px;
}

.slide-enter-from,
.slide-leave-to {
  width: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: width 0.3s ease-in-out;
}

.slide-enter-to,
.slide-leave-from {
  width: 150px;
}
</style>
