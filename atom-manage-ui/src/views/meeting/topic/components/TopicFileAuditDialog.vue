<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";

import {
  addTopicFileAudit,
  getTopicFileAudit,
  updateTopicFileAudit
} from "@/api/meeting/topic-file-audit";
import { TopicFileAudit } from "@/types/meeting/topic-file-audit";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TopicFileAuditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: TopicFileAudit,
    default: () => {
      return new TopicFileAudit();
    }
  }
});

// 表单信息
const form = ref<TopicFileAudit>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单校验
const rules = ref({
  userId: [
    { required: true, message: "审核用户编号不能为空", trigger: "blur" }
  ],
  status: [{ required: true, message: "审核状态不能为空", trigger: "blur" }],
  remarks: [{ required: true, message: "备注不能为空", trigger: "blur" }],
  topicId: [{ required: true, message: "主题编号不能为空", trigger: "blur" }],
  fileId: [{ required: true, message: "文件编号不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return form.value?.id ? "修改议题文件审核" : "添加议题文件审核";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: TopicFileAudit) {
  if (!visible.value) return;
  reset();
  if (data.id) {
    handleUpdate(data);
  }
}

/** 表单重置 */
function reset() {
  form.value = new TopicFileAudit();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: TopicFileAudit) {
  getTopicFileAudit(row.id).then(response => {
    form.value = response.data;
  });
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      if (form.value.id != undefined) {
        updateTopicFileAudit(form.value)
          .then(() => {
            message("修改成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addTopicFileAudit(form.value)
          .then(() => {
            message("新增成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改议题文件审核对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="680px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="审核用户编号" prop="userId">
        <el-input v-model="form.userId" placeholder="请输入审核用户编号" />
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="form.remarks" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="主题编号" prop="topicId">
        <el-input v-model="form.topicId" placeholder="请输入主题编号" />
      </el-form-item>
      <el-form-item label="文件编号" prop="fileId">
        <el-input v-model="form.fileId" placeholder="请输入文件编号" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
