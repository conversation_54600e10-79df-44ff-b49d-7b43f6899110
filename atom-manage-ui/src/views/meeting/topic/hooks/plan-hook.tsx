import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { delTopicPlan, listTopicPlan } from "@/api/meeting/topic-plan";
import { TopicPlan, TopicPlanQuery } from "@/types/meeting/topic-plan";

export function useTopic() {
	const queryParams = reactive<TopicPlanQuery>(new TopicPlanQuery());
	const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();

	const { TOPIC_STATUS } = $useDict("TOPIC_STATUS");

	const loading = ref(true);
	const visible = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);
	const topicList = ref([]);
	const topicData = ref<Topic>();

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			width: 70,
			formatter: () =>
				pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
		},
		{
			label: "标题",
			prop: "title",
			align: "center",
			minWidth: 100
		},
		{
			label: "上报时间",
			prop: "planList",
			align: "center",
			minWidth: 240,
			cellRenderer: ({ row }) => (
				<div>
					{row.planList.map(plan => (
						<div>
							<span class="font-bold">{plan?.typeName}:</span>
							{dayjs(plan?.beginTime).format("YYYY年M月D日H时")} -
							{dayjs(plan?.endTime).format("YYYY年M月D日H时")}
						</div>
					))}
				</div>
			)
		},
		{
			label: "议题个数",
			prop: "count",
			align: "center",
			width: 120
		},
		// {
		//   label: "状态",
		//   prop: "status",
		//   align: "center",
		//   width: 100,
		//   cellRenderer: ({ row }) => {
		//     return <DictTag options={TOPIC_STATUS} value={row.status} />;
		//   }
		// },
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 300,
			slot: "operation"
		}
	];

	/** 新增按钮操作 */
	function handleCreate() {
		visible.value = true;
		topicData.value = new TopicPlan();
	}

	function handleDelete(row?: TopicPlan) {
		if (row?.id) {
			delTopicPlan(row.id).then(() => {
				getList();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm('是否确认删除编号为"' + ids.value + '"的数据项？', {
			type: "warning",
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			title: "系统提示"
		})
			.then(function () {
				return delTopicPlan(ids.value);
			})
			.then(() => {
				getList();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 修改按钮操作 */
	function handleUpdate(row: Topic) {
		visible.value = true;
		topicData.value = row;
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await listTopicPlan(queryParams, pagination);
		topicList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"meeting/topic/export",
			{
				...queryParams
			},
			`topic_${new Date().getTime()}.xlsx`
		);
	}

	onMounted(() => {
		getList();
	});

	return {
		single,
		multiple,
		visible,
		loading,
		columns,
		pagination,
		queryParams,
		topicData,
		topicList,
		handleCreate,
		handleDelete,
		handleUpdate,
		handleQuery,
		resetQuery,
		handleExport,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange,
		getList
	};
}
