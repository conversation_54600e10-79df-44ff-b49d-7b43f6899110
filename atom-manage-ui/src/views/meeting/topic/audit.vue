<script setup lang="ts">
import { ref } from "vue";
import { useTopicFileAudit } from "./hooks/audit-hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import TopicFileAuditDialog from "./components/TopicFileAuditDialog.vue";

//** 议题文件审核 */
defineOptions({
  name: "TopicFileAudit"
});

const queryRef = ref();

const {
  visible,
  loading,
  columns,
  pagination,
  queryParams,
  topicFileAuditData,
  topicFileAuditList,
  handleCreate,
  handleDelete,
  handleUpdate,
  handleQuery,
  handleExport,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList
} = useTopicFileAudit();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="议题" prop="topicId">
        <el-input
          v-model="queryParams.topicName"
          placeholder="请输入议题名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="议题类型" prop="fileId">
        <el-input
          v-model="queryParams.typeName"
          placeholder="请输入议题类型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      title="议题文件审核列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons />
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="center"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="topicFileAuditList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:topicFileAudit:edit']"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              审核
            </el-button>
            <!-- <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['meeting:topicFileAudit:remove']"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm> -->
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <topic-file-audit-dialog
      v-model:visible="visible"
      :data="topicFileAuditData"
      @confirm="getList"
    />

    <topic-audit-list-dialog
      v-model:visible="visible"
      :data="topicFileAuditData"
      @confirm="getList"
    />
  </div>
</template>
