<script setup lang="ts">
import { computed, ref } from "vue";
import { useTaskAssign } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import TaskUserEditDialog from "./dialog.vue";
import TaskExecuteDialog from "@/views/meeting/taskUserExecute/dialog.vue";
import PreviewDialog from "@/views/meeting/taskUserExecute/previewDialog.vue";
import TaskOpLogDialog from "@/views/meeting/task/components/TaskOpLogDialog.vue";
import { useAncestorDetection } from "@/hooks/useAncestorState";

//** 督查督办主 */
defineOptions({
  name: "TaskAssign"
});

const queryRef = ref();

const {
  visible,
  opLogVisible,
  loading,
  columns,
  pagination,
  queryParams,
  taskData,
  taskList,
  tableMaxHeight,
  mainRef,
  tableRef,
  calculateTableMaxHeight,
  handleWork,
  handleSelect,
  handleQuery,
  handleOpLog,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList,
  taskUser,
  executeVisible,
  taskExecute,
  previewExecuteVisible,
  userId,
  tabs,
  handleTabChange,
  handleAudit
} = useTaskAssign();
defineExpose({ getList, calculateTableMaxHeight });
</script>

<template>
  <div class="main" ref="mainRef">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color search-form"
    >
      <el-form-item label="督办事项名称" prop="taskTitle">
        <el-input
          v-model="queryParams.taskTitle"
          placeholder="请输入督办事项名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker
          v-model="queryParams.publishTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
          @change="handleQuery"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #title>
        <div class="flex items-center">
          <template v-for="(label, key) in tabs" :key="key">
            <span
              :class="{
                'font-bold truncate text-lg cursor-pointer mr-2 transition-all duration-200':
                  queryParams.tab === key ||
                  (key === 'ALL' && !queryParams.tab),
                'text-base cursor-pointer text-gray-500 mr-2 transition-all duration-200':
                  queryParams.tab && queryParams.tab !== key
              }"
              @click="handleTabChange(key)"
            >
              {{ label }}
            </span>
          </template>
        </div>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="center"
          :max-height="tableMaxHeight"
          ref="tableRef"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="taskList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              v-if="row.taskStatus === '1' && row.userId === userId"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:taskAssign:option']"
              @click="handleSelect(row)"
              :icon="useRenderIcon('ep:check')"
            >
              指派
            </el-button>
            <el-button
              v-if="row.taskStatus === '1' && row.userId === userId"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:taskAssign:option']"
              @click="handleWork(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              直接办结
            </el-button>
            <el-button
              v-if="
                row.taskStatus === '20' &&
                row.userId === userId &&
                queryParams.tab != 'AUDITED'
              "
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:taskAssign:option']"
              @click="handleAudit(row)"
              :icon="useRenderIcon('ep:coordinate')"
            >
              审核
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleOpLog(row)"
              :icon="useRenderIcon('ep:view')"
            >
              日志
            </el-button>
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <task-execute-dialog
      v-model:visible="executeVisible"
      :data="taskExecute"
      @confirm="getList"
    />

    <preview-dialog
      v-model:visible="previewExecuteVisible"
      :data="taskExecute"
      @confirm="getList"
    />

    <task-user-edit-dialog
      v-model:visible="visible"
      :data="taskUser"
      @confirm="getList"
    />

    <task-op-log-dialog v-model:visible="opLogVisible" :taskId="taskData?.id" />

    <!-- <task-preview-dialog
      v-model:visible="previewVisible"
      :data="taskData"
      @confirm="getList"
    /> -->
  </div>
</template>
