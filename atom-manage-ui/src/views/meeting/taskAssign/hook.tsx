import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { listTaskAssign } from "@/api/meeting/task";
import { Task, TaskQuery, TaskTab } from "@/types/meeting/task";
import DictTag from "@/components/DictTag";
import { TaskUser } from "@/types/meeting/taskUser";
import { TaskUserExecute } from "@/types/meeting/taskUserExecute";
import { useUserStoreHook } from "@/store/modules/user";
import { useAncestorDetection } from "@/hooks/useAncestorState";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";

export function useTaskAssign() {
  const queryParams = reactive<TaskQuery>(new TaskQuery());
  const { $useDict } = useGlobal<GlobalPropertiesApi>();
  const { TASK_TYPE, TASK_STATUS } = $useDict("TASK_TYPE", "TASK_STATUS");

  const loading = ref(true);
  const visible = ref(false);
  const previewVisible = ref(false);
  const executeVisible = ref(false);
  const previewExecuteVisible = ref(false);
  const opLogVisible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const taskList = ref([]);
  const taskData = ref<Task>();
  const taskUser = ref<TaskUser>(new TaskUser());
  const taskExecute = ref<TaskUserExecute>(new TaskUserExecute());
  const taskUserType = ref("");
  const executeType = ref("");
  const type = ref("view");

  const userId = useUserStoreHook().userId;
  const tabs = {
    [TaskTab.ALL]: "全部",
    [TaskTab.ASSIGN]: "待指派",
    [TaskTab.AUDITING]: "待审核",
    [TaskTab.AUDITED]: "已审核"
  };

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns: TableColumnList = [
    // {
    //   label: "勾选列",
    //   type: "selection",
    //   width: 55,
    //   align: "left"
    // },
    {
      label: "序号",
      type: "index",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "任务类型",
      prop: "taskType",
      align: "center",
      minWidth: 70,
      cellRenderer: ({ row }) => (
        <DictTag options={TASK_TYPE} value={row.taskType} />
      )
    },
    {
      label: "督办事项名称",
      prop: "taskTitle",
      align: "left",
      minWidth: 180,
      cellRenderer: ({ row }) => {
        return (
          <span
            class="text-blue-700 cursor-pointer"
            onClick={() => handleView(row)}
          >
            {row.taskTitle}
          </span>
        );
      }
    },
    // {
    //   label: "主责部门",
    //   prop: "deptName",
    //   align: "left",
    //   minWidth: 150
    // },
    {
      label: "发布时间",
      prop: "publishTime",
      align: "center",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span>
          {row.publishTime ? dayjs(row.publishTime).format("YYYY-MM-DD") : ""}
        </span>
      )
    },
    {
      label: "办结时间",
      prop: "completeTime",
      align: "center",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span>{dayjs(row.completeTime).format("YYYY-MM-DD")}</span>
      )
    },
    {
      label: "任务状态",
      prop: "taskStatus",
      align: "center",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <DictTag options={TASK_STATUS} value={row.taskStatus} />
      )
    },
    {
      label: "创建人",
      prop: "createName",
      align: "center",
      minWidth: 80
    },
    {
      label: "操作",
      fixed: "right",
      align: "left",
      width: 300,
      slot: "operation"
    }
  ];

  /** 选择执行人操作 */
  function handleCreate() {
    taskUser.value = new TaskUser();
    taskUserType.value = "edit";
    visible.value = true;
  }

  // 执行
  // function handleExecute() {
  //   taskExecute.value = new TaskUserExecute();
  //   executeType.value = "edit";
  //   executeVisible.value = true;
  // }

  // 指派
  function handleSelect(row?: Task) {
    taskUser.value.taskId = row.id;
    taskUser.value.taskName = row.taskTitle;
    visible.value = true;
  }

  // 查看
  function handleView(row?: Task) {
    taskExecute.value = new TaskUserExecute();
    taskExecute.value.taskId = row.id;
    taskExecute.value.dialogMode = "preview";
    previewExecuteVisible.value = true;
  }

  // 直接执行
  function handleWork(row: Task) {
    taskExecute.value = new TaskUserExecute();
    taskExecute.value.taskId = row.id;
    taskExecute.value.noAssignExecute = true;
    executeVisible.value = true;
  }

  // 审核
  function handleAudit(row: Task) {
    taskExecute.value = new TaskUserExecute();
    taskExecute.value.taskId = row.id;
    taskExecute.value.dialogMode = "audit";
    previewExecuteVisible.value = true;
  }

  /** 查看按钮操作 */
  // function handlePreview(row: Task) {
  //   previewVisible.value = true;
  //   taskData.value = row;
  // }

  function handleDateRange() {
    if (queryParams.publishTimeRange?.length == 2) {
      queryParams.publishTimeStart = queryParams.publishTimeRange[0];
      queryParams.publishTimeEnd = queryParams.publishTimeRange[1];
    } else {
      queryParams.publishTimeStart = "";
      queryParams.publishTimeEnd = "";
    }
  }

  async function getList() {
    loading.value = true;
    handleDateRange();
    const { data, total } = await listTaskAssign(queryParams, pagination);
    taskList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  function handleTabChange(tab: TaskTab) {
    queryParams.tab = tab;
    getList();
  }

  function handleOpLog(row: Task) {
    taskData.value = row;
    opLogVisible.value = true;
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visible,
    previewVisible,
    opLogVisible,
    loading,
    columns,
    pagination,
    queryParams,
    taskData,
    taskList,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleCreate,
    // handleExecute,
    handleWork,
    handleSelect,
    handleAudit,
    // handlePreview,
    handleQuery,
    handleOpLog,
    resetQuery,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList,
    taskUser,
    executeVisible,
    taskExecute,
    taskUserType,
    executeType,
    previewExecuteVisible,
    handleView,
    type,
    userId,
    tabs,
    handleTabChange
  };
}
