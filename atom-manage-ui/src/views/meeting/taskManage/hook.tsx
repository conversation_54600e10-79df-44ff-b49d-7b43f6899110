import { nextTick, onMounted, reactive, ref } from "vue";

import { TopicMeetingAuditQuery } from "@/types/meeting/topicMeetingAudit";
import { hasAuth } from "@/router/utils";

export function taskManager() {
  const queryParams = reactive<TopicMeetingAuditQuery>(
    new TopicMeetingAuditQuery()
  );

  const loading = ref(true);
  const visible = ref(false);
  const activeName = ref<"publish" | "assign" | "execute" | "major">("publish");
  const tabName = ref<string>("publish");
  const publishRef = ref();
  const executeRef = ref();
  const assignRef = ref();
  const majorRef = ref();
  const refs = {
    publishRef,
    executeRef,
    assignRef,
    majorRef
  };

  const single = ref(true);
  const multiple = ref(true);

  function afterTabChanged(tabName: string) {
    nextTick(() => {
      const ref = refs[`${tabName}Ref`];
      if (ref.value) {
        ref.value.getList();
        ref.value.calculateTableMaxHeight();
      }
    });
  }

  async function handleClick(param: any) {
    afterTabChanged(param.paneName);
  }

  function handleAuth() {
    if (hasAuth("meeting:task:query")) {
      activeName.value = "publish";
    } else if (hasAuth("meeting:taskAssign:query")) {
      activeName.value = "assign";
    } else if (hasAuth("meeting:taskUser:query")) {
      activeName.value = "execute";
    } else if (hasAuth("meeting:taskMajor:query")) {
      activeName.value = "major";
    }
    afterTabChanged(activeName.value);
  }

  onMounted(() => {
    handleAuth();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    queryParams,
    activeName,
    tabName,
    publishRef,
    executeRef,
    assignRef,
    majorRef,
    handleClick,
    handleAuth
  };
}
