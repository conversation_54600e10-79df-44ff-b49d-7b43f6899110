<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";

import {
  addFile,
  getFile,
  updateFile,
  getFolderTree
} from "@/api/meeting/file";
import { FileEntity, FolderTree } from "@/types/meeting/file";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "FolderEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: FileEntity,
    default: () => {
      return new FileEntity();
    }
  }
});

// 表单信息
const form = ref<FileEntity>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);
const treeData = ref<FolderTree[]>([]);
// 表单校验
const rules = ref({
  name: [{ required: true, message: "文件夹名称不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return form.value?.id ? "修改文件夹" : "添加文件夹";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: FileEntity) {
  if (!visible.value) return;
  reset();
  if (data.parentId) {
    form.value.parentId = data.parentId;
  } else {
    form.value.parentId = 0;
  }
  getTree();
  if (data.id) {
    handleUpdate(data);
  }
}

function getTree() {
  getFolderTree().then(response => {
    treeData.value = [
      {
        id: 0,
        label: "根节点",
        parentId: 0,
        children: response.data
      }
    ];
  });
}

/** 表单重置 */
function reset() {
  form.value = new FileEntity();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: FileEntity) {
  getFile(row.id).then(response => {
    form.value = response.data;
  });
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      form.value.entityType = "folder";
      loading.value = true;
      if (form.value.id != undefined) {
        updateFile(form.value)
          .then(() => {
            message("修改成功", { type: "success" });
            visible.value = false;
            emit("confirm", form.value);
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addFile(form.value)
          .then(resp => {
            message("新增成功", { type: "success" });
            visible.value = false;
            form.value.id = resp.data;
            emit("confirm", form.value);
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改文件对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="680px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="父节点" prop="parentId">
        <el-tree-select
          :props="{ value: 'id' }"
          ref="treeRef"
          :data="treeData"
          filterable
          v-model="form.parentId"
          placeholder="请选择父节点"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入名称" />
      </el-form-item>

      <el-form-item label="排序" prop="fileOrder">
        <el-input
          v-model="form.fileOrder"
          type="number"
          placeholder="请输入排序"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
