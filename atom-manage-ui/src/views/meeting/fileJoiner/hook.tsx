import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import {
	delFile,
	listFile,
	addFile,
	listSelfJoinFolder,
	listSelfFile,
	submitFile
} from "@/api/meeting/file";
import {
	FileEntity,
	FileEntityQuery,
	FileEntityTree,
	FolderTree
} from "@/types/meeting/file";
import { selectDictLabel } from "@/utils/atom";
import dayjs from "dayjs";
import DictTag from "@/components/DictTag";
import { checkReUpload } from "@/api/meeting/meeting";
import download from "@/plugins/download";
import { useRouter } from "vue-router";

export function useFile() {
	const queryParams = reactive<FileEntityQuery>(new FileEntityQuery());
	const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();

	const { meeting_file_type, FILE_STATUS } = $useDict(
		"meeting_file_type",
		"FILE_STATUS"
	);
	const loading = ref(true);
	const visibleUpload = ref(false);
	const visibleEdit = ref(false);
	const router = useRouter();

	const selfFolderIds = ref([]);
	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);
	const fileList = ref([]);
	const fileData = ref<FileEntity>();
	const selectedTree = ref<FileEntityTree>(null);
	const uploadOpen = ref(false);
	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			width: 70,
			formatter: () =>
				pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
		},
		{
			label: "名称",
			prop: "name",
			align: "center",
			minWidth: 100
		},
		{
			label: "文件大小",
			prop: "fileSize",
			align: "center",
			width: 100,
			formatter: (row: FileEntity) => (row.fileSize / 1024).toFixed(2) + "KB"
		},
		// {
		//   label: "备注",
		//   prop: "remark",
		//   align: "center",
		//   width: 100
		// },
		// {
		//   label: "排序",
		//   prop: "fileOrder",
		//   align: "center",
		//   width: 100
		// },
		{
			label: "文件类型",
			prop: "flag",
			align: "center",
			width: 100,

			formatter: (row: FileEntity) =>
				selectDictLabel(meeting_file_type.value, row.flag)
		},
		{
			label: "上传日期",
			prop: "createTime",
			align: "center",
			width: 100,
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
		},
		{
			label: "状态",
			prop: "fileStatus",
			align: "center",
			width: 100,
			cellRenderer: ({ row }) => {
				return <DictTag options={FILE_STATUS} value={row.fileStatus} />;
			}
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 250,
			slot: "operation"
		}
	];

	/** 新增按钮操作 */
	function handleCreate(file: FileEntity) {
		addFile(file).then(() => {
			message("新增成功", { type: "success" });
			getList();
		});
	}

	function handleSubmit(row: FileEntity) {
		submitFile(row.id)
			.then(resp => {
				if (resp.code === 200) {
					message("提交成功", { type: "success" });
				} else {
					message("提交失败", { type: "error" });
				}
			})
			.finally(() => {
				getList();
			});
	}

	function handleDelete(row?: FileEntity) {
		if (row?.id) {
			delFile(row.id).then(() => {
				getList();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm(
			'是否确认删除文件编号为"' + ids.value + '"的数据项？',
			{
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				title: "系统提示"
			}
		)
			.then(function () {
				return delFile(ids.value);
			})
			.then(() => {
				getList();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 查询到关联自己的资源目录 **/
	async function getSelfFolder() {
		const { data } = await listSelfJoinFolder();
		selfFolderIds.value = data;
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await listSelfFile(queryParams, pagination);
		fileList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	function handleEditFile(file: FileEntity) {
		visibleEdit.value = true;
		fileData.value = file;
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"meeting/file/export",
			{
				...queryParams
			},
			`file_${new Date().getTime()}.xlsx`
		);
	}

	function handlePreview(row) {
		router.push({
			name: "PdfPreview",
			params: {
				id: row.meetingId
			},
			query: {
				ossId: row.ossId
			}
		});
	}

	function downloadFile(file: any) {
		download.oss(file.ossId, null);
	}

	async function getDeadTime(e) {
		const { data } = await checkReUpload(e.id);
		console.log(data);

		const deadTime = data.deadTime;
		const openReUpload = data.openReUpload;
		const timeOut = data.timeOut;
		// 1、若未超时 则正常上传
		// 2、若已超时 判断是否打开重新上传 打开：正常上传 关闭：关闭
		if (!timeOut) {
			uploadOpen.value = true;
		} else {
			uploadOpen.value = openReUpload;
		}
		console.log(uploadOpen.value);
	}

	function handleTreeSelect(e) {
		selectedTree.value = e;
		queryParams.parentId = e.id;
		getList();

		// 查询关联会议的截止时间 和目前时间相比的结果 是否人为控制打开了重新上传
		getDeadTime(e);
	}

	onMounted(() => {
		getSelfFolder();
		getList();
	});

	return {
		single,
		multiple,
		visibleUpload,
		selectedTree,
		visibleEdit,
		loading,
		columns,
		pagination,
		queryParams,
		fileData,
		fileList,
		selfFolderIds,
		uploadOpen,
		handlePreview,
		downloadFile,
		handleCreate,
		handleDelete,
		handleSubmit,
		handleQuery,
		handleTreeSelect,
		handleEditFile,
		resetQuery,
		handleExport,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange,
		getList
	};
}
