<script setup lang="ts">
import { ref, computed } from "vue";
import { useFile } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import FileEditDialog from "./components/FileEditDialog.vue";
import FolderTree from "../file/components/FolderTree";
import { useUpload } from "@/components/Upload";
import UploadIcon from "@iconify-icons/ep/upload";
import { useToggle } from "@vueuse/core";
import { FileUploadResult } from "@/types/system/sys-oss";
import { FileEntity } from "@/types/meeting/file";
//** 文件 */
defineOptions({
  name: "FileJoiner"
});

const queryRef = ref();
const uploadBtn = ref();
const fileFolderMap = ref({});
const {
  multiple,
  visibleEdit,
  loading,
  columns,
  pagination,
  queryParams,
  fileData,
  fileList,
  selectedTree,
  selfFolderIds,
  uploadOpen,
  handlePreview,
  downloadFile,
  handleCreate,
  handleSubmit,
  handleEditFile,
  handleDelete,
  handleTreeSelect,
  handleQuery,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList
} = useFile();

const onSuccessCallback = (fileResult: FileUploadResult) => {
  // 关联附件与会议
  const file = buildFileEntity(fileResult);
  handleCreate(file);
};

const buildFileEntity = (fileResult: FileUploadResult) => {
  const file = new FileEntity();
  file.entityType = "file";
  file.fileSize = fileResult.fileSize;
  file.ossId = fileResult.ossId;
  file.name = fileResult.originalName;
  file.fileType = fileResult.fileSuffix;
  file.parentId = fileFolderMap.value[fileResult.uid];
  return file;
};

const params = ref({
  fileType: ["doc", "xls", "xlsx", "docx", "ppt", "pptx", "txt", "pdf"],
  fileSize: 2048
});

const {
  handleBeforeUpload,
  handleHttpRequest,
  handleUploadError,
  handleCloseUpload,
  headers,
  fileList: uploadList
} = useUpload(params, null, onSuccessCallback);

const [isToggled, toggle] = useToggle(false);

const disabled = computed(() => {
  return (selectedTree.value?.id == null || selectedTree.value?.id === undefined) || !uploadOpen.value;
});

const _handleBeforeUpload = async file => {
  toggle(true);
  fileFolderMap.value[file.uid] = selectedTree.value.id;
  return await handleBeforeUpload(file);
};
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="flex">
      <FolderTree
        class="w-[300px] h-full"
        @selected="handleTreeSelect"
        :disabled="true"
        :filter-array="selfFolderIds"
        :base="true"
      />

      <div style="width: calc(100% - 308px)">
        <pure-table-bar
          :title="selectedTree?.id ? selectedTree.name : '文件列表'"
          :columns="columns"
          :queryRef="queryRef"
          @refresh="handleQuery"
        >
          <template #buttons>
            <el-upload
              class="upload-file"
              multiple
              :action="'#'"
              :before-upload="_handleBeforeUpload"
              :limit="999"
              :show-file-list="false"
              v-model:file-list="uploadList"
              :headers="headers"
              :on-error="handleUploadError"
              :http-request="handleHttpRequest"
              ref="uploadRef"
            >
              <!-- 上传按钮 -->
              <el-button
                :disabled="disabled"
                ref="uploadBtn"
                type="primary"
                class="sm:shrink-button"
                size="default"
                v-auth="['meeting:file:add']"
                :icon="useRenderIcon(UploadIcon)"
              >
                上传
              </el-button>
            </el-upload>

            <el-button
              type="danger"
              class="ml-2 sm:shrink-button"
              plain
              :icon="useRenderIcon('ep:delete')"
              v-auth="['meeting:file:remove']"
              :disabled="multiple"
              @click="handleDelete()"
              >删除
            </el-button>
          </template>
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              border
              align-whole="center"
              row-key="id"
              showOverflowTooltip
              table-layout="auto"
              default-expand-all
              :loading="loading"
              :size="size"
              :data="fileList"
              :columns="dynamicColumns"
              :pagination="pagination"
              :header-cell-style="{
                background: 'var(--el-table-row-hover-bg-color)',
                color: 'var(--el-text-color-primary)'
              }"
              @selection-change="handleSelectionChange"
              @page-size-change="pageSizeChange"
              @page-current-change="pageCurrentChange"
            >
              <template #operation="{ row }">
                <el-button
                  v-if="row.fileType === 'pdf'"
                  link
                  type="primary"
                  :size="size"
                  @click="handlePreview(row)"
                >
                  浏览
                </el-button>
                <el-button
                  v-else
                  link
                  type="primary"
                  :size="size"
                  @click="downloadFile(row)"
                >
                  下载
                </el-button>
                <el-popconfirm
                  title="是否确认提交?"
                  @confirm="handleSubmit(row)"
                >
                  <template #reference>
                    <el-button
                      class="reset-margin"
                      link
                      type="primary"
                      :size="size"
                      v-auth="['meeting:file:submit']"
                      :icon="useRenderIcon('ep:check')"
                      v-if="['0', '9'].includes(row.fileStatus)"
                    >
                      提交
                    </el-button>
                  </template>
                </el-popconfirm>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['meeting:file:edit']"
                  @click="handleEditFile(row)"
                  :icon="useRenderIcon('ep:edit-pen')"
                  v-if="['0', '9'].includes(row.fileStatus)"
                >
                  修改
                </el-button>
                <el-popconfirm
                  title="是否确认删除?"
                  @confirm="handleDelete(row)"
                >
                  <template #reference>
                    <el-button
                      class="reset-margin"
                      link
                      type="primary"
                      :size="size"
                      v-auth="['meeting:file:remove']"
                      :icon="useRenderIcon('ep:delete')"
                      v-if="['0', '9'].includes(row.fileStatus)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </pure-table>
          </template>
        </pure-table-bar>
      </div>
    </div>

    <file-edit-dialog
      v-model:visible="visibleEdit"
      :data="fileData"
      @confirm="getList"
    />

    <div
      class="affix text-sm w-[300px] overflow-hidden h-[150px] bg-white rounded-sm p-2 pt-0 shadow-md"
      v-if="isToggled"
    >
      <div
        class="flex items-center justify-between h-8 border-b border-gray-200"
      >
        <span>上传进度</span>
        <span
          class="pb-2 text-lg text-gray-400 cursor-pointer"
          @click="toggle()"
          >x</span
        >
      </div>
      <div class="overflow-auto h-[120px]">
        <div
          class="flex justify-between py-2"
          v-for="item in uploadList"
          :key="item.uid"
        >
          <span>{{ item.originalName ?? item.name }}</span>
          <el-tag type="success" size="small" v-if="item.status === 'success'"
            >完成</el-tag
          >
          <span class="flex" v-else>
            <el-progress :percentage="item.percentage" />
            <span @click="handleCloseUpload(item)">x</span>
          </span>
        </div>
      </div>
    </div>

    <div class="affix" v-else>
      <el-button
        @click="toggle()"
        type="default"
        plain
        circle
        :icon="useRenderIcon('ep:upload')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.affix {
  position: fixed;
  right: 1rem;
  bottom: 0.5rem;
}
</style>
