import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { delMeetingGroup, listMeetingGroup } from "@/api/meeting/meetingGroup";
import { MeetingGroup, MeetingGroupQuery } from "@/types/meeting/meetinggroup";
import { useAncestorDetection } from "@/hooks/useAncestorState";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";

export function useMeetingGroup() {
  const queryParams = reactive<MeetingGroupQuery>(new MeetingGroupQuery());
  const { $download } = useGlobal<GlobalPropertiesApi>();

  const loading = ref(true);
  const visible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const meetingGroupList = ref([]);
  const meetingGroupData = ref<MeetingGroup>();

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "名字",
      prop: "name",
      align: "center",
      minWidth: 200
    },
    {
      label: "备注",
      prop: "remark",
      align: "center",
      width: 200
    },
    {
      label: "操作",
      fixed: "right",
      align: "left",
      width: 200,
      slot: "operation"
    }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    meetingGroupData.value = new MeetingGroup();
  }

  function handleDelete(row?: MeetingGroup) {
    if (row?.id) {
      delMeetingGroup(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除用户组编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delMeetingGroup(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: MeetingGroup) {
    visible.value = true;
    meetingGroupData.value = row;
  }

  async function getList() {
    loading.value = true;
    const { data, total } = await listMeetingGroup(queryParams, pagination);
    meetingGroupList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "meeting/meetingGroup/export",
      {
        ...queryParams
      },
      `meetingGroup_${new Date().getTime()}.xlsx`
    );
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    columns,
    pagination,
    queryParams,
    meetingGroupData,
    meetingGroupList,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleQuery,
    resetQuery,
    handleExport,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList
  };
}
