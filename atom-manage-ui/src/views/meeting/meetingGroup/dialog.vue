<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";

import {
  addMeetingGroup,
  getMeetingGroup,
  updateMeetingGroup
} from "@/api/meeting/meetingGroup";
import { MeetingGroup } from "@/types/meeting/meetingGroup";
import UserSelectTree from "@/components/UserSelectTree/index.vue";
const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "MeetingGroupEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: MeetingGroup,
    default: () => {
      return new MeetingGroup();
    }
  }
});

// 表单信息
const form = ref<MeetingGroup>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);
const loadingInfo = ref(false);

// 表单校验
const rules = ref({
  name: [{ required: true, message: "名字不能为空", trigger: "blur" }],
  groupUsers: [{ required: true, message: "分组成员不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return form.value?.id ? "修改用户组" : "添加用户组";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: MeetingGroup) {
  if (!visible.value) return;
  reset();
  if (data.id) {
    handleUpdate(data);
  } else {
    loadingInfo.value = true;
  }
}

/** 表单重置 */
function reset() {
  form.value = new MeetingGroup();
  form.value.groupUsers = [];
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: MeetingGroup) {
  getMeetingGroup(row.id).then(response => {
    form.value = response.data;
    loadingInfo.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      if (form.value.id != undefined) {
        updateMeetingGroup(form.value)
          .then(() => {
            message("修改成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addMeetingGroup(form.value)
          .then(() => {
            message("新增成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

function cancel() {
  loadingInfo.value = false;
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改用户组对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="680px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="名字" prop="name">
        <el-input v-model="form.name" placeholder="请输入名字" />
      </el-form-item>
      <el-form-item label="分组成员" prop="groupUsers">
        <user-select-tree
          v-model="form.groupUsers"
          v-if="loadingInfo"
          :import-type="'user'"
          :isSearch="true"
          :multiple="true"
          :enable-sort="true"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
