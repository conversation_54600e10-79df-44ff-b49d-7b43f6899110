<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { TaskOpLog } from "@/types/meeting/taskOpLog";
import { getTaskOpLogList } from "@/api/meeting/taskOpLog";
import { useGlobal } from "@pureadmin/utils";
import dayjs from "dayjs";
const { $useDict } = useGlobal<GlobalPropertiesApi>();

defineOptions({
  name: "TaskOpLogDialog"
});
const visible = defineModel<boolean>("visible", { default: false });
const props = defineProps<{
  taskId: number;
}>();
const loading = ref<boolean>(false);
const logList = ref<TaskOpLog[]>([]);
const { TASK_OP_LOG_TYPE } = $useDict("TASK_OP_LOG_TYPE");

watch(
  () => visible.value,
  () => {
    if (!visible.value) return;
    getOpLogList(props.taskId);
  }
);

/** 查询操作日志列表 */
function getOpLogList(taskId: number) {
  loading.value = true;
  getTaskOpLogList(taskId)
    .then(res => {
      logList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

const title = computed(() => {
  return "操作日志";
});
</script>

<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="680px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <div
      class="op-log-content max-h-[60vh] overflow-y-auto px-4"
      v-loading="loading"
    >
      <el-timeline>
        <el-timeline-item
          v-for="(log, index) in logList"
          :key="index"
          :timestamp="dayjs(log.createTime).format('YYYY-MM-DD HH:mm:ss')"
          placement="top"
        >
          <el-card class="mb-4">
            <div class="flex flex-col gap-2">
              <div class="flex justify-between items-center">
                <span class="font-medium text-[16px]">{{
                  TASK_OP_LOG_TYPE.find(it => it.value == log.opType)?.label
                }}</span>
                <span class="text-gray-500">操作人：{{ log.userName }}</span>
              </div>
              <div class="text-gray-600">操作明细：{{ log.opName }}</div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>

      <!-- 无数据显示 -->
      <el-empty v-if="logList.length === 0" description="暂无操作记录" />
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.op-log-content {
  :deep(.el-timeline-item__timestamp) {
    color: #666;
    font-size: 14px;
  }

  :deep(.el-card) {
    --el-card-padding: 12px;
  }
}
</style>
