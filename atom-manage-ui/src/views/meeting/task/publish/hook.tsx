import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { ComponentCustomProperties, onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import {
  delTask,
  listTask,
  publishTask,
  adjustMajorFillExpDays,
  forceRevokeTask
} from "@/api/meeting/task";
import { Task, TaskQuery } from "@/types/meeting/task";
import DictTag from "@/components/DictTag";
import { TaskUserExecute } from "@/types/meeting/taskUserExecute";
import { useAncestorDetection } from "@/hooks/useAncestorState";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";

export function useTask() {
  const queryParams = reactive<TaskQuery>(new TaskQuery());
  const { $useDict } = useGlobal<GlobalPropertiesApi>();
  const { $prompt, $confirm } = useGlobal<ComponentCustomProperties>();
  const { TASK_TYPE, TASK_STATUS } = $useDict("TASK_TYPE", "TASK_STATUS");

  const loading = ref(true);
  const visible = ref(false);
  const previewVisible = ref(false);
  const opLogVisible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const taskList = ref([]);
  const taskData = ref<Task>();
  const taskDataPreview = ref<TaskUserExecute>(new TaskUserExecute());

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "任务类型",
      prop: "taskType",
      align: "center",
      minWidth: 70,
      cellRenderer: ({ row }) => (
        <DictTag options={TASK_TYPE} value={row.taskType} />
      )
    },
    {
      label: "督办事项名称",
      prop: "taskTitle",
      align: "center",
      minWidth: 180,
      cellRenderer: ({ row }) => {
        return (
          <span
            class="text-blue-700 cursor-pointer"
            onClick={() => handlePreview(row)}
          >
            {row.taskTitle}
          </span>
        );
      }
    },
    // {
    //   label: "主责部门",
    //   prop: "deptName",
    //   align: "center",
    //   minWidth: 150
    // },
    {
      label: "发布时间",
      prop: "publishTime",
      align: "center",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span>
          {row.publishTime ? dayjs(row.publishTime).format("YYYY-MM-DD") : ""}
        </span>
      )
    },
    {
      label: "办结时间",
      prop: "completeTime",
      align: "center",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span>
          {row.completeTime ? dayjs(row.completeTime).format("YYYY-MM-DD") : ""}
        </span>
      )
    },
    {
      label: "实际完成时间",
      prop: "successTime",
      align: "center",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span>
          {row.successTime ? dayjs(row.successTime).format("YYYY-MM-DD") : ""}
        </span>
      )
    },
    {
      label: "任务状态",
      prop: "taskStatus",
      align: "center",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <DictTag options={TASK_STATUS} value={row.taskStatus} />
      )
    },
    {
      label: "创建人",
      prop: "createName",
      align: "center",
      minWidth: 80
    },
    {
      label: "操作",
      fixed: "right",
      align: "left",
      width: 300,
      slot: "operation"
    }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    taskData.value = new Task();
  }

  function handleDelete(row?: Task) {
    if (row?.id) {
      delTask(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm("是否确认删除该督查督办？", {
      type: "warning",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      title: "系统提示"
    })
      .then(function () {
        return delTask(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: Task) {
    visible.value = true;
    taskData.value = row;
  }

  /** 发布按钮操作 */
  function handlePublish(row: Task) {
    publishTask(row.id)
      .then(() => {
        message("发布成功", { type: "success" });
      })
      .finally(() => {
        getList();
      });
  }

  /** 查看按钮操作 */
  function handlePreview(row: Task) {
    taskDataPreview.value = new TaskUserExecute();
    taskDataPreview.value.dialogMode = "preview";
    taskDataPreview.value.taskId = row.id;
    previewVisible.value = true;
  }

  async function getList() {
    loading.value = true;
    handleDateRange();
    const { data, total } = await listTask(queryParams, pagination);
    taskList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleDateRange() {
    if (queryParams.publishTimeRange?.length == 2) {
      queryParams.publishTimeStart = queryParams.publishTimeRange[0];
      queryParams.publishTimeEnd = queryParams.publishTimeRange[1];
    } else {
      queryParams.publishTimeStart = "";
      queryParams.publishTimeEnd = "";
    }
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  function handleOpLog(row: Task) {
    taskData.value = row;
    opLogVisible.value = true;
  }

  // 调整补充意见时效
  function handleAdjustMajorFillExpDays(row: Task) {
    taskData.value = row;
    $prompt("调整补充意见时效（天）", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputType: "number",
      inputPlaceholder: "请输入调整的时效",
      inputValidator: value => {
        // 必须是大于0的整数
        if (value === "") {
          return "请输入调整的时效";
        }
        if (Number(value) <= 0) {
          return "调整的时效必须大于0";
        }
        if (!/^\d+$/.test(value)) {
          return "调整的时效必须是整数";
        }
        return true;
      },
      inputValue: row.majorFillExpDays?.toString()
    })
      .then(({ value, action }) => {
        if (action === "confirm") {
          adjustMajorFillExpDays(row.id, Number(value)).then(() => {
            message("调整成功", { type: "success" });
            getList();
          });
        }
      })
      .catch(() => {});
  }

  // 任务完成后强制退回至执行人
  function handleForceRevoke(row: Task) {
    $confirm("是否确认强制退回该督查督办？", "确认", {
      type: "warning"
    }).then(() => {
      forceRevokeTask(row.id)
        .then(() => {
          message("强制退回成功", { type: "success" });
          getList();
        })
        .catch(() => {});
    });
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visible,
    previewVisible,
    opLogVisible,
    loading,
    columns,
    pagination,
    queryParams,
    taskData,
    taskDataPreview,
    taskList,
    taskStatusDict: TASK_STATUS,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleCreate,
    handleDelete,
    handleUpdate,
    handlePublish,
    handlePreview,
    handleOpLog,
    handleQuery,
    resetQuery,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList,
    handleAdjustMajorFillExpDays,
    handleForceRevoke
  };
}
