<script setup lang="ts">
import { computed, ref } from "vue";
import { useTask } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { hasAuth } from "@/router/utils";
import TaskPublishEditDialog from "@/views/meeting/task/publish/components/dialog.vue";
import TaskUserExecutePreviewDialog from "@/views/meeting/taskUserExecute/previewDialog.vue";
import TaskOpLogDialog from "@/views/meeting/task/components/TaskOpLogDialog.vue";
import { Task } from "@/types/meeting/task";
import More from "@iconify-icons/ep/more";
import { useAncestorDetection } from "@/hooks/useAncestorState";

//** 督查督办主 */
defineOptions({
  name: "TaskPublish"
});

const queryRef = ref();

const {
  visible,
  previewVisible,
  opLogVisible,
  loading,
  columns,
  pagination,
  queryParams,
  taskData,
  taskDataPreview,
  taskList,
  taskStatusDict,
  tableMaxHeight,
  mainRef,
  tableRef,
  calculateTableMaxHeight,
  handleCreate,
  handleDelete,
  handleUpdate,
  handlePublish,
  handleQuery,
  handleOpLog,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList,
  handleAdjustMajorFillExpDays,
  handleForceRevoke
} = useTask();
defineExpose({ getList, calculateTableMaxHeight });

// 是否可以调整补充意见时效
// 有操作权限
// 审核通过 已经补充意见 可以修改时效
const canAdjustMajorFillExpDays = computed(() => {
  return (row: Task) => {
    return (
      ["40", "60"].includes(row.taskStatus) && hasAuth("meeting:task:option")
    );
  };
});
// 是否可以在审核通过后强制退回
// 有操作权限
// 审核通过 已经补充意见 可以强制退回
const canForceRevoke = computed(() => {
  return (row: Task) => {
    return (
      ["40", "60"].includes(row.taskStatus) && hasAuth("meeting:task:option")
    );
  };
});
// 是否显示更多按钮
const showMore = computed(() => {
  return (row: Task) => {
    return canAdjustMajorFillExpDays.value(row) || canForceRevoke.value(row);
  };
});
</script>

<template>
  <div class="main" ref="mainRef">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color search-form"
    >
      <el-form-item label="督办事项名称" prop="taskTitle">
        <el-input
          v-model="queryParams.taskTitle"
          placeholder="请输入督办事项名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker
          v-model="queryParams.publishTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
          @change="handleQuery"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select
          v-model="queryParams.taskStatus"
          class="!w-[180px]"
          placeholder="请选择状态"
          @change="handleQuery"
          clearable
        >
          <el-option
            v-for="item in taskStatusDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      title="督查督办列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="primary"
          class="sm:shrink-button"
          v-auth="['meeting:task:option']"
          :icon="useRenderIcon('ep:circle-plus')"
          @click="handleCreate()"
        >
          新增
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="center"
          :max-height="tableMaxHeight"
          ref="tableRef"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="taskList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-popconfirm title="是否确认发布?" @confirm="handlePublish(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['meeting:task:option']"
                  :icon="useRenderIcon('ep:check')"
                  v-if="row.taskStatus === '0'"
                >
                  发布
                </el-button>
              </template>
            </el-popconfirm>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:task:option']"
              v-if="row.taskStatus === '0'"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              修改
            </el-button>
            <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['meeting:task:option']"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleOpLog(row)"
              :icon="useRenderIcon('ep:view')"
            >
              日志
            </el-button>
            <!-- 更多 -->
            <el-dropdown v-if="showMore(row)" class="mt-2 ml-3">
              <el-button link type="primary" :size="size"
                >更多&nbsp;
                <IconifyIconOffline :icon="More" />
              </el-button>

              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="canAdjustMajorFillExpDays(row)"
                    @click="handleAdjustMajorFillExpDays(row)"
                  >
                    <el-button
                      link
                      class="reset-margin"
                      type="primary"
                      :size="size"
                    >
                      调整补充意见时效
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="canForceRevoke(row)"
                    @click="handleForceRevoke(row)"
                  >
                    <el-button
                      link
                      class="reset-margin"
                      type="primary"
                      :size="size"
                    >
                      强制退回
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <task-publish-edit-dialog
      v-model:visible="visible"
      :data="taskData"
      @confirm="getList"
    />

    <!--    <task-preview-dialog-->
    <!--      v-model:visible="previewVisible"-->
    <!--      :data="taskData"-->
    <!--      @confirm="getList"-->
    <!--    />-->

    <task-user-execute-preview-dialog
      v-model:visible="previewVisible"
      :data="taskDataPreview"
      @confirm="getList"
    />

    <task-op-log-dialog v-model:visible="opLogVisible" :taskId="taskData?.id" />
  </div>
</template>
