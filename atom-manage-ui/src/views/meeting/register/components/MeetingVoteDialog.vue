<script setup lang="ts">
import { computed, ref, watch, PropType } from "vue";
import { message } from "@/utils/message";

import {
  MeetingLinkVote,
  TopicVote,
  TopicVoteAndMeetingLink
} from "@/types/meeting/topicVote";

import { useGlobal } from "@pureadmin/utils";
import {
  addTopicVote,
  getVoteInfoByLinkId,
  getOtherVoteInfoByLinkId
} from "@/api/meeting/topicVote";

const emit = defineEmits(["confirm"]);
defineOptions({
  name: "MeetingEditDialog"
});
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type } = $useDict("meeting_type");
const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  meetingLink: {
    type: Object as PropType<MeetingLinkVote>,
    required: true,
    default: () => {
      return new MeetingLinkVote();
    }
  }
});

// 表单信息
const loading = ref(false);
const joinerList = ref<TopicVote[]>();
const readList = ref<TopicVote[]>();
const allData = ref<TopicVote[]>();
const voteLink = ref<TopicVoteAndMeetingLink>(new TopicVoteAndMeetingLink());
const link = ref<MeetingLinkVote>(new MeetingLinkVote());

// 当前议题在其他会议中的表决结果
const otherVoteLink = ref<TopicVoteAndMeetingLink[]>([]);
const title = computed(() => {
  return "议题表决";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange()
);

function handlePropsDataChange() {
  if (!visible.value) return;
  reset();
  handleUpdate();
}

/** 表单重置 */
function reset() {}

/** 修改按钮操作 */
async function handleUpdate() {
  const linkId = props.meetingLink.id;
  getVoteInfoByLinkId(linkId).then(res => {
    allData.value = res.data.votes;
    link.value = res.data.link;
    joinerList.value = allData.value.filter(item => item.userType === "2"); // 参会人员
    readList.value = allData.value.filter(item => item.userType === "1"); // 列席人员

    // planId为空表示特殊议题，它只会与一个会议关联，不可能在其他会议有表决结果，不需要查询
    if (link.value.planId) {
      // 查询其他会议表决结果
      getOtherVoteInfoByLinkId(linkId).then(res => {
        otherVoteLink.value = res.data;
      });
    } else {
      otherVoteLink.value = [];
    }
  });
}

/** 提交按钮 */
function submitForm() {
  if (allData.value && allData.value.length !== 0) {
    loading.value = true;
    voteLink.value.votes = allData.value;
    voteLink.value.link = link.value;
    addTopicVote(voteLink.value)
      .then(() => {
        message("操作成功", { type: "success" });
        visible.value = false;
        emit("confirm");
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

function cancel() {
  visible.value = false;
}

// 批量选择改变
function handleAgree(type: string, status: string) {
  if (type === "join") {
    joinerList.value.map(item => {
      item.status = status;
      return item;
    });
  } else {
    readList.value.map(item => {
      item.status = status;
      return item;
    });
  }
}
</script>

<template>
  <!-- 添加或修改会议信息对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="70%"
    :close-on-click-modal="false"
  >
    <div class="p-3 mb-4 text-xl font-bold bg-gray-50 rounded-lg">
      议题名称： {{ props.meetingLink.topicTitle }}
    </div>
    <div class="flex flex-row gap-4">
      <el-card
        shadow="never"
        class="flex-1 bg-gray-50 rounded-lg transition-all duration-200 hover:bg-gray-100"
        v-if="joinerList && joinerList.length !== 0"
      >
        <template #header>
          <div class="flex gap-2 items-center">
            <span class="text-lg font-bold text-gray-700">参会人员</span>
            <div class="flex gap-2 ml-2">
              <el-button type="success" plain @click="handleAgree('join', '1')"
                >同意</el-button
              >
              <el-button type="warning" plain @click="handleAgree('join', '2')"
                >原则同意</el-button
              >
              <el-button type="danger" plain @click="handleAgree('join', '3')"
                >不同意</el-button
              >
              <el-button type="info" plain @click="handleAgree('join', '4')"
                >保留意见</el-button
              >
            </div>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="join in joinerList"
            :key="join.id"
            class="flex items-center p-3 bg-white rounded-lg transition-colors duration-200 hover:bg-gray-50"
          >
            <span class="w-20 font-bold text-gray-600">{{
              join.userName
            }}</span>
            <div class="flex gap-4">
              <el-radio v-model="join.status" label="1">同意</el-radio>
              <el-radio v-model="join.status" label="2">原则同意</el-radio>
              <el-radio v-model="join.status" label="3">不同意</el-radio>
              <el-radio v-model="join.status" label="4">保留意见</el-radio>
            </div>
          </div>
        </div>
      </el-card>

      <el-card
        shadow="never"
        class="flex-1 bg-gray-50 rounded-lg transition-all duration-200 hover:bg-gray-100"
        v-if="readList && readList.length !== 0"
      >
        <template #header>
          <div class="flex items-center">
            <span class="my-0.5 text-lg font-bold text-gray-700">列席人员</span>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="read in readList"
            :key="read.id"
            class="flex items-center p-3 bg-white rounded-lg transition-colors duration-200 hover:bg-gray-50"
          >
            <span class="w-20 font-bold text-gray-600">{{
              read.userName
            }}</span>
            <div class="flex-1">
              <el-input
                v-model="read.remark"
                class="w-full"
                clearable
                placeholder="请输入意见"
              />
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div
      class="flex justify-center items-center p-4 mt-6 bg-gray-50 rounded-lg"
    >
      <span class="mr-4 text-lg font-bold text-gray-700">议题最终结论:</span>
      <div class="flex gap-4">
        <el-radio v-model="link.status" label="1">同意</el-radio>
        <el-radio v-model="link.status" label="2">原则同意</el-radio>
        <el-radio v-model="link.status" label="3">不同意</el-radio>
        <el-radio v-model="link.status" label="4">保留意见</el-radio>
      </div>
    </div>
    <!-- 其他会议表决结果 -->
    <div v-if="otherVoteLink && otherVoteLink.length > 0" class="mt-4 mb-4">
      <div class="p-3 mx-3 mb-2 text-lg font-bold bg-gray-50 rounded-lg">
        其他会议表决结果
      </div>
      <div class="flex flex-col gap-2 px-3">
        <div
          v-for="item in otherVoteLink"
          :key="item.link.id"
          class="flex gap-4 items-center p-3 bg-gray-50 rounded-lg transition-colors duration-200 hover:bg-gray-100"
        >
          <div
            class="flex items-center gap-2 min-w-[200px] max-w-[300px] overflow-hidden"
          >
            <span class="font-bold text-gray-600 shrink-0">会议：</span>
            <span class="truncate">{{ item.meetingTitle }}</span>
          </div>
          <div class="flex items-center gap-2 min-w-[150px] overflow-hidden">
            <span class="font-bold text-gray-600 shrink-0">会议类型：</span>
            <el-tag type="primary" class="truncate" size="small">{{
              meeting_type.find(it => it.value == item.meetingType)?.label
            }}</el-tag>
          </div>
          <div class="flex gap-2 items-center">
            <span class="font-bold text-gray-600 shrink-0">表决结果：</span>
            <el-tag
              size="small"
              :type="
                item.link.status === '1'
                  ? 'success'
                  : item.link.status === '2'
                  ? 'warning'
                  : item.link.status === '3'
                  ? 'danger'
                  : 'info'
              "
            >
              {{
                item.link.status === "1"
                  ? "同意"
                  : item.link.status === "2"
                  ? "原则同意"
                  : item.link.status === "3"
                  ? "不同意"
                  : "保留意见"
              }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
