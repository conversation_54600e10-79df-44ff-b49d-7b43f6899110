<script setup lang="ts">
import { ref } from "vue";
import { useMeeting } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import MeetingVoteDialog from "./components/MeetingVoteDialog.vue";
import MeetingPreviewDialog from "@/views/meeting/manager/components/MeetingPreviewDialog.vue";

//** 会议信息 */
defineOptions({
  name: "Meeting"
});

const queryRef = ref();

const {
  multiple,
  visibleEditDialog,
  visibleUploadDialog,
  visiblePreviewDialog,
  loading,
  columns,
  pagination,
  queryParams,
  meetingData,
  meetingList,
  handleUpdate,
  handleQuery,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList
} = useMeeting();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color search-form"
    >
      <el-form-item label="会议标题" prop="meetingTitle">
        <el-input
          v-model="queryParams.meetingTitle"
          placeholder="请输入会议标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会议主要内容" prop="meetingBrief">
        <el-input
          v-model="queryParams.meetingBrief"
          placeholder="请输入会议主要内容"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      title="会议信息列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="left"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="meetingList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              表决意见
            </el-button>
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <meeting-vote-dialog
      v-model:visible="visibleEditDialog"
      :data="meetingData"
      @confirm="getList"
    />
    <meeting-preview-dialog
      v-model:visible="visiblePreviewDialog"
      :data="meetingData"
    />
  </div>
</template>

<style lang="scss" scoped>
.buttons-container {
  display: flex;
  flex-wrap: wrap; /* 允许子项换行 */
  gap: 10px; /* 设置按钮之间的间隙 */
}
</style>
