import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { selectDictLabel } from "@/utils/atom";

import {listMeeting} from "@/api/meeting/meeting";
import { Meeting, MeetingQuery } from "@/types/meeting/meeting";
import DictTag from "@/components/DictTag";
import {useUserStoreHook} from "@/store/modules/user";

export function useMeeting() {
	const queryParams = reactive<MeetingQuery>(new MeetingQuery());
	const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();

	const { MEETING_STATUS, meeting_place, meeting_type }
		= $useDict("MEETING_STATUS", "meeting_place", "meeting_type");
	const loading = ref(true);
	const visibleEditDialog = ref(false);
	const visibleUploadDialog = ref(false);
	const visiblePreviewDialog = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);
	const meetingList = ref([]);
	const meetingData = ref<Meeting>();
  const userId = useUserStoreHook().userId;

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			align: "center",
			width: 70,
			formatter: () =>
				pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
		},
		{
			label: "会议标题",
			prop: "meetingTitle",
			align: "left",
			minWidth: 120,
			cellRenderer: ({ row }) => {
				return (
					<span
						class="cursor-pointer text-blue-700"
						onClick={() => handlePreview(row)}
					>
						{row.meetingTitle}
					</span>
				);
			}
		},
		{
			label: "会议类型",
			prop: "meetingType",
			align: "center",
			width: 168,
			minWidth: 60,
			cellRenderer: ({ row }) => (
				<DictTag options={meeting_type} value={row.meetingType} />
			)
		},
		{
			label: "会议地点",
			prop: "meetingLocation",
			align: "left",
			minWidth: 100,
			formatter: (row) =>
				selectDictLabel(meeting_place.value, row.meetingLocation)
		},
		{
			label: "会议时间",
			prop: "meetingDate",
			align: "center",
			width: 230,
			minWidth: 135,
			cellRenderer: ({ row }) => (
				<span>
					{dayjs(row.meetingDate).format("YYYY-MM-DD")}{" "}
					{toHourMinute(row.meetingTime[0])} -{toHourMinute(row.meetingTime[1])}
				</span>
			)
		},
		{
			label: "会议状态",
			prop: "status",
			align: "center",
			width: 120,
			minWidth: 40,
			cellRenderer: ({ row }) => {
				return <DictTag options={MEETING_STATUS} value={row.status} />;
			}
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 300,
			slot: "operation"
		}
	];

	// 切分时间
	function toHourMinute(time: string): string {
		const parts = time.split(":");
		return parts[0] + ":" + parts[1];
	}



	/** 修改按钮操作 */
	function handleUpdate(row: Meeting) {
    visibleEditDialog.value = true;
    meetingData.value = row;
	}

	async function getList() {
		loading.value = true;
    queryParams.registerId = userId;
		const { data, total } = await listMeeting(queryParams, pagination);
    console.log(userId)
		meetingList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.id);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}


	// ** 预览
	function handlePreview(row: Meeting) {
		meetingData.value = row;
		visiblePreviewDialog.value = true;
	}

	onMounted(() => {
		getList();
	});

	return {
		single,
		multiple,
		visibleEditDialog,
		visibleUploadDialog,
		visiblePreviewDialog,
		loading,
		columns,
		pagination,
		queryParams,
		meetingData,
		meetingList,
		handleUpdate,
		handleQuery,
		resetQuery,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange,
		getList
	};
}
