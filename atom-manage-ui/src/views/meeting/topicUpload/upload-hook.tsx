import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import {
	delTopic,
	listRelatedTopic,
	submitTopicUpload
} from "@/api/meeting/topic";
import { Topic, TopicQuery } from "@/types/meeting/topic";
import DictTag from "@/components/DictTag";

export function useTopic() {
	const queryParams = reactive<TopicQuery>(new TopicQuery());
	const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();

	const { TOPIC_AUDIT_STATUS } = $useDict("TOPIC_AUDIT_STATUS");
	const loading = ref(true);
	const visible = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);
	const topicList = ref([]);
	const topicData = ref<Topic>();

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			width: 70,
			formatter: () =>
				pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
		},
		{
			label: "议题标题",
			prop: "title",
			align: "center"
		},
		{
			label: "议题类型",
			prop: "planTypeName",
			align: "center"
		},

		{
			label: "汇报人",
			prop: "reporterUserName",
			align: "center",
			minWidth: 120
		},
		{
			label: "审核状态",
			prop: "status",
			align: "center",
			width: 150,
			cellRenderer: ({ row }) => {
				return <DictTag options={TOPIC_AUDIT_STATUS} value={row.status} />;
			}
		},
		{
			label: "创建时间",
			prop: "createTime",
			align: "center",
			cellRenderer: ({ row }) => (
				<span> {dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss")}</span>
			)
		}
		// {
		//   label: "操作",
		//   fixed: "right",
		// align: "left",
		//   width: 200,
		//   slot: "operation"
		// }
	];

	/** 新增按钮操作 */
	function handleCreate() {
		visible.value = true;
		topicData.value = new Topic();
	}

	function handleDelete(row?: Topic) {
		if (row?.id) {
			delTopic(row.id).then(() => {
				getList();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm(
			'是否确认删除议题编号为"' + ids.value + '"的数据项？',
			{
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				title: "系统提示"
			}
		)
			.then(function () {
				return delTopic(ids.value);
			})
			.then(() => {
				getList();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 修改按钮操作 */
	function handleUpdate(row: Topic) {
		visible.value = true;
		topicData.value = row;
	}

	function handleSubmit(row: Topic) {
		loading.value = true;
		submitTopicUpload(row.id, row.planTypeId)
			.then(resp => {
				if (resp.code == 200) {
					message("提交成功", { type: "success" });
				}
			})
			.finally(() => {
				loading.value = false;
			});
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await listRelatedTopic(queryParams, pagination);
		topicList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"meeting/topic/export",
			{
				...queryParams
			},
			`topic_${new Date().getTime()}.xlsx`
		);
	}

	onMounted(() => {
		getList();
	});

	return {
		single,
		multiple,
		visible,
		loading,
		columns,
		pagination,
		queryParams,
		topicData,
		topicList,
		handleCreate,
		handleDelete,
		handleUpdate,
		handleSubmit,
		handleQuery,
		resetQuery,
		handleExport,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange,
		getList
	};
}
