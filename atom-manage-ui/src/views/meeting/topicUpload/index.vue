<script setup lang="ts">
import { ref } from "vue";
import { useTopic } from "../topicUpload/upload-hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import TopicUploadDialog from "../topicUpload/components/TopicUploadDialog.vue";

//** 议题 */
defineOptions({
  name: "TopicUpload"
});

const queryRef = ref();

const {
  multiple,
  visible,
  loading,
  columns,
  pagination,
  queryParams,
  topicData,
  topicList,
  handleCreate,
  handleDelete,
  handleUpdate,
  handleSubmit,
  handleQuery,
  handleExport,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList
} = useTopic();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入议题标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="开始时间" prop="beginTime">
        <el-date-picker
          clearable
          v-model="queryParams.beginTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择开始时间"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          clearable
          v-model="queryParams.endTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择结束时间"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      title="议题列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="primary"
          class="sm:shrink-button"
          :icon="useRenderIcon('ep:circle-plus')"
          @click="handleCreate()"
        >
          上传
        </el-button>
        <!-- <el-button
          type="danger"
          class="sm:shrink-button"
          plain
          :icon="useRenderIcon('ep:delete')"
          v-auth="['meeting:topic:remove']"
          :disabled="multiple"
          @click="handleDelete()"
          >删除
        </el-button> -->
        <!-- <el-button
          type="warning"
          class="sm:shrink-button"
          plain
          :icon="useRenderIcon('ep:download')"
          v-auth="['meeting:topic:export']"
          @click="handleExport"
          >导出
        </el-button> -->
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="center"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="topicList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              v-if="row.status === '0'"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleSubmit(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              提交
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              修改
            </el-button>
            <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  v-if="row.status === '0'"
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <topic-upload-dialog
      v-model:visible="visible"
      :data="topicData"
      @confirm="getList"
    />
  </div>
</template>
