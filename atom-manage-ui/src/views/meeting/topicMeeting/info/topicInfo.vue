<script setup lang="ts">
import { useTopicInfo } from "@/views/meeting/topicMeeting/info/hook";
import VuePdfEmbed from "vue-pdf-embed";
import { useGlobal } from "@pureadmin/utils";
import dayjs from "dayjs";
import {
  List,
  CaretLeft,
  CaretRight,
  ArrowLeftBold
} from "@element-plus/icons-vue";
import TopicMeetingFlowDialog from "@/views/meeting/topicMeeting/info/components/dialog.vue";
import { reactive, ref, watch, computed, nextTick, onUnmounted } from "vue";
import TopicMeetingAuditDialog from "@/views/meeting/topicMeeting/audit/components/dialog.vue";
import {
  TopicMeetingAudit,
  TopicMeetingAuditQuery
} from "@/types/meeting/topicMeetingAudit";
import { listTopicMeetingAudit } from "@/api/meeting/topicMeeting";
import { PaginationProps } from "@pureadmin/table";
import PdfComment from "@/components/PdfComment/src/index.vue";
import "vue-pdf-embed/dist/styles/annotationLayer.css";
import "vue-pdf-embed/dist/styles/textLayer.css";
import { useUserStoreHook } from "@/store/modules/user";
import { uniqBy } from "lodash-es";
import { useSwipe } from "@vueuse/core";
import { ElMessageBox } from "element-plus";
//** 议题收集审核 */
defineOptions({
  name: "TopicInfo"
});

const emit = defineEmits(["go-back"]);
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type } = $useDict("meeting_type");
const {
  visible,
  loading,
  srcLoading,
  topicMeet,
  previewSrc,
  file,
  fileOptions,
  handleFileChange,
  handleFlowPreview,
  handleQuery,
  goBack
} = useTopicInfo(emit);

// PdfComment 组件引用
const pdfCommentRef = ref<InstanceType<typeof PdfComment>>();

// 检测PDF是否有未保存的修改（通过PdfComment组件）
function checkPdfModified(): boolean {
  if (!props.commentVisible || !pdfCommentRef.value) {
    return false; // 如果不是批注模式或组件未加载，默认为未修改
  }
  try {
    // 调用PdfComment组件暴露的检测方法
    return pdfCommentRef.value.checkPdfModified();
  } catch (error) {
    console.warn("无法检测PDF修改状态:", error);
    return false; // 无法检测时默认为未修改
  }
}

// 处理文件切换前的确认
async function handleFileChangeConfirm(item: any): Promise<boolean> {
  // 如果选择的是同一个文件，直接允许切换（不需要检测）
  if (item?.ossId === file.value?.ossId) {
    return true;
  }

  if (!checkPdfModified()) {
    return true; // 没有修改，直接允许切换
  }

  try {
    await ElMessageBox.confirm(
      "当前PDF文件有未保存的修改，切换文件将丢失这些修改。是否继续？",
      "系统提示",
      {
        confirmButtonText: "继续切换",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    return true; // 用户确认切换
  } catch {
    return false; // 用户取消切换
  }
}

// 切换前检查是否编辑文件
async function checkBeforeHandleFileChange(item: any) {
  // 如果是来自PdfComment组件内部的切换，直接执行，不需要再次确认
  const confirmed = await handleFileChangeConfirm(item);
  if (confirmed) {
    handleFileChange(item);
  }
}

const auditData = ref<TopicMeetingAudit>(new TopicMeetingAudit());
const auditVisible = ref<boolean>(false);
const auditButtonVisible = ref<boolean>(false);
const font = reactive({
  fontSize: 20,
  color: "rgba(0,0,0,0.25)"
});
const gap = ref<[number, number]>([120, 120]);
// const orgName = useUserStoreHook().orgName;
const username = useUserStoreHook().username;
function handleAudit() {
  auditData.value.topicId = props.id;
  auditVisible.value = true;
}

const queryParams = reactive<TopicMeetingAuditQuery>(
  new TopicMeetingAuditQuery()
);
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 30,
  currentPage: 1
});
async function handleAfterAudit() {
  const { data } = await listTopicMeetingAudit(queryParams, pagination);
  auditButtonVisible.value = data.map(item => item.topicId).includes(props.id);
  handleQuery(props.id);
}

const meetingTypes = computed(() => {
  if (meeting_type == null) return [];
  return uniqBy(topicMeet.value.planList, "type").map(item => {
    return item.type;
  });
});

const props = defineProps({
  id: {
    type: Number,
    required: true
  },
  commentVisible: {
    type: Boolean,
    default: false,
    required: false
  },
  source: {
    type: String,
    default: "",
    required: false
  },
  showGoBack: {
    type: Boolean,
    default: true,
    required: false
  },
  height: {
    type: Number,
    default: 0,
    required: false
  }
});

const leftPanelCollapsed = ref(false);
function toggleLeftPanel() {
  leftPanelCollapsed.value = !leftPanelCollapsed.value;
  previewSrc.value = "";
  nextTick(() => {
    previewSrc.value = file.value.url;
  });
}

watch(
  () => props.id,
  newId => {
    leftPanelCollapsed.value = false;
    handleQuery(newId);
    auditButtonVisible.value = props.source === "audit";
  },
  { immediate: true }
);

// 获取窗口高度
// 添加触摸手势支持
const swipeTarget = ref<HTMLElement>();
const { isSwiping, direction } = useSwipe(swipeTarget, {
  threshold: 50,
  onSwipe(e: TouchEvent) {
    if (direction.value === "left" && !leftPanelCollapsed.value) {
      // 向左滑动，收起左侧面板
      toggleLeftPanel();
    } else if (direction.value === "right" && leftPanelCollapsed.value) {
      // 向右滑动，展开左侧面板
      toggleLeftPanel();
    }
  }
});

onUnmounted(() => {});
</script>

<template>
  <div
    v-loading="loading"
    element-loading-background="rgba(255, 255, 255, 0.1)"
    class="flex flex-col"
    :style="{ height: height ? height + 'px' : '100%' }"
    ref="swipeTarget"
  >
    <!-- 主要内容区域 - 左右布局 -->
    <div class="flex flex-1 gap-0 min-h-0 h-full">
      <!-- 左侧：议题基本信息 -->
      <div
        :class="[
          'flex-none flex flex-col rounded-lg shadow-md overflow-y-auto transition-all duration-300 glass-filter',
          leftPanelCollapsed
            ? 'w-0 min-w-0 opacity-0 p-0 m-0'
            : 'w-1/3 min-w-96'
        ]"
      >
        <div class="p-6 flex-1" :class="{ hidden: leftPanelCollapsed }">
          <!-- 返回按钮 -->
          <div class="flex justify-end mb-6" v-if="showGoBack">
            <el-button
              :icon="ArrowLeftBold"
              size="large"
              @click="goBack"
              type="text"
              >返回</el-button
            >
          </div>

          <!-- 议题基本信息 -->
          <div class="space-y-4 mb-8">
            <div class="flex flex-col space-y-1">
              <span class="labelTitle text-xl opacity-80">议题名称：</span>
              <span class="val text-xl font-medium">{{
                topicMeet.topicTitle
              }}</span>
            </div>
            <div class="flex flex-col space-y-1">
              <span class="labelTitle text-xl opacity-80">上传单位：</span>
              <span class="val">{{ topicMeet.creatorDept }}</span>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div
                class="flex flex-col space-y-1"
                v-if="topicMeet.reportUserName"
              >
                <span class="labelTitle text-xl opacity-80">汇报人：</span>
                <span class="val">{{ topicMeet.reportUserName }}</span>
              </div>
              <div class="flex flex-col space-y-1">
                <span class="labelTitle text-xl opacity-80">上传时间：</span>
                <span class="val time italic">{{
                  dayjs(topicMeet.createTime).format("YYYY-MM-DD")
                }}</span>
              </div>
            </div>
            <div class="flex flex-col space-y-1" v-if="topicMeet.id">
              <span class="labelTitle text-xl opacity-80">会议类型：</span>
              <div class="val" v-if="meetingTypes.length > 0">
                <span
                  v-for="item in meetingTypes"
                  :key="item"
                  class="inline-block mr-2 px-2 py-1 bg-primary/10 text-primary rounded text-xl"
                  >{{ meeting_type.find(it => it.value == item)?.label }}</span
                >
              </div>
            </div>
          </div>

          <!-- 议题文件信息 -->
          <div class="space-y-6 mb-8">
            <div class="space-y-3">
              <span class="labelTitle text-xl opacity-80 font-medium block"
                >议题正文：</span
              >
              <div class="ml-4 space-y-2">
                <template v-for="item in topicMeet.mainFile" :key="item.ossId">
                  <div
                    @click="checkBeforeHandleFileChange(item)"
                    class="hover-container cursor-pointer text-xl text-primary hover:text-primary/80 hover:underline transition-all duration-200 p-2 rounded hover:bg-primary/5"
                  >
                    {{ item.name }}
                  </div>
                </template>
              </div>
            </div>
            <div class="space-y-3">
              <span class="labelTitle text-xl opacity-80 font-medium block"
                >议题附件：</span
              >
              <div class="ml-4 space-y-2">
                <template
                  v-for="item in topicMeet.topicFileList"
                  :key="item.ossId"
                >
                  <div
                    @click="checkBeforeHandleFileChange(item)"
                    class="hover-container cursor-pointer text-xl text-primary hover:text-primary/80 hover:underline transition-all duration-200 p-2 rounded hover:bg-primary/5"
                  >
                    {{ item.name }}
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- 审核操作区域 -->
          <div
            class="mt-auto pt-6 border-t border-gray-200 flex items-center gap-4"
            v-if="topicMeet.id"
          >
            <el-button
              v-if="props.source === 'audit' && auditButtonVisible"
              type="primary"
              v-auth="['meeting:topicMeetingAudit:option']"
              @click="handleAudit"
              >审核</el-button
            >
            <div
              class="flex items-center text-xl gap-2 text-primary hover:text-primary/80 cursor-pointer p-2 rounded hover:bg-primary/5 transition-all duration-200"
              @click="handleFlowPreview"
            >
              <el-icon class="text-xl"><List /></el-icon>
              <span>审核流程</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 切换按钮 - 垂直文字布局 -->
      <div
        class="flex flex-col items-center justify-center z-10 transition-all duration-300 toggle-area-vertical h-full rounded-lg shadow-md"
        @click="toggleLeftPanel()"
        :title="leftPanelCollapsed ? '展开议题信息' : '收起议题信息'"
      >
        <!-- 箭头图标在文字前面 -->
        <div class="toggle-content flex flex-col items-center justify-center">
          <el-icon size="18" class="text-black mb-1">
            <component :is="leftPanelCollapsed ? CaretRight : CaretLeft" />
          </el-icon>

          <!-- 垂直文字 -->
          <span class="vertical-text">
            {{ leftPanelCollapsed ? "展开议题信息" : "收起议题信息" }}
          </span>
        </div>
      </div>

      <!-- 右侧：PDF预览 -->
      <div
        class="flex-1 bg-transparent rounded-lg shadow-md overflow-hidden flex flex-col"
        v-loading="srcLoading"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <div
          ref="pdfContainer"
          class="flex-1 flex flex-col min-h-0"
          :class="{ 'overflow-y-auto': !commentVisible }"
          v-if="file !== null || previewSrc !== null"
        >
          <PdfComment
            v-if="commentVisible"
            ref="pdfCommentRef"
            :url="file.url"
            :file-id="file.ossId"
            :link-id="id"
            :original-name="file.name"
            :file-list="fileOptions"
            class="flex-1"
            :enable-user-watermark="true"
            @file-change="handleFileChange"
          />
          <el-watermark
            v-else
            :font="font"
            :gap="gap"
            :rotate="-35"
            :content="[username]"
            class="flex-1"
          >
            <VuePdfEmbed
              class="flex-1 shadow-sm"
              :source="previewSrc"
              :textLayer="true"
            />
          </el-watermark>
        </div>
        <div v-else class="flex-1 flex items-center justify-center">
          <el-empty description="暂无附件" />
        </div>
      </div>
    </div>

    <!-- 对话框 -->
    <topic-meeting-flow-dialog v-model:visible="visible" :data="id" />
    <topic-meeting-audit-dialog
      v-model:visible="auditVisible"
      :data="auditData"
      @confirm="handleAfterAudit"
    />
  </div>
</template>
<style lang="scss" scoped>
/* 保持与项目风格一致的样式 */
.labelTitle {
  opacity: 0.8;
  vertical-align: top;
}

.val {
  word-wrap: break-word;
  @apply text-xl;

  &.time {
    font-style: italic;
  }
}

.hover-container {
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  color: #409eff;
  cursor: pointer;
}

/* 添加切换按钮样式 - 垂直布局 */
.toggle-area-vertical {
  width: 40px;
  position: relative;
  z-index: 10;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #f5f5f5;
  border: 1px solid #f5f5f5;

  &:hover {
    background-color: #e6e6e6;
    border-color: #e6e6e6;
  }

  &:active {
    background-color: #d9d9d9;
    border-color: #d9d9d9;
  }
}

.toggle-content {
  height: 100%;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  font-weight: 500;
  color: #333333;
  letter-spacing: 1px;
  transition: all 0.2s;
  @apply text-xl;

  .toggle-area-vertical:hover & {
    color: #000000;
    font-weight: 600;
  }
}
</style>
