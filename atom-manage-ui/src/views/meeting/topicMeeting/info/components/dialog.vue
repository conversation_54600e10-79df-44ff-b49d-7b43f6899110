<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { FormInstance } from "element-plus";
import { getFlowRecordByTopicId } from "@/api/meeting/topicMeeting";
import { useGlobal } from "@pureadmin/utils";
import { TopicMeetingFlowRecord } from "@/types/meeting/topicMeetingFlow";
import dayjs from "dayjs";
import { uniqBy } from "lodash-es";
const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TopicMeetingFlowDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: Number,
    default: () => {
      return 0;
    }
  }
});

const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, TOPIC_MEET_AUDIT_STATUS, meeting_matter_code } = $useDict(
  "meeting_type",
  "TOPIC_MEET_AUDIT_STATUS",
  "meeting_matter_code"
);

const meetingTypes = computed(() => {
  if (meeting_type == null) return [];
  return uniqBy(form.value.planList, "type").map(item => {
    return item.type;
  });
});

// 表单信息
const form = ref<TopicMeetingFlowRecord>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单校验
const rules = ref({
  status: [{ required: true, message: "审核意见不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return "审核议题";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

async function handlePropsDataChange(topicId: number) {
  if (!visible.value) return;
  reset();
  if (topicId) {
    getFlowRecord(topicId);
  }
}

function getFlowRecord(topicId: number) {
  getFlowRecordByTopicId(topicId).then(resp => {
    form.value = resp.data;
  });
}

/** 表单重置 */
function reset() {
  form.value = new TopicMeetingFlowRecord();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改议题收集对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="700px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form
      class="shyt"
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="150px"
    >
      <div>
        <el-form-item label="议题名称" prop="topicTitle">
          <span>{{ form.topicTitle }}</span>
        </el-form-item>
        <el-form-item label="预召开会议" prop="planMeetName">
          <div v-for="item in form.planList" :key="item.id">
            <el-tag type="primary" class="mr-2">{{ item.title }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="会议类型" prop="meetType">
          <div v-if="meetingTypes?.length > 0">
            <el-tag
              v-for="item in meetingTypes"
              :key="item"
              type="primary"
              class="mr-2"
              >{{ meeting_type.find(it => it.value == item)?.label }}</el-tag
            >
          </div>
        </el-form-item>

        <el-form-item label="事项编码" prop="noticeCode">
          <span>{{
            meeting_matter_code.find(it => it.value == form.noticeCode)?.label
          }}</span>
        </el-form-item>
        <el-form-item label="补充说明" prop="remark">
          <p>{{ form.remark }}</p>
        </el-form-item>
        <el-form-item label="部门领导" prop="departUserName">
          <p>{{ form.departUserName }}</p>
        </el-form-item>
        <el-form-item label="分管领导" prop="leaderUserName">
          <p>{{ form.leaderUserName }}</p>
        </el-form-item>
      </div>
      <el-divider />
      <div>
        <p class="pl-4 mb-4 titleTips2">审批流程</p>
        <div style="height: 300px; overflow-y: auto">
          <el-timeline class="pl-8">
            <el-timeline-item
              v-for="(item, index) in form.flowList"
              :key="index"
              :color="'#4480f8'"
            >
              <p>
                审核时间：{{ dayjs(item.operateTime).format("YYYY-MM-DD") }}
              </p>
              <p>
                审核状态：{{
                  TOPIC_MEET_AUDIT_STATUS.find(it => it.value == item.status)
                    ?.label
                }}
              </p>
              <p>审核人：{{ item.userName }}</p>
              <p>审核意见：{{ item.remark }}</p>
              <p>
                签名：
                <br />
                <el-tag v-if="item.sign === '暂无签名'">
                  {{ item.sign }}
                </el-tag>
                <span v-else>
                  <el-image :src="item.sign" />
                </span>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-form>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.el-image__inner) {
  width: 200px;
}
</style>
