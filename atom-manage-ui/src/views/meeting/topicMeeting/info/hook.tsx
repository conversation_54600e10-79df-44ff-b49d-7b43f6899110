import { ref, computed } from "vue";
import { TopicMeeting } from "@/types/meeting/topicMeeting";
import { getTopicMeeting } from "@/api/meeting/topicMeeting";
import { useTimeoutFn } from "@vueuse/core";
import { FileUploadResult } from "@/types/system/sys-oss";

export function useTopicInfo(emit: any) {
  const loading = ref(true);
  const srcLoading = ref(true);
  const visible = ref(false);
  const topicMeet = ref<TopicMeeting>(new TopicMeeting());

  const file = ref<any>(new FileUploadResult());
  const previewSrc = computed(() => {
    return file.value?.url || null;
  });
  const fileOptions = ref<any[]>([]);

  function handleQuery(id: number) {
    // id 可能是 议题收集的议题 topic_meeting.id 也可能是 会议资料上传的特殊议题 topic_meeting_link.id
    if (id) {
      loading.value = true;
      srcLoading.value = true;
      handleView(id);
    }
  }

  function goBack() {
    emit("go-back");
  }
  async function handleView(id: number) {
    const { data } = await getTopicMeeting(id);
    topicMeet.value = data;
    if (
      data.mainFile === null ||
      data.mainFile?.length === 0 ||
      typeof data.mainFile === "undefined"
    ) {
      loading.value = false;
      srcLoading.value = false;
      file.value = null;
      fileOptions.value = [];
      return;
    }
    file.value = data.mainFile[0];
    fileOptions.value = data.mainFile;
    if (data.topicFileList) {
      fileOptions.value = fileOptions.value.concat(data.topicFileList);
    }
    loading.value = false;
    srcLoading.value = false;
  }

  function handleFileChange(item: any) {
    srcLoading.value = true;
    file.value = item;
    useTimeoutFn(() => {
      srcLoading.value = false;
    }, 200);
  }

  function handleFlowPreview() {
    visible.value = true;
  }

  return {
    visible,
    loading,
    srcLoading,
    topicMeet,
    previewSrc,
    file,
    fileOptions,
    handleFileChange,
    handleFlowPreview,
    handleQuery,
    goBack
  };
}
