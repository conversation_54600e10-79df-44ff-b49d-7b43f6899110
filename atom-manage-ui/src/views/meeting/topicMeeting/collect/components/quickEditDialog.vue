<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import { TopicMeeting } from "@/types/meeting/topicMeeting";
import {
  getTopicMeeting,
  reSubmitTopicMeeting
} from "@/api/meeting/topicMeeting";
import { useGlobal } from "@pureadmin/utils";
import UserSelectTree from "@/components/UserSelectTree/index.vue";
import FileUpload from "@/components/FileUpload";
import { listAllMeetingPlan } from "@/api/meeting/topicMeetingPlan";
import { TopicMeetingPlan } from "@/types/meeting/topicMeetingPlan";
import { unionBy, uniqBy } from "lodash-es";
const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TopicQuickEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, meeting_matter_code } = $useDict(
  "meeting_type",
  "meeting_matter_code"
);

const props = defineProps({
  data: {
    type: TopicMeeting,
    default: () => {
      return new TopicMeeting();
    }
  }
});

// 表单信息
const form = ref<TopicMeeting>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);
// * 预召开会议列表（未过期的）
const avaliablePlanOptions = ref<TopicMeetingPlan[]>();
// 包含已选中的预召开会议
const planOptions = computed(() => {
  return unionBy(avaliablePlanOptions.value, form.value.planList, "id");
});
const meetingTypes = computed(() => {
  if (meeting_type == null) return [];
  return uniqBy(form.value.planList, "type").map(item => {
    return item.type;
  });
});

// 表单校验
const rules = ref({
  topicTitle: [
    { required: true, message: "议题名称不能为空", trigger: "blur" }
  ],
  planIds: [{ required: true, message: "预召开会议不能为空", trigger: "blur" }],
  departUserId: [
    { required: true, message: "部门领导审批人不能为空", trigger: "blur" }
  ],
  leaderUserId: [
    { required: true, message: "分管领导审批人不能为空", trigger: "blur" }
  ]
});

const title = computed(() => {
  return form.value?.id ? "修改议题" : "添加议题";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

async function handlePropsDataChange(data: TopicMeeting) {
  if (!visible.value) return;
  reset();
  // 获取预召开会议列表（未过期的）
  await listMeetingPlan();
  if (data.id) {
    await handleUpdate(data);
  }
}

async function listMeetingPlan() {
  listAllMeetingPlan().then(resp => {
    avaliablePlanOptions.value = resp.data;
  });
}

/** 表单重置 */
function reset() {
  form.value = new TopicMeeting();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: TopicMeeting) {
  loading.value = true;
  getTopicMeeting(row.id)
    .then(async response => {
      form.value = response.data;
      form.value.planIds = form.value.planList?.map(item => item.id) ?? [];
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重新提交按钮 */
function reSubmitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      reSubmitTopicMeeting(form.value)
        .then(() => {
          message("提交成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function handleMainFile() {
  if (form.value.mainFile?.length == 0) return;
  if (form.value.mainFile?.length > 0) {
    const fileName = form.value.mainFile[0].name;
    const [, extension] = splitFileName(fileName);
    form.value.mainFile[0].name = form.value.topicTitle + extension;
  }
}

function splitFileName(fullName) {
  const dotIndex = fullName.lastIndexOf(".");
  if (dotIndex === -1) return [fullName, ""]; // 没有扩展名
  const name = fullName.substring(0, dotIndex);
  const extension = fullName.substring(dotIndex); // 包括点
  return [name, extension];
}

function cancel() {
  visible.value = false;
}

function handleFile() {
  const title = form.value.topicTitle;
  if (
    title != "" &&
    typeof title != "undefined" &&
    form.value.mainFile?.length > 0
  ) {
    const [, extension] = splitFileName(form.value.mainFile[0].name);
    form.value.mainFile[0].name = form.value.topicTitle + extension;
  }
}

function handlePlanChange(value: number[]) {
  form.value.planList = value.map(id => {
    return planOptions.value.find(item => item.id === id);
  });
}
</script>

<template>
  <!-- 添加或修改议题收集对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="700px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
    v-loading="loading"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="议题名称" prop="topicTitle">
        <el-input
          v-model="form.topicTitle"
          placeholder="请输入议题名称"
          @input="handleMainFile"
        />
      </el-form-item>
      <el-form-item label="预召开会议" prop="planIds">
        <el-select
          v-model="form.planIds"
          placeholder="请选择预召开会议（可多选）"
          multiple
          clearable
          @change="handlePlanChange"
        >
          <el-option
            v-for="item of planOptions"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          >
            <div class="flex justify-between items-center">
              <span>{{ item.title }}</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="截止时间"
                placement="right-start"
              >
                <el-tag class="mr-3" :type="item.delay ? 'danger' : 'primary'">
                  {{ item.delay ? item.lastTime + " - 已逾期" : item.lastTime }}
                </el-tag>
              </el-tooltip>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会议类型">
        <div v-if="meetingTypes?.length > 0">
          <el-tag
            v-for="item in meetingTypes"
            :key="item"
            type="primary"
            class="mr-2"
            >{{ meeting_type.find(it => it.value == item)?.label }}</el-tag
          >
        </div>
        <div v-else>
          <span class="text-gray-500">请选择预召开会议</span>
        </div>
      </el-form-item>
      <el-form-item label="汇报人" prop="reportUserId">
        <user-select-tree
          :key="form.reportUserId"
          :multiple="false"
          import-type="user"
          v-model:model-value="form.reportUserId"
        />
      </el-form-item>
      <el-form-item label="部门领导审批" prop="departUserId">
        <user-select-tree
          :key="form.departUserId"
          :multiple="false"
          import-type="user"
          placeholder="部门领导"
          v-model:model-value="form.departUserId"
        />
      </el-form-item>
      <el-form-item label="分管领导审批" prop="leaderUserId">
        <user-select-tree
          :key="form.leaderUserId"
          :multiple="false"
          import-type="user"
          placeholder="分管领导"
          v-model:model-value="form.leaderUserId"
        />
      </el-form-item>
      <el-form-item label="事项编码" prop="noticeCode">
        <el-select
          v-model="form.noticeCode"
          placeholder="请选择事项编码"
          clearable
          filterable
        >
          <el-option
            v-for="item of meeting_matter_code"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <el-row>
              <el-col :span="12">
                <span style="float: left">{{ item.label }}</span>
              </el-col>
              <el-col :span="12" style="padding-top: 5px">
                <el-tag style="float: right">
                  {{ item.value }}
                </el-tag>
              </el-col>
            </el-row>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="议题附件" prop="mainFile">
        <FileUpload
          v-model="form.mainFile"
          desc="上传议题正文"
          :limit="1"
          @change="handleFile"
          :fileType="['pdf']"
        />
      </el-form-item>
      <el-form-item prop="topicFileList">
        <FileUpload
          v-model="form.topicFileList"
          desc="上传议题附件"
          :fileType="['pdf']"
        />
      </el-form-item>
      <el-form-item label="补充说明" prop="remark">
        <el-input
          v-model="form.remark"
          placeholder="请输入补充说明"
          type="textarea"
          :rows="3"
          show-word-limit
          maxlength="400"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="reSubmitForm"
          >重新提交</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
