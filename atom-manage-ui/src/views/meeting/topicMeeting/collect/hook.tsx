import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref, computed } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { TopicMeeting, TopicMeetingQuery } from "@/types/meeting/topicMeeting";
import {
  delTopicMeeting,
  listMyTopicMeeting,
  submitTopicMeeting
} from "@/api/meeting/topicMeeting";
import DictTag from "@/components/DictTag";
import TopicInfo from "@/views/meeting/topicMeeting/info/topicInfo.vue";
import { uniqBy } from "lodash-es";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";
import { useAncestorDetection } from "@/hooks/useAncestorState";
export function useTopicMeeting() {
  const queryParams = reactive<TopicMeetingQuery>(new TopicMeetingQuery());
  const { $download } = useGlobal<GlobalPropertiesApi>();
  const { $useDict } = useGlobal<GlobalPropertiesApi>();
  const { meeting_type, TOPIC_MEET_AUDIT_STATUS } = $useDict(
    "meeting_type",
    "TOPIC_MEET_AUDIT_STATUS"
  );

  const meetingTypes = computed(() => {
    return (row: TopicMeeting) => {
      if (meeting_type == null) return [];
      return uniqBy(row.planList, "type").map(item => {
        return item.type;
      });
    };
  });
  const loading = ref(true);
  const visible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const topicMeetingList = ref([]);
  const topicMeetingData = ref<TopicMeeting>();
  const topicPreviewVisible = ref(false);

  const topicId = ref();
  const uid = ref();
  const currentComponent = ref(null);
  const isComponentVisible = ref(false);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      align: "center",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "议题标题",
      prop: "topicTitle",
      align: "left",
      minWidth: 150,
      cellRenderer: ({ row }) => {
        return (
          <span
            class="text-blue-700 cursor-pointer"
            onClick={() => handleView(row)}
          >
            {row.topicTitle}
          </span>
        );
      }
    },
    {
      label: "会议类型",
      align: "center",
      minWidth: 150,
      cellRenderer: ({ row }) => {
        return (
          <span>
            {meetingTypes.value(row).map(item => {
              return (
                <DictTag
                  options={meeting_type}
                  value={item}
                  class="inline-block mr-1"
                />
              );
            })}
          </span>
        );
      }
    },
    {
      label: "议题上传时间",
      prop: "createTime",
      align: "center",
      minWidth: 100,
      formatter: ({ createTime }) =>
        createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
    },
    {
      label: "议题状态",
      prop: "status",
      align: "center",
      width: 200,
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <DictTag options={TOPIC_MEET_AUDIT_STATUS} value={row.status} />
      )
    },
    // {
    //   label: "上传单位",
    //   prop: "creatorDept",
    //   align: "left",
    //   minWidth: 180
    // },
    {
      label: "汇报人",
      prop: "reportUserName",
      align: "left"
    },
    {
      label: "操作",
      align: "left",
      slot: "operation",
      width: 350
    }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    topicMeetingData.value = new TopicMeeting();
  }

  function handleDelete(row?: TopicMeeting) {
    if (row?.id) {
      delTopicMeeting(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除议题收集编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delTopicMeeting(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: TopicMeeting) {
    visible.value = true;
    topicMeetingData.value = row;
  }

  function handleSubmit(row: TopicMeeting) {
    if (row?.id) {
      submitTopicMeeting(row.id).then(resp => {
        if (resp.code == 200) {
          getList();
          message("提交成功", { type: "success" });
        }
      });
    }
  }

  function handleView(row: TopicMeeting) {
    isComponentVisible.value = true;
    currentComponent.value = TopicInfo;
    topicId.value = row.id;
  }

  function handleGoBack() {
    isComponentVisible.value = false;
    currentComponent.value = null;
  }

  async function getList() {
    loading.value = true;
    const { data, total } = await listMyTopicMeeting(queryParams, pagination);
    topicMeetingList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "business/topicMeeting/export",
      {
        ...queryParams
      },
      `topicMeeting_${new Date().getTime()}.xlsx`
    );
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    columns,
    pagination,
    queryParams,
    topicPreviewVisible,
    topicMeetingData,
    topicMeetingList,
    topicId,
    isComponentVisible,
    currentComponent,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleGoBack,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleSubmit,
    handleQuery,
    resetQuery,
    handleExport,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    uid,
    getList,
    handleView
  };
}
