import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref, computed } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { TopicMeeting } from "@/types/meeting/topicMeeting";
import {
  delTopicMeeting,
  listTopicMeetingAudit,
  listTopicMeetingAudited,
  submitTopicMeeting
} from "@/api/meeting/topicMeeting";
import DictTag from "@/components/DictTag";
import {
  TopicMeetingAudit,
  TopicMeetingAuditQuery
} from "@/types/meeting/topicMeetingAudit";
import TopicInfo from "@/views/meeting/topicMeeting/info/topicInfo.vue";
import { BaseQuery } from "@/types";
import { uniqBy } from "lodash-es";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";
import { useAncestorDetection } from "@/hooks/useAncestorState";
export function useAuditTopicMeeting() {
  const queryParams = reactive<TopicMeetingAuditQuery>(
    new TopicMeetingAuditQuery()
  );
  const { $download } = useGlobal<GlobalPropertiesApi>();
  const { $useDict } = useGlobal<GlobalPropertiesApi>();
  const { meeting_type, TOPIC_MEET_AUDIT_STATUS } = $useDict(
    "meeting_type",
    "TOPIC_MEET_AUDIT_STATUS"
  );

  const meetingTypes = computed(() => {
    return (row: TopicMeetingAudit) => {
      if (meeting_type == null) return [];
      return uniqBy(row.planList, "type").map(item => {
        return item.type;
      });
    };
  });

  const loading = ref(true);
  const visible = ref(false);
  const activeName = ref<"audit" | "audited" | "">("audit");

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const topicMeetingList = ref([]);
  const topicMeetingData = ref<TopicMeetingAudit>();
  const topicMeeting = ref<TopicMeeting>(new TopicMeeting());

  const topicId = ref();
  const currentComponent = ref(null);
  const isComponentVisible = ref(false);
  const topicPreviewVisible = ref(false);
  const uid = ref();

  const pagination = reactive<PaginationProps & BaseQuery>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    orderByColumn: "ttm.updateTime",
    isAsc: "desc"
  });

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "议题标题",
      prop: "topicTitle",
      align: "left",
      minWidth: 200,
      cellRenderer: ({ row }) => {
        return (
          <span
            class="text-blue-700 cursor-pointer"
            onClick={() => handleView(row)}
          >
            {row.topicTitle}
          </span>
        );
      }
    },
    {
      label: "会议类型",
      prop: "meetType",
      align: "center",
      width: 168,
      minWidth: 50,
      cellRenderer: ({ row }) => (
        <div>
          {meetingTypes.value(row).map(item => {
            return <DictTag options={meeting_type} value={item} />;
          })}
        </div>
      )
    },
    {
      label: "议题上传时间",
      prop: "createTime",
      align: "center",
      width: 230,
      minWidth: 60,
      formatter: ({ createTime }) =>
        createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
    },
    {
      label: "议题状态",
      prop: "status",
      align: "center",
      width: 200,
      minWidth: 50,
      cellRenderer: ({ row }) => (
        <DictTag options={TOPIC_MEET_AUDIT_STATUS} value={row.status} />
      )
    },
    {
      label: "上传单位",
      prop: "creatorDept",
      align: "center",
      minWidth: 180
    },
    {
      label: "汇报人",
      prop: "reportUserName",
      align: "center",
      minWidth: 120
    },
    {
      label: "操作",
      align: "left",
      slot: "operation",
      width: 300
    }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    topicMeetingData.value = new TopicMeeting();
  }

  function handleDelete(row?: TopicMeeting) {
    if (row?.id) {
      delTopicMeeting(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除议题收集编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delTopicMeeting(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: TopicMeeting) {
    visible.value = true;
    topicMeetingData.value = row;
  }

  function handleSubmit(row: TopicMeeting) {
    if (row?.id) {
      submitTopicMeeting(row.id).then(resp => {
        if (resp.code == 200) {
          getList();
          message("提交成功", { type: "success" });
        }
      });
    }
  }

  function handleView(row: TopicMeetingAudit) {
    isComponentVisible.value = true;
    currentComponent.value = TopicInfo;
    topicId.value = row.topicId;
  }

  function handleGoBack() {
    isComponentVisible.value = false;
    currentComponent.value = null;
    getList();
  }

  async function tabClick(param: typeof activeName.value) {
    activeName.value = param;
    getList();
  }

  async function getList() {
    loading.value = true;
    const value = activeName.value;
    topicMeetingList.value = [];
    if (value === "" || value === "audit") {
      const { data, total } = await listTopicMeetingAudit(
        queryParams,
        pagination
      );
      topicMeetingList.value = data;
      pagination.total = total;
    } else if (value === "audited") {
      const { data, total } = await listTopicMeetingAudited(
        queryParams,
        pagination
      );
      topicMeetingList.value = data;
      pagination.total = total;
    }

    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "business/topicMeeting/export",
      {
        ...queryParams
      },
      `topicMeeting_${new Date().getTime()}.xlsx`
    );
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    columns,
    pagination,
    queryParams,
    topicMeeting,
    topicPreviewVisible,
    topicMeetingData,
    topicMeetingList,
    activeName,
    topicId,
    isComponentVisible,
    currentComponent,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleGoBack,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleSubmit,
    handleQuery,
    resetQuery,
    handleExport,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    tabClick,
    uid,
    getList,
    handleView
  };
}
