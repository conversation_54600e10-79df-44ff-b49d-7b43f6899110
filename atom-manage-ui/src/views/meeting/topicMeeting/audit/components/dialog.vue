<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import { auditTopicMeeting } from "@/api/meeting/topicMeeting";
import { useGlobal } from "@pureadmin/utils";
import {
  TopicMeetingAudit,
  TopicMeetingAuditInfo
} from "@/types/meeting/topicMeetingAudit";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TopicMeetingAuditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: TopicMeetingAudit,
    default: () => {
      return new TopicMeetingAudit();
    }
  }
});

// 表单信息
const form = ref<TopicMeetingAuditInfo>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单校验
const rules = ref({
  status: [{ required: true, message: "审核意见不能为空", trigger: "blur" }],
  remark: [
    {
      message: "备注不能为空",
      validator: validateStatus,
      trigger: "blur"
    }
  ]
});

const title = computed(() => {
  return "审核议题";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

async function handlePropsDataChange(data: TopicMeetingAudit) {
  if (!visible.value) return;
  reset();
  form.value.topicId = data.topicId;
}

/** 表单重置 */
function reset() {
  form.value = new TopicMeetingAuditInfo();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

function validateStatus(rule, value, callback) {
  if (form.value.status === "" || typeof form.value.status === "undefined") {
    callback();
  }
  if (
    form.value.status === "pass" ||
    (form.value.status === "recall" && value !== undefined)
  ) {
    callback();
  } else {
    callback(new Error(rule.message));
  }
}

/** 提交按钮 */
function auditForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      auditTopicMeeting(form.value)
        .then(() => {
          message("审核成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改议题收集对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="700px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
      <el-form-item label="审核意见" prop="status">
        <el-radio v-model="form.status" label="pass">同意</el-radio>
        <el-radio v-model="form.status" label="recall">不同意</el-radio>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          placeholder="请输入备注"
          type="textarea"
          :rows="3"
          show-word-limit
          maxlength="500"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="auditForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
