<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import { auditTopicMeeting, getTopicMeeting } from "@/api/meeting/topicMeeting";
import { useGlobal } from "@pureadmin/utils";
import {
  TopicMeetingAudit,
  TopicMeetingAuditInfo
} from "@/types/meeting/topicMeetingAudit";
import FileUpload from "@/components/FileUpload";
import { TopicMeeting } from "@/types/meeting/topicMeeting";
import { uniqBy } from "lodash-es";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TopicQuickAuditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: TopicMeetingAudit,
    default: () => {
      return new TopicMeetingAudit();
    }
  }
});

const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, meeting_matter_code } = $useDict(
  "meeting_type",
  "meeting_matter_code"
);

// 表单信息
const form = ref<TopicMeetingAuditInfo>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);

const topicForm = ref<TopicMeeting>(null);
const topicFormRef = ref<FormInstance>();

const totalLoading = ref(false);

// 表单校验
const rules = ref({
  status: [{ required: true, message: "审核意见不能为空", trigger: "blur" }],
  remark: [
    {
      message: "备注不能为空",
      validator: validateStatus,
      trigger: "blur"
    }
  ]
});

const title = computed(() => {
  return "审核议题";
});

const meetingTypes = computed(() => {
  if (meeting_type == null) return [];
  return uniqBy(topicForm.value.planList, "type").map(item => {
    return item.type;
  });
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

async function handlePropsDataChange(data: any) {
  if (!visible.value) return;
  reset();
  handlePreview(data.id);
}

/** 表单重置 */
function reset() {
  form.value = new TopicMeetingAuditInfo();
  topicForm.value = new TopicMeeting();
  if (formRef.value) {
    formRef.value.resetFields();
  }
  if (topicFormRef.value) {
    topicFormRef.value.resetFields();
  }
}

function handlePreview(topicId: number) {
  totalLoading.value = true;
  form.value.topicId = topicId;
  getTopicMeeting(topicId)
    .then(async response => {
      topicForm.value = response.data;
      // const delay = topicForm.value.delayPlan;
      // if (delay) {
      //   const { data } = await listAllMeetingPlanWithDelay(
      //     topicForm.value.planId
      //   );
      //   planOptions.value = data;
      // } else {
      //   const { data } = await listAllMeetingPlan();
      //   planOptions.value = data;
      // }
    })
    .finally(() => {
      totalLoading.value = false;
    });
}

function validateStatus(rule, value, callback) {
  if (form.value.status === "" || typeof form.value.status === "undefined") {
    callback();
  }
  if (
    form.value.status === "pass" ||
    (form.value.status === "recall" && value !== undefined)
  ) {
    callback();
  } else {
    callback(new Error(rule.message));
  }
}

/** 提交按钮 */
function auditForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      auditTopicMeeting(form.value)
        .then(() => {
          message("审核成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改议题收集对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
    v-loading="totalLoading"
  >
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form ref="topicFormRef" :model="topicForm" label-width="150px">
          <el-form-item label="议题标题" prop="meetingTitle">
            {{ topicForm.topicTitle }}
          </el-form-item>

          <el-form-item label="预召开会议">
            <div class="flex flex-wrap gap-2">
              <div v-for="item in topicForm.planList" :key="item.id">
                <el-tag type="primary">{{ item.title }}</el-tag>
                <el-tag type="danger" v-if="item.delay" class="ml-2"
                  >已逾期</el-tag
                >
              </div>
            </div>
          </el-form-item>
          <el-form-item label="会议类型">
            <el-tag
              v-for="item in meetingTypes"
              :key="item"
              type="primary"
              class="mr-2"
              >{{ meeting_type.find(it => it.value == item)?.label }}</el-tag
            >
          </el-form-item>

          <el-form-item label="汇报人" prop="reportUserId">
            {{ topicForm.reportUserName }}
          </el-form-item>

          <el-form-item label="部门领导" prop="departUserId">
            {{ topicForm.departUserName }}
          </el-form-item>

          <el-form-item label="分管领导" prop="leaderUserId">
            {{ topicForm.leaderUserName }}
          </el-form-item>

          <el-form-item label="事项编码" prop="noticeCode">
            {{
              meeting_matter_code.find(it => it.value == topicForm.noticeCode)
                ?.label
            }}
          </el-form-item>

          <el-form-item
            label="议题正文"
            prop="mainFile"
            style="display: flex; align-items: center"
            :class="topicForm.topicFileList == null ? '' : 'custom-form-item'"
          >
            <p v-if="topicForm.topicFileList == null">暂无附件</p>
            <FileUpload v-else v-model="topicForm.mainFile" :disabled="true" />
          </el-form-item>

          <el-form-item
            label="议题附件"
            prop="topicFileList"
            style="display: flex; align-items: center"
            :class="topicForm.topicFileList == null ? '' : 'custom-form-item'"
          >
            <p v-if="topicForm.topicFileList == null">暂无附件</p>
            <FileUpload
              v-else
              v-model="topicForm.topicFileList"
              :disabled="true"
            />
          </el-form-item>

          <el-form-item label="补充说明" prop="remark">
            <p>{{ topicForm.remark }}</p>
          </el-form-item>
        </el-form></el-col
      >
      <el-col :span="12"
        ><el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="审核意见" prop="status">
            <el-radio v-model="form.status" label="pass">同意</el-radio>
            <el-radio v-model="form.status" label="recall">不同意</el-radio>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注"
              type="textarea"
              :rows="3"
              show-word-limit
              maxlength="500"
            />
          </el-form-item> </el-form
      ></el-col>
    </el-row>

    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="auditForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
