<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { getShareUrl } from "@/api/system/oss/oss";
const emit = defineEmits(["update:visible", "confirm"]);
import VideoPreview from "@/components/VideoPreview";
defineOptions({
  name: "VideoPreview"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  ossId: {
    type: Number,
    required: true
  }
});

const src = ref("");
const loading = ref(false);

const title = computed(() => {
  return "视频";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.ossId)
);

function handlePropsDataChange(ossId: number) {
  if (!visible.value) return;
  getFileUrl(ossId);
}

function getFileUrl(ossId: number) {
  loading.value = true;
  getShareUrl(ossId).then(res => {
    src.value = res.data;
    loading.value = false;
  });
}
</script>

<template>
  <!-- 添加或修改文件对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
  >
    <div
      v-if="src"
      v-loading="loading"
      style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      "
    >
      <VideoPreview :url="src" />
    </div>
    <div v-else>
      <span>视频正在打开中。。。</span>
    </div>
  </el-dialog>
</template>
