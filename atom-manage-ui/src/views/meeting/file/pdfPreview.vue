<script setup lang="ts">
import {
  computed,
  ref,
  watch,
  reactive,
  onUnmounted,
  watchEffect,
  watchPostEffect
} from "vue";
import { uploadFile } from "@/types/meeting/meeting";
import { getShareUrl } from "@/api/system/oss/oss";
const emit = defineEmits(["update:visible", "confirm"]);
import VuePdfEmbed from "vue-pdf-embed";
import "vue-pdf-embed/dist/styles/annotationLayer.css";
import "vue-pdf-embed/dist/styles/textLayer.css";
import VueOfficePdf from "@vue-office/pdf";
import VideoPreview from "@/components/VideoPreview";
import { useUserStoreHook } from "@/store/modules/user";
import { WatermarkOptions } from "@/utils/watermark/types";
import Watermark from "@/utils/watermark";
import { useTimeoutFn } from "@vueuse/core";
defineOptions({
  name: "PdfPreview"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  ossId: {
    type: Number,
    required: true
  }
});

const src = ref("");
const loading = ref(false);

const watermarks = ref<Watermark[]>([]);
const pdfRef = ref();
// const font = reactive({
//   fontSize: 20,
//   color: "rgba(0,0,0,0.25)"
// });
// const gap = ref([70, 70]);
const title = computed(() => {
  return "文件浏览";
});
const orgName = useUserStoreHook().orgName;
const username = useUserStoreHook().username;
const watermarkOptions: WatermarkOptions = {
  content: [orgName, username],
  font: {
    color: "rgba(0,0,0,0.25)",
    fontSize: 20
  },
  gap: [70, 70],
  rotate: -35
};
// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.ossId)
);

function handlePropsDataChange(ossId: number) {
  if (!visible.value) return;
  getFileUrl(ossId);
}

function handleClosed() {
  clearWatermark();
  src.value = "";
}

function getFileUrl(ossId: number) {
  loading.value = true;
  getShareUrl(ossId)
    .then(res => {
      src.value = res.data;
    })
    .catch(() => {
      loading.value = false;
    });
}

function handleRendered() {
  loading.value = false;
  // 为每一页添加水印
  const pdfPageContainers: HTMLCollection =
    pdfRef.value.$el.getElementsByClassName("vue-pdf-embed__page");
  if (pdfPageContainers.length > 0) {
    Array.from(pdfPageContainers).forEach(
      (pdfPageContainer: HTMLDivElement) => {
        watermarks.value.push(
          new Watermark(pdfPageContainer, watermarkOptions)
        );
      }
    );
  }
}

function clearWatermark() {
  watermarks.value.forEach(watermark => {
    watermark.destroyWatermark();
  });
  watermarks.value = [];
}
</script>

<template>
  <!-- 添加或修改文件对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="65%"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <!-- <el-watermark
      :font="font"
      :gap="gap"
      :rotate="-35"
      :content="[orgName, username]"
    > -->
    <div
      v-loading="loading"
      style="min-height: 640px; max-height: 80vh; overflow-y: auto"
    >
      <VuePdfEmbed
        v-if="src"
        ref="pdfRef"
        :source="src"
        @rendered="handleRendered"
      />
    </div>
    <!-- </el-watermark> -->
  </el-dialog>
</template>
