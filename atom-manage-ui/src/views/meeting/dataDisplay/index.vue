<script setup lang="ts">
import PieCharts from "@/views/meeting/dataDisplay/PieCharts.vue";
import LineChart from "@/views/meeting/dataDisplay/LineChart.vue";
import { getBarData, getPieData } from "@/api/meeting/dataDisplay";
import { dataDisplayVo } from "@/types/meeting/file";
import { computed, onMounted, ref } from "vue";

const pieData = ref([]);
const lineChartdata = ref([]);
const pieCharts = ref<dataDisplayVo[]>([]);
const lineChart = ref<dataDisplayVo[]>([]);
// 初始话数据
const getinit = () => {
  getPieData(String(new Date().getFullYear())).then(res => {
    pieData.value = res.data;
  });

  getBarData(String(new Date().getFullYear())).then(res => {
    lineChartdata.value = res.data;
  });
  pieCharts.value && pieCharts.value.initChart();
  lineChart.value && lineChart.value.initChart();
};

const handleLine = async val => {
  getBarData(val._value).then(res => {
    lineChartdata.value = res.data;
  });
  lineChart.value && lineChart.value.initChart();
};

const handleLinePie = async val => {
  getPieData(val._value).then(res => {
    pieData.value = res.data;
  });
  pieCharts.value && pieCharts.value.initChart();
};

async function init() {
  await getinit();
}
onMounted(init);
</script>
<template>
  <el-row
    :gutter="20"
    justify="center"
    class="pagelevel1"
    style="width: 100%; height: 100%"
  >
    <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" class="center-col">
      <div class="chart-title">
        <span>会议类型次数</span>
      </div>
      <PieCharts @refresh="handleLinePie" :data="pieData" ref="pieCharts" />
    </el-col>
    <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" class="center-col">
      <div class="chart-title">
        <span>会议日期统计数量</span>
      </div>
      <LineChart @refresh="handleLine" :data="lineChartdata" ref="lineChart" />
    </el-col>
  </el-row>
</template>
<style scoped>
.center-col {
  display: flex;
  flex-direction: column; /* 垂直布局 */
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
  height: 100%; /* 让 el-col 高度充满父元素 */
}

.center-col > div {
  text-align: center; /* 使内部文字居中 */
}
</style>
