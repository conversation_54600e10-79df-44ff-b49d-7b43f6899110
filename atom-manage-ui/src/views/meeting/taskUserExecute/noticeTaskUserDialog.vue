<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import { Task } from "@/types/meeting/task";
import {
  addOrUpdateTaskUserExecute,
  getTaskUserExecute,
  getTaskUserExecuteByTaskId
} from "@/api/meeting/taskUserExecute";
import { TaskUserExecute } from "@/types/meeting/taskUserExecute";
import { listVoTask, getTask } from "@/api/meeting/task";
import dayjs from "dayjs";
import { Meeting } from "@/types/meeting/meeting";
import { getMeeting } from "@/api/meeting/meeting";
import { getUserInfo } from "@/api/system/user";
import download from "@/plugins/download";
import MeetingPreviewDialog from "@/views/meeting/manager/components/MeetingPreviewDialog.vue";
const emit = defineEmits(["update:visible", "confirm"]);
const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();
const { TASK_TYPE, meeting_type, TASK_FILE_TYPE } = $useDict(
  "TASK_TYPE",
  "meeting_type",
  "TASK_FILE_TYPE"
);

defineOptions({
  name: "TaskUserExecuteEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: Task,
    default: () => {
      return new Task();
    }
  },
  look: {
    type: String,
    default: "view"
  }
});

// 表单信息
const form = ref<TaskUserExecute>(null);
const formRef = ref<FormInstance>();
const formRefs = ref<FormInstance>();
const loading = ref(false);
const loadingForm = ref(false);
const taskList = ref([]);
const formTask = ref<Task>(null);
const meet = ref<Meeting>(new Meeting());
const meetVisible = ref(false);
const sourceVisible = ref(false);
const userName = ref();
const visiblePreviewDialog = ref(false);
const meetingData = ref<Meeting>();
const task = ref<Task>();
// 表单校验
const rules = ref({
  taskId: [{ required: true, message: "督查督办不能为空", trigger: "change" }],
  executeWorkable: [
    { required: true, message: "落实情况不能为空", trigger: "blur" }
  ],
  executePlan: [
    { required: true, message: "当前进度，后续计划不能为空", trigger: "blur" }
  ],
  planTime: [
    { required: true, message: "计划完成时间不能为空", trigger: "blur" }
  ],
  successTime: [
    { required: true, message: "实际完成时间不能为空", trigger: "blur" }
  ]
});

const title = computed(() => {
  return "督查督办执行";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: Task) {
  if (!visible.value) return;
  reset();
  getListTask();
  if (data.id) {
    form.value.taskId = data.id;
    getListVoTask(data.id);
  }
}

/** 表单重置 */
function reset() {
  form.value = new TaskUserExecute();
  if (formRef.value) {
    formRef.value.resetFields();
  }
  taskList.value = [];
  meetVisible.value = false;
  sourceVisible.value = false;
}

async function getListTask() {
  const { data } = await listVoTask();
  taskList.value = data;
}

/** 查询操作 */
async function getListVoTask(id: number) {
  loadingForm.value = true;
  getTask(id).then(async response => {
    task.value = response.data;
    meetVisible.value = task.value.taskType === "1";
    sourceVisible.value = task.value.taskType === "2";
    if (meetVisible.value) {
      const { data } = await getMeeting(task.value.meetingId);
      meet.value = data;
    }
    if (task.value.userId) {
      const { data } = await getUserInfo([task.value.userId]);
      userName.value = data[0].name;
    }
  });
  loadingForm.value = false;
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      form.value.taskId = task.value.id;
      form.value.isExecute = 1;
      addOrUpdateTaskUserExecute(form.value)
        .then(() => {
          message("操作成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function downloadFile(file: any) {
  download.oss(file.ossId, null);
}

function handlePreview(row: Meeting) {
  meetingData.value = row;
  visiblePreviewDialog.value = true;
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改督查督办执行对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="70%"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-row :gutter="20" v-loading="loadingForm">
      <el-col :span="12">
        <el-form
          ref="formRefs"
          v-if="task"
          :model="formTask"
          label-width="120px"
        >
          <el-form-item label="任务类型" prop="taskType">
            <span>{{
              TASK_TYPE.find(it => it.value == task.taskType)?.label
            }}</span>
          </el-form-item>
          <div v-if="meetVisible">
            <el-form-item label="关联会议" prop="meetingId">
              <span
                class="text-blue-500 cursor-pointer"
                @click="handlePreview(meet)"
                >{{ meet.meetingTitle }}</span
              >
            </el-form-item>
          </div>
          <div v-if="sourceVisible">
            <el-form-item label="任务来源" prop="sourceType">
              <span>{{
                TASK_FILE_TYPE.find(it => it.value == task.sourceType)?.label
              }}</span>
            </el-form-item>
            <div v-if="task.sourceType === '1'">
              <el-form-item prop="sourceFileList">
                <div
                  v-for="(file, index) in task.sourceFileList"
                  :key="file.id"
                  class="text-blue-400 cursor-pointer"
                  @click="downloadFile(file)"
                >
                  {{ index + 1 }}、 {{ file.name }}
                </div>
              </el-form-item>
            </div>
            <div v-else-if="task.sourceType === '2'">
              <el-form-item prop="sourceContent">
                <p>{{ task.sourceContent }}</p>
              </el-form-item>
            </div>
          </div>
          <el-form-item label="督办事项" prop="taskTitle">
            <p>{{ task.taskTitle }}</p>
          </el-form-item>
          <el-form-item label="主责部门" prop="deptId">
            <span>{{ task.deptName }}</span>
          </el-form-item>
          <el-form-item label="部门负责人" prop="userId">
            <span>{{ userName }}</span>
          </el-form-item>
          <el-form-item label="办结时间" prop="completeTime">
            <span>{{ dayjs(task.completeTime).format("YYYY-MM-DD") }}</span>
          </el-form-item>
          <el-form-item label="任务内容" prop="taskContent">
            <p>{{ task.taskContent }}</p>
          </el-form-item>
          <el-form-item label="附件" prop="fileList">
            <div
              v-for="(file, index) in task.fileList"
              :key="file.id"
              class="text-blue-400 cursor-pointer"
              @click="downloadFile(file)"
            >
              {{ index + 1 }}、 {{ file.name }}
            </div>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
          <el-form-item label="督查督办" prop="taskId">
            <span v-if="task">{{ task.taskTitle }}</span>
          </el-form-item>
          <el-form-item label="计划完成时间" prop="planTime">
            <el-date-picker
              clearable
              style="width: 100%"
              v-model="form.planTime"
              type="date"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择计划完成时间"
            />
          </el-form-item>
          <el-form-item label="实际完成时间" prop="successTime">
            <el-date-picker
              clearable
              style="width: 100%"
              v-model="form.successTime"
              type="date"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择实际完成时间"
            />
          </el-form-item>
          <el-form-item label="落实情况" prop="executeWorkable">
            <el-input
              type="textarea"
              show-word-limit
              maxlength="1000"
              rows="2"
              v-model="form.executeWorkable"
              placeholder="请输入落实情况"
            />
          </el-form-item>
          <el-form-item label="当前进度、后续计划" prop="executePlan">
            <el-input
              type="textarea"
              show-word-limit
              maxlength="1000"
              rows="2"
              v-model="form.executePlan"
              placeholder="请输入当前进度，后续计划"
            />
          </el-form-item>
          <el-form-item label="延迟原因" show-word-limit prop="executeDelay">
            <el-input
              type="textarea"
              show-word-limit
              maxlength="1000"
              rows="2"
              v-model="form.executeDelay"
              placeholder="请输入延迟原因"
            />
          </el-form-item>
          <el-form-item label="备注" show-word-limit prop="remark">
            <el-input
              type="textarea"
              show-word-limit
              maxlength="1000"
              rows="2"
              v-model="form.remark"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <meeting-preview-dialog
    v-model:visible="visiblePreviewDialog"
    :data="meetingData"
  />
</template>
