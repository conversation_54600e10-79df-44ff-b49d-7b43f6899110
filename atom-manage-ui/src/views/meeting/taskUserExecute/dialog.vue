<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";

import {
  addOrUpdateTaskUserExecute,
  getTaskUserExecute,
  getTaskUserExecuteByTaskId
} from "@/api/meeting/taskUserExecute";
import { TaskUserExecute } from "@/types/meeting/taskUserExecute";
import { listVoTask, getTask } from "@/api/meeting/task";
import { Task } from "@/types/meeting/task";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TaskUserExecuteEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: TaskUserExecute,
    default: () => {
      return new TaskUserExecute();
    }
  }
});

// 表单信息
const form = ref<TaskUserExecute>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);
const loadingForm = ref(false);
const taskList = ref([]);
const task = ref<Task>();

// 表单校验
const rules = ref({
  taskId: [{ required: true, message: "督查督办不能为空", trigger: "change" }],
  executeWorkable: [
    { required: true, message: "落实情况不能为空", trigger: "blur" }
  ],
  executeDelay: [
    { required: true, message: "延迟原因不能为空", trigger: "blur" }
  ],
  executePlan: [
    { required: true, message: "当前进度，后续计划不能为空", trigger: "blur" }
  ],
  planTime: [
    { required: true, message: "计划完成时间不能为空", trigger: "blur" }
  ],
  successTime: [
    { required: true, message: "实际完成时间不能为空", trigger: "blur" }
  ]
});

const title = computed(() => {
  return "督查督办执行";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: TaskUserExecute) {
  if (!visible.value) return;
  reset();
  // getListVoTask();
  if (data.taskId) {
    form.value.taskId = data.taskId;
    form.value.noAssignExecute = data.noAssignExecute;
    // 默认执行状态 1 已办结
    form.value.status = "1";
    getTaskVo(data.taskId);
  }
}

/** 表单重置 */
function reset() {
  form.value = new TaskUserExecute();
  if (formRef.value) {
    formRef.value.resetFields();
  }
  taskList.value = [];
}

async function getTaskVo(taskId: number) {
  getTask(taskId).then(async response => {
    task.value = response.data;
    if (response.data.taskExecuteId) {
      Object.assign(form.value, {
        id: response.data.taskExecuteId,
        executeWorkable: response.data.executeWorkable,
        executePlan: response.data.executePlan,
        planTime: response.data.planTime,
        successTime: response.data.successTime,
        executeDelay: response.data.executeDelay,
        remark: response.data.remark,
        // auditRemark: response.data.auditRemark,
        // majorRemark: response.data.majorRemark,
        status: response.data.executeStatus
      });
    }
  });
}

/** 修改按钮操作 */
// async function handleUpdate(row: TaskUserExecute) {
//   if (props.look === "edit") {
//     getTaskUserExecuteByTaskId(row.taskId).then(response => {
//       if (response.data) {
//         form.value = response.data;
//       } else {
//         form.value.taskId = row.taskId;
//       }
//     });
//   } else {
//     form.value.taskId = row.taskId;
//     getTask(row.taskId).then(res => {
//       task.value = res.data;
//     });
//   }
// }

/**
 * 查询可选择督查督办
 */
// async function getListVoTask() {
//   const { data } = await listVoTask();
//   taskList.value = data;
// }

// function findTaskExecute() {
//   const taskId = form.value.taskId;
//   loadingForm.value = true;
//   getTaskUserExecuteByTaskId(form.value.taskId)
//     .then(response => {
//       if (response.data) {
//         form.value = response.data;
//       } else {
//         form.value = new TaskUserExecute();
//         form.value.taskId = taskId;
//       }
//       loadingForm.value = false;
//     })
//     .finally(() => {
//       loadingForm.value = false;
//     });
// }

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      addOrUpdateTaskUserExecute(form.value)
        .then(() => {
          message("操作成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改督查督办执行对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form
      v-loading="loadingForm"
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="210px"
      v-if="task"
    >
      <el-form-item label="督查督办">
        <p>{{ task.taskTitle }}</p>
      </el-form-item>
      <el-form-item label="审核意见" v-if="task.auditRemark">
        <p>{{ task.auditRemark }}</p>
      </el-form-item>
      <el-form-item label="执行情况" prop="status">
        <div v-if="form.noAssignExecute">已办结</div>
        <el-radio-group v-model="form.status" v-else>
          <el-radio value="1">已办结</el-radio>
          <el-radio value="0">未办结</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="落实情况" prop="executeWorkable">
        <el-input
          type="textarea"
          show-word-limit
          maxlength="1000"
          rows="2"
          v-model="form.executeWorkable"
          placeholder="请输入落实情况"
        />
      </el-form-item>
      <div v-if="form.status === '1'">
        <el-form-item label="实际完成时间" prop="successTime">
          <el-date-picker
            clearable
            style="width: 100%"
            v-model="form.successTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择实际完成时间"
          />
        </el-form-item>
      </div>
      <div v-if="form.status === '0'">
        <el-form-item label="延迟原因" show-word-limit prop="executeDelay">
          <el-input
            type="textarea"
            show-word-limit
            maxlength="1000"
            rows="2"
            v-model="form.executeDelay"
            placeholder="请输入延迟原因"
          />
        </el-form-item>
        <el-form-item label="当前进度、后续计划" prop="executePlan">
          <el-input
            type="textarea"
            show-word-limit
            maxlength="1000"
            rows="2"
            v-model="form.executePlan"
            placeholder="请输入当前进度，后续计划"
          />
        </el-form-item>
        <el-form-item label="计划完成时间" prop="planTime">
          <el-date-picker
            clearable
            style="width: 100%"
            v-model="form.planTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择计划完成时间"
          />
        </el-form-item>
        <el-form-item label="备注" show-word-limit prop="remark">
          <el-input
            type="textarea"
            show-word-limit
            maxlength="1000"
            rows="2"
            v-model="form.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
