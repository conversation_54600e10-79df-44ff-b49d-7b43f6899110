import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { delTaskUserExecute, listTaskUserExecute } from "@/api/meeting/taskUserExecute";
import { TaskUserExecute, TaskUserExecuteQuery } from "@/types/meeting/taskUserExecute";

export function useTaskUserExecute() {
	const queryParams = reactive<TaskUserExecuteQuery>(new TaskUserExecuteQuery());
	const { $download } = useGlobal<GlobalPropertiesApi>();

	const loading = ref(true);
	const visible = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);
	const taskUserExecuteList = ref([]);
	const taskUserExecuteData = ref<TaskUserExecute>();

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left",
		},
		{
			label: "序号",
			type: "index",
			width: 70,
			formatter: () =>
				pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
		},
		{
			label: "主键",
			prop: "id",
			align: "center",
			width: 100,
		},
		{
			label: "督办主表id",
			prop: "taskId",
			align: "center",
			width: 100,
		},
		{
			label: "落实情况",
			prop: "executeWorkable",
			align: "center",
			width: 100,
		},
		{
			label: "延迟原因",
			prop: "executeDelay",
			align: "center",
			width: 100,
		},
		{
			label: "当前进度，后续计划",
			prop: "executePlan",
			align: "center",
			width: 100,
		},
		{
			label: "计划完成时间",
			prop: "planTime",
			align: "center",
			width: 100,
			cellRenderer: ({ row }) => (
				<span>{dayjs(row.planTime).format("YYYY-MM-DD")}</span>
			)
		},
		{
			label: "实际完成时间",
			prop: "successTime",
			align: "center",
			width: 100,
			cellRenderer: ({ row }) => (
				<span>{dayjs(row.successTime).format("YYYY-MM-DD")}</span>
			)
		},
		{
			label: "备注",
			prop: "remark",
			align: "center",
			width: 100,
		},
		{
			label: "状态",
			prop: "status",
			align: "center",
			width: 100,
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 200,
			slot: "operation"
		}
	]

	/** 新增按钮操作 */
	function handleCreate() {
		visible.value = true;
		taskUserExecuteData.value = new TaskUserExecute();
	}


	function handleDelete(row?: TaskUserExecute) {
		if (row?.id) {
			delTaskUserExecute(row.id).then(() => {
				getList();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm(
			'是否确认删除督查督办执行编号为"' + ids.value + '"的数据项？',
			{
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				title: "系统提示"
			}
		)
			.then(function () {
				return delTaskUserExecute(ids.value);
			})
			.then(() => {
				getList();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 修改按钮操作 */
	function handleUpdate(row: TaskUserExecute) {
		visible.value = true;
		taskUserExecuteData.value = row;
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await listTaskUserExecute(queryParams, pagination);
		taskUserExecuteList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200)
	}

	function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download('business/taskUserExecute/export', {
			...queryParams
		}, `taskUserExecute_${new Date().getTime()}.xlsx`)
	}


	onMounted(() => {
		getList();
	});

	return {
		single,
		multiple,
		visible,
		loading,
		columns,
		pagination,
		queryParams,
		taskUserExecuteData,
		taskUserExecuteList,
		handleCreate,
		handleDelete,
		handleUpdate,
		handleQuery,
		resetQuery,
		handleExport,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange,
		getList
	};
}
