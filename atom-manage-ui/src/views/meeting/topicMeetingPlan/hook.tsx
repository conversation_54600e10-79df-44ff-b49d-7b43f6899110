import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import {
  delTopicMeetingPlan,
  listTopicMeetingPlan
} from "@/api/meeting/topicMeetingPlan";
import {
  TopicMeetingPlan,
  TopicMeetingPlanQuery
} from "@/types/meeting/topicMeetingPlan";
import DictTag from "@/components/DictTag";
import TopicLink from "@/views/meeting/topicMeetingPlan/link/topicLink.vue";
import { useAncestorDetection } from "@/hooks/useAncestorState";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";

export function useTopicMeetingPlan() {
  const queryParams = reactive<TopicMeetingPlanQuery>(
    new TopicMeetingPlanQuery()
  );
  const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();
  const { meeting_type } = $useDict("meeting_type");

  const loading = ref(true);
  const visible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const topicMeetingPlanList = ref([]);
  const topicMeetingPlanData = ref<TopicMeetingPlan>();

  const planId = ref();
  const currentComponent = ref(null);
  const isComponentVisible = ref(false);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "标题",
      prop: "title",
      align: "left",
      minWidth: 200
    },
    {
      label: "截止时间",
      prop: "lastTime",
      align: "center",
      width: 230,
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <span>{dayjs(row.lastTime).format("YYYY-MM-DD HH:mm:ss")}</span>
      )
    },
    {
      label: "会议类型",
      prop: "type",
      align: "center",
      width: 168,
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <DictTag options={meeting_type} value={row.type} />
      )
    },
    {
      label: "操作",
      align: "left",
      width: 320,
      minWidth: 300,
      slot: "operation"
    }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    topicMeetingPlanData.value = new TopicMeetingPlan();
  }

  function handleDelete(row?: TopicMeetingPlan) {
    if (row?.id) {
      delTopicMeetingPlan(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除预约编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delTopicMeetingPlan(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: TopicMeetingPlan) {
    visible.value = true;
    topicMeetingPlanData.value = row;
  }

  function handleView(row: TopicMeetingPlan) {
    planId.value = row.id;
    currentComponent.value = TopicLink;
    isComponentVisible.value = true;
  }

  function handleGoBack() {
    isComponentVisible.value = false;
    currentComponent.value = null;
  }

  async function getList() {
    loading.value = true;
    const { data, total } = await listTopicMeetingPlan(queryParams, pagination);
    topicMeetingPlanList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "business/topicMeetingPlan/export",
      {
        ...queryParams
      },
      `topicMeetingPlan_${new Date().getTime()}.xlsx`
    );
  }
  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");

  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    columns,
    pagination,
    queryParams,
    topicMeetingPlanData,
    topicMeetingPlanList,
    planId,
    currentComponent,
    isComponentVisible,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleQuery,
    resetQuery,
    handleView,
    handleExport,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList,
    handleGoBack
  };
}
