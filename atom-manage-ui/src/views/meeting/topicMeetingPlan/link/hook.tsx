import dayjs from "dayjs";
import Sortable from "sortablejs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { nextTick, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { TopicMeeting, TopicMeetingQuery } from "@/types/meeting/topicMeeting";
import {
  delTopicMeeting,
  listTopicMeetingByPlanId,
  submitTopicMeeting,
  updateLinkTopicOrder
} from "@/api/meeting/topicMeeting";
import { TopicMeetingPlan } from "@/types/meeting/topicMeetingPlan";
import { getTopicMeetingPlan } from "@/api/meeting/topicMeetingPlan";
import TopicInfo from "@/views/meeting/topicMeeting/info/topicInfo.vue";
import { TopicMeetingAudit } from "@/types/meeting/topicMeetingAudit";
import { useAncestorDetection } from "@/hooks/useAncestorState";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";

export function useTopicLink(emit: any) {
  const queryParams = reactive<TopicMeetingQuery>(new TopicMeetingQuery());
  const { $download } = useGlobal<GlobalPropertiesApi>();

  const loading = ref(true);
  const visible = ref(false);
  const editVisible = ref(false);
  const auditVisible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const topicMeetingList = ref([]);
  const topicMeetingData = ref<TopicMeeting>();
  const topicPlanData = ref<TopicMeetingPlan>(new TopicMeetingPlan());
  const planId = ref();

  const topicId = ref();
  const currentComponent = ref(null);
  const isComponentVisible = ref(false);
  const topicPreviewVisible = ref(false);

  const sortTopicId = ref([]);

  const rowDrop = (event: { preventDefault: () => void }) => {
    event.preventDefault();
    nextTick(() => {
      const wrapper: HTMLElement = document.querySelector(
        ".el-table__body-wrapper tbody"
      );
      Sortable.create(wrapper, {
        animation: 300,
        handle: ".drag-btn",
        onEnd: ({ newIndex, oldIndex }) => {
          const currentRow = topicMeetingList.value.splice(oldIndex, 1)[0];
          topicMeetingList.value.splice(newIndex, 0, currentRow);

          sortTopicId.value = topicMeetingList.value.map(item => item.id);
          updateLinkTopicOrder(
            planId.value,
            sortTopicId.value,
            pagination
          ).then(() => getList());
        }
      });
    });
  };

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      width: 70,
      cellRenderer: ({ row }) => {
        const rowIndex = topicMeetingList.value.indexOf(row);
        return (
          <div class="flex items-center">
            <iconify-icon-online
              icon="icon-park-outline:drag"
              class="drag-btn cursor-grab"
              onMouseenter={(event: { preventDefault: () => void }) =>
                rowDrop(event)
              }
            />
            <p class="ml-[16px]">
              {pagination.pageSize * (pagination.currentPage - 1) +
                rowIndex +
                1 +
                ""}
            </p>
          </div>
        );
      }
    },
    {
      label: "议题标题",
      prop: "topicTitle",
      align: "center",
      minWidth: 170,
      cellRenderer: ({ row }) => {
        return (
          <span
            class="cursor-pointer text-blue-700"
            onClick={() => handlePreview(row)}
          >
            {row.topicTitle}
          </span>
        );
      }
    },
    {
      label: "议题上传时间",
      prop: "createTime",
      align: "center",
      width: 230,
      minWidth: 50,
      formatter: ({ createTime }) =>
        createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
    },
    {
      label: "上传单位",
      prop: "creatorDept",
      align: "center",
      minWidth: 180
    },
    {
      label: "汇报人",
      prop: "reportUserName",
      align: "center",
      minWidth: 120
    },
    {
      label: "操作",
      align: "left",
      slot: "operation",
      width: 300
    }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    topicMeetingData.value = new TopicMeeting();
  }

  function handleDelete(row?: TopicMeeting) {
    if (row?.id) {
      delTopicMeeting(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除议题收集编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delTopicMeeting(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: TopicMeeting) {
    visible.value = true;
    topicMeetingData.value = row;
    getList();
  }

  function handleSubmit(row: TopicMeeting) {
    if (row?.id) {
      submitTopicMeeting(row.id).then(resp => {
        if (resp.code == 200) {
          getList();
          message("提交成功", { type: "success" });
        }
      });
    }
  }

  async function handleTopicQuery(id: number) {
    if (id) {
      loading.value = true;
      planId.value = id;

      const planPromise = new Promise<TopicMeetingPlan>((resolve, reject) => {
        getTopicMeetingPlan(id)
          .then(resp => {
            resolve(resp.data);
          })
          .catch(error => {
            reject(error);
          });
      });

      topicPlanData.value = await planPromise;

      await getList();
    }
  }

  function handleEdit(row: TopicMeeting) {
    editVisible.value = true;
    topicMeetingData.value = row;
  }

  function handleAudit(row: TopicMeeting) {
    auditVisible.value = true;
    topicMeetingData.value = row;
  }

  function handlePreview(row: TopicMeeting) {
    topicMeetingData.value = row;
    topicPreviewVisible.value = true;
  }

  async function getList() {
    loading.value = true;
    queryParams.planId = planId.value;
    const { data, total } = await listTopicMeetingByPlanId(
      queryParams,
      pagination
    );
    topicMeetingList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function goBack() {
    emit("go-back");
  }

  function handleView(row: TopicMeeting) {
    isComponentVisible.value = true;
    currentComponent.value = TopicInfo;
    topicId.value = row.id;
  }

  function handleGoBack() {
    currentComponent.value = null;
    isComponentVisible.value = false;
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "business/topicMeeting/export",
      {
        ...queryParams
      },
      `topicMeeting_${new Date().getTime()}.xlsx`
    );
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  return {
    single,
    multiple,
    visible,
    editVisible,
    auditVisible,
    loading,
    columns,
    pagination,
    queryParams,
    topicMeetingData,
    topicMeetingList,
    topicPlanData,
    topicId,
    isComponentVisible,
    currentComponent,
    topicPreviewVisible,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleView,
    handleGoBack,
    handleTopicQuery,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleSubmit,
    handleEdit,
    handleAudit,
    handleQuery,
    resetQuery,
    handleExport,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList,
    goBack
  };
}
