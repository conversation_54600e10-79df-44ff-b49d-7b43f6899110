<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import { getTopicMeeting, updateLinkTopic } from "@/api/meeting/topicMeeting";
import {
  TopicMeeting,
  TopicMeetingLinkEdit
} from "@/types/meeting/topicMeeting";
import UserSelectTree from "@/components/UserSelectTree/index.vue";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "TopicLinkEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  data: {
    type: TopicMeeting,
    default: () => {
      return new TopicMeeting();
    }
  }
});

// 表单信息
const form = ref<TopicMeetingLinkEdit>(null);
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单校验
const rules = ref({
  status: [{ required: true, message: "审核意见不能为空", trigger: "blur" }],
  remark: [{ required: true, message: "补充说明不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return "修改议题";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

async function handlePropsDataChange(data: TopicMeeting) {
  if (!visible.value) return;
  reset();
  await handleUpdate(data);
}

/** 表单重置 */
function reset() {
  form.value = new TopicMeetingLinkEdit();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: TopicMeeting) {
  getTopicMeeting(row.id).then(resp => {
    form.value.id = resp.data.id;
    form.value.topicTitle = resp.data.topicTitle;
    form.value.reportUserId = resp.data.reportUserId;
  });
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      updateLinkTopic(form.value)
        .then(() => {
          message("更新成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="700px"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="议题名称" prop="topicTitle">
        <el-input v-model="form.topicTitle" placeholder="请输入议题名称" />
      </el-form-item>
      <el-form-item label="汇报人" prop="reportUserId">
        <user-select-tree
          :key="form.reportUserId"
          :multiple="false"
          import-type="user"
          v-model:model-value="form.reportUserId"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
