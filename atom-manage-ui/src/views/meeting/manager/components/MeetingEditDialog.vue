<script setup lang="ts">
import { computed, nextTick, ref, watch } from "vue";
import { message, confirm } from "@/utils/message";
import { FormInstance } from "element-plus";
import Sortable from "sortablejs";

import { addMeeting, getMeeting, updateMeeting } from "@/api/meeting/meeting";
import { Meeting, MeetingForm } from "@/types/meeting/meeting";
import FileUpload from "@/components/FileUpload";
import { getAuditedByPlanId } from "@/api/meeting/topicMeeting";
import {
  listAllMeetingPlan,
  listAllMeetingPlanWithDelay
} from "@/api/meeting/topicMeetingPlan";
import { TopicMeetingPlan } from "@/types/meeting/topicMeetingPlan";
import { MeetingGroup } from "@/types/meeting/meetingGroup";
import { placeList } from "@/api/system/dict/data";
import {
  listMeetingGroupSelf,
  getMeetingGroupUser
} from "@/api/meeting/meetingGroup";
import UserSelectTree from "@/components/UserSelectTree/index.vue";
import { useGlobal } from "@pureadmin/utils";

const emit = defineEmits(["confirm"]);
defineOptions({
  name: "MeetingEditDialog"
});
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_notify, meeting_type } = $useDict(
  "meeting_notify",
  "meeting_type"
);
const visible = defineModel<boolean>("visible", { default: false });
const loadingInfo = ref(false);
const loadData = ref(false);
const planOptions = ref<TopicMeetingPlan[]>();
const groupUsers = ref<MeetingGroup[]>();
const meetingPlace = ref();

// 待选议题列表
const candidateTopics = ref([]);
const loadingCandidates = ref(false);
// 用于记录每个预会议对应的已选议题
const planTopicMap = ref(new Map());

const props = defineProps({
  data: {
    type: Meeting,
    default: () => {
      return new Meeting();
    }
  }
});

// 表单信息
const form = ref<MeetingForm>(new MeetingForm());
const formRef = ref<FormInstance>();
const loading = ref(false);
const userTag = ref<string>("read");
const groupLoading1 = ref(false);
const groupLoading2 = ref(false);
const meetingMessage = ref();
// 表单校验
const rules = ref({
  meetingTitle: [
    { required: true, message: "会议标题不能为空", trigger: "blur" }
  ],
  meetingDate: [
    { required: true, message: "会议时间不能为空", trigger: "blur" }
  ],

  meetingType: [
    { required: true, message: "会议类型不能为空", trigger: "change" }
  ],

  meetingLocation: [
    { required: true, message: "会议地点不能为空", trigger: "change" }
  ],
  userId: [{ required: true, message: "主持人不能为空", trigger: "change" }],
  registerId: [
    { required: true, message: "记录人不能为空", trigger: "change" }
  ],
  meetingNotify: [
    { required: true, message: "会议通知不能为空", trigger: "change" }
  ],
  fileList: [{ required: true, message: "会议文件不能为空", trigger: "blur" }],
  attendList: [{ required: true, message: "参会人员不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return form.value?.id ? "修改会议信息" : "添加会议信息";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: Meeting) {
  // 会议分组
  listMeetingGroupUser();
  // 会议地点
  placeMeetingAddr();
  if (!visible.value) return;
  reset();
  if (data.id) {
    handleUpdate(data);
  } else {
    listMeetingPlan();
    loadingInfo.value = true;
  }
}

// 获得会议地点
function placeMeetingAddr() {
  placeList().then(res => {
    meetingPlace.value = res.data;
  });
}

// 获得有效期内的预会议
function listMeetingPlan() {
  listAllMeetingPlan().then(resp => {
    planOptions.value = resp.data;
  });
}
// 获得有效期内的预会议与已选中的预会议
function listMeetingPlanAddId(planId: number) {
  listAllMeetingPlanWithDelay(planId).then(res => {
    planOptions.value = res.data;
  });
}

// 获得会议分组
function listMeetingGroupUser() {
  listMeetingGroupSelf().then(resp => {
    groupUsers.value = resp.data;
  });
}

/** 表单重置 */
function reset() {
  form.value = new MeetingForm();
  form.value.readList = [];
  form.value.attendList = [];
  meetingMessage.value = "";
  userTag.value = "attend";
  planTopicMap.value = new Map();
  candidateTopics.value = [];
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 修改会议类型 */
function changeMeetingType() {
  // 选择党委中心组学习时，清空预会议和议题
  if (form.value.meetingType === "4") {
    form.value.meetingPlanId = null;
    form.value.topicMeeting = [];
  }
}

// 保存当前预会议的议题选择
function saveCurrentTopics() {
  if (!form.value.meetingPlanId) return;

  // 记录当前预会议的已选议题
  if (form.value.topicMeeting?.length) {
    planTopicMap.value.set(form.value.meetingPlanId, [
      ...form.value.topicMeeting
    ]);
  }
}

// 切换预会议
function changePlan() {
  // 清空当前已选议题
  form.value.topicMeeting = [];
  if (!form.value.meetingPlanId) return;

  // 如果有之前选择过的议题，恢复显示
  if (planTopicMap.value.has(form.value.meetingPlanId)) {
    form.value.topicMeeting = [
      ...planTopicMap.value.get(form.value.meetingPlanId)
    ];
  }

  // 设置会议类型
  const plan = planOptions.value.find(
    item => item.id === form.value.meetingPlanId
  );
  if (plan) {
    form.value.meetingType = plan.type.toString();
  }
  // 加载待选区议题
  loadCandidateTopics();

  // 如果预会议有标题且当前会议标题未设置或用户同意修改,则使用预会议标题
  if (plan.title) {
    if (!form.value.meetingTitle) {
      form.value.meetingTitle = plan.title;
    } else {
      confirm("是否使用预会议标题替换当前会议标题?", {
        type: "warning"
      })
        .then(() => {
          form.value.meetingTitle = plan.title;
        })
        .catch(() => {});
    }
  }
}

// 加载待选区议题
function loadCandidateTopics() {
  if (!form.value.meetingPlanId) return;

  loadingCandidates.value = true;

  getAuditedByPlanId(form.value.meetingPlanId).then(res => {
    candidateTopics.value = res.data || [];
    loadingCandidates.value = false;
  });
}

// 判断议题是否已经被选择
function isTopicSelected(topicId) {
  return (
    form.value.topicMeeting &&
    form.value.topicMeeting.some(item => item.id === topicId)
  );
}

// 添加议题到已选区
function addTopic(topic) {
  // 检查是否已经添加过
  if (form.value.topicMeeting.some(item => item.id === topic.id)) {
    message("该议题已添加", { type: "warning" });
    return;
  }

  // 添加到已选区（添加一个复制，而不是引用，避免修改待选区数据）
  form.value.topicMeeting.push({ ...topic });

  // 更新当前预会议的议题映射
  saveCurrentTopics();
}

// 从已选区移除议题
function handleRemove(id) {
  loadData.value = true;

  // 从已选区移除
  form.value.topicMeeting = form.value.topicMeeting.filter(
    option => option.id !== id
  );

  // 更新当前预会议的议题映射
  saveCurrentTopics();

  loadData.value = false;
}

// 添加参会人员
function addAttendUsers(groupId: number) {
  if (!groupId) return;
  groupLoading1.value = true;
  getMeetingGroupUser(groupId).then(res => {
    // 追加不重复的人员
    const attendList = form.value.attendList || [];
    const newData = res.data;
    const filteredData = newData.filter(item => !attendList.includes(item));
    attendList.push(...filteredData);
    form.value.attendList = [...attendList];
    groupLoading1.value = false;
  });
}

// 添加列席人员
function addReadUsers(groupId: number) {
  if (!groupId) return;
  groupLoading2.value = true;
  getMeetingGroupUser(groupId).then(res => {
    // 追加不重复的人员
    const readList = form.value.readList || [];
    const newData = res.data;
    const filteredData = newData.filter(item => !readList.includes(item));
    readList.push(...filteredData);
    form.value.readList = [...readList];
    groupLoading2.value = false;
  });
}

/** 修改按钮操作 */
async function handleUpdate(row: Meeting) {
  getMeeting(row.id).then(response => {
    form.value = response.data;
    form.value.meetingType = response.data.meetingType.toString();
    // 只显示会议资料
    form.value.fileList = form.value.fileList.filter(
      item => item.fileType === "main"
    );
    if (form.value.meetingPlanId) {
      // 数据格式转换：将 TopicMeetingLink 转换为 TopicMeetingVo
      if (form.value.topicMeeting && form.value.topicMeeting.length) {
        // 将已选择的议题转换为标准格式
        form.value.topicMeeting = form.value.topicMeeting
          // 重要‼️预会议不为空id不为空，为空表示会议资料直接上传的特殊议题
          .filter(topic => topic.planId)
          .map(topic => {
            return {
              id: topic.topicId || topic.id, // 使用topicId作为id，如果没有则使用原id
              planId: topic.planId,
              topicTitle: topic.topicTitle,
              reportUserId: topic.reportUserId,
              reportUserName: topic.reportUserName,
              topicOrder: topic.topicOrder,
              createTime: topic.createTime,
              creatorDept: topic.creatorDept,
              status: topic.status
            };
          });

        // 记录已选议题到映射中
        planTopicMap.value.set(form.value.meetingPlanId, [
          ...form.value.topicMeeting
        ]);
      }

      // 获得有效期内预会议下拉以及已选的预会议
      listMeetingPlanAddId(form.value.meetingPlanId);

      // 加载待选区议题
      loadCandidateTopics();
    } else {
      // 获得有效期内预会议下拉
      listMeetingPlan();
    }

    loadingInfo.value = true;
  });
}

/** 提交按钮 */
function submitForm(publish = false) {
  formRef.value.validate(valid => {
    if (valid) {
      // 选择了预会议，检查议题是否已选择
      if (form.value.meetingPlanId && form.value.topicMeeting?.length === 0) {
        message("请至少选择一个议题", { type: "warning" });
        return;
      }

      loading.value = true;
      if (form.value.id != undefined) {
        updateMeeting(form.value, publish)
          .then(() => {
            message("修改成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            reset();
            loading.value = false;
          });
      } else {
        addMeeting(form.value, publish)
          .then(() => {
            message("新增成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            reset();
            loading.value = false;
          });
      }
    }
  });
}

function cancel() {
  loadingInfo.value = false;
  visible.value = false;
}

function handleTabClick() {
  // Tab切换时可以执行一些操作
}

function changeNotify(value: any) {
  if (
    form.value.meetingDate === undefined &&
    form.value.meetingTime === undefined &&
    form.value.meetingLocation === undefined &&
    form.value.meetingTitle === undefined
  ) {
    message("请先选择会议时间、会议地点，填写会议标题", { type: "warning" });
    return;
  }
  let notify = value;
  if (!form.value.meetingDate || !form.value.meetingTime) {
    message("请先选择会议时间", { type: "warning" });
    return;
  }
  notify = notify.replace("{{date}}", form.value.meetingDate);
  // 处理时间
  const formatTime = () => {
    const start = form.value.meetingTime[0].substring(0, 5);
    const end = form.value.meetingTime[1].substring(0, 5);
    return `${start} 至 ${end}`;
  };
  notify = notify.replace("{{time}}", formatTime);
  if (!form.value.meetingLocation) {
    message("请先选择会议地点", { type: "warning" });
    return;
  }
  const place = meetingPlace.value.find(
    item => item.dictValue === form.value.meetingLocation
  );
  notify = notify.replace("{{meetingLocation}}", place.dictLabel);
  if (!form.value.meetingTitle) {
    message("请先填写会议标题", { type: "warning" });
    return;
  }
  notify = notify.replace("{{meetingTitle}}", form.value.meetingTitle);
  form.value.meetingNotify = notify;
}

const dragTable = ref();
const itemKey = ref();

const rowDrop = () => {
  nextTick(() => {
    const tbody = dragTable.value.$el.querySelector(
      ".el-table__body-wrapper tbody"
    );
    if (tbody) {
      Sortable.create(tbody, {
        animation: 300,
        handle: ".drag-btn",
        onEnd({ newIndex, oldIndex }) {
          const currentRow = form.value.topicMeeting.splice(oldIndex, 1)[0];
          form.value.topicMeeting.splice(newIndex, 0, currentRow);
          itemKey.value = Math.random();
          // 拖拽排序后保存当前议题顺序
          saveCurrentTopics();
        }
      });
    }
  });
};
</script>

<template>
  <!-- 添加或修改会议信息对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="60%"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会议类型" prop="meetingType">
            <el-select
              v-model="form.meetingType"
              placeholder="请选择会议类型"
              @change="changeMeetingType"
            >
              <el-option
                v-for="item of meeting_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >{{ item.label }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会议标题" prop="meetingTitle">
            <el-input
              v-model="form.meetingTitle"
              placeholder="请输入会议标题"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会议地点" prop="meetingLocation">
            <el-select
              popper-class="custom-select-popper"
              v-model="form.meetingLocation"
              placeholder="请选择会议地点"
            >
              <el-option
                v-for="item of meetingPlace"
                :key="item.dictCode"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主持人" prop="userId">
            <user-select-tree
              :key="form.userId"
              :multiple="false"
              import-type="user"
              v-model:model-value="form.userId"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="记录人" prop="registerId">
            <user-select-tree
              :key="form.registerId"
              :multiple="false"
              import-type="user"
              v-model="form.registerId"
            />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item label="投票功能" prop="isVote">-->
        <!--            <el-switch-->
        <!--              v-model="form.isVote"-->
        <!--              active-value="1"-->
        <!--              inactive-value="0"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="会议时间" prop="meetingDate" style="width: 100%">
            <div class="flex">
              <el-date-picker
                v-model="form.meetingDate"
                class="!w-1/3"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择会议日期"
              />

              <el-time-picker
                class="flex-1 ml-2"
                v-model="form.meetingTime"
                is-range
                range-separator="到"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm:00"
              />
            </div> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="会议通知"
            prop="meetingNotify"
            style="width: 100%"
          >
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入"
              v-model="form.meetingNotify"
            />
            <!-- 快捷选择通知模板 -->
            <div class="mt-2" v-if="meeting_notify?.length > 0">
              <div class="flex mb-3">
                <span class="w-[150px] pt-1 text-base text-gray-600"
                  >通知模板快捷选择:</span
                >
                <div class="flex-1">
                  <div
                    v-for="item in meeting_notify"
                    :key="item.value"
                    class="mb-1"
                  >
                    <el-button
                      @click="changeNotify(item.label)"
                      class="text-base"
                    >
                      {{ item.elTagClass }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="会议内容" prop="meetingBrief">
        <el-input
          v-model="form.meetingBrief"
          type="textarea"
          placeholder="请输入内容"
          rows="4"
        />
      </el-form-item>
      <el-form-item
        label="预会议"
        prop="meetingPlanId"
        v-if="form.meetingType && form.meetingType !== '4'"
      >
        <el-select
          popper-class="custom-select-popper"
          v-model="form.meetingPlanId"
          placeholder="请选择预会议"
          clearable
          @change="changePlan"
        >
          <el-option
            v-for="item of planOptions"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          >
            <div class="flex gap-10 justify-between">
              <span>{{ item.title }}</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="截止时间"
                placement="right-start"
              >
                <el-tag class="mt-1">
                  {{ item.lastTime }}
                </el-tag>
              </el-tooltip>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 议题选择区域 -->
      <div v-if="form.meetingType !== '4' && form.meetingPlanId">
        <el-row :gutter="20">
          <el-col>
            <el-form-item label="可选议题">
              <el-table
                :data="candidateTopics"
                border
                v-loading="loadingCandidates"
                style="width: 100%"
                max-height="300px"
              >
                <el-table-column type="index" width="50" align="center" />
                <el-table-column
                  prop="topicTitle"
                  label="议题名称"
                  align="left"
                  minWidth="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="createTime"
                  label="上传时间"
                  align="center"
                  width="230"
                  minWidth="150"
                />
                <el-table-column
                  prop="reportUserName"
                  align="center"
                  width="100"
                  label="汇报人"
                />
                <el-table-column align="center" label="操作" width="100">
                  <template #default="{ row }">
                    <el-button
                      type="text"
                      @click="addTopic(row)"
                      :disabled="isTopicSelected(row.id)"
                    >
                      {{ isTopicSelected(row.id) ? "已选择" : "添加" }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col>
            <el-form-item label="已选议题" prop="topicMeeting">
              <el-table
                :data="form.topicMeeting"
                border
                v-loading="loadData"
                style="width: 100%"
                ref="dragTable"
                :key="itemKey"
                max-height="300px"
              >
                <el-table-column type="index" width="50" align="center">
                  <template #default="{ row }">
                    <div class="flex items-center">
                      <iconify-icon-online
                        icon="icon-park-outline:drag"
                        class="drag-btn cursor-grab"
                        @mouseenter="rowDrop"
                      />
                      <span>
                        {{ form.topicMeeting.indexOf(row) + 1 }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="topicTitle"
                  label="议题名称"
                  align="left"
                  minWidth="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="createTime"
                  label="上传时间"
                  align="center"
                  width="230"
                  minWidth="150"
                />
                <el-table-column
                  prop="reportUserName"
                  align="center"
                  width="100"
                  label="汇报人"
                />
                <el-table-column align="center" label="操作" width="100">
                  <template #default="{ row }">
                    <el-button type="text" @click="handleRemove(row.id)"
                      >移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-form-item label="会议资料" prop="fileList">
        <FileUpload v-model="form.fileList" :fileType="['pdf']" />
      </el-form-item>
      <el-tabs v-model="userTag" @tab-click="handleTabClick">
        <el-tab-pane label="参会人员" name="attend">
          <el-form-item label="参会人员" prop="attendList">
            <UserSelectTree
              v-model="form.attendList"
              v-if="loadingInfo"
              :import-type="'user'"
              :isSearch="true"
              :multiple="true"
              :enable-sort="true"
            />
            <!-- 快捷选择分组 -->
            <div class="mt-2" v-if="groupUsers?.length > 0">
              <div class="flex flex-wrap gap-2 items-center mb-3">
                <span class="text-base text-gray-600">人员分组快捷选择:</span>
                <el-button
                  v-for="group in groupUsers"
                  :key="group.id"
                  :loading="groupLoading1"
                  @click="addAttendUsers(group.id)"
                >
                  {{ group.name }}
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="列席人员" name="read">
          <el-form-item label="列席人员" prop="readList">
            <UserSelectTree
              v-model="form.readList"
              v-if="loadingInfo"
              :import-type="'user'"
              :isSearch="true"
              :multiple="true"
              :enable-sort="true"
            />
            <!-- 快捷选择分组 -->
            <div class="mt-2" v-if="groupUsers?.length > 0">
              <div class="flex flex-wrap gap-2 items-center mb-3">
                <span class="text-base text-gray-600">人员分组快捷选择:</span>
                <el-button
                  v-for="group in groupUsers"
                  :key="group.id"
                  :loading="groupLoading2"
                  @click="addReadUsers(group.id)"
                >
                  {{ group.name }}
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm()"
          >保 存</el-button
        >
        <el-button
          :loading="loading"
          type="primary"
          @click="submitForm(true)"
          v-if="form.status === '0' || !form.id"
          >保存并发布</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
