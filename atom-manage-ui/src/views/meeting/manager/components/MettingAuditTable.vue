<script lang="ts" setup>
import { getToken } from "@/utils/auth";
import { message } from "@/utils/message";
import { computed, ref, watch } from "vue";
import { calculateFileMD5 } from "@/utils/file";
import { FileUploadResult } from "@/types/system/sys-oss";
import download from "@/plugins/download";
import { find, remove } from "lodash";
import OssUtils from "@/utils/oss";
import arrowUpIcon from "@/assets/svg/arrow-up-filling.svg?component";
import { auditFile } from "@/api/meeting/file";

const emit = defineEmits<{
  (e: "update:modelValue", value: FileUploadResult[]): void;
  (e: "update:editStatus", value: boolean): void;
  (e: "confirm"): void;
}>();

const props = defineProps({
  // 值
  modelValue: [String, Object, Array],
  disabled: {
    type: Boolean,
    default: false
  },
  fileSize: {
    type: Number,
    default: 50
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "xlsx", "docx", "ppt", "pptx", "txt", "pdf"]
  },
  editStatus: {
    type: Boolean,
    default: false
  }
});

const uploadFileUrl = import.meta.env.VITE_BASE_URL + "/system/oss/upload";
const fileList = ref<FileUploadResult[]>([]);
const headers = { Authorization: "Bearer " + getToken() };
const uploadRef = ref(null);
const editStatusChild = ref(false);
const editIndex = ref(null);
const oldFile = ref<FileUploadResult>();
const editFile = ref<FileUploadResult>();
const cancelController = ref({});
watch(
  () => props.modelValue,
  async val => {
    if (val) {
      const list = Array.isArray(val)
        ? val
        : (props.modelValue as string).split(",");
      fileList.value = list;
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.editStatus,
  async val => {
    if (val) {
      editStatusChild.value = val;
    } else {
      editStatusChild.value = false;
      return false;
    }
  },
  { deep: true, immediate: true }
);

async function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType) {
    const fileName = file.name.split(".");
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.includes(fileExt);
    if (!isTypeOk) {
      message(`文件格式不正确, 请上传${props.fileType.join("/")}格式的文件`, {
        type: "error"
      });
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isSizeOk = file.size / 1024 / 1024 < props.fileSize;
    if (!isSizeOk) {
      message(`文件大小超过${props.fileSize}MB, 请重新上传`, { type: "error" });
      return false;
    }
  }

  try {
    const md5 = await calculateFileMD5(file);
    const isExist = await checkFileExist(md5, file);
    if (isExist) {
      message("该文件已存在", { type: "error" });
      return false;
    }
  } catch {}
}

const checkFileExist = async (md5: string, file: any) => {
  const md5File = fileList.value.find(item => item.md5 === md5);
  if (md5File) return true;
  file.md5 = md5;
  return false;
};

function handleExceed() {
  message("超出文件数量限制", { type: "warning" });
}

function handleUploadError() {
  message("上传失败", { type: "error" });
}

function downloadFile(file: any) {
  download.oss(file.ossId);
}

const handleRemove = (file: any, fileList: any[]) => {
  const findex = fileList.map(f => f.ossId).indexOf(file.ossId);
  if (findex > -1) {
    fileList.splice(findex, 1);
    emit("update:modelValue", fileList);
  }
  sortProblemForms();
};

// 覆盖默认上传
function handleHttpRequest(files): any {
  const { file } = files;
  const foundFile: any = find(fileList.value, { uid: file.uid });
  const fileName = file.name;
  if (file.status === "exist") {
    file.originalName = fileName;
    handleSuccessUploadFile(foundFile, file);
    return;
  }

  OssUtils.upload({
    file: file,
    filename: fileName,
    serverUploadUrl: uploadFileUrl,
    cancel: f => {
      cancelController.value[foundFile.uid] = f;
    },
    onProgress: ({ percent }) => {
      files.onProgress({ percent: percent });
    }
  })
    .then(res => {
      // 根据file.uid查询fileList中的文件
      if (res.code === 200) {
        // 如果上传成功的文件的ossId在fileList中存在，则阻止上传
        if (find(fileList.value, { ossId: res.data.ossId })) {
          fileRejecWithUploadSuccessHas(file, "该文件已存在");
          return;
        }

        const data = res.data;
        data.md5 = file.md5;
        data.fileSize = file.size;
        handleSuccessUploadFile(foundFile, data);
      } else {
        fileRejecWithUploadSuccessHas(file, res.msg);
      }
    })
    .catch(msg => {
      message(msg, { type: "error" });
    });
}

// 如果上传成功的文件的ossId在fileList中存在，则阻止上传
function fileRejecWithUploadSuccessHas(file: any, error: string) {
  uploadRef.value.abort(file);
  remove(fileList.value, { uid: file.uid });
  message(error, { type: "error" });
  return;
}

function handleSuccessUploadFile(foundFile: any, data: FileUploadResult) {
  // 如果未找到fileList中的文件，则添加
  if (!foundFile) {
    foundFile = data;
    fileList.value.push(data);
  }

  foundFile.status = "success";
  foundFile.url = data.url;
  foundFile.fileSize = foundFile.size;
  foundFile.ossId = data.ossId;
  foundFile.md5 = data.md5;
  foundFile.fileType = data.fileSuffix;
  foundFile.fileSize = data.fileSize;
  emit("update:modelValue", fileList.value);
}

function handleReadyUpdate(file: any, fileList: any[]) {
  editIndex.value = fileList.map(f => f.ossId).indexOf(file.ossId);
  editStatusChild.value = true;
  emit("update:editStatus", editStatusChild.value);
  oldFile.value = { ...file };
  editFile.value = file;
}

const isEditing = index => {
  return index === editIndex.value && editStatusChild.value;
};

function handleCloseEditFileName(file: any) {
  file.name = oldFile.value.name;
  editStatusChild.value = false;
  emit("update:editStatus", editStatusChild.value);
}

// 用于分割文件名和扩展名的函数
function splitFileName(fullName) {
  const dotIndex = fullName.lastIndexOf(".");
  if (dotIndex === -1) return [fullName, ""]; // 没有扩展名
  const name = fullName.substring(0, dotIndex);
  const extension = fullName.substring(dotIndex); // 包括点
  return [name, extension];
}

const editFileName = computed({
  get() {
    // 仅获取文件名，不包括扩展名
    const [name] = splitFileName(editFile.value.name);
    return name;
  },
  set(newValue) {
    // 更新文件名时保持扩展名不变
    const [, extension] = splitFileName(editFile.value.name);
    editFile.value.name = newValue + extension;
  }
});

const handleUploadChange = async (status, file) => {
  if (status == "close") {
    await confirm("取消上传");
    closeUpload(file.uid);
  }
};

const closeUpload = uid => {
  if (cancelController.value[uid]) {
    cancelController.value[uid].abort();
    delete cancelController.value[uid];
    fileList.value = fileList.value.filter(item => item.uid != uid);
  }
};

function adjustProblemIndex(index) {
  // 当前对象前面的order + 1 后面的order不变
  for (let i = 0; i < index; i++) {
    fileList.value[i].fileOrder++;
  }
  // 当前对象的checkOrder减1
  fileList.value[index].fileOrder--;
  sortProblemForms();
}

function sortProblemForms() {
  // 排序
  fileList.value.sort((a, b) => a.fileOrder - b.fileOrder);

  // 然后 更新每个对象的index属性
  for (let i = 0; i < fileList.value.length; i++) {
    fileList.value[i].index = i;
    fileList.value[i].fileOrder = i;
  }
}

function handlePass(file: any) {
  auditFile(file.id, "2")
    .then(res => {
      if (res.code == 200) {
        message("审核成功", { type: "success" });
      } else {
        message("审核失败", { type: "error" });
      }
    })
    .finally(() => {
      emit("confirm");
    });
}

function handleReject(file: any) {
  auditFile(file.id, "9")
    .then(res => {
      if (res.code == 200) {
        message("审核成功", { type: "success" });
      } else {
        message("审核失败", { type: "error" });
      }
    })
    .finally(() => {
      emit("confirm");
    });
}
</script>

<template>
  <div>
    <el-table :data="fileList" style="width: 100%" max-height="250">
      <el-table-column fixed type="index" label="序号" width="80" />
      <el-table-column
        prop="name"
        show-overflow-tooltip
        label="附件名称"
        minWidth="120"
      >
        <template #default="scope">
          <a
            style="text-decoration: underline"
            @click="downloadFile(scope.row)"
            >{{ scope.row.name }}</a
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="url"
        label="附件地址"
        show-overflow-tooltip
        minWidth="180"
      >
        <template #default="scope">
          <el-progress
            v-if="scope.row.status == 'uploading'"
            :percentage="scope.row.percentage"
            size="small"
          />
          <span v-else>{{ scope.row.url?.split("?")[0] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传人" width="80" prop="createName" />
      <el-table-column fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            size="small"
            @click="handlePass(scope.row)"
          >
            通过
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            @click="handleReject(scope.row)"
          >
            退回
          </el-button>
          <!--          <template v-if="scope.row.status == 'success'">-->
          <!--            <template v-if="isEditing(scope.$index)">-->
          <!--              <el-button-->
          <!--                link-->
          <!--                type="primary"-->
          <!--                size="small"-->
          <!--                @click="editStatusChild = false"-->
          <!--              >-->
          <!--                确认-->
          <!--              </el-button>-->
          <!--              <el-button-->
          <!--                type="primary"-->
          <!--                size="small"-->
          <!--                @click="handleCloseEditFileName(scope.row)"-->
          <!--              >-->
          <!--                取消-->
          <!--              </el-button>-->
          <!--            </template>-->
          <!--            <template v-else>-->
          <!--              <el-button-->
          <!--                link-->
          <!--                type="primary"-->
          <!--                size="small"-->
          <!--                @click="handleReadyUpdate(scope.row, fileList)"-->
          <!--              >-->
          <!--                修改名称-->
          <!--              </el-button>-->
          <!--              <el-button-->
          <!--                link-->
          <!--                type="primary"-->
          <!--                size="small"-->
          <!--                @click="handleRemove(scope.row, fileList)"-->
          <!--              >-->
          <!--                删除-->
          <!--              </el-button>-->
          <!--            </template>-->
          <!--          </template>-->
          <!--          <template v-else>-->
          <!--            <span-->
          <!--              class="text-sm text-red-400 cursor-pointer"-->
          <!--              @click="handleUploadChange('close', scope.row)"-->
          <!--              >中止</span-->
          <!--            >-->
          <!--          </template>-->
        </template>
      </el-table-column>
    </el-table>

    <!--    <el-upload-->
    <!--      class="mt-4 upload-file"-->
    <!--      multiple-->
    <!--      :action="'#'"-->
    <!--      :before-upload="handleBeforeUpload"-->
    <!--      :show-file-list="false"-->
    <!--      v-model:file-list="fileList"-->
    <!--      :headers="headers"-->
    <!--      :on-error="handleUploadError"-->
    <!--      :on-exceed="handleExceed"-->
    <!--      :on-remove="handleRemove"-->
    <!--      :on-preview="downloadFile"-->
    <!--      :http-request="handleHttpRequest"-->
    <!--      ref="uploadRef"-->
    <!--      :disabled="disabled"-->
    <!--    >-->
    <!--      &lt;!&ndash; 上传按钮 &ndash;&gt;-->
    <!--      <slot name="title">-->
    <!--        <el-button plain type="primary"> 添加附件 </el-button>-->
    <!--      </slot>-->
    <!--      &lt;!&ndash; 上传提示 &ndash;&gt;-->
    <!--      <template #tip />-->
    <!--    </el-upload>-->
  </div>
</template>
<style scoped lang="scss">
.upload-file {
  width: 100%;
}
.el-upload-list__item-file-name {
  word-wrap: break-word;
  max-width: 100%;
  display: inline-flex;
}
</style>
