<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { FormInstance } from "element-plus";

import { getMeeting, getLinkedMeetingFiles } from "@/api/meeting/meeting";
import { Meeting, MeetingForm } from "@/types/meeting/meeting";
import { useGlobal } from "@pureadmin/utils";
import { selectDictLabel } from "@/utils/atom";
import { getUserInfo } from "@/api/system/user";
import { FileUploadResult } from "@/types/system/sys-oss";
import download from "@/plugins/download";

defineOptions({
  name: "MeetingPreviewDialog"
});

const visible = defineModel<boolean>("visible", { default: false });
const loadingInfo = ref(false);
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_type, meeting_place } = $useDict(
  "meeting_type",
  "meeting_place"
);

const props = defineProps({
  data: {
    type: Meeting,
    default: () => {
      return new Meeting();
    }
  }
});

// 表单信息
const form = ref<MeetingForm>(null);
const formRef = ref<FormInstance>();
const joinUserNames = ref<string[]>([]);
const readUserNames = ref<string[]>([]);
const attendUserNames = ref<string[]>([]);
const fileList = ref<FileUploadResult[]>([]);

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePreview(props.data)
);

function handlePreview(data: Meeting) {
  if (!visible.value) return;
  reset();
  getMeeting(data.id).then(response => {
    form.value = response.data;
    loadingInfo.value = true;
    getUserNameList();
    getFileList();
  });
}

function getUserNameList() {
  if (form.value.joinList.length > 0) {
    getUserInfo(form.value.joinList).then(response => {
      joinUserNames.value = response.data.map(item => item.name);
    });
  }
  if (form.value.readList.length > 0) {
    getUserInfo(form.value.readList).then(response => {
      readUserNames.value = response.data.map(item => item.name);
    });
  }
  if (form.value.attendList.length > 0) {
    getUserInfo(form.value.attendList).then(response => {
      attendUserNames.value = response.data.map(item => item.name);
    });
  }
}

function getFileList() {
  if (form.value.id) {
    getLinkedMeetingFiles({ meetingId: form.value.id }).then(response => {
      fileList.value = response.data;
    });
  }
}

// 切分时间
function toHourMinute(time: string): string {
  const parts = time.split(":");
  return parts[0] + ":" + parts[1];
}

const timeRange = computed(() => {
  if (!form.value.meetingTime) return "";
  return (
    toHourMinute(form.value.meetingTime[0]) +
    "至" +
    toHourMinute(form.value.meetingTime[1])
  );
});
/** 表单重置 */
function reset() {
  form.value = new MeetingForm();
  joinUserNames.value = [];
  readUserNames.value = [];
  attendUserNames.value = [];
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改会议信息对话框 -->
  <el-dialog
    title="查看会议信息"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="form" label-width="120px">
      <el-form-item label="会议标题" prop="meetingTitle">
        {{ form.meetingTitle }}
      </el-form-item>

      <el-form-item label="会议类型" prop="meetingTypeText">
        <el-tag size="small">{{ form.meetingTypeText }}</el-tag>
      </el-form-item>

      <el-form-item label="会议地点" prop="meetingLocation">
        {{ selectDictLabel(meeting_place, form.meetingLocation) }}
      </el-form-item>

      <el-form-item label="会议时间" prop="meetingDate">
        {{ form.meetingDate }} {{ timeRange }}
      </el-form-item>

      <el-form-item label="列席人员" prop="readList">
        {{ readUserNames?.join(",") }}
      </el-form-item>

      <el-form-item label="参会人员" prop="attendList">
        {{ attendUserNames?.join(",") }}
      </el-form-item>

      <el-form-item label="会议内容" prop="meetingBrief">
        <div>
          {{ form.meetingBrief }}
        </div>
      </el-form-item>

      <el-form-item
        label="议题"
        prop="topicMeeting"
        v-if="form.meetingType != '4'"
      >
        <el-table :data="form.topicMeeting" border style="width: 100%">
          <el-table-column type="index" width="50" align="center" />
          <el-table-column
            prop="topicTitle"
            label="议题名称"
            align="left"
            minWidth="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="上传时间"
            align="center"
            width="230"
            minWidth="150"
          />
          <el-table-column
            prop="reportUserName"
            align="center"
            label="汇报人"
            width="120"
          />
        </el-table>
      </el-form-item>
    </el-form>

    <div v-if="fileList.length > 0">
      <div class="mb-2 border-t border-gray-300 border-solid" />
      <div class="py-2 text-lg">会议附件</div>
      <el-table :data="fileList" style="width: 100%" max-height="250">
        <el-table-column fixed type="index" label="序号" width="80" />
        <el-table-column
          prop="name"
          show-overflow-tooltip
          label="附件名称"
          minWidth="120"
        >
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="url"
          label="操作"
          show-overflow-tooltip
          minWidth="180"
        >
          <template #default="{ row }">
            <el-link @click="download.oss(row.ossId, null)">下载</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
