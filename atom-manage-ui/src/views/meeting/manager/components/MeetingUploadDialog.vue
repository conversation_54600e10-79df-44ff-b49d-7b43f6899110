<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";

import { addFileList } from "@/api/meeting/meeting";
import { useGlobal } from "@pureadmin/utils";
import FileUpload from "@/components/FileUpload";
import { Meeting, uploadFile } from "@/types/meeting/meeting";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "FileAddDialog"
});

const visible = defineModel<boolean>("visible", { default: false });
const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { meeting_file_type } = $useDict("meeting_file_type");
const filteredMeetingFileType = computed(() => {
  return meeting_file_type.value?.filter(item => item.value !== "topic") ?? [];
});
const fileType = ref(["pdf"]);
const props = defineProps({
  data: {
    type: Meeting,
    default: () => {
      return new Meeting();
    }
  }
});

// 表单信息
const form = ref<uploadFile>(new uploadFile());
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单校验
const rules = ref({
  fileType: [{ required: true, message: "类型不能为空", trigger: "blur" }],
  fileList: [{ required: true, message: "附件不能为空", trigger: "blur" }]
});

const title = computed(() => {
  return "添加文件";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: Meeting) {
  if (!visible.value) return;
  reset();
  form.value.meetingId = data.id;
}

/** 表单重置 */
function reset() {
  form.value = new uploadFile();
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      addFileList(form.value)
        .then(() => {
          message("新增成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}
function hanleFileType() {
  if (form.value.fileType === "video") {
    fileType.value = ["mp4", "mov", "mkv"];
  } else {
    fileType.value = ["pdf"];
  }
}

function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改文件对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
    class="w-[99/100] pl-8 pt-4"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="文件类型" prop="fileType">
        <el-select
          v-model="form.fileType"
          placeholder="请选择文件类型"
          @change="hanleFileType"
        >
          <el-option
            v-for="item of filteredMeetingFileType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="附件" prop="fileList">
        <FileUpload v-model="form.fileList" :fileType="fileType" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
