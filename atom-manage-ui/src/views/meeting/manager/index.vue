<script setup lang="ts">
import { computed, ref } from "vue";
import { useMeeting } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import MeetingEditDialog from "./components/MeetingEditDialog.vue";
import MeetingUploadDialog from "./components/MeetingUploadDialog.vue";
import MeetingPreviewDialog from "./components/MeetingPreviewDialog.vue";
import { useAncestorDetection } from "@/hooks/useAncestorState";

//** 会议信息 */
defineOptions({
  name: "Meeting"
});

const queryRef = ref();

const {
  multiple,
  visibleEditDialog,
  visibleUploadDialog,
  visiblePreviewDialog,
  loading,
  columns,
  pagination,
  queryParams,
  meetingData,
  meetingList,
  tableMaxHeight,
  mainRef,
  tableRef,
  calculateTableMaxHeight,
  handleCreate,
  handleDelete,
  handleUpdate,
  handleReUpload,
  handleSubmit,
  handleQuery,
  handleUploadFile,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange,
  getList
} = useMeeting();
</script>

<template>
  <div class="main" ref="mainRef">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color search-form"
    >
      <el-form-item label="会议标题" prop="meetingTitle">
        <el-input
          v-model="queryParams.meetingTitle"
          placeholder="请输入会议标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会议主要内容" prop="meetingBrief">
        <el-input
          v-model="queryParams.meetingBrief"
          placeholder="请输入会议主要内容"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      title="会议信息列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="primary"
          class="sm:shrink-button"
          v-auth="['meeting:manager:option']"
          :icon="useRenderIcon('ep:circle-plus')"
          @click="handleCreate()"
        >
          新增
        </el-button>
        <el-button
          type="danger"
          class="sm:shrink-button"
          plain
          :icon="useRenderIcon('ep:delete')"
          v-auth="['meeting:manager:option']"
          :disabled="multiple"
          @click="handleDelete()"
          >删除
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="left"
          :max-height="tableMaxHeight"
          ref="tableRef"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="meetingList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-if="row.timeOut"
              v-auth="['meeting:manager:edit']"
              @click="handleReUpload(row)"
              :icon="
                row.isReUpload === '1'
                  ? useRenderIcon('ep:turn-off')
                  : useRenderIcon('ep:open')
              "
            >
              {{ row.isReUpload === "1" ? "关闭上传" : "开启上传" }}
            </el-button>
            <el-button
              v-if="row.status === '0'"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:manager:option']"
              @click="handleSubmit(row)"
              :icon="useRenderIcon('ep:check')"
            >
              发布
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:manager:option']"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              修改
            </el-button>
            <el-popconfirm
              width="10.4167vw"
              title="是否确认删除?"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['meeting:manager:option']"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['meeting:manager:option']"
              @click="handleUploadFile(row)"
              :icon="useRenderIcon('ep:upload')"
            >
              添加附件
            </el-button>
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <meeting-edit-dialog
      v-model:visible="visibleEditDialog"
      :data="meetingData"
      @confirm="getList"
    />
    <meeting-upload-dialog
      v-model:visible="visibleUploadDialog"
      :data="meetingData"
    />

    <meeting-preview-dialog
      v-model:visible="visiblePreviewDialog"
      :data="meetingData"
    />
  </div>
</template>

<style lang="scss" scoped>
.buttons-container {
  display: flex;
  flex-wrap: wrap; /* 允许子项换行 */
  gap: 10px; /* 设置按钮之间的间隙 */
}
</style>
