import dayjs from "dayjs";
import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { selectDictLabel } from "@/utils/atom";
import { useUserStoreHook } from "@/store/modules/user";

const { userId: currentUserId } = useUserStoreHook();
import {
  delMeeting,
  listMeeting,
  publish,
  submit,
  updateReUpload
} from "@/api/meeting/meeting";
import { Meeting, MeetingQuery } from "@/types/meeting/meeting";
import DictTag from "@/components/DictTag";
import { BaseQuery } from "@/types";
import { useAncestorDetection } from "@/hooks/useAncestorState";
import { usePureTableMaxHeight } from "@/hooks/usePureTableMaxHeight";

export function useMeeting() {
  const queryParams = reactive<MeetingQuery>(new MeetingQuery());
  const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();

  const { MEETING_STATUS, meeting_place, meeting_type } = $useDict(
    "MEETING_STATUS",
    "meeting_place",
    "meeting_type"
  );
  const loading = ref(true);
  const visibleEditDialog = ref(false);
  const visibleUploadDialog = ref(false);
  const visiblePreviewDialog = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const meetingList = ref([]);
  const meetingData = ref<Meeting>();

  const pagination = reactive<PaginationProps & BaseQuery>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    orderByColumn: "meetingBeginTime",
    isAsc: "desc"
  });

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      align: "center",
      width: 70,
      formatter: () =>
        pagination.pageSize * (pagination.currentPage - 1) + 1 + ""
    },
    {
      label: "会议标题",
      prop: "meetingTitle",
      align: "left",
      minWidth: 120,
      cellRenderer: ({ row }) => {
        return (
          <span
            class="cursor-pointer text-blue-700"
            onClick={() => handlePreview(row)}
          >
            {row.meetingTitle}
          </span>
        );
      }
    },
    {
      label: "会议类型",
      prop: "meetingType",
      align: "center",
      width: 168,
      minWidth: 60,
      cellRenderer: ({ row }) => (
        <DictTag options={meeting_type} value={row.meetingType} />
      )
    },
    {
      label: "会议地点",
      prop: "meetingLocation",
      align: "left",
      minWidth: 100,
      formatter: row =>
        selectDictLabel(meeting_place.value, row.meetingLocation)
    },
    {
      label: "会议时间",
      prop: "meetingDate",
      align: "center",
      width: 230,
      minWidth: 135,
      cellRenderer: ({ row }) => (
        <span>
          {dayjs(row.meetingDate).format("YYYY-MM-DD")}{" "}
          {toHourMinute(row.meetingTime[0])} -{toHourMinute(row.meetingTime[1])}
        </span>
      )
    },
    {
      label: "会议状态",
      prop: "status",
      align: "center",
      width: 120,
      minWidth: 40,
      cellRenderer: ({ row }) => {
        return <DictTag options={MEETING_STATUS} value={row.status} />;
      }
    },
    {
      label: "操作",
      align: "left",
      width: 350,
      slot: "operation"
    }
  ];

  // 切分时间
  function toHourMinute(time: string): string {
    const parts = time.split(":");
    return parts[0] + ":" + parts[1];
  }
  /** 新增按钮操作 */
  function handleCreate() {
    visibleEditDialog.value = true;
    meetingData.value = new Meeting();
  }

  function handleDelete(row?: Meeting) {
    if (row?.id) {
      delMeeting(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除会议信息编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delMeeting(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: Meeting) {
    visibleEditDialog.value = true;
    meetingData.value = row;
  }

  function handleReUpload(row: Meeting) {
    const reUpload = row.isReUpload !== "1";
    updateReUpload({ id: row.id, reUpload: reUpload })
      .then(resp => {
        if (resp.code === 200) {
          message("操作成功", { type: "success" });
        } else {
          message("操作失败", { type: "error" });
        }
      })
      .finally(() => {
        getList();
      });
  }

  /**
   * 发布
   * @param row
   */
  function handlePublish(row: Meeting) {
    publish(row.id).then(() => {
      getList();
      message("发布成功", { type: "success" });
    });
  }

  async function getList() {
    loading.value = true;
    // 会议创建人查看
    queryParams.creatorId = Number(currentUserId);
    const { data, total } = await listMeeting(queryParams, pagination);
    meetingList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  function handleSubmit(row: Meeting) {
    submit(row.id).then(() => {
      getList();
      message("提交成功", { type: "success" });
    });
  }
  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "meeting/meeting/export",
      {
        ...queryParams
      },
      `meeting_${new Date().getTime()}.xlsx`
    );
  }

  // ** 上传更新文件
  function handleUploadFile(row: Meeting) {
    meetingData.value = row;
    visibleUploadDialog.value = true;
  }

  // ** 预览
  function handlePreview(row: Meeting) {
    meetingData.value = row;
    visiblePreviewDialog.value = true;
  }

  const tableMaxHeight = ref("unset");
  const mainRef = ref();
  const tableRef = ref();

  const { isInsideTarget } = useAncestorDetection("UserHomeLayout");
  function calculateTableMaxHeight() {
    if (isInsideTarget.value) {
      setTimeout(
        () => (tableMaxHeight.value = usePureTableMaxHeight(mainRef, tableRef))
      );
    }
  }

  onMounted(() => {
    getList();
    calculateTableMaxHeight();
  });

  return {
    single,
    multiple,
    visibleEditDialog,
    visibleUploadDialog,
    visiblePreviewDialog,
    loading,
    columns,
    pagination,
    queryParams,
    meetingData,
    meetingList,
    tableMaxHeight,
    mainRef,
    tableRef,
    calculateTableMaxHeight,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleReUpload,
    handleSubmit,
    handlePublish,
    handleQuery,
    resetQuery,
    handleExport,
    handleUploadFile,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList
  };
}
