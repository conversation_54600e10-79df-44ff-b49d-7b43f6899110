import { nextTick, onMounted, reactive, ref } from "vue";
import {
  TopicMeetingAudit,
  TopicMeetingAuditQuery
} from "@/types/meeting/topicMeetingAudit";
import { hasAuth } from "@/router/utils";

export function useTopicManager() {
  const queryParams = reactive<TopicMeetingAuditQuery>(
    new TopicMeetingAuditQuery()
  );

  const loading = ref(true);
  const visible = ref(false);
  const activeName = ref<"plan" | "collect" | "audit">("plan");
  const tabName = ref<string>("plan");
  const planRef = ref();
  const collectRef = ref();
  const auditRef = ref();
  const refs = {
    planRef,
    collectRef,
    auditRef
  };

  const single = ref(true);
  const multiple = ref(true);
  const topicMeetingList = ref([]);
  const topicMeetingData = ref<TopicMeetingAudit>();

  function afterTabChanged(tabName: string) {
    nextTick(() => {
      const ref = refs[`${tabName}Ref`];
      if (ref.value) {
        ref.value.handleGoBack();
        ref.value.getList();
        ref.value.calculateTableMaxHeight();
      }
    });
  }

  function handleClick(param: any) {
    afterTabChanged(param.paneName);
  }

  function handleAuth() {
    if (hasAuth("meeting:topicMeetingPlan:query")) {
      activeName.value = "plan";
    } else if (hasAuth("meeting:topicMeeting:query")) {
      activeName.value = "collect";
    } else if (hasAuth("meeting:topicMeetingAudit:query")) {
      activeName.value = "audit";
    }
    afterTabChanged(activeName.value);
  }

  onMounted(() => {
    handleAuth();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    queryParams,
    topicMeetingData,
    topicMeetingList,
    activeName,
    tabName,
    planRef,
    collectRef,
    auditRef,
    handleClick,
    handleAuth
  };
}
