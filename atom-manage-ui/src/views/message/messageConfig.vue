<script lang="ts" setup>
import ConfigEditDialog from "@/views/message/components/ConfigEditDialog.vue";
import { useMessageConfig } from "./config-hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ref } from "vue";
//** 消息推送主体 */
defineOptions({
  name: "MessageConfig"
});

const queryRef = ref();

const {
  loading,
  columns,
  MESSAGE_MODULE,
  MESSAGE_TYPE,
  MESSAGE_CLIENT_TYPE,
  visible,
  configData,
  pagination,
  queryParams,
  configList,
  handleDelete,
  handleCreate,
  handleUpdate,
  handleQuery,
  resetQuery,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange
} = useMessageConfig();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="模块" prop="messageModule" class="w-64">
        <el-select
          v-model="queryParams.messageModule"
          placeholder="请选择消息模块"
          class="w-full"
        >
          <el-option
            v-for="item in MESSAGE_MODULE"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="消息类型" prop="messageType" class="w-64">
        <el-select
          v-model="queryParams.messageType"
          class="w-full"
          clearable
          placeholder="请选择待办类型"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in MESSAGE_TYPE"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户端类型" prop="clientType" class="w-64">
        <el-select
          v-model="queryParams.clientType"
          class="w-full"
          clearable
          placeholder="请选择客户端类型"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="dict in MESSAGE_CLIENT_TYPE"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table-bar
      title="消息配置列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          v-auth="['message:config:option']"
          :icon="useRenderIcon(`ep:plus`)"
          plain
          type="primary"
          @click="handleCreate"
        >
          新增
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          border
          align-whole="center"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="configList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              v-auth="['message:config:option']"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              修改
            </el-button>
            <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['message:config:option']"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </pure-table-bar>

    <ConfigEditDialog
      v-model:visible="visible"
      :data="configData"
      @confirm="handleQuery"
    />
  </div>
</template>
