<template>
  <!-- 添加或修改待办配置对话框 -->
  <el-dialog
    :close="closeDialog"
    :close-on-click-modal="false"
    :open="reset"
    :title="dialogForm?.id ? '修改待办配置' : '添加待办配置'"
    v-model="visible"
    width="800px"
  >
    <el-form
      ref="formRef"
      label-position="right"
      label-width="120px"
      :model="dialogForm"
      :rules="rules"
      :size="(size as any)"
    >
      <el-form-item label="模块" prop="messageModule">
        <template #label>
          <span>
            <el-tooltip content="所属业务模块" placement="top">
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            模块
          </span>
        </template>
        <el-select v-model="dialogForm.messageModule" placeholder="请选择模块">
          <el-option
            v-for="dict in MESSAGE_MODULE"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="消息类型" prop="messageType">
        <template #label>
          <span>
            <el-tooltip content="消息业务类型细分" placement="top">
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            消息类型
          </span>
        </template>
        <el-select
          v-model="dialogForm.messageType"
          placeholder="请选择待办类型"
          @change="
            handleChangeTodoType(
              dialogForm.clientType,
              dialogForm.messageModule
            )
          "
        >
          <el-option
            v-for="dict in todoTypeList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="客户端类型" prop="clientType">
        <template #label>
          <span>
            <el-tooltip
              content="PC和移动端需要分别设置对应的组件，如PC端设置弹窗组件，移动端设置路由组件。"
              placement="top"
            >
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            客户端类型
          </span>
        </template>
        <el-select
          v-model="dialogForm.clientType"
          placeholder="请选择客户端类型"
          @change="
            handleChangeTodoType(
              dialogForm.clientType,
              dialogForm.messageModule
            )
          "
        >
          <el-option
            v-for="dict in MESSAGE_CLIENT_TYPE"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="组件类型" prop="componentType">
        <template #label>
          <span>
            <el-tooltip
              content="1.弹窗组件需要引用对于的dialog组件 2.路由组件需要填写操作对应的路由地址 3.自定义组件需要自己去实现跳转逻辑"
              placement="top"
            >
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            组件类型
          </span>
        </template>
        <el-select
          v-model="dialogForm.componentType"
          placeholder="请选择组件类型"
        >
          <el-option
            v-for="dict in MESSAGE_COMPONENT_TYPE"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="
          dialogForm.componentType == 'router' ||
          dialogForm.componentType == 'dialog'
        "
        label="组件路径"
        prop="componentUrl"
      >
        <template #label>
          <span>
            <el-tooltip
              :content="
                dialogForm.componentType == 'dialog'
                  ? '组件填写组件导入地址如：setting/todo/todoDailog.vue。注意组件一定要使用dialog.vue后缀；路由根据Menu菜单配置填写跳转路径'
                  : '填写路由地址'
              "
              placement="top"
            >
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            组件路径
          </span>
        </template>
        <el-input
          v-model="dialogForm.componentUrl"
          placeholder="请输入组件路径"
        />
      </el-form-item>
      <el-form-item
        label="关联业务表"
        prop="businessTable"
        v-if="dialogForm.componentType != 'none'"
      >
        <template #label>
          <span>
            <el-tooltip
              content="关联的业务表名称, 如： sys_user"
              placement="top"
            >
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            关联业务表
          </span>
        </template>
        <el-input
          v-model="dialogForm.businessTable"
          placeholder="请输入关联的业务表"
        />
      </el-form-item>
      <el-form-item
        label="参数"
        prop="componentParams"
        v-if="dialogForm.componentType != 'none'"
      >
        <template #label>
          <span>
            <el-tooltip placement="top">
              <template #content>
                弹窗或者路由跳转所需要的参数，构建一个map如: '{id: sql(id),
                username: sql(username), type: view, title:
                审核$(username)}'等。
                <br />
                获取待办详细时会将当前map构建在params参数中。
                <br />
                sql()包含的字段将搜索对应业务表的值插入。如businesstable为sys_user,sql(user_id)则会搜索用户表的user_id字段插入到map的id字段中
                <br />
                没有添加sql()的字段将以字符串形式直接渲染。
                包含$()字符的字段将最后渲染，只能使用构建map中存在的数据。注意$(fliedName)中fliedName为map中的key
              </template>
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            组件参数
          </span>
        </template>

        <el-input
          v-model="dialogForm.componentParams"
          placeholder="请输入参数 跳转或者对话框弹出需要的参数信息"
          type="textarea"
        />
      </el-form-item>

      <el-form-item
        v-if="dialogForm.componentType == 'dialog'"
        label="参数"
        prop="dialogfield"
      >
        <template #label>
          <span>
            <el-tooltip placement="top">
              <template #content>
                使用对话框组件时映射对应的对话框参数字段,暂时只有form(表单数据)和visible(对话框参数)，confirm(确认事件)
                close(取消事件).
                key为以上四个，value为自己弹窗里匹配的props名称.如：{"form":
                "dialog", "visible": "open", "confirm": "submit","close":
                "close"}
              </template>
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            对话框参数
          </span>
        </template>
        <el-input
          v-model="dialogForm.dialogField"
          placeholder="对话框参数字段映射"
          type="textarea"
        />
      </el-form-item>

      <el-form-item
        label="关联完成"
        prop="associated"
        v-if="dialogForm.componentType != 'none'"
      >
        <template #label>
          <span>
            <el-tooltip placement="top">
              <template #content>
                勾选后，待办完成时会完成全部业务编号相同的关联待办
              </template>
              <el-icon>
                <IconifyIconOnline icon="ep:question-filled" />
              </el-icon>
            </el-tooltip>
            关联完成
          </span>
        </template>
        <el-checkbox label="关联" v-model="dialogForm.associated" />
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dialogForm.remarks" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          :loading="loading"
          :size="(size as any)"
          type="primary"
          @click="submitForm"
        >
          确 定
        </el-button>
        <el-button :size="(size as any)" @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  getMessageConfigByType,
  updateMessageConfig,
  addMessageConfig
} from "@/api/message/config";
import { ref, reactive, watch, computed } from "vue";
import { MessageConfig } from "@/types/message/config";
import { useGlobal } from "@pureadmin/utils";
import { nextTick } from "process";

const { $useDict } = useGlobal<GlobalPropertiesApi>();

const {
  MESSAGE_MODULE,
  MESSAGE_TYPE,
  MESSAGE_COMPONENT_TYPE,
  MESSAGE_CLIENT_TYPE
} = $useDict(
  "MESSAGE_MODULE",
  "MESSAGE_TYPE",
  "MESSAGE_COMPONENT_TYPE",
  "MESSAGE_CLIENT_TYPE"
);

// Props and emits
const props = defineProps({
  size: {
    type: String,
    default: "default"
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});
const visible = defineModel<boolean>("visible", { default: false });
const emit = defineEmits(["confirm", "cancel"]);
const formRef = ref(null);

// Reactive state
const dialogForm = ref<MessageConfig>(new MessageConfig());
const rules = reactive({
  messageModule: [{ required: true, message: "模块不能为空", trigger: "blur" }],
  messageType: [{ required: true, message: "类型不能为空", trigger: "change" }],
  componentUrl: [
    { required: true, message: "组件路径不能为空", trigger: "blur" }
  ],
  componentType: [
    {
      required: true,
      message: "组件类型 dialog router custom不能为空",
      trigger: "change"
    }
  ],
  componentParams: [
    { required: true, message: "组件参数不能为空", trigger: "blur" }
  ],
  businessTable: [
    { required: true, message: "关联的业务表不能为空", trigger: "blur" }
  ]
});

const todoTypeList = computed(() =>
  MESSAGE_TYPE.value.filter(item =>
    item.value.startsWith(dialogForm.value.messageModule + "_")
  )
);

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => handlePropsDataChange(props.data)
);

function handlePropsDataChange(data: MessageConfig) {
  if (!visible.value) return;
  reset();

  if (data.id) {
    handleUpdate(data);
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: MessageConfig) {
  getMessageConfigByType(row.messageType, row.clientType).then(response => {
    nextTick(() => {
      dialogForm.value = response.data;
    });
  });
}

// Methods
const submitForm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (dialogForm.value.id) {
        updateMessageConfig(dialogForm.value).then(response => {
          emit("confirm");
          visible.value = false;
        });
      } else {
        addMessageConfig(dialogForm.value).then(response => {
          emit("confirm");
          visible.value = false;
        });
      }
    }
  });
  // Adjust according to Vue 3 form validation approach
};

const reset = () => {
  dialogForm.value = new MessageConfig();
  nextTick(() => {
    formRef?.value.resetFields();
  });
};

const handleChangeTodoType = (clientType, messageType) => {
  getMessageConfigByType(messageType, clientType).then(response => {
    const data = response.data;
    console.log(data);
    if (data) {
      dialogForm.value = Object.assign({}, data);
    } else {
      // Resets, assuming $set equivalent in Vue 3 setup
    }
  });
};

const closeDialog = () => {
  reset();
  visible.value = false;
};
</script>
