import { useTimeoutFn } from "@vueuse/core";
import { message } from "@/utils/message";
import { onMounted, reactive, ref } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { ElMessageBox, FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import {
  delMessageManage,
  listMessageManage,
  readOne
} from "@/api/message/messageManage";
import {ChatDotSquare,ChatSquare} from '@element-plus/icons-vue'
import { Message, MessageQuery } from "@/types/message/messageManage";
import { IconifyIconOffline } from "@/components/ReIcon";
// @ts-ignore
import emailOpen from "@iconify-icons/ri/mail-check-line";
import outlineEmail from "@iconify-icons/ri/mail-download-line";

export function useMessageManage() {
  const queryParams = reactive<MessageQuery>(new MessageQuery());
  const { $download } = useGlobal<GlobalPropertiesApi>();

  const loading = ref(true);
  const visible = ref(false);

  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const messageManageList = ref([]);
  const messageManageData = ref<Message>();

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  function readOneMessage(id) {
    console.log('ssss')
    readOne(id).then(() => {
      getList();
    });
  }

  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      index: (index: number) => {
        return pagination.pageSize * (pagination.currentPage - 1) + index + 1;
      },
      width: 70
    },
    {
      label: "消息推送内容",
      prop: "description",
      align: "center",
      minWidth: 200,
      cellRenderer: ({ row }) =>
      {
        return (
          <div>
            <div style={{
              padding:"0px 10px",
              display:"flex",
              justifyContent: "space-between",
            }}>
              <span>
                 {row.description}
              </span>
              {
                row.isRead == "0"?<>
                  <el-button  type="danger" icon={ChatDotSquare} plain onClick={() => readOneMessage(row.id)}>未读</el-button>
                </>:<>
                  <el-button  type="success" icon={ChatSquare}  plain>已读</el-button>
                </>
              }
            </div>
          </div>

        )
      }
      //   if (row.isRead == "0") {
      //     return (
      //       <div class="text-left" onClick={() => readOneMessage(row.id)}>
      //         <span>
      //           <IconifyIconOffline
      //             icon={outlineEmail}
      //             style={{
      //               color: "#F56C6C",
      //               display: "inline",
      //               fontSize: "20px",
      //               paddingTop: "8px"
      //             }}
      //           />
      //           {row.description}
      //         </span>
      //       </div>
      //     );
      //   } else {
      //     return (
      //       <div class="text-left">
      //         <span>
      //           <IconifyIconOffline
      //             icon={emailOpen}
      //             style={{
      //               display: "inline",
      //               fontSize: "20px",
      //               paddingTop: "8px"
      //             }}
      //           />
      //           {row.description}
      //         </span>
      //       </div>
      //     );
      //   }
      // }
    }
    // {
    //   label: "类型",
    //   prop: "type",
    //   align: "center",
    //   width: 100
    // }
  ];

  /** 新增按钮操作 */
  function handleCreate() {
    visible.value = true;
    messageManageData.value = new Message();
  }

  function handleDelete(row?: Message) {
    if (row?.id) {
      delMessageManage(row.id).then(() => {
        getList();
        message("删除成功", { type: "success" });
      });
      return;
    }
    ElMessageBox.confirm(
      '是否确认删除消息推送主体编号为"' + ids.value + '"的数据项？',
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        title: "系统提示"
      }
    )
      .then(function () {
        return delMessageManage(ids.value);
      })
      .then(() => {
        getList();
        message("删除成功", { type: "success" });
      })
      .catch(() => {});
  }

  /** 修改按钮操作 */
  function handleUpdate(row: Message) {
    visible.value = true;
    messageManageData.value = row;
  }

  async function getList() {
    loading.value = true;
    const { data, total } = await listMessageManage(queryParams, pagination);
    messageManageList.value = data;
    pagination.total = total;
    useTimeoutFn(() => {
      loading.value = false;
    }, 200);
  }

  function handleQuery() {
    pagination.currentPage = 1;
    getList();
  }

  function resetQuery(formEl: FormInstance) {
    if (!formEl) return;
    formEl.resetFields();
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  function pageSizeChange(size: number) {
    pagination.pageSize = size;
    getList();
  }

  function pageCurrentChange(num: number) {
    pagination.currentPage = num;
    getList();
  }

  /** 导出按钮操作 */
  function handleExport() {
    $download(
      "message/messageManage/export",
      {
        ...queryParams
      },
      `messageManage_${new Date().getTime()}.xlsx`
    );
  }

  onMounted(() => {
    getList();
  });

  return {
    single,
    multiple,
    visible,
    loading,
    columns,
    pagination,
    queryParams,
    messageManageData,
    messageManageList,
    handleCreate,
    handleDelete,
    handleUpdate,
    handleQuery,
    resetQuery,
    handleExport,
    handleSelectionChange,
    pageSizeChange,
    pageCurrentChange,
    getList
  };
}
