<script setup lang="ts">
import { computed, ref, watchEffect } from "vue";

import { getNotice } from "@/api/system/notice";
import { SysNotice } from "@/types/system/sys-notice";
import { Editor } from "@wangeditor/editor-for-vue";

defineOptions({
  name: "NoticeEditDialog"
});

const props = defineProps({
  data: {
    type: SysNotice,
    default: () => {
      return new SysNotice();
    }
  }
});
const loading = ref(true);
// const noticeId = computed(() => props.data.noticeId)

// 表单信息
const form = ref<SysNotice>(new SysNotice());
const visible = defineModel<boolean>("visible", { default: false });

// * -- 对话框显示状态
// watch(() => props.data, (val) => handlePropsDataChange(val.noticeId), {immediate: true, deep: true});

watchEffect(async () => {
  if (!visible.value) return;
  if (!props.data.noticeId) return;
  getNotice(props.data.noticeId)
    .then(res => {
      if (res.code === 200) {
        form.value = res.data;
        loading.value = false;
      } else {
        loading.value = true;
      }
    })
    .catch(() => {
      loading.value = false;
    });
});

const handleClose = () => {
  form.value = [];
};
function cancel() {
  visible.value = false;
}
</script>

<template>
  <!-- 添加或修改通知公告对话框 -->
  <el-dialog
    title="查看通知公告"
    v-model="visible"
    width="1040px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="wangeditor" v-loading="loading">
      <h3 class="text-center">{{ form.noticeTitle }}</h3>
      <editor
        class="min-h-[300px]"
        :default-config="{
          readOnly: true
        }"
        :disabled="true"
        v-model="form.noticeContent"
        :min-height="300"
      />
    </div>
    <!--<template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">确定</el-button>
      </div>
    </template>-->
  </el-dialog>
</template>

<style src="@wangeditor/editor/dist/css/style.css"></style>
