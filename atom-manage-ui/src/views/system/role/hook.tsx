import dayjs from "dayjs";
import { message } from "@/utils/message";
import { changeRoleStatus, delRole, listRole } from "@/api/system/role";
import { useGlobal } from "@pureadmin/utils";

import { ElMessageBox, FormInstance } from "element-plus";
import { SysRole, SysRoleQuery } from "@/types/system/sys-role";

import { type PaginationProps } from "@pureadmin/table";
import { computed, onMounted, reactive, ref } from "vue";
import { useTimeoutFn } from "@vueuse/core";
import router from "@/router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

export function useRole() {
	const queryParams = reactive(new SysRoleQuery());
	const dataList = ref([]);
	const loading = ref(true);
	const visibleEditDialog = ref(false);
	const visibleDataScopeDialog = ref(false);
	const visibleRoleScopeDialog = ref(false);
	const multiple = ref(false);
	const roleData = ref<SysRole>();
	const ids = ref<number[]>([]);

	const { $download, $useDict } = useGlobal<GlobalPropertiesApi>();
	const { sys_normal_disable } = $useDict("sys_normal_disable");

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const buttonClass = computed(() => {
		return [
			"!h-[20px]",
			"reset-margin",
			"!text-gray-500",
			"dark:!text-white",
			"dark:hover:!text-primary"
		];
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			index: (index: number) => {
				return pagination.pageSize * (pagination.currentPage - 1) + index + 1;
			},
			width: 70
		},
		{
			label: "角色编号",
			prop: "roleId",
			minWidth: 100
		},
		{
			label: "角色名称",
			prop: "roleName",
			minWidth: 120
		},
		{
			label: "角色标识",
			prop: "roleKey",
			minWidth: 150
		},
		{
			label: "显示顺序",
			prop: "roleSort",
			minWidth: 100
		},
		{
			label: "状态",
			minWidth: 130,
			cellRenderer: scope => (
				<el-switch
					size={scope.props.size === "small" ? "small" : "default"}
					v-model={scope.row.status}
					active-value="0"
					inactive-value="1"
					active-text="启用"
					inactive-text="禁用"
					inline-prompt
					disabled={
						scope.row.roleKey === "admin" ||
						scope.row.roleKey === "main_leader" ||
						scope.row.roleKey === "project_receiver"
					}
					onChange={() => handleStatusChange(scope.row)}
				/>
			)
		},
		{
			label: "创建时间",
			width: 180,
			minWidth: 180,
			prop: "createTime",
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss") : ""
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 220,
			slot: "operation"
		}
	];

	/** 添加角色 */
	function handleCreate() {
		roleData.value = new SysRole();
		visibleEditDialog.value = true;
	}

	/** 角色状态修改 */
	function handleStatusChange(row: SysRole) {
		const text = row.status === "0" ? "启用" : "停用";
		ElMessageBox.confirm(
			`确认要<strong>${text}</strong><strong style='color:var(--el-color-primary)'>${row.roleName}</strong>角色吗?`,
			"系统提示",
			{
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
				dangerouslyUseHTMLString: true,
				draggable: true
			}
		)
			.then(function () {
				return changeRoleStatus(row.roleId, row.status);
			})
			.then(() => {
				message(text + "成功", { type: "success" });
			})
			.catch(function () {
				row.status = row.status === "0" ? "1" : "0";
			});
	}

	function handleUpdate(row) {
		roleData.value = row;
		visibleEditDialog.value = true;
	}

	/** 删除按钮操作 */
	function handleDelete(row?: SysRole) {
		if (row?.roleId) {
			delRole(row.roleId).then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			});
			return;
		}
		if (ids.value.length < 1) {
			return;
		}
		ElMessageBox.confirm('是否确认删除角色编号为"' + ids.value + '"的数据项?', {
			type: "warning",
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			title: "系统提示"
		})
			.then(function () {
				return delRole(ids.value);
			})
			.then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"system/role/export",
			{
				...queryParams
			},
			`role_${new Date().getTime()}.xlsx`
		);
	}

	function handleSelectionChange(val) {
		console.log("handleSelectionChange", val);
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await listRole(queryParams);
		dataList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	async function handleQuery() {
		pagination.currentPage = 1;
		await getList();
	}
	/** 分配数据权限操作 */
	function handleDataScope(row) {
		roleData.value = row;
		visibleDataScopeDialog.value = true;
	}

	function handleRoleScope(row) {
		roleData.value = row;
		visibleRoleScopeDialog.value = true;
	}
	/** 分配用户 */
	function handleAuthUser(row) {
		useMultiTagsStoreHook().handleTags("push", {
			path: `/role-auth/user/:id`,
			name: "AuthUser",
			params: { id: String(row.roleId) },
			meta: {
				title: "分配用户",
				dynamicLevel: 3
			}
		});
		router.push({
			name: "AuthUser",
			params: { id: String(row.roleId) }
		});
	}

	const resetQuery = (formEl: FormInstance) => {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	};

	onMounted(() => {
		handleQuery();
	});

	return {
		sys_normal_disable,
		queryParams,
		loading,
		roleData,
		visibleEditDialog,
		visibleDataScopeDialog,
		visibleRoleScopeDialog,
		columns,
		buttonClass,
		dataList,
		resetQuery,
		multiple,
		getList,
		handleQuery,
		handleUpdate,
		handleCreate,
		handleDelete,
		handleExport,
		handleDataScope,
		handleRoleScope,
		handleAuthUser,
		handleSelectionChange
	};
}
