import dayjs from "dayjs";
import { message } from "@/utils/message";
import { useGlobal } from "@pureadmin/utils";

import { ElMessageBox, FormInstance } from "element-plus";
import { SysRoleUserQuery } from "@/types/system/sys-role";

import { type PaginationProps } from "@pureadmin/table";
import { computed, onMounted, reactive, ref } from "vue";
import {
	allocatedUserList,
	authUserCancel,
	authUserCancelAll
} from "@/api/system/role";
import DictTag from "@/components/DictTag";
import { useRoute } from "vue-router";
import { SysUser } from "@/types/system/sys-user";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import router from "@/router";

export function useAuthUser() {
	const queryParams = reactive(new SysRoleUserQuery());
	const dataList = ref([]);
	const loading = ref(true);
	const multiple = ref(false);

	const visible = ref(false);
	const userIds = ref<number[]>([]);

	const route = useRoute();

	const { $useDict } = useGlobal<GlobalPropertiesApi>();
	const { sys_normal_disable } = $useDict("sys_normal_disable");

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1,
		background: true
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			index: (index: number) => {
				return pagination.pageSize * (pagination.currentPage - 1) + index + 1;
			},
			width: 70
		},
		{
			label: "用户名称",
			prop: "nickName",
			minWidth: 100
		},
		{
			label: "账户",
			prop: "userName",
			minWidth: 120
		},
		{
			label: "状态",
			prop: "status",
			minWidth: 130,
			cellRenderer: ({ row }) => (
				<DictTag value={row.status} options={sys_normal_disable} />
			)
		},
		{
			label: "创建时间",
			width: 180,
			minWidth: 180,
			prop: "createTime",
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss") : ""
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 180,
			slot: "operation"
		}
	];

	const buttonClass = computed(() => {
		return [
			"!h-[20px]",
			"reset-margin",
			"!text-gray-500",
			"dark:!text-white",
			"dark:hover:!text-primary"
		];
	});

	/** 取消授权按钮操作 */
	function cancelAuthUser(row: SysUser) {
		ElMessageBox.confirm(
			'确认要取消该用户"' + row.userName + '"角色吗？',
			"提示",
			{
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			}
		)
			.then(() => {
				return authUserCancel({
					userId: row.userId,
					roleId: queryParams.roleId
				});
			})
			.then(() => {
				message("取消授权成功", { type: "success" });
				getList();
			})
			.catch(() => { });
	}

	/** 查询授权用户列表 */
	function getList() {
		loading.value = true;
		allocatedUserList({ ...queryParams, ...pagination }).then(response => {
			dataList.value = response.data;
			pagination.total = response.total;
			loading.value = false;
		});
	}

	// 多选框选中数据
	function handleSelectionChange(selection) {
		userIds.value = selection.map(item => item.userId);
		multiple.value = !selection.length;
	}

	/** 打开授权用户表弹窗 */
	function openSelectUser() {
		visible.value = true;
	}

	// 返回按钮
	function handleClose() {
		useMultiTagsStoreHook().handleTags("splice", "/role-auth/user/:id");
		router.push({ name: "Role" });
	}

	/** 批量取消授权按钮操作 */
	function cancelAuthUserAll() {
		const roleId = queryParams.roleId;
		const uIds = userIds.value.join(",");
		ElMessageBox.confirm("是否取消选中用户授权数据项?", "系统消息", {
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			type: "warning"
		})
			.then(() => {
				authUserCancelAll({ roleId: roleId, userIds: uIds }).then(() => {
					getList();
					message("取消授权成功", { type: "success" });
				});
			})
			.catch(() => { });
	}

	async function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	const resetQuery = (formEl: FormInstance) => {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	};

	// * 页面大小改变
	function handleSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	// * 翻页
	function handleCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	onMounted(() => {
		const roleId = route.params && route.params.id;
		queryParams.roleId = roleId as unknown as number;
		handleQuery();
	});

	return {
		sys_normal_disable,
		queryParams,
		loading,
		columns,
		visible,
		userIds,
		buttonClass,
		cancelAuthUser,
		cancelAuthUserAll,
		openSelectUser,
		handleClose,
		dataList,
		getList,
		resetQuery,
		multiple,
		pagination,
		handleQuery,
		handleSelectionChange,
		handleSizeChange,
		handleCurrentChange
	};
}
