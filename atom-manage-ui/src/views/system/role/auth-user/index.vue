<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号码"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar
      title="当前角色用户列表"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="primary"
          plain
          :icon="useRenderIcon('ep:circle-plus')"
          @click="openSelectUser"
          v-auth="['system:role:add']"
          >添加用户
        </el-button>
        <el-button
          type="danger"
          plain
          :icon="useRenderIcon('ep:circle-close')"
          :disabled="!multiple"
          @click="cancelAuthUserAll"
          v-auth="['system:role:remove']"
          >批量取消授权
        </el-button>
        <el-button
          type="warning"
          plain
          :icon="useRenderIcon('ep:close')"
          @click="handleClose"
          >关闭
        </el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <pure-table
          ref="tableRef"
          border
          align-whole="center"
          row-key="menuId"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="columns"
          :checkList="checkList"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          :pagination="pagination"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #operation="{ row }">
            <el-button
              link
              v-if="row.userId !== 1"
              type="primary"
              :icon="useRenderIcon('ep:close')"
              @click="cancelAuthUser(row)"
              v-auth="['system:role:remove']"
              >取消授权
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <select-user
      v-model:visible="visible"
      :roleId="queryParams.roleId"
      @confirm="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useAuthUser } from "./hook";
import SelectUser from "./selectUser.vue";
defineOptions({
  name: "AuthUser"
});

const queryRef = ref(null);
const {
  queryParams,
  loading,
  visible,
  columns,
  dataList,
  resetQuery,
  pagination,
  multiple,
  handleQuery,
  openSelectUser,
  cancelAuthUserAll,
  cancelAuthUser,
  handleClose,
  getList,
  handleSelectionChange,
  handleSizeChange,
  handleCurrentChange
} = useAuthUser();

// const route = useRoute();

// const { $useDict } = useGlobal<GlobalPropertiesApi>();
// const { sys_normal_disable } = $useDict("sys_normal_disable");

// const userList = ref([]);
// const loading = ref(true);
// const multiple = ref(true);
// const total = ref(0);
// const userIds = ref([]);

// const queryParams = reactive({
//   pageNum: 1,
//   pageSize: 10,
//   roleId: route.params.roleId,
//   userName: undefined,
//   phonenumber: undefined
// });
</script>
