<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="角色状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 1, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar
      title="角色列表"
      :columns="columns"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          class="sm:shrink-button"
          type="primary"
          :icon="useRenderIcon('ep:circle-plus')"
          @click="handleCreate"
          >新增
        </el-button>

        <el-button
          type="danger"
          :icon="useRenderIcon('ep:delete')"
          :disabled="multiple"
          @click="handleDelete()"
          >删除
        </el-button>
        <el-button
          type="warning"
          :icon="useRenderIcon('ri:download-2-fill')"
          @click="handleExport"
          >导出
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          align-whole="center"
          row-key="menuId"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
        >
          <template #operation="{ row }">
            <template v-if="row.roleKey !== 'admin'">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                @click="handleUpdate(row)"
                :icon="useRenderIcon('ep:edit-pen')"
              >
                修改
              </el-button>
              <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    link
                    type="primary"
                    :size="size"
                    :icon="useRenderIcon('ep:delete')"
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>

              <el-dropdown>
                <el-button
                  class="ml-3 mt-[2px]"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon('ep:more')"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <el-button
                        :class="buttonClass"
                        link
                        type="primary"
                        :size="size"
                        :icon="useRenderIcon('ep:user')"
                        @click="handleAuthUser(row)"
                      >
                        分配用户
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        :class="buttonClass"
                        link
                        type="primary"
                        :size="size"
                        :icon="useRenderIcon('ri:tools-line')"
                        @click="handleDataScope(row)"
                      >
                        数据权限
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        :class="buttonClass"
                        link
                        type="primary"
                        :size="size"
                        :icon="useRenderIcon('ep:aim')"
                        @click="handleRoleScope(row)"
                      >
                        权限范围
                      </el-button>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
    <RoleEditDialog
      v-model:visible="visibleEditDialog"
      :data="roleData"
      @confirm="getList"
    />

    <DataScopeEditDialog
      v-model:visible="visibleDataScopeDialog"
      :data="roleData"
      @confirm="getList"
    />

    <RoleScopeEditDialog
      v-model:visible="visibleRoleScopeDialog"
      :data="roleData"
      @confirm="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElForm } from "element-plus";
import { useRole } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import RoleEditDialog from "./components/RoleEditDialog.vue";
import DataScopeEditDialog from "./components/DataScopeEditDialog.vue";
import RoleScopeEditDialog from "./components/RoleScopeEditDialog.vue";
defineOptions({
  name: "Role"
});

const queryRef = ref();
const tableRef = ref();

const {
  sys_normal_disable,
  queryParams,
  loading,
  roleData,
  visibleEditDialog,
  visibleDataScopeDialog,
  visibleRoleScopeDialog,
  columns,
  buttonClass,
  dataList,
  resetQuery,
  multiple,
  handleQuery,
  getList,
  handleAuthUser,
  handleDataScope,
  handleRoleScope,
  handleUpdate,
  handleCreate,
  handleDelete,
  handleExport,
  handleSelectionChange
} = useRole();
</script>
