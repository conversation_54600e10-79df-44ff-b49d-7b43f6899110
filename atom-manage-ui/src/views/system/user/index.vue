<template>
  <div class="main">
    <org-tree class="w-[17%] float-left" @click="handleClickTree" />
    <div class="float-right w-[81%]">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        class="bg-bg_color w-[99/100] pl-8 pt-4"
      >
        <el-form-item label="用户名称" prop="userName" v-role="['admin']">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入用户姓名"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="用户状态"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon(`ep:search`)"
            @click="handleQuery"
            >搜索
          </el-button>
          <el-button
            :icon="useRenderIcon('ep:refresh')"
            @click="resetQuery(queryRef)"
            >重置
          </el-button>
        </el-form-item>
      </el-form>

      <PureTableBar
        title="用户管理"
        :queryRef="queryRef"
        @refresh="handleQuery"
        :columns="columns"
      >
        <template #buttons>
          <el-button
            type="primary"
            class="sm:shrink-button"
            v-auth="'system:user:add'"
            :icon="useRenderIcon('ep:circle-plus')"
            @click="handleCreate()"
          >
            新增
          </el-button>
          <el-button
            type="danger"
            plain
            :icon="useRenderIcon('ep:delete')"
            v-auth="'system:user:remove'"
            :disabled="multiple"
            @click="handleDelete()"
            >删除
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <pure-table
            border
            align-whole="center"
            row-key="id"
            showOverflowTooltip
            table-layout="auto"
            default-expand-all
            :loading="loading"
            :size="size"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-table-row-hover-bg-color)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="handleSelectionChange"
            @page-current-change="pageCurrentChange"
            @page-current-size="pageSizeChange"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                v-if="row.userId !== 1"
                v-auth="'system:user:edit'"
                @click="handleUpdate(row)"
                :icon="useRenderIcon('ep:edit-pen')"
              >
                修改
              </el-button>
              <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    link
                    type="primary"
                    :size="size"
                    v-auth="'system:user:remove'"
                    v-if="row.userId !== 1"
                    :icon="useRenderIcon('ep:delete')"
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>

              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                v-auth="'system:user:edit'"
                @click="handleAuthRole(row)"
                :icon="useRenderIcon('ri:admin-line')"
                v-if="row.userId !== 1"
              >
                分配角色
              </el-button>
            </template>
          </pure-table>
        </template>
      </PureTableBar>
    </div>

    <UserEditDialog
      v-model:visible="visibleEdit"
      @confirm="getList"
      :data="userData"
    />

    <UserImportDialog v-model:visible="visibleImport" @confirm="getList" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElForm } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUser } from "./hook";
import OrgTree from "./org-tree.vue";
import UserEditDialog from "./components/UserEditDialog.vue";
import UserImportDialog from "./components/UserImportDialog.vue";
defineOptions({
  name: "User"
});

const queryRef = ref();

const {
  sys_normal_disable,
  multiple,
  queryParams,
  userData,
  loading,
  columns,
  dataList,
  resetQuery,
  visibleEdit,
  visibleImport,
  pagination,
  pageCurrentChange,
  pageSizeChange,
  handleQuery,
  getList,
  handleUpdate,
  handleCreate,
  handleDelete,
  handleAuthRole,
  handleClickTree,
  handleSelectionChange
} = useUser();
</script>
