<script setup lang="ts">
import { listOrgChildren, searchOrg } from "@/api/system/org";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { computed, getCurrentInstance, onMounted, ref, watch } from "vue";

import Dept from "@iconify-icons/ri/git-branch-line";
import Reset from "@iconify-icons/ri/restart-line";
import Search from "@iconify-icons/ep/search";
import More2Fill from "@iconify-icons/ri/more-2-fill";
import OfficeBuilding from "@iconify-icons/ep/office-building";
import LocationCompany from "@iconify-icons/ep/add-location";

import ExpandIcon from "./svg/expand.svg?component";
import UnExpandIcon from "./svg/unexpand.svg?component";

const emit = defineEmits<{ click: [code: string] }>();

interface Tree {
  id: number;
  code: string;
  name: string;
  parentId: number;
  highlight?: boolean;
  children?: Tree[];
}

const treeRef = ref();
const treeData = ref([]);
const isExpand = ref(true);
const searchValue = ref("");
const highlightMap = ref({});
const { proxy } = getCurrentInstance();

const orgOptions = ref([]);
const loading = ref(false);

const defaultProps = {
  id: "id",
  children: "children",
  label: "name",
  isLeaf: "isLeaf"
};
const buttonClass = computed(() => {
  return [
    "!h-[20px]",
    "reset-margin",
    "!text-gray-500",
    "dark:!text-white",
    "dark:hover:!text-primary"
  ];
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.name.includes(value);
};

const remoteSearchOrg = name => {
  if (name !== "") {
    loading.value = true;
    searchOrg(name)
      .then(res => {
        orgOptions.value = res.data.map(item => {
          return {
            value: item.code,
            label: item.name
          };
        });
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    orgOptions.value = [];
  }
};

function handleSelectOrg(val) {
  emit("click", val);
}

function nodeClick(value) {
  const nodeId = value.$treeNodeId;
  highlightMap.value[nodeId] = highlightMap.value[nodeId]?.highlight
    ? Object.assign({ orgId: nodeId }, highlightMap.value[nodeId], {
        highlight: false
      })
    : Object.assign({ orgId: nodeId }, highlightMap.value[nodeId], {
        highlight: true
      });
  Object.values(highlightMap.value).forEach((v: Tree) => {
    if (v.id !== nodeId) {
      v.highlight = false;
    }
  });
  emit("click", value.code);
}

const loadNode = async (node: any, resolve: any) => {
  const { data } = node;
  try {
    const response = await listOrgChildren(data.id ?? 0);
    const result = response.data ?? [];

    const orgNodes = result.map((item: any) => ({
      id: item.id,
      code: item.code,
      name: item.name,
      parentId: data.id,
      type: item.type,
      isLeaf: item.type == 3 || (!!item.children && item.children.length > 0)
    }));

    resolve(orgNodes);
  } catch (error) {
    console.error("Error fetching org data:", error);
    resolve([]);
  }
};

function toggleRowExpansionAll(status) {
  isExpand.value = status;
  const nodes = (proxy.$refs["treeRef"] as any).store._getAllNodes();
  for (let i = 0; i < nodes.length; i++) {
    nodes[i].expanded = status;
  }
}

/** 重置状态（选中状态、搜索框值、树初始化） */
function onReset() {
  highlightMap.value = {};
  searchValue.value = "";
  toggleRowExpansionAll(true);
}

// watch(searchValue, val => {
//   treeRef.value!.filter(val);
// });

onMounted(async () => {
  // const { data } = await treeselectOrg();
  // treeData.value = data;
});
</script>

<template>
  <div class="h-full min-h-[780px] bg-bg_color overflow-scroll">
    <div class="flex items-center h-[34px]">
      <p class="flex-1 ml-2 font-bold text-base truncate" title="机构列表">
        机构列表
      </p>

      <el-select-v2
        size="small"
        style="flex: 2"
        v-model="searchValue"
        filterable
        remote
        :remote-method="remoteSearchOrg"
        clearable
        :options="orgOptions"
        :loading="loading"
        @change="handleSelectOrg"
        placeholder="请输入机构名称"
        popper-class="auto-width"
      />

      <el-dropdown :hide-on-click="false">
        <IconifyIconOffline
          class="w-[28px] cursor-pointer"
          width="18px"
          :icon="More2Fill"
        />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <el-button
                :class="buttonClass"
                link
                type="primary"
                :icon="useRenderIcon(isExpand ? ExpandIcon : UnExpandIcon)"
                @click="toggleRowExpansionAll(isExpand ? false : true)"
              >
                {{ isExpand ? "折叠全部" : "展开全部" }}
              </el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button
                :class="buttonClass"
                link
                type="primary"
                :icon="useRenderIcon(Reset)"
                @click="onReset"
              >
                重置状态
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-divider />
    <el-tree
      class="mt-1"
      ref="treeRef"
      :data="treeData"
      node-key="id"
      size="small"
      lazy
      :load="loadNode"
      :props="defaultProps"
      :default-expanded-keys="[1]"
      :default-expand-all="false"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      @node-click="nodeClick"
    >
      <template #default="{ node, data }">
        <span
          :class="[
            'pl-1',
            'pr-1',
            'rounded',
            'flex',
            'items-center',
            'select-none',
            searchValue?.trim().length > 0 &&
              node.label.includes(searchValue) &&
              'text-red-500',
            highlightMap[node.id]?.highlight ? 'dark:text-primary' : ''
          ]"
          :style="{
            background: highlightMap[node.id]?.highlight
              ? 'var(--el-color-primary-light-7)'
              : 'transparent'
          }"
        >
          <IconifyIconOffline
            :icon="
              data.type === 1
                ? OfficeBuilding
                : data.type === 2
                ? LocationCompany
                : Dept
            "
          />

          {{ node.label }}
        </span>
      </template>
    </el-tree>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-divider) {
  margin: 0;
}

.el-popper.auto-width .el-select-dropdown__list {
  width: 450px !important;
  max-width: 95% !important;
}
</style>
