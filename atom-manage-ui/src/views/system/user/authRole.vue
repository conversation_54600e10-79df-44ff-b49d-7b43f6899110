<script setup lang="tsx">
import dayjs from "dayjs";
import { getAuthRole, updateAuthRole } from "@/api/system/user";
import { message } from "@/utils/message";
import { PaginationProps } from "@pureadmin/table";
import { nextTick, reactive, ref } from "vue";
import { useRoute } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import router from "@/router";

defineOptions({
  name: "AuthRole"
});
const route = useRoute();
const tableRef = ref();

const loading = ref(true);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const roleIds = ref([]);
const roles = ref([]);
const form = ref({
  nickName: undefined,
  userName: undefined,
  userId: undefined
});

const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

const columns: TableColumnList = [
  {
    type: "selection",
    width: 55,
    align: "left"
  },
  {
    label: "角色编号",
    minWidth: 100,
    align: "center",
    prop: "roleId"
  },
  {
    label: "角色名称",
    minWidth: 100,
    align: "center",
    prop: "roleName"
  },
  {
    label: "权限字符",
    minWidth: 100,
    align: "center",
    prop: "roleKey"
  },
  {
    label: "创建时间",
    minWidth: 100,
    align: "center",
    prop: "createTime",
    formatter: ({ createTime }) =>
      createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss") : ""
  }
];

/** 单击选中行数据 */
// function clickRow(row) {
//   tableRef.value.getTableRef().toggleRowSelection(row, true);
// }

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  roleIds.value = selection.map(item => item.roleId);
}

/** 保存选中的数据编号 */
// function getRowKey(row) {
//   return row.roleId;
// }

/** 关闭按钮 */
function close() {
  // useMultiTagsStoreHook().handleTags("splice", "/auth-role/:id");
  router.push({ name: "User" });
  // const obj = { path: "/system/user" };
  // proxy.$tab.closeOpenPage(obj);
}

/** 提交按钮 */
function submitForm() {
  const userId = form.value.userId;
  const rIds = roleIds.value.join(",") as unknown as number[];
  updateAuthRole({ userId: userId, roleIds: rIds }).then(() => {
    message("授权成功", { type: "success" });
    close();
  });
}

function handleQuery() {
  pagination.currentPage = 1;
}

(() => {
  const userId = (route.params && route.params.id) as string;
  if (userId) {
    loading.value = true;
    getAuthRole(userId).then(response => {
      form.value = response.data.user;
      roles.value = response.data.roles;
      total.value = roles.value.length;
      nextTick(() => {
        roles.value.forEach(row => {
          if (row.flag) {
            tableRef.value.getTableRef().toggleRowSelection(row, true);
          }
        });
      });
      loading.value = false;
    });
  }
})();
</script>

<template>
  <div class="main">
    <el-form
      :model="form"
      label-width="80px"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-row>
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="form.nickName" disabled />
        </el-form-item>
        <el-form-item label="登录账号" prop="userName">
          <el-input v-model="form.userName" disabled />
        </el-form-item>
      </el-row>
    </el-form>

    <PureTableBar title="角色信息" @refresh="handleQuery">
      <template v-slot="{ size, checkList }">
        <pure-table
          border
          ref="tableRef"
          align-whole="center"
          row-key="id"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="roles"
          :columns="columns"
          :checkList="checkList"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
        />

        <div style="text-align: center; margin-left: -120px; margin-top: 30px">
          <el-button type="primary" @click="submitForm()">提交</el-button>
          <el-button @click="close()">返回</el-button>
        </div>
      </template>
    </PureTableBar>
  </div>
</template>
