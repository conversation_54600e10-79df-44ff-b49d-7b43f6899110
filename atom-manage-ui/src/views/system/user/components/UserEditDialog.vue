<script setup lang="ts">
import { addUser, getUser, orgTreeSelect, updateUser } from "@/api/system/user";
import { SysUser } from "@/types/system/sys-user";
import { message } from "@/utils/message";
import { useGlobal } from "@pureadmin/utils";
import { FormInstance } from "element-plus";
import { computed, ref, watch } from "vue";

const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { sys_normal_disable } = $useDict("sys_normal_disable");

const emit = defineEmits(["confirm"]);

// 表单相关
const form = ref<SysUser>();
const userRef = ref<FormInstance>();
const roleOptions = ref([]);
const orgOptions = ref([]);
const loading = ref(false);

const props = defineProps({
  data: {
    type: SysUser,
    default: () => {
      return new SysUser();
    }
  }
});

const visible = defineModel<boolean>("visible", { default: false });

const title = computed(() => {
  return form.value?.userId ? "修改用户" : "添加用户";
});

watch(() => props.data, handlePropsDataChange);

function handlePropsDataChange(data: SysUser) {
  reset();
  getOrgTree();
  if (data.userId) {
    handleUpdate(data);
  } else {
    handleAdd();
  }
}

/** 新增按钮操作 */
function handleAdd() {
  getUser().then(response => {
    roleOptions.value = response.data.roles;
  });
}
/** 修改按钮操作 */
function handleUpdate(row: SysUser) {
  const userId = row.userId;
  getUser(userId).then(response => {
    form.value = response.data.user;
    roleOptions.value = response.data.roles;
    form.value.roleIds = response.data.roleIds;
    form.value.password = "";
  });
}

/** 查询部门下拉树结构 */
function getOrgTree() {
  orgTreeSelect().then(response => {
    orgOptions.value = [response.data];
  });
}

/** 提交按钮 */
function submitForm() {
  userRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      if (form.value.userId != undefined) {
        updateUser(form.value)
          .then(() => {
            message("修改成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addUser(form.value)
          .then(() => {
            message("新增成功", { type: "success" });
            visible.value = false;
            emit("confirm");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

function cancel() {
  visible.value = false;
}

/** 表单重置 */
function reset() {
  form.value = new SysUser();
  if (userRef.value) {
    userRef.value.resetFields();
  }
}
</script>

<template>
  <!-- 添加或修改用户配置对话框 -->
  <el-dialog :title="title" v-model="visible" width="600px">
    <el-form :model="form" ref="userRef" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户名" prop="nickName">
            <el-input
              v-model="form.nickName"
              placeholder="请输入用户昵称"
              maxlength="30"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="归属机构" prop="orgId">
            <el-tree-select
              filterable
              v-model="form.orgId"
              :data="orgOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              placeholder="请选择归属机构"
              check-strictly
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="角色">
            <el-select v-model="form.roleIds" multiple placeholder="请选择">
              <el-option
                v-for="item in roleOptions"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
                :disabled="item.status == 1"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
