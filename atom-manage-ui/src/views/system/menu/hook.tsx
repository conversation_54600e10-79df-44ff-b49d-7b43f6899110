import dayjs from "dayjs";
import { handleTree } from "@/utils/tree";
import { delMenu, listMenu } from "@/api/system/menu";
import { onMounted, reactive, ref } from "vue";
import { SysMenu, SysMenuQuery } from "@/types/system/sys-menu";
import { message } from "@/utils/message";
import { IconifyIconOnline } from "@/components/ReIcon";
import { FormInstance } from "element-plus";
import { useGlobal } from "@pureadmin/utils";
import DictTag from "@/components/DictTag";

export function useMenu() {
	const queryParams = reactive<SysMenuQuery>({
		status: "0",
		visible: undefined,
		menuName: ""
	});

	const dataList = ref([]);
	const loading = ref(true);
	const visible = ref(false);
	const menuData = ref<SysMenu>();

	const { $useDict } = useGlobal<GlobalPropertiesApi>();
	const { sys_normal_disable } = $useDict("sys_normal_disable");

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			width: 70,
			cellRenderer: ({ row, index }) => {
				return (
					<el-tooltip placement="top" content={row.menuId}>
						<span>{index + 1}</span>
					</el-tooltip>
				);
			}
		},
		{
			label: "菜单名称",
			prop: "menuName",
			width: 180,
			align: "left"
		},
		{
			label: "图标",
			prop: "icon",
			width: 100,
			cellRenderer: ({ row }) => <IconifyIconOnline icon={row.icon} />
		},
		{
			label: "排序",
			prop: "orderNum",
			minWidth: 50
		},
		{
			label: "权限标识",
			prop: "perms",
			minWidth: 100
		},
		{
			label: "组件路径",
			prop: "component",
			minWidth: 100
		},
		{
			label: "状态",
			prop: "status",
			width: 80,
			cellRenderer: ({ row }) => (
				<DictTag options={sys_normal_disable} value={row.status} />
			)
		},
		{
			label: "创建时间",
			width: 180,
			minWidth: 100,
			prop: "createTime",
			formatter: ({ createTime }) =>
				dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 200,
			slot: "operation"
		}
	];

	function handleCreate(row?: SysMenu) {
		visible.value = true;
		const temp = new SysMenu();
		if (row != null && row.menuId) {
			temp.parentId = row.menuId;
		} else {
			temp.parentId = 0;
		}
		menuData.value = temp;
	}

	function handleUpdate(row: SysMenu) {
		visible.value = true;
		menuData.value = row;
	}

	/** 删除按钮操作 */
	function handleDelete(row: SysMenu) {
		delMenu(row.menuId).then(() => {
			handleQuery();
			message("删除成功", { type: "success" });
		});
	}

	function handleSelectionChange(val) {
		console.log("handleSelectionChange", val);
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	async function handleQuery() {
		getList();
	}

	async function getList() {
		loading.value = true;
		const { data } = await listMenu(queryParams);
		dataList.value = handleTree(data, "menuId");
		setTimeout(() => {
			loading.value = false;
		}, 200);
	}

	onMounted(() => {
		handleQuery();
	});

	return {
		queryParams,
		loading,
		columns,
		dataList,
		resetQuery,
		visible,
		menuData,
		handleQuery,
		handleUpdate,
		handleCreate,
		handleDelete,
		handleSelectionChange
	};
}
