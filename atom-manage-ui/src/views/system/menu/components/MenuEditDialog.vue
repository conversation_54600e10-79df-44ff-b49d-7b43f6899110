<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { ElForm, FormInstance } from "element-plus";
import IconSelect from "@/components/ReIcon/src/Select.vue";
import { SysMenu } from "@/types/system/sys-menu";
import { handleTree } from "@/utils/tree";

import { addMenu, getMenu, listMenu, updateMenu } from "@/api/system/menu";

defineOptions({
  name: "MenuEditDialog"
});

const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { sys_normal_disable, sys_show_hide } = $useDict(
  "sys_show_hide",
  "sys_normal_disable"
);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: SysMenu,
    default: () => {
      return new SysMenu();
    }
  }
});

// * -- 对话框显示状态
const visible = ref(false);

const emit = defineEmits(["update:visible", "confirm"]);

watch(
  () => props.visible,
  val => {
    visible.value = val;
  }
);

watch(visible, val => {
  emit("update:visible", val);
});

watch(
  () => props.data,
  val => {
    if (val) {
      val.menuId ? handleUpdate(val) : handleAdd(val);
    }
  }
);

const title = computed(() => {
  return form.value?.menuId ? "修改菜单" : "添加菜单";
});

const cancel = () => {
  visible.value = false;
};

// 表单信息
const form = ref<SysMenu | null>(null);
const menuRef = ref<FormInstance>(null);

const rules = {
  menuName: [{ required: true, message: "菜单名称不能为空", trigger: "blur" }],
  orderNum: [{ required: true, message: "菜单顺序不能为空", trigger: "blur" }],
  path: [{ required: true, message: "路由地址不能为空", trigger: "blur" }]
};

/** 表单重置 */
function reset() {
  form.value = new SysMenu();
  if (menuRef.value) {
    menuRef.value.resetFields();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row: SysMenu) {
  reset();
  getTreeselect();
  getMenu(row.menuId).then(response => {
    form.value = response.data;
  });
}

/** 新增按钮操作 */
function handleAdd(row?: SysMenu) {
  reset();
  getTreeselect();
  form.value.parentId = row?.parentId;
}

/** 提交按钮 */
function submitForm() {
  menuRef.value.validate(valid => {
    if (valid) {
      if (form.value.menuId != undefined) {
        updateMenu(form.value).then(() => {
          message("修改成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        });
      } else {
        addMenu(form.value).then(() => {
          message("新增成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        });
      }
    }
  });
}

const menuOptions = ref([]);

// /** 查询菜单下拉树结构 */
function getTreeselect() {
  menuOptions.value = [];
  listMenu().then(response => {
    const menu = { menuId: 0, menuName: "主类目", children: [] };
    menu.children = handleTree(response.data, "menuId");
    menuOptions.value.push(menu);
  });
}
</script>

<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog :title="title" v-model="visible" width="680px">
    <el-form ref="menuRef" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="上级菜单">
            <el-tree-select
              v-model="form.parentId"
              :data="menuOptions"
              :props="{
                value: 'menuId',
                label: 'menuName',
                children: 'children'
              }"
              value-key="menuId"
              placeholder="选择上级菜单"
              check-strictly
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="菜单类型" prop="menuType">
            <el-radio-group v-model="form.menuType">
              <el-radio label="M">目录</el-radio>
              <el-radio label="C">菜单</el-radio>
              <el-radio label="F">按钮</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.menuType != 'F'">
          <el-form-item label="菜单图标" prop="icon">
            <IconSelect v-model="form.icon" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单名称" prop="menuName">
            <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示排序" prop="orderNum">
            <el-input-number
              v-model="form.orderNum"
              controls-position="right"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType != 'F'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip
                  content="选择是外链则路由地址需要以`http(s)://`开头"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                是否外链
              </span>
            </template>
            <el-radio-group v-model="form.isFrame">
              <el-radio label="0">是</el-radio>
              <el-radio label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType != 'F'">
          <el-form-item prop="path">
            <template #label>
              <span>
                <el-tooltip
                  content="访问的路由地址，如：`/user`，如外网地址需内链访问则以`http(s)://`开头"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                路由地址
              </span>
            </template>
            <el-input v-model="form.path" placeholder="请输入路由地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType == 'C'">
          <el-form-item prop="component">
            <template #label>
              <span>
                <el-tooltip
                  content="访问的组件路径，如：`system/user/index`，默认在`views`目录下"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                组件路径
              </span>
            </template>
            <el-input v-model="form.component" placeholder="请输入组件路径" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType == 'C'">
          <el-form-item prop="routerName">
            <template #label>
              <span>
                <el-tooltip
                  content="路由名字（必须唯一并且和当前路由component字段对应的页面里用defineOptions包起来的name保持一致）"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                路由名称
              </span>
            </template>
            <el-input v-model="form.routerName" placeholder="请输入组件路径" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType != 'M'">
          <el-form-item>
            <el-input
              v-model="form.perms"
              placeholder="请输入权限标识"
              maxlength="100"
            />
            <template #label>
              <span>
                <el-tooltip
                  content="控制器中定义的权限字符，如：@SaCheckPermission('system:user:list')"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                权限字符
              </span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType == 'C'">
          <el-form-item>
            <el-input
              v-model="form.queryParam"
              placeholder="请输入路由参数"
              maxlength="255"
            />
            <template #label>
              <span>
                <el-tooltip
                  content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`'
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                路由参数
              </span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType == 'C'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip
                  content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                是否缓存
              </span>
            </template>
            <el-radio-group v-model="form.isCache">
              <el-radio label="0">缓存</el-radio>
              <el-radio label="1">不缓存</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType != 'F'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip
                  content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                显示状态
              </span>
            </template>
            <el-radio-group v-model="form.visible">
              <el-radio
                v-for="dict in sys_show_hide"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType != 'F'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip
                  content="选择停用则路由将不会出现在侧边栏，也不能被访问"
                  placement="top"
                >
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                菜单状态
              </span>
            </template>
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType == 'C'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip content="是否显示父级菜单" placement="top">
                  <el-icon>
                    <IconifyIconOnline icon="ep:question-filled" />
                  </el-icon>
                </el-tooltip>
                父级菜单
              </span>
            </template>
            <el-radio-group v-model="form.showParent">
              <el-radio
                v-for="dict in sys_show_hide"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
