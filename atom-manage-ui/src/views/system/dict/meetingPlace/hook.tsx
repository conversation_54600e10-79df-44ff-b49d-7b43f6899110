import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { message } from "@/utils/message";
import { ElMessageBox, FormInstance } from "element-plus";
import { useGlobal } from "@pureadmin/utils";
import useDictStore from "@/store/modules/dict";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { PaginationProps } from "@pureadmin/table";
import { useUserStoreHook } from "@/store/modules/user"
import { optionselect as getDictOptionselect } from "@/api/system/dict/type";
import { delData, listData, listDictList } from "@/api/system/dict/data";
import { SysDictData, SysDictDataQuery } from "@/types/system/sys-dict-data";

export function useDictData() {
	const { $useDict, $download } = useGlobal<GlobalPropertiesApi>();
	const { sys_normal_disable } = $useDict("sys_normal_disable");

	const queryParams = reactive<SysDictDataQuery>(new SysDictDataQuery());
	const typeOptions = ref([]);

	const dataList = ref([]);
	const loading = ref(true);
	const visible = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);

	const defaultDictType = ref("");

	const dictData = ref<SysDictData>();

	const orgCode = useUserStoreHook().orgCode

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1,
	});
	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			index: (index: number) => {
				return pagination.pageSize * (pagination.currentPage - 1) + index + 1;
			},
			width: 70
		},
		{
			label: "地址名称",
			prop: "dictLabel",
			minWidth: 150
		},
		{
			label: "地址排序",
			prop: "dictSort",
			minWidth: 100
		},
		{
			label: "创建时间",
			width: 180,
			minWidth: 140,
			prop: "createTime",
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 200,
			slot: "operation"
		}
	];


	/** 新增按钮操作 */
	function handleCreate() {
		visible.value = true;
		dictData.value = new SysDictData();
		dictData.value.dictType = "meeting_place";
	}

	function handleUpdate(row: SysDictData) {
		dictData.value = row;
		visible.value = true;
	}

	function handleDelete(row?: SysDictData) {
		if (row?.dictCode) {
			delData(row.dictCode).then(() => {
				getList();
				message("删除成功", { type: "success" });
			});
			return;
		}
		const dictCodes = ids.value;
		ElMessageBox.confirm(
			'是否确认删除字典编码为"' + dictCodes + '"的数据项？',
			"系统消息",
			{
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			}
		)
			.then(() => {
				delData(dictCodes).then(() => {
					getList();
					message("删除成功", { type: "success" });
					useDictStore().removeDict(queryParams.dictType);
				});
			})
			.catch(() => { });
	}

	/** 查询字典数据列表 */
	function getList() {
		loading.value = true;
		queryParams.dictType = "meeting_place";
		queryParams.remark = orgCode;
		listDictList(queryParams, pagination).then(response => {
			dataList.value = response.data;
			pagination.total = response.total;
			loading.value = false;
		});
	}

	/** 多选框选中数据 */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.dictCode);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		queryParams.dictType = defaultDictType.value;

		handleQuery();
	}

	async function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	/** 返回按钮操作 */
	function handleClose() {
		// const obj = { path: "/system/dict" };
		useMultiTagsStoreHook().handleTags("slice");
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"system/dict/data/export",
			{
				...queryParams
			},
			`dict_data_${new Date().getTime()}.xlsx`
		);
	}

	/** 查询字典类型列表 */
	function getTypeList() {
		getDictOptionselect().then(response => {
			typeOptions.value = response.data;
		});
	}


	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	onMounted(() => {
		handleQuery();
	});

	return {
		sys_normal_disable,
		single,
		multiple,
		dictData,
		queryParams,
		defaultDictType,
		typeOptions,
		loading,
		columns,
		dataList,
		resetQuery,
		visible,
		handleQuery,
		handleUpdate,
		handleCreate,
		handleDelete,
		handleExport,
		handleClose,
		handleSelectionChange,
		getList,
		pageSizeChange,
		pageCurrentChange,
		pagination
	};
}
