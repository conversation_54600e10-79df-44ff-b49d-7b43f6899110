<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useGlobal } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { ElForm } from "element-plus";
import { SysDictType } from "@/types/system/sys-dict-type";
import { handleTree } from "@/utils/tree";

import { addType, getType, listType, updateType } from "@/api/system/dict/type";

const { $useDict } = useGlobal<GlobalPropertiesApi>();
const { sys_normal_disable } = $useDict("sys_normal_disable");

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: SysDictType,
    default: () => {
      return new SysDictType();
    }
  }
});

// * -- 对话框显示状态
const visible = ref(false);

const emit = defineEmits(["update:visible", "confirm"]);

watch(
  () => props.visible,
  val => {
    visible.value = val;
  }
);

watch(visible, val => {
  emit("update:visible", val);
});

watch(() => props.data, handlePropsDataChange);

const title = computed(() => {
  return form.value?.dictId ? "修改字典类型" : "添加字典类型";
});

const cancel = () => {
  visible.value = false;
};

// 表单信息
const form = ref<SysDictType>(null);
const dictRef = ref<InstanceType<typeof ElForm>>(null);

const rules = {
  dictName: [{ required: true, message: "字典名称不能为空", trigger: "blur" }],
  dictType: [{ required: true, message: "字典类型不能为空", trigger: "blur" }]
};

/** 表单重置 */
function reset() {
  form.value = new SysDictType();
  if (dictRef.value) {
    dictRef.value.resetFields();
  }
}

function handlePropsDataChange(data: SysDictType) {
  reset();
  getTreeselect();
  if (data.dictId) {
    getType(data.dictId).then(response => {
      form.value = response.data;
    });
  } else {
    form.value.parentId = data?.parentId;
  }
}

/** 提交按钮 */
function submitForm() {
  dictRef.value.validate(valid => {
    if (valid) {
      if (form.value.dictId != undefined) {
        updateType(form.value).then(() => {
          message("修改成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        });
      } else {
        addType(form.value).then(() => {
          message("新增成功", { type: "success" });
          visible.value = false;
          emit("confirm");
        });
      }
    }
  });
}

const dictTypeOptions = ref([]);

// /** 查询菜单下拉树结构 */
function getTreeselect() {
  dictTypeOptions.value = [];
  listType().then(response => {
    const dictType = { dictId: 0, dictName: "根字典", children: [] };
    dictType.children = handleTree(response.data, "dictId");
    dictTypeOptions.value.push(dictType);
  });
}
</script>

<template>
  <!-- 添加或修改参数配置对话框 -->
  <el-dialog :title="title" v-model="visible" width="500px">
    <el-form ref="dictRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="父字典项" prop="parentId">
        <el-tree-select
          v-model="form.parentId"
          :data="dictTypeOptions"
          :props="{
            label: 'dictName',
            value: 'dictId',
            children: 'children'
          }"
          placeholder="请选择父字典项"
        />
      </el-form-item>
      <el-form-item label="字典名称" prop="dictName">
        <el-input v-model="form.dictName" placeholder="请输入字典名称" />
      </el-form-item>
      <el-form-item label="字典类型" prop="dictType">
        <el-input v-model="form.dictType" placeholder="请输入字典类型" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
