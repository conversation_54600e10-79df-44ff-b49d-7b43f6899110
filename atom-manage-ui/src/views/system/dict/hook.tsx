import dayjs from "dayjs";
import { handleTree } from "@/utils/tree";
import { delType, listType, refreshCache } from "@/api/system/dict/type";
import { onMounted, reactive, ref } from "vue";
import { SysDictType, SysDictTypeQuery } from "@/types/system/sys-dict-type";
import { message } from "@/utils/message";
import { ElButton, ElMessageBox, FormInstance } from "element-plus";
import { useGlobal } from "@pureadmin/utils";
import DictTag from "@/components/DictTag";
import useDictStore from "@/store/modules/dict";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import router from "@/router";

export function useDictType() {
	const queryParams = reactive<SysDictTypeQuery>(new SysDictTypeQuery());

	const dataList = ref([]);
	const loading = ref(true);
	const visible = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);

	const dictTypeData = ref<SysDictType>();
	const { $useDict, $download } = useGlobal<GlobalPropertiesApi>();
	const { sys_normal_disable } = $useDict("sys_normal_disable");

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			width: 70
		},
		{
			label: "字典编号",
			prop: "dictId",
			width: 100,
			align: "left"
		},
		{
			label: "字典名称",
			prop: "dictName",
			width: 100
		},
		{
			label: "字典类型",
			prop: "dictType",
			minWidth: 200,
			cellRenderer: ({ row }) => (
				<ElButton
					text
					type="primary"
					onClick={() => handleEditData(row.dictId)}
				>
					{row.dictType}
				</ElButton>
			)
		},
		{
			label: "状态",
			prop: "status",
			width: 80,
			cellRenderer: ({ row }) => (
				<DictTag options={sys_normal_disable} value={row.status} />
			)
		},
		{
			label: "创建时间",
			width: 140,
			prop: "createTime",
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 200,
			slot: "operation"
		}
	];

	function handleCreate(row?: SysDictType) {
		visible.value = true;
		const temp = new SysDictType();
		if (row != null && row.dictId) {
			temp.parentId = row.dictId;
		} else {
			temp.parentId = "0";
		}
		dictTypeData.value = temp;
	}

	function handleUpdate(row: SysDictType) {
		visible.value = true;
		dictTypeData.value = row;
	}

	function handleDelete(row?: SysDictType) {
		if (row?.dictId) {
			delType(row.dictId).then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm(
			'是否确认删除字典编号为"' + ids.value + '"的数据项？',
			{
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				title: "系统提示"
			}
		)
			.then(function () {
				return delType(ids.value);
			})
			.then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 字典数据页 */
	function handleEditData(id: number): void {
		useMultiTagsStoreHook().handleTags("push", {
			path: `/dict-data/index/:id`,
			name: "DictData",
			params: { id: String(id) },
			meta: {
				title: "修改字典数据",
				dynamicLevel: 3
			}
		});
		router.push({
			name: "DictData",
			params: { id: String(id) }
		});
	}
	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	async function handleQuery() {
		loading.value = true;
		const { data } = await listType(queryParams);
		dataList.value = handleTree(data, "dictId");
		setTimeout(() => {
			loading.value = false;
		}, 500);
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"system/dict/type/export",
			{
				...queryParams
			},
			`dict_${new Date().getTime()}.xlsx`
		);
	}

	/** 刷新缓存按钮操作 */
	function handleRefreshCache() {
		refreshCache().then(() => {
			message("刷新成功", { type: "success" });
			useDictStore().cleanDict();
		});
	}

	onMounted(() => {
		handleQuery();
	});

	return {
		sys_normal_disable,
		dictTypeData,
		queryParams,
		loading,
		columns,
		dataList,
		resetQuery,
		visible,
		handleQuery,
		handleUpdate,
		handleCreate,
		handleDelete,
		handleExport,
		handleRefreshCache,
		handleSelectionChange
	};
}
