<script setup lang="ts">
import { ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDictData } from "./data-hook";
import DataEditDialog from "./components/DataEditDialog.vue";

defineOptions({
  name: "DictData"
});

const queryRef = ref();
const tableRef = ref();

const {
  sys_normal_disable,
  dictData,
  queryParams,
  loading,
  columns,
  dataList,
  resetQuery,
  visible,
  typeOptions,
  handleClose,
  handleQuery,
  handleUpdate,
  handleCreate,
  handleDelete,
  handleExport,
  handleSelectionChange,
  getList
} = useDictData();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="字典名称" prop="dictType">
        <el-select v-model="queryParams.dictType" style="width: 200px">
          <el-option
            v-for="item in typeOptions"
            :key="item.dictId"
            :label="item.dictName"
            :value="item.dictType"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="字典标签" prop="dictLabel">
        <el-input
          v-model="queryParams.dictLabel"
          placeholder="请输入字典标签"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar
      title="菜单列表"
      :columns="columns"
      :tableRef="tableRef?.getTableRef()"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="primary"
          class="sm:shrink-button"
          :icon="useRenderIcon('ep:circle-plus')"
          @click="handleCreate()"
          v-auth="['system:dict:add']"
        >
          新增
        </el-button>

        <el-button
          type="danger"
          class="sm:shrink-button"
          :icon="useRenderIcon('ep:delete')"
          @click="handleCreate()"
          v-auth="['system:dict:remove']"
        >
          删除
        </el-button>
        <el-button
          class="sm:shrink-button"
          type="warning"
          plain
          :icon="useRenderIcon('ri:download-2-line')"
          v-auth="['system:dict:export']"
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="warning"
          class="sm:shrink-button"
          plain
          :icon="useRenderIcon('ep:circle-close')"
          @click="handleClose"
          >关闭
        </el-button>
      </template>

      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :icon="useRenderIcon('ep:edit-pen')"
              :size="size"
              @click="handleUpdate(row)"
              v-auth="['system:dict:edit']"
              >修改
            </el-button>

            <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  v-auth="['system:dict:remove']"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <DataEditDialog
      v-model:visible="visible"
      :data="dictData"
      @confirm="getList"
    />
  </div>
</template>
