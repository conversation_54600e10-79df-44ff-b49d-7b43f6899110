<template>
  <div>
    <div
      class="flex justify-between items-center"
      style="border-bottom: solid 1px #cbd2e0"
    >
      <p class="title">历史会议</p>
<!--      <el-button @click="handleBack">返回</el-button>-->
    </div>
    <div class="infinite-list" v-infinite-scroll="load" style="overflow:auto;height: 500px" :infinite-scroll-distance="50">
      <div class="mt-[20px]" v-for="(info, index) in list" :key="index" v-loading="loading">
        <el-tag class="meetingDate" type="primary">
          {{ info.date }}
        </el-tag>
        <ul>
          <li v-for="(item, index) in info.list" class="flex justify-between">
            <div>
              {{ item.meetingTitle }}
              <div class="mt-[10px] text-[14px]">
                <i class="icon iconfont icon-shizhongfill"></i>
                时间：{{ item.meetingTime[0] }}  ~  {{ item.meetingTime[1] }}
              </div>
            </div>
            <div class="text-right">
              <div>
                <el-tag type="warning" v-if="item.meetingType == 1">
                  {{ meeting_type.find(it => it.value == item.meetingType)?.label }}
                </el-tag>
                <el-tag type="success" v-else-if="item.meetingType == 2">
                  {{ meeting_type.find(it => it.value == item.meetingType)?.label }}
                </el-tag>
                <el-tag type="danger" v-else-if="item.meetingType == 3">
                  {{ meeting_type.find(it => it.value == item.meetingType)?.label }}
                </el-tag>
              </div>
              <div class="mt-[10px] text-[14px]">
                <i class="icon iconfont icon-renyuan" style="font-size: 14px"></i>
                主持人：{{ item.userName }}
              </div>
            </div>
          </li>
        </ul>
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {defineComponent} from "vue";
import {homeMeeting} from "./home.tsx";
defineOptions({
  name: "histroyMeeting"
});

  const {
    meeting_type,
    pagination,
    queryParams,
    dataList,
    list,
    getList,
    load,
    loading
  } = homeMeeting();
</script>

<style scoped lang="scss">
:deep(.el-tag) {
  border-radius: 20px;
  font-size: 15px;
  height: 28px;
}
.meetingDate {
  font-size: 18px;
  padding: 15px 12px;
}
.title {
  font-size: 24px;
  line-height: 60px;
}
ul {
  li {
    border-radius: 10px;
    padding: 20px;
    color: #5a5a5a;
    background: rgb(255 255 255 /60%);
    margin: 20px 0;
  }
}
</style>
