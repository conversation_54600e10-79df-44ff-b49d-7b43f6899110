<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";
// 导入新的签名组件
import ScaledEsign from "@/components/ScaledEsign/index.vue";

import { addUserSign } from "@/api/system/userSign";
import { UserSign } from "@/types/system/userSign";
import { useUserStoreHook } from "@/store/modules/user";
import { useWindowSize } from "@vueuse/core/index";

const emit = defineEmits(["update:visible", "confirm"]);

defineOptions({
  name: "UserSignEditDialog"
});

const visible = defineModel<boolean>("visible", { default: false });
const userId = useUserStoreHook().userId;

// 表单信息
const form = ref<UserSign>(null);
const loading = ref(false);
const lineWidth = ref(4);
const lineColor = ref("#000000");
const bgColor = ref("");
const isCrop = ref(false);
const esignRef = ref(null);

// 笔触效果相关配置
const brushEffect = ref(true);
const pressureSensitive = ref(true);
const minLineWidth = ref(2);
const maxLineWidth = ref(12);

const title = computed(() => {
  return "设置您的签名";
});

// * -- 对话框显示状态
watch(
  () => visible.value,
  () => cancel()
);

/** 提交按钮 */
function submitForm() {
  loading.value = true;
  esignRef.value
    .generate()
    .then(res => {
      form.value = {
        signPic: res,
        userId: userId
      };
      addUserSign(form.value)
        .then(() => {
          message("签名已设置", { type: "success" });
          visible.value = false;
          esignRef.value.reset();
          emit("confirm");
        })
        .finally(() => {
          loading.value = false;
          esignRef.value.reset();
        });
    })
    .catch(e => {
      message(e.message, { type: "warning" });
      loading.value = false;
      return;
    });
}

function cancel() {
  if (esignRef.value) {
    esignRef.value.reset(); // 确保组件有这个方法
  }
}
</script>

<template>
  <!-- 添加或修改签名对话框 -->
  <el-dialog
    :title="title"
    v-model="visible"
    style="width: 1310px"
    :close-on-click-modal="false"
    align-center
  >
    <div style="width: 1280px; height: 768px">
      <scaled-esign
        v-if="visible"
        ref="esignRef"
        style="border: 1px dashed #1c1a1a"
        :width="1280"
        :height="768"
        :isCrop="isCrop"
        :lineWidth="lineWidth"
        :lineColor="lineColor"
        :brushEffect="brushEffect"
        :pressureSensitive="pressureSensitive"
        :minLineWidth="minLineWidth"
        :maxLineWidth="maxLineWidth"
        v-model:bgColor="bgColor"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">清 空</el-button>
      </div>
    </template>
  </el-dialog>
</template>
