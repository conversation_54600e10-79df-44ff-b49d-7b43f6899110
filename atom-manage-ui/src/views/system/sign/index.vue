<script setup lang="ts">
import { useUserSign } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import UserSignEditDialog from "./dialog.vue";
import vueQr from "vue-qr/src/packages/vue-qr.vue";
import zgtj from "@/assets/images/zgtj.jpg";
import { useWindowSize } from "@vueuse/core";
//** 签名 */
defineOptions({
  name: "UserSign"
});

const { signImg, handleCreate, visible, toLoadUserSign, eSignUrl } =
  useUserSign();

const { height } = useWindowSize();
</script>

<template>
  <div>
    <el-card
      shadow="hover"
      class="jmpz-view transition-all duration-300 h-full"
      body-class="h-full"
    >
      <el-row
        :gutter="24"
        :style="{
          height: height - 100 + 'px'
        }"
      >
        <el-col :span="6" class="border-r border-gray-100">
          <div class="flex flex-col items-center justify-center h-full p-5">
            <div
              class="bg-white/30 backdrop-blur-md p-3 rounded-lg shadow-md border border-white/20"
            >
              <vue-qr :logoSrc="zgtj" :text="eSignUrl" :size="200" />
            </div>
            <div class="my-4 text-gray-600 text-base">
              微信扫码添加/更新签名
            </div>
            <el-button
              v-if="signImg"
              @click="handleCreate"
              size="large"
              type="primary"
              class="mt-2 px-6 py-3 font-medium"
              >更新签名
            </el-button>
          </div>
        </el-col>
        <el-col :span="18" class="p-5">
          <div
            class="text-lg font-semibold text-gray-800 pb-3 mb-5 border-b border-gray-100"
          >
            您的签名
          </div>
          <div
            class="flex justify-center items-center h-[calc(100%-60px)] bg-gray-50 rounded-md"
          >
            <el-image
              v-if="signImg"
              class="p-5"
              :src="signImg"
              alt="用户签名"
              style="height: 768px"
              fit="contain"
            />
            <div
              v-else
              class="flex flex-col items-center justify-center w-full py-10"
            >
              <div class="text-gray-400 mb-4">暂无签名</div>
              <el-button @click="handleCreate" type="primary" size="large"
                >添加签名</el-button
              >
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <user-sign-edit-dialog
      v-model:visible="visible"
      @confirm="toLoadUserSign"
    />
  </div>
</template>
