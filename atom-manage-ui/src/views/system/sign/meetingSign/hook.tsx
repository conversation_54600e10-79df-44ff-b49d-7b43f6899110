import {message} from "@/utils/message";
import {onMounted, reactive, ref} from "vue";
import {ElMessageBox, FormInstance} from "element-plus";
import {addUserSign, getUserName} from "@/api/system/userSign";
import { UserSign }from "@/types/system/userSign";
import { useRoute } from 'vue-router';
import {signMeeting} from "@/api/meeting/meeting";
export function userSign() {
  // 当前用户id
  const route = useRoute();
  // 表单信息
  const form = ref<UserSign>(null);
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const lineWidth = ref(6);
  const lineColor = ref('#000000');
  const bgColor = ref('');
  const isCrop = ref(false);
  const esignRef = ref(null);
  const name = ref();
  const title = ref();

  /** 提交按钮 */
  async function submitForm() {
        loading.value = true;
        esignRef.value.generate().then(res => {
           rotateBase64Img(res,-90,(img)=> {
            // imgUrl.value = img;
             form.value ={
               signPic : img,
               userId : route.query?.userId,
               meetingId: route.query?.meetingId
             };
             signMeeting(form.value).then(() => {
               message("签名成功", { type: "success" });
               esignRef.value.reset();
             }).finally( () => {
               loading.value = false;
               esignRef.value.reset();
             });
           })
        }).catch(() => {
          loading.value = false;
          return;
        })
  }


   function getUserBuUid() {
    getUserName(route.query?.userId).then(res => {
      title.value = "请"+res.data + "在下面签名！"
    })
  }

  async function rotateBase64Img(src, edg, callback) {
    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      let imgW = 0; // 图片宽度
      let imgH = 0; // 图片高度
      let size = 0; // canvas初始大小

      if (edg % 90 !== 0) {
        throw new Error("旋转角度必须是90的倍数!");
      }

      edg < 0 && (edg = (edg % 360) + 360);
      const quadrant = (edg / 90) % 4; // 旋转象限
      const cutCoor = { sx: 0, sy: 0, ex: 0, ey: 0 }; // 裁剪坐标

      const image = new Image();
      image.src = src;
      image.crossOrigin = "anonymous";

      image.onload = function () {
        imgW = image.width;
        imgH = image.height;
        size = imgW > imgH ? imgW : imgH;

        canvas.width = size * 2;
        canvas.height = size * 2;
        switch (quadrant) {
          case 0:
            cutCoor.sx = size;
            cutCoor.sy = size;
            cutCoor.ex = size + imgW;
            cutCoor.ey = size + imgH;
            break;
          case 1:
            cutCoor.sx = size - imgH;
            cutCoor.sy = size;
            cutCoor.ex = size;
            cutCoor.ey = size + imgW;
            break;
          case 2:
            cutCoor.sx = size - imgW;
            cutCoor.sy = size - imgH;
            cutCoor.ex = size;
            cutCoor.ey = size;
            break;
          case 3:
            cutCoor.sx = size;
            cutCoor.sy = size - imgW;
            cutCoor.ex = size + imgH;
            cutCoor.ey = size + imgW;
            break;
        }

        ctx.translate(size, size);
        ctx.rotate((edg * Math.PI) / 180);
        ctx.drawImage(image, 0, 0);

        const imgData = ctx.getImageData(cutCoor.sx, cutCoor.sy, cutCoor.ex, cutCoor.ey);
        if (quadrant % 2 === 0) {
          canvas.width = imgW;
          canvas.height = imgH;
        } else {
          canvas.width = imgH;
          canvas.height = imgW;
        }
        ctx.putImageData(imgData, 0, 0);

        if (typeof callback === "function") {
          callback(canvas.toDataURL("image/png", 0.7));
        }

        return new Promise((resolve) => {
          // 异步操作...
          resolve(canvas.toDataURL("image/png", 0.7));
        });
      };
    } catch (e) {
      console.log(e);
    }
  }

  function cancel() {
    if (esignRef.value) {
      esignRef.value.reset(); // 确保组件有这个方法
    }
  }

  onMounted(() => {
     getUserBuUid()
  });

    return {
      form,
      formRef,
      loading,
      lineWidth,
      lineColor,
      bgColor,
      isCrop,
      esignRef,
      submitForm,
      cancel,
      title
    };
}
