
<script setup lang="ts">
import { ref } from "vue";
import {userSign} from "./hook";
import vueEsign from "vue-esign/src/index.vue";

//** 签名 */
defineOptions({
  name: "meetingSign"
});
const {
  form,
  formRef,
  loading,
  lineWidth,
  lineColor,
  bgColor,
  isCrop,
  esignRef,
  submitForm,
  cancel,
  title
} = userSign();

</script>

<template>
  <div class="container">
    <div class="left">
      <span class="rotate-button">
        <el-button :loading="loading" type="primary" class="canvas_footer_su" @click="submitForm">确 定</el-button>
        <el-button class="canvas_footer_re" @click="cancel">清 空</el-button>
      </span>
    </div>
    <div class="center">
      <vue-esign ref="esignRef" style="border: 1px dashed #1c1a1a;" :width="600" :height="1200" :isCrop="isCrop" :lineWidth="lineWidth" :lineColor="lineColor" :bgColor.sync="bgColor" />
    </div>
    <div class="right">
      <span class="rotate-text">
        <el-alert
          :closable="false"
          show-icon
          :title="title"
          type="warning"
        />
      </span>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.container {
  display: flex;          /* 使用 Flexbox 布局 */
  width: 100%; /* 容器宽度为 100% */
  height: 100%;
}

.left {
  width: 50px;               /* 左侧固定宽度 */
  background-color: #f0f0f0;  /* 背景颜色 */
  display: flex;              /* 使用 Flexbox */
  align-items: center;        /* 垂直居中 */
  justify-content: center;     /* 水平居中 */
}

.rotate-button {
  transform: rotate(90deg);   /* 旋转90度 */
  white-space: nowrap;         /* 防止换行 */
}

.center {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column-reverse;
  justify-content: center;
  align-items: center;
}

.right {
  width:50px;
  background-color: #f0f0f0;
  margin-left: auto;
  display: flex;           /* 使用 Flexbox */
  align-items: center;     /* 垂直居中对齐 */
  justify-content: center;  /* 水平居中对齐 */
}

.rotate-text {
  transform: rotate(90deg); /* 旋转90度 */
  white-space: nowrap;      /* 防止换行 */
}

.canvas_footer_re {
  width: 100px;
  background: #f11919;
  border-radius: 10px;
}
.canvas_footer_su {
  width: 100px;
  background: #2dcb7a;
  border-radius: 10px;
}
</style>
