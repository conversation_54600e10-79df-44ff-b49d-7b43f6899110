import dayjs from "dayjs";
import { delOss, listOss } from "@/api/system/oss/oss";
import { getConfigKey, updateConfigBy<PERSON><PERSON> } from "@/api/system/config";
import { onMounted, reactive, ref } from "vue";
import { SysOss, SysOssQuery } from "@/types/system/sys-oss";
import { message } from "@/utils/message";
import { ElMessageBox, FormInstance } from "element-plus";
import { useGlobal } from "@pureadmin/utils";
import { useRouter } from "vue-router";
import { PaginationProps } from "@pureadmin/table";
import { useTimeoutFn } from "@vueuse/core";
import { ImagePreview } from "@/components/ImagePreview";

export function useOss() {
	const router = useRouter();

	const { $useDict } = useGlobal<GlobalPropertiesApi>();
	const { sys_normal_disable } = $useDict("sys_normal_disable");

	const queryParams = reactive<SysOssQuery>(new SysOssQuery());

	const dataList = ref([]);
	const loading = ref(true);
	const visible = ref(false);
	const fileType = ref(0);
	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);

	const previewListResource = ref(false);
	const dictTypeData = ref<SysOss>();
	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			index: (index: number) => {
				return pagination.pageSize * (pagination.currentPage - 1) + index + 1;
			},
			width: 70
		},
		{
			label: "主键",
			prop: "id",
			width: 70,
			align: "left"
		},
		{
			label: "文件名",
			prop: "fileName",
			width: 100
		},
		{
			label: "原名",
			prop: "originalName",
			minWidth: 200
		},
		{
			label: "文件后缀",
			prop: "fileSuffix",
			width: 100
		},
		{
			label: "文件展示",
			prop: "url",
			width: 120,
			cellRenderer: ({ row }) => {
				return (
					<>
						{previewListResource.value && checkFileSuffix(row.fileSuffix) ? (
							<ImagePreview
								width={100}
								height={100}
								src={row.url}
								previewSrcList={[row.url]}
							/>
						) : null}

						{!checkFileSuffix(row.fileSuffix) || !previewListResource.value ? (
							<span v-text={row.url} />
						) : null}
					</>
				);
			}
		},
		{
			label: "创建时间",
			width: 180,
			prop: "createTime",
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD") : ""
		},
		{
			label: "服务商",
			width: 140,
			prop: "service"
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 200,
			slot: "operation"
		}
	];

	function handleCreate() {
		visible.value = true;
		const temp = new SysOss();
		dictTypeData.value = temp;
	}

	function handleUpdate(row: SysOss) {
		visible.value = true;
		dictTypeData.value = row;
	}

	function handleDelete(row?: SysOss) {
		if (row?.id) {
			delOss(row.id).then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm(
			'是否确认删除OSS对象存储编号为"' + ids.value + '"的数据项？',
			{
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				title: "系统提示"
			}
		)
			.then(function () {
				return delOss(ids.value);
			})
			.then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 用户状态修改  */
	function handlePreviewListResource() {
		console.log(previewListResource.value);
		previewListResource.value = !previewListResource.value;
		console.log(previewListResource.value);
		const text = previewListResource.value ? "启用" : "停用";
		ElMessageBox.confirm('确认要"' + text + '""预览列表图片"配置吗?')
			.then(() => {
				return updateConfigByKey(
					"sys.oss.previewListResource",
					previewListResource.value
				);
			})
			.then(() => {
				handleQuery();
				message(text + "成功", { type: "success" });
			})
			.catch(function () {
				previewListResource.value = previewListResource.value !== true;
			});
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	async function getList() {
		loading.value = true;
		getConfig();
		const { data, total } = await listOss({ ...pagination, ...queryParams });
		dataList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	async function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	// * 页面大小改变
	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	// * 翻页
	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	/** 文件按钮操作 */
	function handleFile() {
		visible.value = true;
		fileType.value = 0;
	}

	/** 图片按钮操作 */
	function handleImage() {
		visible.value = true;
		fileType.value = 1;
	}

	function checkFileSuffix(fileSuffix) {
		const arr = ["png", "jpg", "jpeg"];
		return arr.some(type => {
			return fileSuffix.indexOf(type) > -1;
		});
	}

	function getConfig() {
		getConfigKey("sys.oss.previewListResource").then(res => {
			previewListResource.value =
				res.msg === undefined ? true : res.msg === "true";
			console.log(previewListResource.value);
		});
	}

	/** 配置查询 */
	function handleOssConfig() {
		router.push({ name: "OssConfig" });
	}

	onMounted(() => {
		handleQuery();
	});

	return {
		sys_normal_disable,
		dictTypeData,
		visible,
		fileType,
		previewListResource,
		queryParams,
		pagination,
		loading,
		columns,
		dataList,
		resetQuery,
		handleQuery,
		handleUpdate,
		handleCreate,
		handleDelete,
		handleFile,
		handleImage,
		handleOssConfig,
		handlePreviewListResource,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange
	};
}
