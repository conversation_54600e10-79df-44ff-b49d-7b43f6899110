<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";
import FileUpload from "@/components/FileUpload";
import ImageUpload from "@/components/ImageUpload";

defineOptions({
  name: "FileUploadDialog"
});

const visible = defineModel<boolean>("visible", { default: false });

const props = defineProps({
  type: {
    type: Number,
    default: 0
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "ppt", "txt", "pdf"]
  }
});

const emit = defineEmits(["confirm"]);

const title = computed(() => {
  return props.type === 0 ? "上传文件" : "上传图片";
});

watch(
  () => visible,
  val => {
    visible.value = val.value;
  }
);

watch(visible, val => {
  emit("update:visible", val);
});

const loading = ref(false);
// 表单信息
const form = reactive({
  file: []
});
const ossRef = ref();

const rules = {
  file: [{ required: true, message: "附件不能为空", trigger: "blur" }]
};

/** 提交按钮 */
function submitForm() {
  ossRef.value.validate(valid => {
    if (valid) {
      visible.value = false;
      emit("confirm", form.file);
    }
  });
}
const cancel = () => {
  visible.value = false;
};
</script>

<template>
  <!-- 添加或修改OSS对象存储对话框 -->
  <el-dialog :title="title" v-model="visible" width="500px">
    <el-form ref="ossRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="文件名" prop="file">
        <FileUpload
          v-model="form.file"
          :fileType="fileType"
          v-if="type === 0"
        />
        <ImageUpload v-model="form.file" v-if="type === 1" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitForm"
          >确 定
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
