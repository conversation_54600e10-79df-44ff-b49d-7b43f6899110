<script setup lang="ts">
import { ref } from "vue";
import { useOssConfig } from "./config-hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import OssConfigEditDialog from "./components/OssConfigEditDialog.vue";
defineOptions({
  name: "OssConfig"
});

const queryRef = ref(null);
const tableRef = ref(null);

const {
  configData,
  visible,

  queryParams,
  pagination,
  loading,
  columns,
  dataList,
  resetQuery,
  handleQuery,
  handleUpdate,
  handleCreate,
  handleDelete,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange
} = useOssConfig();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="配置key" prop="configKey">
        <el-input
          v-model="queryParams.configKey"
          placeholder="配置key"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="桶名称" prop="bucketName">
        <el-input
          v-model="queryParams.bucketName"
          placeholder="请输入桶名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否默认" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 200px"
        >
          <el-option key="0" label="是" value="0" />
          <el-option key="1" label="否" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar
      title="配置列表"
      :columns="columns"
      :tableRef="tableRef?.getTableRef()"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="primary"
          class="sm:shrink-button"
          plain
          :icon="useRenderIcon('ep:plus')"
          @click="handleCreate()"
          v-auth="['system:oss:add']"
          >新增
        </el-button>
        <el-button
          type="danger"
          class="sm:shrink-button"
          :icon="useRenderIcon('ep:delete')"
          @click="handleDelete()"
          v-auth="['system:oss:remove']"
        >
          删除
        </el-button>
      </template>

      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          :pagination="pagination"
          @selection-change="handleSelectionChange"
          @page-current-change="pageCurrentChange"
          @page-size-change="pageSizeChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleUpdate(row)"
              :icon="useRenderIcon('ep:edit-pen')"
            >
              修改
            </el-button>

            <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <OssConfigEditDialog v-model:visible="visible" :data="configData" />
  </div>
</template>
