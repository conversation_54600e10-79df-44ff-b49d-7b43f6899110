import dayjs from "dayjs";

import { delConfig, listConfig, refreshCache } from "@/api/system/config";

import { onMounted, reactive, ref } from "vue";
import { SysConfig, SysConfigQuery } from "@/types/system/sys-config";
import { message } from "@/utils/message";
import { FormInstance } from "element-plus";
import { PaginationProps } from "@pureadmin/table";
import { useGlobal } from "@pureadmin/utils";
import DictTag from "@/components/DictTag";
import { useTimeoutFn } from "@vueuse/core";

export function useConfig() {
	const { $useDict } = useGlobal<GlobalPropertiesApi>();
	const { sys_yes_no } = $useDict("sys_yes_no");

	const queryParams = reactive<SysConfigQuery>(new SysConfigQuery());

	const dataList = ref([]);
	const loading = ref(true);
	const visible = ref(false);
	const configData = ref();

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			label: "勾选列",
			type: "selection",
			width: 55,
			align: "left"
		},
		{
			label: "序号",
			type: "index",
			index: (index: number) => {
				return pagination.pageSize * (pagination.currentPage - 1) + index + 1;
			},
			width: 70
		},
		{
			label: "参数名称",
			prop: "configName",
			width: 180,
			align: "left"
		},
		{
			label: "参数键名",
			prop: "configKey",
			width: 100
		},
		{
			label: "参数键值",
			prop: "configValue",
			minWidth: 50
		},
		{
			label: "备注",
			prop: "remark",
			minWidth: 50
		},
		{
			label: "系统内置",
			prop: "isSys",
			width: 100,
			cellRenderer: ({ row }) => (
				<DictTag options={sys_yes_no} value={row.isSys} />
			)
		},
		{
			label: "创建时间",
			width: 180,
			minWidth: 100,
			prop: "createTime",
			formatter: ({ createTime }) =>
				createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss") : ""
		},
		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 140,
			slot: "operation"
		}
	];

	function handleCreate() {
		visible.value = true;
		configData.value = new SysConfig();
	}

	function handleUpdate(row: SysConfig) {
		visible.value = true;
		configData.value = row;
	}

	/** 刷新缓存按钮操作 */
	function handleRefreshCache() {
		refreshCache().then(() => {
			message("刷新缓存成功", { type: "success" });
		});
	}

	/** 删除按钮操作 */
	function handleDelete(row: SysConfig) {
		delConfig(row.id).then(() => {
			getList();
			message("删除成功", { type: "success" });
		});
	}

	function handleSelectionChange(val) {
		console.log(val);
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await listConfig({ ...pagination, ...queryParams });
		dataList.value = data;
		pagination.total = total;
		useTimeoutFn(() => {
			loading.value = false;
		}, 200);
	}

	async function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	// * 页面大小改变
	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	// * 翻页
	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	onMounted(() => {
		handleQuery();
	});

	return {
		queryParams,
		loading,
		columns,
		dataList,
		resetQuery,
		visible,
		pagination,
		configData,
		handleQuery,
		handleUpdate,
		handleCreate,
		handleDelete,
		handleRefreshCache,
		handleSelectionChange,
		pageSizeChange,
		pageCurrentChange
	};
}
