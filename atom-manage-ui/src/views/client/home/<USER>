<script setup lang="tsx">
import dayjs from "dayjs";
import { listJoinerMeetingByWaiting } from "@/api/meeting/meeting";
import { JoinerMeeting } from "@/types/meeting/meeting";
import { assetPath } from "@/utils/atom";

import { onMounted, ref, computed } from "vue";
import { groupBy } from "lodash";
import WarningIcon from "@iconify-icons/ep/warning";

const meetingList = ref<JoinerMeeting[]>([]);
const selectedMeeting = ref<JoinerMeeting>(null);

const emit = defineEmits(["select"]);

const listMode: Ref<"group" | "list"> = ref("list");

function getMeetingList() {
  listJoinerMeetingByWaiting().then(res => {
    meetingList.value = res.data;
    if (meetingList.value.length > 0) {
      handleSelectMeeting(res.data[0]);
    }
  });
}

const meetingGroup = computed(() => {
  return groupBy(meetingList.value, "meetingDate");
});

// 切分时间
function toHourMinute(time: string): string {
  const parts = time.split(":");
  return parts[0] + ":" + parts[1];
}

function dayAndMonth(date: string) {
  return dayjs(date).format("M月D号");
}

function handleSelectMeeting(item) {
  selectedMeeting.value = item;
  emit("select", item);
}

onMounted(() => {
  getMeetingList();
});
</script>

<template>
  <div class="h-[200px] overflow-auto">
    <el-empty v-if="meetingList.length < 1" description=" " :image-size="150">
      <template #image>
        <img
          :src="assetPath('images/nomeet.png')"
          class="h-auto opacity-50 w-34"
        />
      </template>
    </el-empty>
    <template v-else>
      <template v-if="listMode == 'group'">
        <div v-for="(list, key) in meetingGroup" :key="key">
          <div class="my-2 text-sm text-blue-500 md:my-2 text-start">
            {{ dayjs(key).isSame(dayjs(), "day") ? "今天" : "" }}
            {{ dayjs(key).format("M月D号") }}
          </div>

          <!-- 调整日期的外边距 -->
          <div class="mb-3 md:mb-4" v-for="item in list" :key="item.id">
            <!-- 调整会议信息的外边距 -->
            <div
              @click="handleSelectMeeting(item)"
              class="flex items-center justify-between p-2 text-black rounded-sm md:p-3"
              :class="{
                'bg-gradient-to-r from-[#c9e4ff] to-[#b2d6ff]':
                  item.id == selectedMeeting?.id
              }"
            >
              <div class="text-[#1f2e9c]">
                <div class="flex items-center text-sm md:text-base">
                  {{ item.meetingTitle }}
                  <iconify-icon-offline
                    class="ml-1 text-red-500"
                    :icon="WarningIcon"
                  />
                  <!-- <span>（请上传会议/议题文件）</span> -->
                </div>
                <!-- 调整字体大小 -->
                <div class="text-xs md:text-sm">
                  {{ toHourMinute(item.meetingTime[0]) }} -
                  {{ toHourMinute(item.meetingTime[1]) }}

                  <span>会议组织部门: 办公室</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <div class="divide-y divide-blue-400 divide-dashed">
          <div
            v-for="item in meetingList"
            :key="item.id"
            @click="handleSelectMeeting(item)"
            class="flex items-center justify-between p-2 text-black rounded-sm md:p-3"
            :class="{
              'bg-gradient-to-r from-[#c9e4ff] to-[#b2d6ff]':
                item.id == selectedMeeting?.id
            }"
          >
            <div class="text-[#1f2e9c]">
              <div class="flex items-center text-base md:text-base">
                {{ item.meetingTitle }}
                <iconify-icon-offline
                  class="ml-1 text-red-500"
                  :icon="WarningIcon"
                />
                <!-- <span>（请上传会议/议题文件）</span> -->
              </div>
              <!-- 调整字体大小 -->
              <div class="text-xs md:text-sm">
                {{ dayAndMonth(item.meetingDate) }}
                {{ toHourMinute(item.meetingTime[0]) }} -
                {{ toHourMinute(item.meetingTime[1]) }}
                <!-- <span>会议组织部门: 办公室</span> -->
              </div>
            </div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<style scoped></style>
