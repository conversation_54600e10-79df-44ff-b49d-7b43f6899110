<script setup lang="ts">
import { onMounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { assetPath } from "@/utils/atom";
import { computed } from "vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  name: {
    type: String,
    default: "菜单"
  },
  note: {
    type: String,
    required: false
  },
  icon: {
    type: String,
    default: "iconpng/icon1.png"
  },
  colorClass: {
    type: String,
    default: "bg-gradient-to-b from-[#448ef9FF] to-[#025fcfFF]"
  },

  splitMode: {
    type: String,
    default: "vertical"
  },
  imageCss: {
    type: String,
    required: false
  }
});
</script>

<template>
  <div
    class="flex flex-col p-4 text-white shadow-md item-card rounded-xl"
    :class="colorClass"
    :style="{
      height: splitMode === 'horizontal' ? '90%' : 'inherit'
    }"
  >
    <div class="card-title">
      <div class="text-lg two-lines">
        <span
          class="pr-1"
          :class="{
            'border-l-2 border-white': splitMode === 'horizontal'
          }"
        />
        {{ name }}
      </div>
      <div class="split-line" v-if="splitMode != 'horizontal'" />
    </div>

    <div class="flex justify-center mt-auto">
      <slot name="note">
        <span class="text-sm opacity-80">{{ note }}</span>
      </slot>
      <img class="ml-auto w-9" :class="imageCss" :src="assetPath(icon)" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.item-card {
  // max-width: 180px;
  width: 100%;
  height: inherit;
}
.card-title {
  height: 3.3rem;
}

.two-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 控制文本的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.6; /* 根据需要调整 */
  max-height: 3.2em; /* 这里是行高的两倍，因为我们设置了两行文本 */
  word-wrap: break-word; /* 防止单词在换行时被拆分 */
}

.split-line {
  width: 2rem;
  height: 2px;
  background-color: #fff;
  @apply ml-1;
}
</style>
