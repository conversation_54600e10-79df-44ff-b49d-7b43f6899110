<script setup lang="ts">
import AtomHeader from "@/views/client/layout/components/Header.vue";
import Menu from "./menu.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { assetPath } from "@/utils/atom";
import { computed, ref } from "vue";
import { isEmpty, useGlobal } from "@pureadmin/utils";
import { useRouter } from "vue-router";
import { useData } from "./cardDate";
import Calendar from "./calendar.vue";
import MeetingList from "./MeetingList.vue";
import MeetingOperationView from "./MeetingOperationView.vue";
import { useElementSize } from "@vueuse/core";
import { message } from "@/utils/message";
defineOptions({
  name: "JoinerHome"
});

const { $useDict } = useGlobal<GlobalPropertiesApi>();

const { meeting_type } = $useDict("meeting_type");

const user = useUserStoreHook();
const router = useRouter();

const cardBoxRef = ref(null);
const { width, height: cardBoxHeight } = useElementSize(cardBoxRef);

const avatarUrl = computed(() => {
  return assetPath("avatar/default.svg");
  return assetPath(
    "avatar/" + isEmpty(user.avatar) ? "default.svg" : user.avatar
  );
});

function navigation(row) {
  if (!row.path) {
    message("功能开发中...", { type: "info" });
    return;
  }
  router.push({
    path: row.path
  });
}

function handleChangeMeeting(item) {
  console.log(item);
}

const { cardList } = useData();
</script>

<template>
  <div class="flex flex-col backgroud">
    <AtomHeader />

    <div class="flex">
      <Menu class="px-6" />
      <!-- 主体内容 -->
      <main
        class="flex items-start justify-center flex-1 flex-grow px-8 mt-8 mb-8 lg:px-16"
      >
        <div class="w-full h-full p-4">
          <div
            id="home-content"
            class="flex flex-wrap w-full h-full bg-white divide-x divide-gray-300 rounded-lg md:py-8 bg-opacity-30 shadow-box"
          >
            <!-- 左侧区域 -->
            <div class="flex flex-col w-full px-3 lg:w-1/3 md:px-4">
              <!-- 调整左侧区域的内边距 -->
              <div
                class="flex flex-col items-center md:flex-row md:justify-between"
              >
                <!-- 调整左侧顶部区域的布局 -->
                <div class="flex items-center mb-3 md:mb-0">
                  <!-- 调整用户信息的布局 -->
                  <img
                    :src="avatarUrl"
                    class="w-16 h-16 p-2 bg-[#e9f3fe] rounded-full"
                  />
                  <!-- 调整头像大小 -->
                  <div class="ml-2 text-[#1D2460FF]">
                    <div class="text-sm md:text-lg">{{ user.username }}</div>
                    <!-- 调整字体大小 -->
                    <div class="text-sm">
                      {{ user.orgName }} {{ user.post }}
                    </div>
                  </div>
                </div>

                <!-- <div>提醒</div> -->
                <!-- 在小屏幕上隐藏提醒内容 -->

                <div class="flex space-x-2 md:space-x-3">
                  <!-- 调整按钮的间距 -->
                  <el-button type="primary" class="bg-btn-primary" size="small"
                    >历史会议></el-button
                  >
                </div>
              </div>

              <div class="my-2 border-t border-gray-400 md:my-4" />

              <div class="flex flex-col justify-between flex-1">
                <MeetingList @select="handleChangeMeeting" />
                <div>
                  <Calendar />
                </div>
              </div>
            </div>

            <div class="flex-1 mt-4">
              <MeetingOperationView />
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.backgroud {
  background-image: url("@/assets/images/background.png");
  /* height: 100%;
  width: 100%; */
  background-color: #f5f7fa;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh;
}
.folder-bg {
  background-image: url("@/assets/images/light_mark.png");
  max-width: inherit;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.shadow-box {
  box-shadow: 0 0 0px 20px rgb(255 255 255 / 15%);
}

.two-lines {
  display: -webkit-box;
  height: 3.2em;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 控制文本的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.6; /* 根据需要调整 */
  max-height: 3.2em; /* 这里是行高的两倍，因为我们设置了两行文本 */
  word-wrap: break-word; /* 防止单词在换行时被拆分 */
}

/* 渐变按钮背景 */
.bg-btn-primary {
  @apply bg-gradient-to-r from-[#266abc] to-[#4f86d5];
}
</style>
