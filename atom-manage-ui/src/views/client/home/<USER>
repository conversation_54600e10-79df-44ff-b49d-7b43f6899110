export function useData() {
  const cardList = [
    {
      name: "会议回执",
      index: 1,
      icon: "iconpng/icon1.png",
      backgroudColor: "bg-gradient-to-b from-[#448ef9cc] to-[#025fcfcc]",
      span: "1/5"
    },
    {
      name: "会议签到",
      index: 2,
      icon: "iconpng/icon2.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "座位查看",
      index: 3,
      icon: "iconpng/icon3.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "会议日程会序册",
      index: 4,
      icon: "iconpng/icon4.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "参考资料",
      index: 5,
      icon: "iconpng/icon13.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "会议资料议题查看",
      note: "点击查看该会议会议资料",
      index: 11,
      icon: "iconpng/icon11.png",
      backgroudColor: "bg-gradient-to-r from-[#26cf5cFF] to-[#2ebb5bFF]",
      span: "2/5",
      path: "/joiner/file-manager"
    },
    {
      name: "候会提醒",
      index: 12,
      note: "提醒信息显示支持自定义设置",
      icon: "iconpng/icon12.png",
      backgroudColor: "bg-gradient-to-r from-[#e6ac82FF] to-[#cf976eFF]",
      span: "2/5"
    },
    {
      name: "会议记录",
      index: 13,
      icon: "iconpng/icon13.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "会议便签稿纸",
      note: "手写或输入的会议草稿",
      index: 6,
      icon: "iconpng/icon6.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d5cc] to-[#1b5ba7cc]",
      span: "2/5"
    },
    {
      name: "演示文稿",
      index: 10,
      note: "演示会议需演示文稿",
      icon: "iconpng/icon10.png",
      backgroudColor: "bg-gradient-to-b from-[#21757ecc] to-[#0e5b63cc]",
      span: "2/5"
    },
    {
      name: "视频文件",
      index: 7,
      icon: "iconpng/icon14.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "会议纪要",
      index: 13,
      icon: "iconpng/icon13.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "督查督办",
      index: 16,
      icon: "iconpng/icon16.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "通讯录",
      index: 15,
      icon: "iconpng/icon15.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },

    // {
    //   name: "视频文件",
    //   index: 5,
    //   icon: "iconpng/icon5.png",
    //   backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
    //   span: "1/5"
    // },

    {
      name: "投票表决",
      index: 8,
      icon: "iconpng/icon8.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    {
      name: "进展统计",
      index: 8,
      icon: "iconpng/icon8.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/5"
    },
    // {
    //   name: "参考资料资讯",
    //   index: 9,
    //   icon: "iconpng/icon9.png",
    //   backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
    //   span: "1/5"
    // },

    // {
    //   name: "视频参会入口",
    //   index: 14,
    //   icon: "iconpng/icon14.png",
    //   backgroudColor: "bg-gradient-to-r from-[#5f8fe5FF] to-[#4c7ed9FF]",
    //   span: "1/5"
    // },

    {
      name: "参会须知",
      index: 17,
      icon: "iconpng/icon17.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/6",
      splitModel: "horizontal"
    },
    {
      name: "联系我们",
      index: 18,
      icon: "iconpng/icon17.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/6",
      splitModel: "horizontal"
    },
    {
      name: "地图导航",
      index: 19,
      icon: "iconpng/icon17.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/6",
      splitModel: "horizontal"
    },
    {
      name: "我的餐券",
      index: 20,
      icon: "iconpng/icon17.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/6",
      splitModel: "horizontal"
    },
    {
      name: "酒店住宿",
      index: 21,
      icon: "iconpng/icon17.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/6",
      splitModel: "horizontal"
    },
    {
      name: "我的签名",
      index: 22,
      icon: "iconpng/icon17.png",
      backgroudColor: "bg-gradient-to-b from-[#4f86d590] to-[#1b5ba790]",
      span: "1/6",
      splitModel: "horizontal"
    }
  ];

  return {
    cardList
  };
}
