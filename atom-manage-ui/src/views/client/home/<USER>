<script setup lang="ts">
import DocumentIcon from "@iconify-icons/ri/file-text-line";
import EditIcon from "@iconify-icons/ri/edit-fill";
import CalendarCheckIcon from "@iconify-icons/ri/calendar-check-fill";
import ChatCheckIcon from "@iconify-icons/ri/chat-check-line";
import EditBoxIcon from "@iconify-icons/ri/edit-box-fill";
import PermContactIcon from "@iconify-icons/ic/baseline-perm-contact-calendar";
import MajestionEditPenIcon from "@/assets/svg/majesticons-edit-pen.svg?component";
import { message } from "@/utils/message";

const meetingResourceButtons = [
  {
    name: "会序册",
    action: () => {
      console.log("会序册");
    }
  },
  {
    name: "参考资料",
    action: () => {
      console.log("参考资料");
    }
  },
  {
    name: "议题资料",
    action: () => {
      console.log("议题资料");
    }
  },
  {
    name: "会议纪要",
    action: () => {
      console.log("会议纪要");
    }
  },
  {
    name: "视频文件",
    action: () => {
      console.log("视频文件");
    }
  },
  {
    name: "演示文稿",
    action: () => {
      console.log("会议记录");
    }
  }
];

const handleFeature = () => {
  message("该功能暂未开放", { type: "warning" });
};
</script>

<template>
  <div class="px-5">
    <div class="grid grid-cols-4 gap-4">
      <!-- Sidebar -->
      <div
        class="col-span-2 p-6 text-white bg-gradient-to-br from-[#448ef9bb] to-[#67a3ecbb] rounded-lg shadow-lg"
      >
        <h2 class="mb-4 text-lg font-bold text-underline">会议资料</h2>
        <div class="flex flex-wrap xl:w-[24rem] xl:mb-36">
          <button
            class="mb-2 mr-2 text-sm xl:mr-6 btn"
            v-for="item in meetingResourceButtons"
            :key="item.name"
          >
            {{ item.name }}
          </button>
        </div>
        <IconifyIconOffline class="ml-auto text-[5rem]" :icon="DocumentIcon" />
      </div>

      <!-- Main Content -->
      <div class="grid grid-cols-2 col-span-2 gap-4">
        <!-- Document Management -->
        <div
          @click="handleFeature"
          class="flex cursor-pointer flex-col col-span-1 p-6 text-white bg-gradient-to-r from-[#79d28fbb] to-[#72e5a2bb] rounded-lg shadow-lg"
        >
          <h2 class="mb-4 text-lg font-bold text-underline">会议签到</h2>
          <IconifyIconOffline
            class="ml-auto mt-auto text-[2em]"
            :icon="EditIcon"
          />
        </div>

        <!-- Reporting -->
        <div
          @click="handleFeature"
          class="flex cursor-pointer flex-col col-span-1 p-6 text-white bg-gradient-to-r from-[#c8aa92bb] to-[#dec0adbb] rounded-lg shadow-lg"
        >
          <h2 class="mb-4 text-lg font-bold text-underline">督查督办</h2>
          <IconifyIconOffline
            class="ml-auto mt-auto text-[2em]"
            :icon="CalendarCheckIcon"
          />
          <!-- Content -->
        </div>

        <!-- Data Analysis -->
        <div
          @click="handleFeature"
          class="flex cursor-pointer flex-col col-span-1 p-6 text-white bg-gradient-to-r from-[#a7afecbb] to-[#bac4f3bb] rounded-lg shadow-lg"
        >
          <h2 class="mb-4 text-lg font-bold text-underline">投票表决</h2>
          <!-- Content -->
          <IconifyIconOffline
            class="ml-auto mt-auto text-[2em]"
            :icon="ChatCheckIcon"
          />
        </div>

        <!-- Settings -->
        <div
          @click="handleFeature"
          class="flex cursor-pointer flex-col col-span-1 p-6 text-white bg-gradient-to-r from-[#6e98a4bb] to-[#77b7c1bb] rounded-lg shadow-lg"
        >
          <h2 class="mb-4 text-lg font-bold text-underline">会议便签稿纸</h2>
          <!-- Content -->
          <IconifyIconOffline
            class="ml-auto mt-auto text-[2em]"
            :icon="EditBoxIcon"
          />
        </div>
      </div>

      <div
        @click="handleFeature"
        class="flex cursor-pointer flex-col col-span-2 p-6 text-white bg-gradient-to-r from-[#025ecfbb] to-[#448ff9bb] rounded-lg shadow-lg"
      >
        <h2 class="mb-12 text-lg font-bold text-underline">通讯录</h2>
        <IconifyIconOffline
          class="ml-auto mt-auto text-[2em]"
          :icon="PermContactIcon"
        />
      </div>

      <div
        @click="handleFeature"
        class="flex cursor-pointer flex-col col-span-2 p-6 text-white bg-gradient-to-r from-[#025ecfbb] to-[#448ff9bb] rounded-lg shadow-lg"
      >
        <h2 class="mb-12 text-lg font-bold text-underline">我的签名</h2>

        <MajestionEditPenIcon class="ml-auto mt-auto text-[2em]" />
      </div>
    </div>
  </div>
</template>

<style>
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  width: 6rem;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.25rem;
}

.btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.text-underline {
  position: relative;
  padding-bottom: 0.25em;
}

.text-underline::after {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 1.5rem;
  height: 2px;
  background-color: #ffffffd6;
  border-radius: 2px;
  overflow: hidden;
  content: "";
  display: block;
}
</style>
