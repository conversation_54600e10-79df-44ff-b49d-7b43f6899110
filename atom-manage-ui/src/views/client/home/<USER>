<script setup lang="ts">
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { ref } from "vue";

const calendar = ref();
const trans = (str: string) => str.replace(/(\d+) 年 (\d+) 月/, "$1-$2");

//月份改变时获取数据
function selectDate(val: string, date?: string) {
  calendar.value.selectDate(val);

  // let dateParam: string;
  // switch (val) {
  //   case "prev-month":
  //     dateParam = dayjs(trans(date)).subtract(1, "M").format("YYYY-MM");
  //     break;
  //   case "today":
  //     dateParam = dayjs(new Date()).format("YYYY-MM");
  //     break;
  //   case "next-month":
  //     dateParam = dayjs(trans(date)).add(1, "M").format("YYYY-MM");
  //     break;
  // }
}
</script>

<template>
  <div class="calendar-container">
    <VCalendar borderless transparent expanded trim-weeks />
    <!-- <el-calendar ref="calendar">
      <template #header="{ date }">
        <span class="font-semibold text-blue-600">{{ date }}</span>
        <el-button-group>
          <el-button
            type="text"
            :icon="ArrowLeftBold"
            size="small"
            @click="selectDate('prev-month', date)"
          />

          <el-button type="text" size="small" @click="selectDate('today')">
            <template #icon>
              <Iconify icon="ic:outline-circle" />
            </template>
          </el-button>
          <el-button
            type="text"
            :icon="ArrowRightBold"
            size="small"
            @click="selectDate('next-month', date)"
          />
        </el-button-group>
      </template>
      <template #date-cell="{ data }">
        <span class="absolute z-10 py-1 -translate-x-1/2 left-1/2">
          {{ dayjs(data.day).format("DD") }}
        </span>
      </template>
    </el-calendar> -->
  </div>
</template>

<style scoped lang="scss">
.calendar-container {
  background-color: #ffffff6c;
  border-radius: 8px;
  :deep(.vc-arrow) {
    background: none;
  }
  :deep(.vc-title) {
    background: none;
  }
}
</style>
