<script setup lang="ts">
import { ref, watch } from "vue";

import VuePdfEmbed from "vue-pdf-embed";

// optional styles
import "vue-pdf-embed/dist/styles/annotationLayer.css";
import "vue-pdf-embed/dist/styles/textLayer.css";

import { useRoute, useRouter } from "vue-router";
import { getFiles } from "@/api/meeting/meeting";
import { getShareUrl } from "@/api/system/oss/oss";

defineOptions({
  name: "PdfPreview"
});

const route = useRoute();
const router = useRouter();
const pageCount = ref(1);
const totalCount = ref(1);
const visibleDrawer = ref(false);
const fileList = ref([]);

const previewFile = ref("");

function handleDocumentLoad({ numPages }) {
  totalCount.value = numPages;
}

function handleBack() {
  router.back();
}

// 监听pageCount
watch(
  () => pageCount.value,
  val => {
    if (val < 1) {
      pageCount.value = 1;
    } else if (val > totalCount.value) {
      pageCount.value = totalCount.value;
    }
  }
);

function getFileList(meetingId: number) {
  return getFiles({ meetingId }).then(res => {
    pageCount.value = 1;
    fileList.value = res.data;
    return res;
  });
}

function getFileUrl(ossId: number) {
  getShareUrl(ossId).then(res => {
    previewFile.value = res.data;
  });
}

// 获取跳转过来的路由id
(() => {
  const ossId = route.query && route.query.ossId;
  const meetingId = route.params && route.params.id;

  if (ossId) {
    const id = ossId as any;
    getFileUrl(id);
    getFileList(meetingId as any);
  } else {
    getFileList(meetingId as any).then(res => {
      getFileUrl(res.data[0].ossId);
    });
  }
})();
</script>

<template>
  <div class="flex">
    <el-drawer v-model="visibleDrawer" title="附件列表" :with-header="false">
      <div>
        <h2 class="font-mono text-lg">附件列表</h2>
        <div class="p-2">
          <div
            v-for="(file, index) in fileList"
            :key="file.id"
            @click="getFileUrl(file.ossId)"
            class="text-base text-gray-700 cursor-pointer"
          >
            {{ index + 1 }}、 {{ file.name }}
          </div>
        </div>
      </div>
    </el-drawer>

    <VuePdfEmbed
      class="flex-1"
      :source="previewFile"
      :page="pageCount"
      @loaded="handleDocumentLoad"
    />

    <div
      class="fixed flex justify-between bottom-0 px-4 z-10 w-full bg-[#444444]"
    >
      <el-button type="text" @click="handleBack">返回</el-button>

      <div class="flex text-blue-50">
        <span
          class="text-lg font-bold"
          v-show="pageCount > 1"
          @click="pageCount--"
          ><</span
        >
        <span
          ><el-input
            v-model="pageCount"
            placeholder=""
            size="small"
            type="number"
            :max="totalCount"
            :min="1"
            class="!w-12 mx-3"
          >
            <template #suffix> ss </template>
          </el-input>
        </span>
        <span
          class="text-lg font-bold"
          v-show="pageCount < totalCount"
          @click="pageCount++"
          >></span
        >
      </div>
      <el-button
        type="text"
        @click="visibleDrawer = !visibleDrawer"
        icon="el-icon-arrow-right"
        >附件</el-button
      >
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
