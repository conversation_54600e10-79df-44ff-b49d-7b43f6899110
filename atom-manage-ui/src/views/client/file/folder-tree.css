.floder-tree .el-tree-node .el-tree-node__expand-icon {
  display: none;
}

.floder-tree {
  --el-tree-node-content-height: none;
}

.floder-tree .el-tree-node:focus > .el-tree-node__content {
  --el-tree-node-hover-bg-color: transparent;
  color: #387dfe;
}
.floder-tree .el-tree-node:hover > .el-tree-node__content {
  --el-tree-node-hover-bg-color: transparent;
  color: #387dfe;
}

.floder-tree .highlight::before {
  content: "•"; /* 圆点符号 */
  @apply mr-2 text-black; /* 举例：右边距2，红色文字 */
}

.floder-tree.el-tree--highlight-current
  .el-tree-node.is-current
  .el-tree-node__content {
  background-color: transparent !important;
}
/* .floder-tree .el-tree-node.is-expanded > .el-tree-node__children {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
} */
