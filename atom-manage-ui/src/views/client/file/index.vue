<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import AtomHeader from "@/views/client/layout/components/Header.vue";
import FolderTree from "./FolderTree";
import { reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useGlobal } from "@pureadmin/utils";
import { getFolderTree, listFile } from "@/api/meeting/file";
import { PaginationProps } from "@pureadmin/table";
import { FileEntityQuery } from "@/types/meeting/file";
import dayjs from "dayjs";

interface Tree {
  label: string;
  value: string;
  children?: Tree[];
}

const { $useDict } = useGlobal<GlobalPropertiesApi>();

const { meeting_file_type, meeting_type } = $useDict(
  "meeting_file_type",
  "meeting_type"
);
const route = useRoute();
const router = useRouter();

defineOptions({
  name: "JoinerFilePreview"
});

const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1
});
const queryForm = ref(new FileEntityQuery());
const queryRef = ref();

const treeOptions = ref([]);

const tableData = ref([]);
const title = ref();
const meetingId = ref(0);
function handlePreview(row) {
  router.push({
    name: "PdfPreview",
    params: {
      id: meetingId.value
    },
    query: {
      ossId: row.ossId
    }
  });
}

function handleBack() {
  router.back();
}

function onCurrentChange(num) {
  pagination.currentPage = num;
  getMeetingFileList();
}

function getTree() {
  getFolderTree().then(response => {
    treeOptions.value = response.data;
  });
}

function getMeetingFileList() {
  listFile(queryForm.value, pagination).then(res => {
    tableData.value = res.data;
    pagination.total = res.total;
  });
}

function handleTreeSelect(e) {
  queryForm.value.parentId = e.id;
  getMeetingFileList();
}

function handleQuery() {
  pagination.currentPage = 1;
  getMeetingFileList();
}

function resetQuery() {
  queryForm.value = {};
  handleQuery();
}

(() => {
  getTree();
  getMeetingFileList();
})();
</script>

<template>
  <div class="flex flex-col backgroud">
    <atom-header />
    <!-- 主体内容 -->
    <main
      class="flex items-center justify-center flex-1 flex-grow p-0 mb-4 md:p-6"
    >
      <div
        class="flex h-full w-full p-6 bg-white bg-opacity-20 min-h-[60vh] rounded-lg"
      >
        <div class="rounded-lg w-[300px] overflow-hidden mr-4">
          <FolderTree
            @selected="handleTreeSelect"
            class="w-full h-full p-3 !bg-white !bg-opacity-20"
            :data="treeOptions"
          />
        </div>

        <div class="flex-1 w-full p-4 bg-white rounded-lg bg-opacity-20">
          <div class="flex justify-between">
            <span class="inline-block px-3 py-1 mb-4 text-white bg-[#4678f6]">
              {{ title ?? "会议资料" }}</span
            >

            <el-button type="text" class="text-[#4678f6]" @click="handleBack"
              >返回</el-button
            >
          </div>

          <div class="w-full">
            <el-form
              :model="queryForm"
              class="flex flex-wrap"
              ref="queryRef"
              :inline="true"
            >
              <el-form-item label="" class="w-48">
                <el-select
                  v-model="queryForm.flag"
                  placeholder="请选择文件类型"
                  clearable
                >
                  <el-option
                    v-for="item of meeting_file_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" class="w-48">
                <el-date-picker
                  clearable
                  v-model="queryForm.date"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="请选择上传日期"
                />
              </el-form-item>

              <el-button
                type="primary"
                class="!bg-[#4678f6]"
                :icon="useRenderIcon('ep:search')"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button
                plain
                class="bg-white"
                :icon="useRenderIcon('ep:refresh')"
                @click="resetQuery()"
                >重置
              </el-button>
            </el-form>
          </div>

          <div>
            <el-table
              :data="tableData"
              style="width: 100%"
              class="mt-4"
              :header-cell-style="{
                'background-image':
                  'linear-gradient(to bottom, #6a8ef7, #4678f6 50%, #6a8ef7)',
                color: '#fff'
              }"
            >
              <el-table-column
                prop="index"
                label="序号"
                type="index"
                width="90"
              />
              <el-table-column
                prop="name"
                label="文件名称"
                minWidth="180"
                show-overflow-tooltip
              />
              <el-table-column
                prop="createTime"
                label="上传日期"
                width="180"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span>{{
                    scope.row.createTime
                      ? dayjs(scope.row.createTime).format("YYYY-MM-DD")
                      : ""
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" show-overflow-tooltip>
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    size="small"
                    style="color: #4678f6"
                    @click="handlePreview(scope.row)"
                  >
                    浏览
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="flex justify-end mt-3">
              <el-pagination
                small
                :total="pagination.total"
                :page-size="pagination.pageSize"
                :current-page="pagination.currentPage"
                background
                layout="prev, pager, next"
                class="flex items-center justify-center h-10"
                @current-change="onCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped lang="scss">
.backgroud {
  background-image: url("@/assets/images/background.png");
  width: 100%;
  height: 100vh;
  background-color: #f5f7fa;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.el-table {
  --el-table-border-color: transparent;
  --el-table-border: none;
  --el-table-text-color: #1a1a1a;
  --el-table-header-text-color: #bdbdbe;
  --el-table-row-hover-bg-color: transparent;
  --el-table-current-row-bg-color: transparent;
  --el-table-header-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-expanded-cell-bg-color: transparent;
}
.el-pagination {
  --el-pagination-button-bg-color: transparent;
  --el-disabled-bg-color: transparent;
  --el-pagination-button-color: #409eff;
}
</style>
./pdf-preview.vue
