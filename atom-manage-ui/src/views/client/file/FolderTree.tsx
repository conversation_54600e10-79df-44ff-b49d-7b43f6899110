import { ElTree } from "element-plus";
import { defineComponent } from "vue";
import "./folder-tree.css";
import Folder from "@/assets/images/file.png";
const props = {
  data: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: <PERSON><PERSON>an,
    default: false
  }
};

const defaultProps = {
  children: "children",
  label: "label",
  value: "value"
};

export default defineComponent({
  name: "FolderTree",
  props,
  emits: ["selected"],
  setup(props, { emit }) {
    function handleSelect(data: any, node: any) {
      data.meetingTitle = node.parent.data.label;
      emit("selected", data);
    }

    return () => (
      <>
        <ElTree
          class="w-full h-full p-3 !bg-white !bg-opacity-20 floder-tree"
          data={props.data}
          props={defaultProps}
          highlightCurrent={true}
          defaultExpandAll
          accordion
          renderContent={(h, { node, data, store }) => {
            const baseClass =
              "text-white w-full flex items-center p-3 pl-4 mb-2 rounded-sm " +
              (node.expanded
                ? " bg-gradient-to-r from-[#2370ff] to-[#b3d3ff] "
                : " bg-gradient-to-b from-[#e2e9fb] to-[#c3d4fb] ");

            if (node.level === 1) {
              return (
                <div class={baseClass}>
                  <img src={Folder} class="object-fill w-[24px] h-auto" />
                  <span
                    class={
                      "ml-2 " + (node.expanded ? "text-white" : "text-gray-900")
                    }
                    onClick={() => handleSelect(data, node)}
                  >
                    {data.label}
                  </span>
                </div>
              );
            } else {
              return (
                <div class="w-full flex items-center text-sm p-2 mb-1 rounded-sm">
                  <span
                    class="ml-2 highlight"
                    onClick={() => handleSelect(data, node)}
                  >
                    {data.label}
                  </span>
                </div>
              );
            }
          }}
        />
      </>
    );
  }
});
