<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { getTopMenu, initRouter } from "@/router/utils";
import { useRoute, useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginSeesion } from "@/api/auth";
import { onMounted, onUnmounted, ref, watch } from "vue";
import ScreenLoading from "@/assets/lottie/loading-screen.json";
import { useSettingStore } from "@/store/modules/settings";
// 获取 url 中的参数
defineOptions({
  name: "SSO"
});

const router = useRouter();
const route = useRoute();
const errorType = ref("");

const load = () => {
  if (location.href.includes("token")) {
    // 清空本地旧信息
    loginSeesion()
      .then(response => {
        const userToken = response.data;
        useUserStoreHook()
          .loginByToken(userToken)
          .then(() => {
            initRouter().then(() => {
              const path =
                localStorage.getItem("referrer") ?? getTopMenu(true).path;
              // router.push(getTopMenu(true).path);
              // router.push(path);
              useSettingStore().$state.hiddenSideBar = true;
              router.push("/user/home/<USER>");
              message("登录成功", { type: "success" });
            });
          });
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {});

    return;
  } else if (location.href.includes("unauthorized")) {
    errorType.value = "unauthorized";
    return;
  }

  // console.log('locationUrl:', locationUrl);
  window.location.href =
    window.location.protocol +
    "//" +
    window.location.host +
    (import.meta.env.VITE_BASE_URL ?? "") +
    "/oauth2/authorization/crcc";
};

const handleRefresh = () => {
  errorType.value = "";
  const url =
    window.location.protocol +
    "//" +
    window.location.host +
    "" +
    window.location.pathname;
  window.location.href = url;
};

onMounted(() => {
  load();
});

onUnmounted(() => {
  errorType.value = "";
});

watch(
  () => route.params,
  () => {
    load();
  }
);
</script>

<template>
  <div class="flex flex-col justify-center text-center">
    <!-- <el-row>&nbsp;</el-row> -->

    <div v-if="errorType == ''">
      <lottie
        :animationData="ScreenLoading"
        :height="200"
        :width="200"
        :loop="false"
      />
      <div class="flex-center">
        <i class="el-icon-loading" />
        <span style="font-size: 18px; color: #409eff">加载中。。。</span>
      </div>
    </div>
    <div v-else-if="errorType == 'unauthorized'">
      <div>你暂无登录权限，请联系管理员分配</div>
      <div @click="handleRefresh">点击重试</div>
    </div>

    <!-- <el-alert type="warning" center show-icon :closable="false">
        <template #title>
          <div>
            <span>{{ tips }}</span>
            <el-button  text @click="logout" size="small">退出</el-button>
          </div>
        </template>
      </el-alert> -->
  </div>
</template>

<style scoped></style>
