<script setup lang="ts">
import { ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useLgoininfor } from "./hook";

defineOptions({
  name: "logininfor"
});

const queryRef = ref();
const tableRef = ref();

const {
  sys_common_status,
  multiple,
  queryParams,
  loading,
  columns,
  pagination,
  // visible,
  handleUnlock,
  dataList,
  resetQuery,
  handleQuery,
  handleDelete,
  handleClean,
  handleExport,
  handleSelectionChange,
  pageSizeChange,
  pageCurrentChange
} = useLgoininfor();
</script>

<template>
  <div class="main">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="bg-bg_color w-[99/100] pl-8 pt-4"
    >
      <el-form-item label="登录地址" prop="ipaddr">
        <el-input
          v-model="queryParams.ipaddr"
          placeholder="请输入登录地址"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="登录状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_common_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="登录时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 1, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(`ep:search`)"
          @click="handleQuery"
          >搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetQuery(queryRef)"
          >重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar
      title="登录日志列表"
      :tableRef="tableRef?.getTableRef()"
      :queryRef="queryRef"
      @refresh="handleQuery"
    >
      <template #buttons>
        <el-button
          type="danger"
          class="sm:shrink-button"
          :icon="useRenderIcon('ep:delete')"
          @click="handleDelete()"
          :multiple="multiple"
          v-auth="['monitor:logininfor:remove']"
        >
          删除
        </el-button>
        <el-button
          type="danger"
          class="sm:shrink-button"
          :icon="useRenderIcon('ep:delete')"
          @click="handleClean"
          v-auth="['monitor:logininfor:remove']"
        >
          清空
        </el-button>
        <el-button
          type="primary"
          class="sm:shrink-button"
          :icon="useRenderIcon('ri:lock-unlock-line')"
          @click="handleUnlock"
          v-auth="['monitor:logininfor:unlock']"
        >
          解锁
        </el-button>
        <el-button
          type="warning"
          class="sm:shrink-button"
          plain
          :icon="useRenderIcon('ri:download-2-line')"
          @click="handleExport"
          v-auth="['monitor:logininfor:export']"
        >
          导出
        </el-button>
      </template>

      <template v-slot="{ size, checkList }">
        <pure-table
          ref="tableRef"
          border
          row-key="infoId"
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          default-expand-all
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="columns"
          :checkList="checkList"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-table-row-hover-bg-color)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="pageSizeChange"
          @page-current-change="pageCurrentChange"
        >
          <template #operation="{ row }">
            <el-popconfirm title="是否确认删除?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>
