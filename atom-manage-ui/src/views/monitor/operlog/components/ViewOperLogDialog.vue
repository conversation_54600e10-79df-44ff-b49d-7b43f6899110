<script setup lang="ts">
import { ref, watch } from "vue";
import { SysOperLog } from "@/types/monitor/sys-oper-log";
import { dayjs, ElForm } from "element-plus";

defineOptions({
  name: "OperLogDialog"
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: SysOperLog,
    default: () => {
      return new SysOperLog();
    }
  }
});

// * -- 对话框显示状态
const _visible = ref(false);

const emit = defineEmits(["update:visible", "confirm"]);

watch(
  () => props.visible,
  val => {
    _visible.value = val;
  }
);

watch(_visible, val => {
  emit("update:visible", val);
});

watch(() => props.data, handlePropsDataChange);

// 表单信息
const form = ref<SysOperLog>();

function handlePropsDataChange() {
  form.value = props.data;
}
</script>

<template>
  <!-- 操作日志详细 -->
  <el-dialog title="操作日志详细" v-model="_visible" width="80%">
    <el-form
      :model="form"
      label-width="100px"
      class="w-[99/100] pl-8 pt-4"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="操作模块：">{{ form.title }} </el-form-item>
          <el-form-item label="登录信息："
            >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
          <el-form-item label="请求方式："
            >{{ form.requestMethod }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="请求参数：">
            <div class="break-all">
              {{ form.operParam }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="操作状态：">
            <div v-if="form.status === 0">正常</div>
            <div v-else-if="form.status === 1">失败</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="消耗时间："
            >{{ form.costTime }}毫秒
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="操作时间："
            >{{ dayjs(form.operTime).format("YYYY-MM-DD") }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="异常信息：" v-if="form.status === 1"
            >{{ form.errorMsg }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="_visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
