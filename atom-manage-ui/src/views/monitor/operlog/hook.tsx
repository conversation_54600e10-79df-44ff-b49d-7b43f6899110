import dayjs from "dayjs";
import { cleanOperlog, delOperlog, list } from "@/api/monitor/operlog";
import { onMounted, reactive, ref } from "vue";
import { SysOperLog, SysOperLogQuery } from "@/types/monitor/sys-oper-log";
import { message } from "@/utils/message";
import { ElMessageBox, FormInstance } from "element-plus";
import { useGlobal } from "@pureadmin/utils";
import DictTag from "@/components/DictTag";
import { PaginationProps } from "@pureadmin/table";

export function useOperLog() {
	const queryParams = reactive<SysOperLogQuery>(new SysOperLogQuery());

	const dataList = ref([]);
	const loading = ref(true);
	const visible = ref(false);

	const ids = ref([]);
	const single = ref(true);
	const multiple = ref(true);
	const operData = ref<SysOperLog>();
	const { $useDict, $download } = useGlobal<GlobalPropertiesApi>();
	const { sys_common_status, sys_oper_type } = $useDict(
		"sys_common_status",
		"sys_oper_type"
	);

	const pagination = reactive<PaginationProps>({
		total: 0,
		pageSize: 10,
		currentPage: 1
	});

	const columns: TableColumnList = [
		{
			type: "selection",
			width: 55,
			align: "center"
		},
		{
			label: "日志编号",
			prop: "operId",
			align: "center"
		},
		{
			label: "系统模块",
			prop: "title",
			align: "center",
			showOverflowTooltip: true
		},
		{
			label: "操作类型",
			prop: "ipaddr",
			align: "center",
			cellRenderer: ({ row }) => (
				<DictTag options={sys_oper_type} value={row.businessType} />
			)
		},
		{
			label: "操作人员",
			prop: "operName",
			align: "center",
			showOverflowTooltip: true
		},
		{
			label: "主机",
			prop: "operIp",
			align: "center",
			showOverflowTooltip: true
		},
		{
			label: "操作状态",
			prop: "status",
			align: "center",
			cellRenderer: ({ row }) => (
				<DictTag options={sys_common_status} value={row.status} />
			)
		},
		{
			label: "消耗时间",
			prop: "costTime",
			align: "center",
			sortable: true,
			sortMethod: (a, b) => dayjs(b).valueOf() - dayjs(a).valueOf(),
			sortOrders: ["descending", "ascending"],
			width: 180,
			cellRenderer: ({ row }) => <span>{row.costTime}毫秒</span>
		},
		{
			label: "操作日期",
			prop: "operTime",
			align: "center",
			sortable: true,
			sortMethod: (a, b) => dayjs(b).valueOf() - dayjs(a).valueOf(),
			sortOrders: ["descending", "ascending"],
			width: 180,
			cellRenderer: ({ row }) => (
				<span>{dayjs(row.operTime).format("YYYY-MM-DD HH:mm:ss")}</span>
			)
		},

		{
			label: "操作",
			fixed: "right",
			align: "left",
			width: 200,
			slot: "operation"
		}
	];

	function handleDelete(row?: SysOperLog) {
		if (row?.operId) {
			delOperlog(row.operId).then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			});
			return;
		}
		ElMessageBox.confirm(
			'是否确认删除访问编号为"' + ids.value + '"的数据项？',
			{
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				title: "系统提示"
			}
		)
			.then(function () {
				return delOperlog(ids.value);
			})
			.then(() => {
				handleQuery();
				message("删除成功", { type: "success" });
			})
			.catch(() => { });
	}

	/** 选择条数  */
	function handleSelectionChange(selection) {
		ids.value = selection.map(item => item.userId);
		single.value = selection.length != 1;
		multiple.value = !selection.length;
	}

	function resetQuery(formEl: FormInstance) {
		if (!formEl) return;
		formEl.resetFields();
		handleQuery();
	}

	async function getList() {
		loading.value = true;
		const { data, total } = await list({ ...queryParams, ...pagination });
		dataList.value = data;
		pagination.total = total;
		setTimeout(() => {
			loading.value = false;
		}, 500);
	}

	function handleQuery() {
		pagination.currentPage = 1;
		getList();
	}

	/** 清空按钮操作 */
	function handleClean() {
		ElMessageBox.confirm("是否确认清空所有操作日志数据项?", "系统消息", {
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			type: "warning"
		})
			.then(() => {
				cleanOperlog().then(() => {
					handleQuery();
					message("清空成功", { type: "success" });
				});
			})
			.catch(() => { });
	}

	/** 导出按钮操作 */
	function handleExport() {
		$download(
			"monitor/operlog/export",
			{
				...queryParams
			},
			`config_${new Date().getTime()}.xlsx`
		);
	}

	function handleViewLog(data: SysOperLog) {
		operData.value = data;
		visible.value = true;
	}

	function pageSizeChange(size: number) {
		pagination.pageSize = size;
		getList();
	}

	function pageCurrentChange(num: number) {
		pagination.currentPage = num;
		getList();
	}

	onMounted(() => {
		handleQuery();
	});

	return {
		sys_common_status,
		sys_oper_type,
		single,
		multiple,
		pageSizeChange,
		pageCurrentChange,
		operData,
		pagination,
		visible,
		queryParams,
		loading,
		columns,
		dataList,
		resetQuery,
		handleQuery,
		handleDelete,
		handleExport,
		handleViewLog,
		handleClean,
		handleSelectionChange
	};
}
