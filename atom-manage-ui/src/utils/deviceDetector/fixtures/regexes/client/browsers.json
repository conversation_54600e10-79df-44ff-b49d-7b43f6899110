[{"regex": "AltiBrowser/([\\d.]+)", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Maple (?!III)(\\d+[.\\d]+)|Maple\\d{4}", "name": "Maple", "version": "$1", "engine": {"default": "Maple"}}, {"regex": "Singlebox/(\\d+\\.[\\.\\d]+)", "name": "Singlebox", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "RCATorExplorer", "name": "RCA Tor Explorer", "version": ""}, {"regex": "TQ<PERSON><PERSON><PERSON>", "name": "TQ Browser", "version": ""}, {"regex": "XXXAndroidApp", "name": "XnBrowse", "version": "", "engine": {"default": "Blink"}}, {"regex": "ProxyFox", "name": "ProxyFox", "version": ""}, {"regex": "Priva<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "", "engine": {"default": "Blink"}}, {"regex": "TUSK/(\\d+[.\\d]+)", "name": "TUSK", "version": "$1"}, {"regex": "Dezor/(\\d+[.\\d]+)", "name": "Dezor", "version": "$1"}, {"regex": "OJR Browser/([\\d.]+)", "name": "OJ<PERSON>rowser", "version": "$1"}, {"regex": "SecureBrowser/([\\d.]+)", "name": "AppTec Secure Browser", "version": "$1"}, {"regex": "Veera/([\\d.]+)", "name": "Veera", "version": "$1"}, {"regex": "Ninesky(?:-android-mobile)?/([\\d.]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Perk/([\\d.]+)", "name": "Perk", "version": "$1"}, {"regex": "Presearch \\(Tempest\\)", "name": "Presearch", "version": ""}, {"regex": "QtWeb Internet Browser(?:/(\\d+[.\\d]+))?", "name": "QtWeb", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "UPhoneWebBrowser(\\d+[.\\d]+)", "name": "UPhone Browser", "version": "$1"}, {"regex": "(?:MIB|MotorolaWebKit.*Version)/(\\d+[.\\d]+)", "name": "Motorola Internet Browser", "version": "$1"}, {"regex": "iNet Browser(?: (\\d+[.\\d]+))?", "name": "iNet Browser", "version": "$1"}, {"regex": "Prism/([\\d.]+)", "name": "Prism", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Awesomium/([\\d.]+)", "name": "Awesomium", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Roccat(?:/(\\d+[.\\d]+))?", "name": "Roccat", "version": "$1"}, {"regex": "Swiftweasel(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "wkbrowser (\\d+[.\\d]+)", "name": "Wukong Browser", "version": "$1"}, {"regex": "KUN/(\\d+[.\\d]+)", "name": "KUN", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON>Brows<PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "nook browser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON> Browser", "version": "$1"}, {"regex": "xChaos_Arachne/5\\.(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "WeltweitimnetzBrowser/(\\d+\\.[.\\d]+)", "name": "Weltweitimnetz Browser", "version": "$1"}, {"regex": "(?:Ladybird|LibWeb\\+LibJS/.*<PERSON><PERSON>er)/(\\d+\\.[.\\d]+)", "name": "<PERSON>bird", "version": "$1", "engine": {"default": "LibWeb"}}, {"regex": "Kitt/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "sppm_bizbrowser", "name": "BizB<PERSON><PERSON>", "version": ""}, {"regex": "SkyLeap/(\\d+\\.[.\\d]+)", "name": "SkyLeap", "version": "$1"}, {"regex": "MaxBrowser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "YouBrowser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "MixerBox-<PERSON><PERSON>er", "name": "MixerBox AI", "version": ""}, {"regex": "<PERSON><PERSON><PERSON><PERSON> (\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Eolie", "name": "Eolie", "version": ""}, {"regex": "^w3m/(\\d+[.\\d]+)", "name": "w3m", "version": "$1", "engine": {"default": "Text-based"}}, {"regex": "Classilla/", "name": "Classilla", "version": "$1", "engine": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}, {"regex": "WebianShell/(\\d+[.\\d]+)", "name": "<PERSON>ian <PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "<PERSON><PERSON><PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Wyzo/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Liri/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Columbus/(\\d+\\.[.\\d]+)", "name": "<PERSON> Browser", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "", "engine": {"default": "Trident"}}, {"regex": "K-Ninja/(\\d+\\.[.\\d]+)", "name": "K-Ninja", "version": "$1"}, {"regex": "^PB(\\d+\\.[.\\d]+)", "name": "Pirate<PERSON><PERSON>er", "version": "$1"}, {"regex": "EastBrowser/(\\d+\\.[.\\d]+)", "name": "East Browser", "version": "$1"}, {"regex": "Qiyu/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "WebDiscover/(\\d+\\.[.\\d]+)", "name": "WebDiscover", "version": "$1"}, {"regex": "LeganBrowser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "A<PERSON>o Browser", "name": "A<PERSON>o Browser", "version": "", "engine": {"default": "Trident"}}, {"regex": "Aplix_.*_browser/(\\d+\\.[.\\d]+)", "name": "Aplix", "version": "$1"}, {"regex": "Mogok/(\\d+\\.[.\\d]+)", "name": "Mogok Browser", "version": "$1"}, {"regex": "(?:IOS)?TrueLocationBrowser/(\\d+\\.[.\\d]+)", "name": "TrueLocation Browser", "version": "$1"}, {"regex": "DiigoBrowser$", "name": "<PERSON><PERSON>", "version": ""}, {"regex": ".*OnBrowserLite(\\d+\\.[.\\d]+)", "name": "OnBrowser Lite", "version": "$1"}, {"regex": "Bluefy/(\\d+\\.[.\\d]+)", "name": "Bluefy", "version": "$1"}, {"regex": "(?:Novarra-Vision|Vision-Browser)(?:/(\\d+[.\\d]+))?", "name": "Vision Mobile Browser", "version": "$1"}, {"regex": "SurfyBrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "18\\+/([\\d.]+)", "name": "18+ <PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "GoKu-iOS/(\\d+[.\\d]+)", "name": "GoKu", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Ask\\.com Mobile Browser", "name": "Ask.com", "version": ""}, {"regex": "Bang/(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1"}, {"regex": "ManagedBrowser(?:/(\\d+[.\\d]+))?", "name": "Intune Managed Browser", "version": "$1"}, {"regex": "Lotus/(\\d+[.\\d]+)", "name": "Lotus", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "JUZI Browser", "version": ""}, {"regex": "Ninetails(?:/(\\d+[.\\d]+))?", "name": "Ninetails", "version": "$1"}, {"regex": "Wexond(?:/(\\d+[.\\d]+))?", "name": "Wexond", "version": "$1"}, {"regex": "catalyst(?:/(\\d+[.\\d]+))?", "name": "Catalyst", "version": "$1"}, {"regex": "Impervious(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "RakutenBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "RakutenWebSearch(?:/(\\d+[.\\d]+))?", "name": "Rakuten Web Search", "version": "$1"}, {"regex": "VibeMate(?:/(\\d+[.\\d]+))?", "name": "VibeMate", "version": "$1"}, {"regex": "yixia\\.browser/com\\.donerbrowser\\.app/", "name": "<PERSON><PERSON>er", "version": ""}, {"regex": "tararia/(\\d+\\.[.\\d]+)", "name": "tararia", "version": "$1"}, {"regex": "SberBrowser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Raspbian Chromium/(?:(\\d+[.\\d]+))?", "name": "Raspbian Chromium", "version": "$1"}, {"regex": "Quick Search TV(?:/(?:Wild Moon Edition )?(\\d+[.\\d]+))?", "name": "Quick Search TV", "version": "$1"}, {"regex": "Skye/(\\d+\\.[.\\d]+)", "name": "Skye", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "VD/\\d+", "name": "<PERSON><PERSON> Browser", "version": "", "engine": {"default": "Blink"}}, {"regex": "\\[(?:HB/29|PB/(?:66|81))\\]", "name": "SecureX", "version": "", "engine": {"default": "Blink"}}, {"regex": "\\[HS/\\d+\\]", "name": "HotBrowser", "version": "", "engine": {"default": "Blink"}}, {"regex": "\\[PB/\\d+\\]", "name": "Proxy Browser", "version": "", "engine": {"default": "Blink"}}, {"regex": "^Normalized (?:iPad|iPhone) \\(iOS Safari\\)", "name": "<PERSON><PERSON> Browser", "version": "", "engine": {"default": "WebKit"}}, {"regex": "fGet/", "name": "fGet", "version": ""}, {"regex": "Nuviu/(?:(\\d+[.\\d]+))?", "name": "<PERSON>u<PERSON><PERSON>", "version": "$1"}, {"regex": "DoCoMo/(?:(\\d+[.\\d]+))?", "name": "DoCoMo", "version": "$1"}, {"regex": "com\\.airfind\\.browser/(?:(\\d+[.\\d]+))?", "name": "Airfind Secure Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "ArcMobile2(?:/(\\d+\\.[.\\d]+);)?", "name": "Arc Search", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Nuanti(?:Meta)?/(\\d+\\.[.\\d]+)", "name": "Nuanti Meta", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "RokuBrowser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "PicoBrowser/(\\d+\\.[.\\d]+)", "name": "PIC<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Alva/(\\d+\\.[.\\d]+)", "name": "ALVA", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Norton/(\\d+\\.[.\\d]+)", "name": "Norton Private Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Odd/(\\d+\\.[.\\d]+)", "name": "<PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Safari/537\\.36 (?:<PERSON><PERSON><PERSON>|Navegador)", "name": "APN Browser", "version": "", "engine": {"default": "Blink"}}, {"regex": "YAGI/(\\d+\\.[.\\d]+)", "name": "YAGI", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "InspectB<PERSON>er", "name": "Inspect Browser", "version": ""}, {"regex": "Keepsafe Browser(?:/(\\d+[.\\d]+))?", "name": "Keep<PERSON> Browser", "version": "$1"}, {"regex": "(.*)Vast Browser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON> Browser", "version": "$2", "engine": {"default": "Blink"}}, {"regex": "bloket", "name": "Bloket", "version": "", "engine": {"default": "Blink"}}, {"regex": "(.*)<PERSON>(\\d+\\.[.\\d]+)", "name": "<PERSON>rowser", "version": "$2", "engine": {"default": "Blink"}}, {"regex": "Chrome/(\\d+\\.[.\\d]+).+TeslaBrowser/", "name": "<PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome/(\\d+\\.[.\\d]+).+Sparrow", "name": "Viasat Browser", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "Sparrow/.+CFNetwork", "name": "Viasat Browser", "version": "", "engine": {"default": "WebKit"}}, {"regex": "Lilo/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Lilo/.+CFNetwork", "name": "<PERSON><PERSON>", "version": "", "engine": {"default": "WebKit"}}, {"regex": "lexi/(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Floorp/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "SurfBrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Decentr", "name": "Decentr", "version": "", "engine": {"default": "Blink"}}, {"regex": "youcare-android-app", "name": "YouCare", "version": "", "engine": {"default": "Blink"}}, {"regex": "youcare-ios-app", "name": "YouCare", "version": "", "engine": {"default": "WebKit"}}, {"regex": "ABB/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "\\d+/tclwebkit(?:\\d+[.\\d]*)", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": ""}, {"regex": "HiBrowser/v?(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "CYBrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Chrome/.+ SiteKiosk (\\d+[.\\d]+)", "name": "SiteKiosk", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SiteKiosk (\\d+[.\\d]+)", "name": "SiteKiosk", "version": "$1"}, {"regex": "ReqwirelessWeb/(\\d+[.\\d]+)", "name": "Reqwireless WebViewer", "version": "$1"}, {"regex": "T\\+Browser/(\\d+[.\\d]+)", "name": "T+Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Private Browser/(\\d+[.\\d]+) Chrome/", "name": "Secure Private Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "ChanjetCloud/(\\d+[.\\d]+)", "name": "ChanjetCloud", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SushiBrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "dBrowser/(\\d+[.\\d]+)", "name": "Peeps dBrowser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "LTBrowser/(\\d+[.\\d]+)", "name": "L<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "lagatos-browser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "psi-secure-browser/(\\d+[.\\d]+)", "name": "PSI Secure Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "<PERSON><PERSON>_Browser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "bonsai-browser/(\\d+[.\\d]+)", "name": "Bonsai", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "spectre-browser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "FlashBrowser/(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Secure/(?:(\\d+[.\\d]+))?", "name": "Se<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Arvin/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Version/.+Chrome/.+EdgW/(\\d+[.\\d]+)", "name": "Edge WebView", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Mandarin Browser/(\\d+[.\\d]+)", "name": "Mandarin", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Torrent/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Helio/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "7654Browser/(\\d+[.\\d]+)", "name": "7654 Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Qazweb/(\\d+[.\\d]+)", "name": "Qazweb", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Degdegan/(\\d+[.\\d]+)", "name": "deg-degan", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "JavaFX/(\\d+[.\\d]+)", "name": "JavaFX", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Chedot/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome/(\\d+\\.[.\\d]+) .*\\(Chromium GOST\\)", "name": "Chromium GOST", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "(?:DeledaoPersonal|DeledaoFamily)/(\\d+[.\\d]+)", "name": "Deledao", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "HasBrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Byffox/(\\d+[.\\d]+)", "name": "Byffox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Chrome/(\\d+\\.[.\\d]+) .*AgentWeb.+UCBrowser", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "DotBrowser/(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "CravingExplorer/(\\d+[.\\d]+)", "name": "Craving Explorer", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "DeskBrowse/(\\d+[.\\d]+)", "name": "DeskBrowse", "version": "$1"}, {"regex": "Lolifox/(\\d+[.\\d]+)", "name": "Lolifox", "version": "$1"}, {"regex": "PiBrowser/(\\d+[.\\d]+)", "name": "<PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "qutebrowser/(\\d+\\.[.\\d]+) .*Chrome", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "qutebrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "flast/(\\d+[.\\d]+)", "name": "Flast", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "PolyBrowser/(\\d+[.\\d]+)", "name": "<PERSON>y<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Chrome.+BriskBard/(\\d+[.\\d]+)", "name": "BriskBard", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "BriskBard(?:/(\\d+[.\\d]+))?", "name": "BriskBard", "version": "$1"}, {"regex": "GinxDroid(?:<PERSON><PERSON><PERSON>)?/(\\d+[.\\d]+)", "name": "Ginx<PERSON><PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "A<PERSON><PERSON>(?:Scout)?/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "VenusBrowser/(\\d+[.\\d]+)", "name": "<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome.+Otter(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Otter(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON> Browser", "version": "$1"}, {"regex": "Chrome.+Smooz/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Smooz/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "BanglaBrowser/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Cornowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Orca/(\\d+[.\\d]+)", "name": "Orca", "version": "$1"}, {"regex": "Android (?:[\\d.]+;) ?(?:[^;]+;)? Flow\\) AppleWebKit/537.+Chrome/\\d{3}", "name": "Flow Browser", "version": "", "engine": {"default": "Blink"}}, {"regex": "Flow/(?:(\\d+[.\\d]+))", "name": "Flow", "version": "$1", "engine": {"default": "EkiohFlow"}}, {"regex": "Ekioh/(?:(\\d+[.\\d]+))", "name": "Flow", "version": "$1", "engine": {"default": "EkiohFlow"}}, {"regex": "xStand/(\\d+[.\\d]+)", "name": "xStand", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Biyubi/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "(?:Perfect%20Browser(?:-iPad)?|Perfect(?:BrowserPro)?)/(\\d+[.\\d]+)", "name": "<PERSON> Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Browser/Phantom/V(\\d+[.\\d]+)", "name": "<PERSON> Browser", "version": "$1"}, {"regex": "AwoX(?:/(\\d+[.\\d]+))? Browser", "name": "AwoX", "version": "$1"}, {"regex": "Zetakey/(\\d+[.\\d]+)", "name": "Zetakey", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "PlayFreeBrowser/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>er", "version": "$1"}, {"regex": "(?:chimlac_browser|chimlac)/(?:(\\d+[.\\d]+))", "name": "<PERSON>m <PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Odin/(?:(\\d+[.\\d]+))", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Tbrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "com\\.tcl\\.browser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "WhaleBrowser/(\\d+[.\\d]+)", "name": "Whale TV Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SFive(?:_Android)?/.+ Chrome/(\\d+[.\\d]+)", "name": "SFive", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SFive_IOS/(\\d+[.\\d]+)", "name": "SFive", "version": "$1"}, {"regex": "Navigateur web/(?:(\\d+[.\\d]+))?", "name": "Navigateur Web", "version": "$1"}, {"regex": "Sraf(?:[/ ](\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SeewoBrowser/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "(?:Kode(?:iOS)?/(?:(\\d+[.\\d]+))?|TansoDL)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "UR/(?:(\\d+[.\\d]+))", "name": "UR Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "OceanHero/([.\\d]+)", "name": "OceanHero", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome/.+ SLBrowser/(?:(\\d+[.\\d]+))?", "name": "Smart Lenovo Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SLBrowser/(?:(\\d+[.\\d]+))?", "name": "Smart Lenovo Browser", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "version": ""}, {"regex": "Stargon/(?:(\\d+[.\\d]+))?", "name": "Stargon", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "NFSBrowser/(?:(\\d+[.\\d]+))?", "name": "NFS Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Borealis/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON> Navigator", "version": "$1"}, {"regex": "YoloBrowser/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "PHX/(?:(\\d+[.\\d]+))?", "name": "<PERSON> Browser", "version": "$1"}, {"regex": "PrivacyWall/(?:(\\d+[.\\d]+))?", "name": "PrivacyWall", "version": "$1"}, {"regex": "Ghostery:?(\\d+[.\\d]+)?", "name": "Ghostery Privacy Browser", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": ""}, {"regex": "Firefox/.*(?:Turkcell-)?YaaniBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "(?:Turkcell-)?YaaniBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SEB/(?:(\\d+[.\\d]+))?", "name": "Safe <PERSON><PERSON>", "version": "$1"}, {"regex": "Colibri/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Xvast/(?:(\\d+[.\\d]+))?", "name": "Xvast", "version": "$1"}, {"regex": "TungstenBrowser/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Lulumi-browser/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "ybrowser/(?:(\\d+[.\\d]+))?", "name": "Yahoo! Japan Browser", "version": "$1"}, {"regex": "iLunascapeLite/(?:(\\d+\\.[.\\d]+))?", "name": "Lunascape Lite", "version": "$1"}, {"regex": "Chrome/.+ i?Lunascape(?:[/ ](\\d+\\.[.\\d]+))?", "name": "Lunascape", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "i?Lunascape(?:[/ ](\\d+\\.[.\\d]+))?", "name": "Lunascape", "version": "$1", "engine": {"default": ""}}, {"regex": "Polypane/(?:(\\d+[.\\d]+))?", "name": "Polypane", "version": "$1"}, {"regex": "OhHaiBrowser/(?:(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>rowser", "version": "$1"}, {"regex": "Sizzy/(?:(\\d+[.\\d]+))?", "name": "Sizzy", "version": "$1"}, {"regex": "GlassBrowser/(?:(\\d+[.\\d]+))?", "name": "<PERSON> Browser", "version": "$1"}, {"regex": "ToGate/(?:(\\d+[.\\d]+))?", "name": "ToGate", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "(?:AirWatch Browser v|AirWatchBrowser/)(?:(\\d+[.\\d]+))?", "name": "VMware AirWatch", "version": "$1"}, {"regex": "AOL (\\d+[.\\d]+)", "name": "AOL Explorer", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "ADG/(?:(\\d+[.\\d]+))?", "name": "AOL Desktop", "version": "$1"}, {"regex": "Elements Browser/(?:(\\d+[.\\d]+))?", "name": "<PERSON> Browser", "version": "$1"}, {"regex": "Light/(\\d+[.\\d]+)", "name": "Light", "version": "$1"}, {"regex": "Valve Steam GameOverlay/(?:(\\d+[.\\d]+))?", "name": "Steam In-Game Overlay", "version": "$1"}, {"regex": "115Browser/(?:(\\d+[.\\d]+))?", "name": "115 Browser", "version": "$1"}, {"regex": "Atom/(?:(\\d+[.\\d]+))?", "name": "Atom", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "W<PERSON><PERSON>/(\\d+\\.[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Mobile VR.+Firefox", "name": "Firefox Reality", "version": ""}, {"regex": "AVG(?:/(\\d+[.\\d]+))?", "name": "AVG Secure Browser", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "AT/(\\d+[.\\d]+)", "name": "AVG Secure Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Start/(?:(\\d+[.\\d]+))?", "name": "START Internet Browser", "version": "$1"}, {"regex": "Lovense(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON> Browser", "version": "$1"}, {"regex": "(?:com\\.airfind\\.deltabrowser|AirSearch)(?:/(\\d+[.\\d]+))?", "name": "<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "(?:Ordissimo|webissimo3)(?:/(\\d+[.\\d]+))?", "name": "Ordissimo", "version": "$1"}, {"regex": "CCleaner(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "AlohaLite(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "TaoBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON>er", "version": "$1"}, {"regex": "Falkon(?:/(\\d+[.\\d]+))?", "name": "Falkon", "version": "$1"}, {"regex": "mCent(?:/(\\d+[.\\d]+))?", "name": "mCent", "version": "$1"}, {"regex": "SalamWeb(?:/(\\d+[.\\d]+))?", "name": "<PERSON>am<PERSON>eb", "version": "$1"}, {"regex": "BlackHawk(?:/(\\d+[.\\d]+))?", "name": "BlackHawk", "version": "$1"}, {"regex": "Minimo(?:/(\\d+[.\\d]+))?", "name": "Minimo", "version": "$1"}, {"regex": "WIB(?:/(\\d+[.\\d]+))?", "name": "Wear <PERSON> Browser", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON>er", "name": "<PERSON><PERSON><PERSON>er", "version": ""}, {"regex": "Ki<PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Beamrise(?:/(\\d+[.\\d]+))?", "name": "Be<PERSON>rise", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "Faux(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON> Browser", "version": "$1"}, {"regex": "splash Version(?:/(\\d+[.\\d]+))?", "name": "Splash", "version": "$1"}, {"regex": "MZBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "COSBrowser(?:/(\\d+[.\\d]+))?", "name": "COS Browser", "version": "$1"}, {"regex": "Crusta(?:/(\\d+[.\\d]+))?", "name": "Crust<PERSON>", "version": "$1"}, {"regex": "Hawk/TurboBrowser(?:/v?(\\d+[.\\d]+))?", "name": "Hawk Turbo Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "<PERSON>/QuickBrowser(?:/v?(\\d+[.\\d]+))?", "name": "<PERSON>rowser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "FreeU(?:/(\\d+[.\\d]+))?", "name": "FreeU", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "NoxBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Basilisk(?:/(\\d+[.\\d]+))?", "name": "Basilisk", "version": "$1", "engine": {"default": "Goanna"}}, {"regex": "SputnikBrowser(?:/(\\d+[.\\d]+))?", "name": "Sputnik Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "TNSBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "OculusBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Jio(?:<PERSON><PERSON><PERSON>|Pages|Sphere)(?:/(\\d+[.\\d]+))?", "name": "JioSphere", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SY/(\\d+[.\\d]+) Chrome/", "name": "<PERSON><PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome/.+ <PERSON><PERSON>(?:/(\\d+[.\\d]+))?", "name": "hola! Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SlimBoat/(?:(\\d+[.\\d]+))", "name": "SlimBoat", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Slimjet/(?:(\\d+[.\\d]+))", "name": "Slimjet", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "(?:7Star|Kuaiso)/(?:(\\d+[.\\d]+))", "name": "7Star", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "MxNitro/(?:(\\d+[.\\d]+))", "name": "MxNitro", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "HuaweiBrowser(?:/(\\d+[.\\d]+))?", "name": "Huawei Browser Mobile", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "HBPC/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "ZTE ?Browser/", "name": "ZTE Browser", "version": "$1"}, {"regex": "VivoBrowser(?:/(\\d+[.\\d]+))?", "name": "vivo Browser", "version": "$1"}, {"regex": "RealmeBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Beaker ?Browser(?:[/ ](\\d+[.\\d]+))?", "name": "Be<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "QwantiOS/(\\d+[.\\d]+)", "name": "Qwant Mobile", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Chrome/.*QwantMobile(?:/(\\d+[.\\d]+))?", "name": "Qwant Mobile", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "QwantMobile(?:/(\\d+[.\\d]+))?", "name": "Qwant Mobile", "version": "$1", "engine": {"default": ""}}, {"regex": "Qwant/(\\d+[.\\d]+)", "name": "Qwant Mobile", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "TenFourFox(?:/(\\d+[.\\d]+))?", "name": "TenFourFox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Chrome/.+ AOLShield(?:/(\\d+[.\\d]+))?", "name": "AOL Shield Pro", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "AOLShield(?:/(\\d+[.\\d]+))?", "name": "AOL Shield", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "(?<!motorola |; )Edge[ /](\\d+[.\\d]+)", "name": "Microsoft Edge", "version": "$1", "engine": {"default": "Edge"}}, {"regex": "EdgiOS[ /](\\d+[.\\d]+)", "name": "Microsoft Edge", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "EdgA[ /](\\d+[.\\d]+)", "name": "Microsoft Edge", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Edg[ /](\\d+[.\\d]+)", "name": "Microsoft Edge", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "QIHU 360[ES]E|QihooBrowserHD/(\\d+[.\\d]+)", "name": "360 Secure Browser", "version": "$1"}, {"regex": "Chrome.+Safari/537\\.36/(\\d+[.\\d]+)$", "name": "360 Secure Browser", "version": "$1"}, {"regex": "360 Aphone Browser(?:[ /]?\\(?(\\d+[.\\d]+)(?:beta)?\\)?)?", "name": "360 Phone Browser", "version": "$1"}, {"regex": "SailfishBrowser(?:/(\\d+[.\\d]+))?", "name": "Sailfish Browser", "version": "$1", "engine": {"default": ""}}, {"regex": "IceCat(?:/(\\d+[.\\d]+))?", "name": "IceCat", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "version": "", "engine": {"default": "Gecko"}}, {"regex": "Camino(?:/(\\d+[.\\d]+))?", "name": "Camino", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Waterfox(?:/(\\d+[.\\d]+))?", "name": "Waterfox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "VertexSurf/(\\d+\\.[.\\d]+)", "name": "Vertex Surf", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome/.+ <PERSON><PERSON><PERSON><PERSON><PERSON>er(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "AlohaBrowser(?:App)?(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Aloha/", "name": "<PERSON><PERSON><PERSON>", "version": ""}, {"regex": "Chrome.+(?:Avast(?:SecureBrowser)?|ASW|Safer)(?:/(\\d+[.\\d]+))?", "name": "Avast Secure Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "(?:Avast(?:Se<PERSON>Browser)?|ASW|Safer)(?:/(\\d+[.\\d]+))?", "name": "Avast Secure Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Epic(?:/(\\d+[.\\d]+))", "name": "Epic", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Fennec(?:/(\\d+[.\\d]+))?", "name": "Fennec", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Firefox.*Tablet browser (\\d+[.\\d]+)", "name": "MicroB", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "<PERSON><PERSON>(?: (\\d+[.\\d]+))?", "name": "MicroB", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Deepnet Explorer (\\d+[.\\d]+)?", "name": "Deepnet Explorer", "version": "$1"}, {"regex": "Avant ?Browser", "name": "Avant Browser", "version": "", "engine": {"default": ""}}, {"regex": "OppoBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Chrome/(\\d+\\.[.\\d]+) .*MRCHROME", "name": "Amigo", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "AtomicBrowser(?:/(\\d+[.\\d]+))?", "name": "Atomic Web Browser", "version": "$1"}, {"regex": "Bunjalloo(?:/(\\d+[.\\d]+))?", "name": "Bunjalloo", "version": "$1"}, {"regex": "Chrome/(\\d+\\.[.\\d]+).*Brave/", "name": "Brave", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Brave(?: Chrome)?(?:/(\\d+[.\\d]+))?", "name": "Brave", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Iridium(?:/(\\d+[.\\d]+))?", "name": "Iridium", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "Iceweasel(?:/(\\d+[.\\d]+))?", "name": "Iceweasel", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "WebPositive", "name": "WebPositive", "version": "", "engine": {"default": "WebKit"}}, {"regex": ".*Goanna.*PaleMoon(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Goanna"}}, {"regex": "PaleMoon(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "CometBird(?:/(\\d+[.\\d]+))?", "name": "CometBird", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "IceDragon(?:/(\\d+[.\\d]+))?", "name": "IceDragon", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Flock(?:/(\\d+[.\\d]+))?", "name": "Flock", "version": "$1", "engine": {"default": "Gecko", "versions": {"3": "WebKit"}}}, {"regex": "JigBrowserPlus/(?:(\\d+[.\\d]+))?", "name": "Jig Browser Plus", "version": "$1"}, {"regex": "jig browser(?: web;|9i?)?(?:[/ ](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Kylo(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Origin/(?:(\\d+[.\\d]+))?", "name": "Origin In-Game Overlay", "version": "$1"}, {"regex": "Cunaguaro(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>gua<PERSON>", "version": "$1"}, {"regex": "(?:TO-Browser/TOB|DT-Browser/DTB)(\\d+[.\\d]+)", "name": "t-online.de Browser", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": ""}}, {"regex": "ArcticFox(?:/(\\d+[.\\d]+))?", "name": "Arctic Fox", "version": "$1", "engine": {"default": "Goanna"}}, {"regex": "Mypal(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Goanna"}}, {"regex": "Centaury(?:/(\\d+[.\\d]+))?", "name": "Centaury", "version": "$1", "engine": {"default": "Goanna"}}, {"regex": "(?:Focus|Klar)(?:/(\\d+[.\\d]+))", "name": "Firefox Focus", "version": "$1"}, {"regex": "Cyberfox(?:/(\\d+[.\\d]+))?", "name": "Cyberfox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Firefox/(\\d+\\.[.\\d]+).*\\(Swiftfox\\)", "name": "Swiftfox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "UCBrowserHD/(\\d[\\d.]+)", "name": "UC Browser HD", "version": "$1"}, {"regex": "UCMini(?:[ /]?(\\d+[.\\d]+))?", "name": "UC Browser Mini", "version": "$1"}, {"regex": "UC[ ]?Browser.* \\(UCMini\\)", "name": "UC Browser Mini", "version": ""}, {"regex": "Chrome.+uc mini browser(\\d+[.\\d]+)?", "name": "UC Browser Mini", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "UCTurbo(?:[ /]?(\\d+[.\\d]+))?", "name": "UC Browser Turbo", "version": "$1"}, {"regex": "UC[ ]?Browser.* \\(UCTurbo\\)", "name": "UC Browser Turbo", "version": ""}, {"regex": "UC[ ]?Browser(?:[ /]?(\\d+[.\\d]+))?", "name": "UC Browser", "version": "$1"}, {"regex": "UCWEB(?:[ /]?(\\d+[.\\d]+))?", "name": "UC Browser", "version": "$1"}, {"regex": "UC AppleWebKit", "name": "UC Browser", "version": ""}, {"regex": "UC%20Browser/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)", "name": "UC Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Chrome.+<PERSON> Browser(\\d+[.\\d]+)", "name": "UC Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Firefox.+UCKai/(\\d+[.\\d]+)", "name": "UC Browser", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "(?:Mobile|Tablet).*Servo.*Firefox(?:/(\\d+[.\\d]+))?", "name": "Firefox Mobile", "version": "$1", "engine": {"default": "Servo"}}, {"regex": "(?:Mobile|Tablet).*Firefox(?:/(\\d+[.\\d]+))?", "name": "Firefox Mobile", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "FxiOS/(\\d+[.\\d]+)", "name": "Firefox Mobile iOS", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": ".*Servo.*Firefox(?:/(\\d+[.\\d]+))?", "name": "Firefox", "version": "$1", "engine": {"default": "Servo"}}, {"regex": "(?!.*Opera[ /])Firefox(?:[ /](\\d+[.\\d]+))?", "name": "Firefox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "(?:BonEcho|GranParadiso|Lorentz|Minefield|Namoroka|Shiretoko)[ /](\\d+[.\\d]+)", "name": "Firefox", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "ANTFresco(?:[/ ](\\d+[.\\d]+))?", "name": "ANT Fresco", "version": "$1"}, {"regex": "ANTGalio(?:/(\\d+[.\\d]+))?", "name": "ANTGalio", "version": "$1"}, {"regex": "(?:E<PERSON>ial|Escape)(?:[/ ](\\d+[.\\d]+))?", "name": "Espial TV Browser", "version": "$1"}, {"regex": "RockMelt(?:/(\\d+[.\\d]+))?", "name": "RockMelt", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Fireweb Navigator(?:/(\\d+[.\\d]+))?", "name": "Fireweb Navigator", "version": "$1"}, {"regex": "Fireweb(?:/(\\d+[.\\d]+))?", "name": "Fireweb", "version": "$1"}, {"regex": "(?:Navigator|Netscape6?)(?:/(\\d+[.\\d]+))?", "name": "Netscape", "version": "$1", "engine": {"default": ""}}, {"regex": "(?:Polarity)(?:[/ ](\\d+[.\\d]+))?", "name": "Polarity", "version": "$1"}, {"regex": "(?:<PERSON><PERSON><PERSON><PERSON>)(?:[/ ](\\d+[.\\d]+))?", "name": "QupZilla", "version": "$1"}, {"regex": "(?:Doo<PERSON>)(?:[/ ](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Whale/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>er", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Obigo[ ]?(?:InternetBrowser|Browser)?(?:[ /]([a-z0-9]*))?", "name": "Obigo", "version": "$1"}, {"regex": "Obigo|Teleca", "name": "Obigo", "version": ""}, {"regex": "Chrome/.+ OP(?:RG)?X(?:/(\\d+[.\\d]+))?", "name": "Opera GX", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "OP(?:RG)?X(?:/(\\d+[.\\d]+))?", "name": "Opera GX", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Opera(?:%20)?GX/.+CFNetwork/.+Darwin/", "name": "Opera GX", "version": "", "engine": {"default": "WebKit"}}, {"regex": "(?:Opera Tablet.*Version|Opera/.+(?<!SymbOS; )Opera Mobi.+Version|Mobile.+OPR)/(\\d+[.\\d]+)", "name": "Opera Mobile", "version": "$1", "engine": {"default": "Presto", "versions": {"15": "Blink"}}}, {"regex": "MMS/(\\d+[.\\d]+)", "name": "Opera Neon", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "OMI/(\\d+[.\\d]+)", "name": "Opera Devices", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Opera%20Touch/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)", "name": "Opera Touch", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Opera%20Touch/.+CFNetwork/.+Darwin/.+(?!.*x86_64)", "name": "Opera Touch", "version": "", "engine": {"default": "WebKit"}}, {"regex": "OPT/(\\d+[.\\d]+)", "name": "Opera Touch", "version": "$1"}, {"regex": "Opera/(\\d+\\.[.\\d]+) .*(?<!SymbOS; )Opera Mobi", "name": "Opera Mobile", "version": "$1", "engine": {"default": "Presto", "versions": {"15": "Blink"}}}, {"regex": "Opera ?Mini/(?:att/)?(\\d+[.\\d]+)", "name": "Opera Mini", "version": "$1", "engine": {"default": "Presto"}}, {"regex": "Opera ?Mini.+Version/(\\d+[.\\d]+)", "name": "Opera Mini", "version": "$1", "engine": {"default": "Presto"}}, {"regex": "OPiOS/(\\d+[.\\d]+)", "name": "Opera Mini iOS", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Opera%20Mini/(\\d+[.\\d]+) CFNetwork", "name": "Opera Mini iOS", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Opera.+Edition Next.+Version/(\\d+[.\\d]+)", "name": "Opera Next", "version": "$1", "engine": {"default": "Presto", "versions": {"15": "Blink"}}}, {"regex": "(?:Opera|OPR)[/ ](?:9\\.80.*Version/)?(\\d+\\.[.\\d]+) .*Edition Next", "name": "Opera Next", "version": "$1", "engine": {"default": "Presto", "versions": {"15": "Blink"}}}, {"regex": "(?:Opera[/ ]?|OPR[/ ])(?:9\\.80.*Version/)?(\\d+[.\\d]+)", "name": "Opera", "version": "$1", "engine": {"default": "", "versions": {"7": "Presto", "15": "Blink", "3.5": "Elektra"}}}, {"regex": "Opera/.+CFNetwork", "name": "Opera", "version": "", "engine": {"default": "WebKit"}}, {"regex": "Chrome.+Opera/", "name": "Opera", "version": "", "engine": {"default": "Blink"}}, {"regex": "rekonq(?:/(\\d+[.\\d]+))?", "name": "Rekonq", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "CoolNovo(?:/(\\d+[.\\d]+))?", "name": "CoolNovo", "version": "$1", "engine": {"default": ""}}, {"regex": "(?:<PERSON><PERSON>[ _])?Dragon/(\\d+[.\\d]+)", "name": "Comodo Dragon", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "ChromePlus(?:/(\\d+[.\\d]+))?", "name": "ChromePlus", "version": "$1", "engine": {"default": ""}}, {"regex": "Conkeror(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Konqueror(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "KHTML", "versions": {"4": ""}}}, {"regex": "bdhonorbrowser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON> Browser", "version": "$1"}, {"regex": "(?:b<PERSON><PERSON><PERSON>er|bdbrowser(?:(?:hd)?_i18n)?|FlyFlow|BaiduHD)(?:[/ ](\\d+[.\\d]*))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "(?:(?:BD)?Spark(?:Safe)?|BIDUBrowser)[/ ](\\d+[.\\d]*)", "name": "Baidu Spark", "version": "$1"}, {"regex": "YaBrowser(?:/(\\d+[.\\d]*)) YaApp_iOS", "name": "Yande<PERSON> Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "iP(?:hone|ad).*YaBrowser(?:/(\\d+[.\\d]*))", "name": "Yande<PERSON> Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "YaBrowser(?:/(\\d+[.\\d]*)) \\(lite\\)?", "name": "Yandex Browser Lite", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "YaBrowser/(\\d+[.\\d]*).*corp", "name": "Yandex Browser Corp", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "YaBrowser(?:/(\\d+[.\\d]*))(?: \\((alpha|beta)\\))?", "name": "Yande<PERSON> Browser", "version": "$1 $2", "engine": {"default": "Blink"}}, {"regex": "Ya(?:ndex)?SearchBrowser(?:/(\\d+[.\\d]*))", "name": "Yande<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Viv(?:aldi)?/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "TweakStyle(?:/(\\d+[.\\d]+))?", "name": "TweakStyle", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome.+<PERSON><PERSON>/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Midori(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Mercury/(?:(\\d+[.\\d]+))?", "name": "Mercury", "version": "$1"}, {"regex": "Chrome.+<PERSON><PERSON><PERSON>/\\d{4}", "name": "Maxthon", "version": "", "engine": {"default": "Blink"}}, {"regex": "Chrome.+(?:Mx<PERSON>rows<PERSON>|Maxthon)(?:.+\\(portable\\))?/(\\d+\\.[.\\d]+)", "name": "Maxthon", "version": "$1", "engine": {"default": "WebKit", "versions": {"4.2": "Blink"}}}, {"regex": "(?:<PERSON><PERSON><PERSON>(?:%20Browser)?|MxBrowser(?:-inhouse|-iPhone)?|MXiOS)[ /](\\d+[.\\d]+)?", "name": "Maxthon", "version": "$1", "engine": {"default": "", "versions": {"3": "WebKit"}}}, {"regex": "(?:Maxthon|MyIE2)", "name": "Maxthon", "version": "", "engine": {"default": ""}}, {"regex": "Puffin/(\\d+[.\\d]+)FP", "name": "<PERSON><PERSON><PERSON> Browser", "version": "$1"}, {"regex": "Puffin/(\\d+[.\\d]+)(?:[LMW]D)", "name": "<PERSON><PERSON><PERSON> Browser", "version": "$1"}, {"regex": "Puffin/(\\d+[.\\d]+)(?:[AILW][PT]|M)?", "name": "Puffin Web Browser", "version": "$1"}, {"regex": "MobileIron(?:/(\\d+[.\\d]+))?", "name": "Iron Mobile", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome(?:/(\\d+\\.[.\\d]+) )?.*Iron", "name": "Iron", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "Iron/(\\d+[.\\d]+)", "name": "Iron", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "Epiphany(?:/(\\d+[.\\d]+))?", "name": "GNOME Web", "version": "$1", "engine": {"default": "Gecko", "versions": {"2.9.16": "", "2.28": "WebKit"}}}, {"regex": "LieBaoFast(?:[ /](\\d+[.\\d]+))?", "name": "LieBaoFast", "version": "$1"}, {"regex": "LBBrowser(?:[ /](\\d+[.\\d]+))?", "name": "Cheetah Browser", "version": "$1"}, {"regex": "SE (\\d+[.\\d]+)", "name": "Sogou Explorer", "version": "$1"}, {"regex": "QQBrowserLite/([\\d.]+)", "name": "QQ Browser Lite", "version": "$1"}, {"regex": "M?QQBrowser/Mini([.\\d]+)?", "name": "QQ Browser Mini", "version": "$1", "engine": {"default": ""}}, {"regex": "M?QQ(?:Browser|浏览器)(?:/([.\\d]+))?", "name": "<PERSON><PERSON> Browser", "version": "$1", "engine": {"default": ""}}, {"regex": "(?:<PERSON><PERSON><PERSON><PERSON><PERSON>|MiuiBrowser)(?:/(\\d+[.\\d]+))?", "name": "<PERSON>", "version": "$1", "engine": {"default": ""}}, {"regex": "(?:coc_coc_browser|coccocbrowser|CocCoc)(?:/(\\d+[.\\d]+))?", "name": "Coc Coc", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "(?:DuckDuckGo|Ddg)/(\\d+[.\\d]*)", "name": "DuckDuckGo Privacy Browser", "version": "$1"}, {"regex": "(?:DDG-Android-|ddg_android/)(\\d+[.\\d]*)", "name": "DuckDuckGo Privacy Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Samsung ?Browser(?:[/ ](\\d+[.\\d]+))?", "name": "Samsung Browser", "version": "$1"}, {"regex": "(?:SFB(?:rowser)?)/(\\d+[.\\d]+)", "name": "Super Fast Browser", "version": "$1"}, {"regex": "com\\.browser\\.tssomas(?:/(\\d+[.\\d]+))?", "name": "Super Fast Browser", "version": "$1"}, {"regex": "EUI Browser(?:/(\\d+[.\\d]+))?", "name": "EUI Browser", "version": "$1"}, {"regex": "UBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Streamy(?:/(\\d+[.\\d]+))?", "name": "Streamy", "version": "$1", "engine": {"default": ""}}, {"regex": "isivioo", "name": "Isivioo", "version": "", "engine": {"default": ""}}, {"regex": "Chrome/.+Tenta/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Tenta/(\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Rocket/(\\d+[.\\d]+)", "name": "Firefox Rocket", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Web Explorer/(\\d+\\.[.\\d]+) .*Chrome", "name": "Web Explorer", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "webexplorer/(\\d+)", "name": "Web Explorer", "version": "$1", "engine": {"default": ""}}, {"regex": "Chrome.+SznProhlizec/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SznProhlizec/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON> Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "SogouMobileBrowser/(\\d+[.\\d]+)", "name": "Sogou Mobile Browser", "version": "$1", "engine": {"default": ""}}, {"regex": "Mint Browser/(\\d+[.\\d]+)", "name": "<PERSON> Browser", "version": "$1", "engine": {"default": ""}}, {"regex": "Ecosia (?:android|ios)@(\\d+[.\\d]+)", "name": "Ecosia", "version": "$1", "engine": {"default": "", "versions": {"28": "Blink"}}}, {"regex": "ACHEETAHI", "name": "<PERSON><PERSON>rowser", "version": "", "engine": {"default": ""}}, {"regex": "Chrome/.+ (?:LeBrowser|MobileLenovoBrowser)(?:/(\\d+[.\\d]+))?", "name": "Lenovo Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "LeBrowser(?:/(\\d+[.\\d]+))?", "name": "Lenovo Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "<PERSON>wi Chrome", "name": "<PERSON><PERSON>", "version": "", "engine": {"default": ""}}, {"regex": "Mb2345Browser/(\\d+[.\\d]+)", "name": "2345 Browser", "version": "$1", "engine": {"default": ""}}, {"regex": "Silk/(\\d+[.\\d]+) like Chrome", "name": "Mobile Silk", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Silk(?:/(\\d+[.\\d]+))?", "name": "Mobile Silk", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "iBrowser/Mini(\\d+\\.\\d+)", "name": "iBrowser Mini", "version": "$1"}, {"regex": "iBrowser/(\\d+\\.[.\\d]+)?", "name": "i<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "IBrowse(?:[ /](\\d+[.\\d]+))?", "name": "IBrowse", "version": "$1"}, {"regex": "UP\\.Browser(?:/(\\d+[.\\d]+))?", "name": "Openwave Mobile Browser", "version": "$1"}, {"regex": "Openwave(?:/(\\d+[.\\d]+))?", "name": "Openwave Mobile Browser", "version": "$1"}, {"regex": "OneBrowser(?:[ /](\\d+[.\\d]+))?", "name": "ONE Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "GoBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "(?:NokiaBrowser|BrowserNG|WicKed|Nokia-Communicator-WWW-Browser)(?:/(\\d+[.\\d]+))?", "name": "Nokia Browser", "version": "$1"}, {"regex": "Series60/5\\.0", "name": "Nokia Browser", "version": "7.0"}, {"regex": "Series60/(\\d+[.\\d]+)", "name": "Nokia OSS Browser", "version": "$1"}, {"regex": "S40OviBrowser/(\\d+[.\\d]+)", "name": "Nokia Ovi Browser", "version": "$1"}, {"regex": "^Nokia|Nokia[EN]?\\d+", "name": "Nokia Browser", "version": ""}, {"regex": "Sleipnir(?:(?:%20Browser)?[ /](\\d+[.\\d]+))?", "name": "Sleipnir", "version": "$1", "engine": {"default": ""}}, {"regex": "NTENTBrowser(?:/(\\d+[.\\d]+))?", "name": "NTENT Browser", "version": "$1"}, {"regex": "TV Bro/(\\d+[.\\d]+)", "name": "TV Bro", "version": "$1"}, {"regex": "Quark(?:/(\\d+[.\\d]+))?", "name": "Quark", "version": "$1"}, {"regex": "MonumentBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON> Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "BlueBrowser(?:/(\\d+[.\\d]+))?", "name": "<PERSON>rowser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "JAPAN Browser(?:/(\\d+[.\\d]+))?", "name": "Japan Browser", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "OpenFin/(?:(\\d+[.\\d]+))", "name": "OpenFin", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "SuperBird(?:/(\\d+[.\\d]+))?", "name": "SuperBird", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "Soul(?:Browser)?$|Soul/", "name": "<PERSON> Browser", "version": "", "engine": {"default": "Blink"}}, {"regex": "LG Browser(?:/(\\d+[.\\d]+))", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "QtWebEngine/(\\d+[.\\d]+)", "name": "QtWebEngine", "version": "$1", "engine": {"default": ""}}, {"regex": "(?: wv\\)|Version/).* Chrome(?:/(\\d+[.\\d]+))?", "name": "Chrome Webview", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "CrMo(?:/(\\d+[.\\d]+))?", "name": "Chrome Mobile", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "CriOS(?:/(\\d+[.\\d]+))?", "name": "Chrome Mobile iOS", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Chrome(?:/(\\d+[.\\d]+))? Mobile", "name": "Chrome Mobile", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "chromeframe(?:/(\\d+[.\\d]+))?", "name": "Chrome Frame", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Chromium(?:/(\\d+[.\\d]+))?", "name": "Chromium", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": ".*Chromium(?:_(\\d+[.\\d]+))?", "name": "Chromium", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "HeadlessChrome(?:/(\\d+[.\\d]+))?", "name": "Headless Chrome", "version": "$1", "engine": {"default": "Blink"}}, {"regex": "Chrome(?!book)(?:/(\\d+[.\\d]+))?", "name": "Chrome", "version": "$1", "engine": {"default": "WebKit", "versions": {"28": "Blink"}}}, {"regex": "PocketBook/", "name": "PocketBook Browser", "version": "", "engine": {"default": "WebKit"}}, {"regex": "(?:Tizen|SLP) ?Browser(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON><PERSON> B<PERSON>er", "version": "$1"}, {"regex": "Tizen (?:\\d+\\.[.\\d]+)[^\\.\\d].* Version/(\\d+[.\\d]+) (?:TV|Mobile|like)", "name": "<PERSON><PERSON><PERSON> B<PERSON>er", "version": "$1", "engine": {"default": "WebKit", "versions": {"4": "Blink"}}}, {"regex": "Blazer(?:/(\\d+[.\\d]+))?", "name": "Palm Blazer", "version": "$1"}, {"regex": "Pre/(\\d+[.\\d]+)", "name": "Palm Pre", "version": "$1"}, {"regex": "(?:hpw|web)OS/(\\d+[.\\d]+)", "name": "w<PERSON><PERSON><PERSON>er", "version": "$1"}, {"regex": "WebPro(?:[ /](\\d+[.\\d]+))?", "name": "Palm WebPro", "version": "$1"}, {"regex": "Palmscape(?:[ /](\\d+[.\\d]+))?", "name": "Palmscape", "version": "$1"}, {"regex": "Jasmine(?:[ /](\\d+[.\\d]+))?", "name": "Jasmine", "version": "$1"}, {"regex": "Lynx(?:/(\\d+[.\\d]+))?", "name": "Lynx", "version": "$1", "engine": {"default": "Text-based"}}, {"regex": "NCSA_Mosaic(?:/(\\d+[.\\d]+))?", "name": "NCSA Mosaic", "version": "$1"}, {"regex": "VMS_Mosaic(?:/(\\d+[.\\d]+))?", "name": "VMS Mosaic", "version": "$1"}, {"regex": "ABrowse(?: (\\d+[.\\d]+))?", "name": "<PERSON><PERSON>e", "version": "$1"}, {"regex": "amaya(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "AmigaVoyager(?:/(\\d+[.\\d]+))?", "name": "Amiga Voyager", "version": "$1"}, {"regex": "Amiga-Aweb(?:/(\\d+[.\\d]+))?", "name": "Amiga Aweb", "version": "$1"}, {"regex": "Arora(?:/(\\d+[.\\d]+))?", "name": "Arora", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Beonex(?:/(\\d+[.\\d]+))?", "name": "Beonex", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "bline(?:/(\\d+[.\\d]+))?", "name": "B-Line", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "BrowseX \\((\\d+[.\\d]+)", "name": "BrowseX", "version": "$1"}, {"regex": "Charon(?:[/ ](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Cheshire(?:/(\\d+[.\\d]+))?", "name": "Cheshire", "version": "$1"}, {"regex": "dbrowser", "name": "dbrowser", "version": "", "engine": {"default": "WebKit"}}, {"regex": "<PERSON><PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "<PERSON><PERSON>"}}, {"regex": "Dolfin(?:/(\\d+[.\\d]+))?|dolphin", "name": "Dolphin", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Elinks(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "Text-based"}}, {"regex": "Element Browser(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>er", "version": "$1"}, {"regex": "eZBrowser(?:/(\\d+[.\\d]+))?", "name": "e<PERSON> Browser", "version": "$1"}, {"regex": "Firebird(?! Build)(?:/(\\d+[.\\d]+))?", "name": "Firebird", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Fluid(?:/(\\d+[.\\d]+))?", "name": "Fluid", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Galeon(?:/(\\d+[.\\d]+))?", "name": "Gale<PERSON>", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "(?:Google Earth Pro|Google%20Earth%20Pro)(?:/(\\d+[.\\d]+))?", "name": "Google Earth Pro", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "GoogleEarth/(\\d+\\.[.\\d]+)[^\\.\\d].*client:(?:Plus|Pro)", "name": "Google Earth Pro", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Google ?Earth(?:/v?(\\d+[.\\d]+))?", "name": "Google Earth", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "HotJava(?:/(\\d+[.\\d]+))?", "name": "HotJava", "version": "$1"}, {"regex": "iCabMobile(?:[ /](\\d+[.\\d]+))?", "name": "iCab Mobile", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "iCab(?:[ /](\\d+[.\\d]+))?", "name": "iCab", "version": "$1", "engine": {"default": "iCab", "versions": {"4": "WebKit"}}}, {"regex": "Crazy Browser (\\d+[.\\d]+)", "name": "<PERSON> Browser", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "IEMobile[ /](\\d+[.\\d]+)", "name": "IE Mobile", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "MSIE (\\d+\\.[.\\d]+)[^\\.\\d].*XBLWP7", "name": "IE Mobile", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "MSIE.*Trident/4\\.0", "name": "Internet Explorer", "version": "8.0", "engine": {"default": "Trident"}}, {"regex": "MSIE.*Trident/5\\.0", "name": "Internet Explorer", "version": "9.0", "engine": {"default": "Trident"}}, {"regex": "MSIE.*Trident/6\\.0", "name": "Internet Explorer", "version": "10.0", "engine": {"default": "Trident"}}, {"regex": "Trident/[78]\\.0", "name": "Internet Explorer", "version": "11.0", "engine": {"default": "Trident"}}, {"regex": "MSIE (\\d+[.\\d]+)", "name": "Internet Explorer", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "IE[ /](\\d+[.\\d]+)", "name": "Internet Explorer", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "(?:MSPIE|Pocket Internet Explorer)[ /](\\d+[.\\d]+)", "name": "Pocket Internet Explorer", "version": "$1", "engine": {"default": "Trident"}}, {"regex": "Kindle/(\\d+[.\\d]+)", "name": "<PERSON>le Browser", "version": "$1"}, {"regex": "K-meleon(?:/(\\d+[.\\d]+))?", "name": "K-meleon", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "Links(?: \\((\\d+[.\\d]+))?", "name": "Links", "version": "$1", "engine": {"default": "Text-based"}}, {"regex": "LuaKit(?:/(\\d+[.\\d]+))?", "name": "LuaKit", "version": "$1"}, {"regex": "OmniWeb(?:/[v]?(\\d+[.\\d]+))?", "name": "OmniWeb", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "(?<!/)Phoenix(?:/(\\d+[.\\d]+))?", "name": "Phoenix", "version": "$1"}, {"regex": "NetFrontLifeBrowser(?:/(\\d+[.\\d]+))?", "name": "NetFront Life", "version": "$1", "engine": {"default": "NetFront"}}, {"regex": "Browser/(?:NetFont-|NF|NetFront)(\\d+[.\\d]+)", "name": "NetFront", "version": "$1", "engine": {"default": "NetFront"}}, {"regex": "NetFront(?:/(\\d+[.\\d]+))?", "name": "NetFront", "version": "$1", "engine": {"default": "NetFront"}}, {"regex": "PLAYSTATION|NINTENDO 3|AppleWebKit.+ N[XF]/\\d+\\.\\d+\\.\\d+", "name": "NetFront", "version": ""}, {"regex": "NetPositive(?:/(\\d+[.\\d]+))?", "name": "NetPositive", "version": "$1"}, {"regex": "Odyssey Web Browser(?:.*OWB/(\\d+[.\\d]+))?", "name": "Odyssey Web Browser", "version": "$1"}, {"regex": "OffByOne", "name": "Off By One", "version": ""}, {"regex": "(?:Oregano|OreganMediaBrowser)(?:[ /](\\d+[.\\d]+))?", "name": "Oregano", "version": "$1"}, {"regex": "(?:<PERSON><PERSON>|Embider)(?:[/ ](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "SEMC-Browser(?:[/ ](\\d+[.\\d]+))?", "name": "SEMC-<PERSON><PERSON>er", "version": "$1"}, {"regex": "<PERSON><PERSON>(?:[/ ](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Skyfire(?:[/ ](\\d+[.\\d]+))?", "name": "Skyfire", "version": "$1"}, {"regex": "Snowshoe(?:/(\\d+[.\\d]+))?", "name": "Snowshoe", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Sunrise(?:<PERSON><PERSON><PERSON>)?(?:/(\\d+[.\\d]+))?", "name": "Sunrise", "version": "$1"}, {"regex": "WeTab<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>er", "version": ""}, {"regex": "<PERSON><PERSON>(?:/(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "BlackBerry|PlayBook|BB10", "name": "<PERSON><PERSON><PERSON>", "version": ""}, {"regex": "Browlser/(\\d+[.\\d]+)", "name": "<PERSON><PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "(?<! like )Android(?!\\.)", "name": "Android Browser", "version": "", "engine": {"default": "WebKit"}}, {"regex": "Coast(?:/(\\d+[.\\d]+))?", "name": "Coast", "version": "$1"}, {"regex": "Opera%20Coast/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)", "name": "Coast", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Surf(?:/(\\d+[.\\d]+))?", "name": "surf", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Safari%20Technology%20Preview/(\\d+[.\\d]+)", "name": "Safari Technology Preview", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "(?:(?:iPod|iPad|iPhone).+Version|MobileSafari)/(\\d+[.\\d]+)", "name": "Mobile Safari", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "NetworkingExtension/.+ Network/.+ iOS/(\\d+[.\\d]+)", "name": "Mobile Safari", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "(?:Version/(\\d+\\.[.\\d]+) .*)?Mobile.*Safari/", "name": "Mobile Safari", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "(?!^AppleCoreMedia/1\\.0\\.0)(?:iPod|(?<!Apple TV; U; CPU )iPhone|iPad)", "name": "Mobile Safari", "version": "", "engine": {"default": "WebKit"}}, {"regex": "Version/(\\d+\\.[.\\d]+) .*Safari/|(?:Safari|Safari(?:%20)?%E6%B5%8F%E8%A7%88%E5%99%A8)/?\\d+", "name": "Safari", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "NetworkingExtension/(\\d+[.\\d]+).+ CFNetwork", "name": "Safari", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "Macintosh", "name": "Safari", "version": "", "engine": {"default": "WebKit"}}, {"regex": "(?:\\w{1,5}[_ ])?Dorado(?: WAP-<PERSON><PERSON>er)?(?:[/ ]?(\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "NetSurf(?:/(\\d+[.\\d]+))?", "name": "NetSurf", "version": "$1", "engine": {"default": "NetSurf"}}, {"regex": "Uzbl", "name": "Uzbl", "version": ""}, {"regex": "SimpleBrowser", "name": "SimpleBrowser", "version": ""}, {"regex": "Zvu(?:/(\\d+[.\\d]+))?", "name": "Zvu", "version": "$1", "engine": {"default": "Gecko"}}, {"regex": "GOGGalaxyClient/(\\d+[.\\d]+)?", "name": "GOG Galaxy", "version": "$1"}, {"regex": "WAP Browser/MAUI|(?:\\w*)<PERSON><PERSON> Wap Browser|MAUI[- ]Browser", "name": "MAUI WAP Browser", "version": ""}, {"regex": "SP%20Browser/(\\d+[.\\d]+)", "name": "SP Browser", "version": "$1", "engine": {"default": "WebKit"}}, {"regex": "(?<!like )Gecko(?!/\\d+ SlimerJS)", "name": "Firefox", "version": "", "engine": {"default": "Gecko"}}]