[{"regex": "NetFront", "name": "NetFront"}, {"regex": "<PERSON>/", "name": "Edge"}, {"regex": "Trident", "name": "Trident"}, {"regex": "Chr[o0]me/(?!1?\\d\\.|2[0-7]\\.)", "name": "Blink"}, {"regex": "(?:Apple)?WebKit", "name": "WebKit"}, {"regex": "Presto", "name": "Presto"}, {"regex": "Goanna", "name": "Goanna"}, {"regex": "(?<!like )C<PERSON>cko", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"regex": "(?<!like )Gecko", "name": "Gecko"}, {"regex": "KHTML", "name": "KHTML"}, {"regex": "NetSurf", "name": "NetSurf"}, {"regex": "Servo", "name": "Servo"}, {"regex": "<PERSON><PERSON><PERSON>(?:Flow)?", "name": "EkiohFlow"}, {"regex": "xChaos_<PERSON>ne", "name": "<PERSON><PERSON><PERSON>"}, {"regex": "LibWeb\\+LibJs", "name": "LibWeb"}, {"regex": "Maple (?!III)(\\d+[.\\d]+)|Maple\\d{4}", "name": "Maple"}]