[{"regex": "Coolita OS QJY/.+LiteOS(\\d+[.\\d]+)", "name": "Coolita OS", "version": "$1"}, {"regex": "Coolita OS QJY/(\\d+[.\\d]+)", "name": "Coolita OS", "version": "$1"}, {"regex": "^NEXT ", "name": "RTOS & Next", "version": ""}, {"regex": "(?:Pacific|(?<!like )Quest).+OculusBrowser|Standalone HMD|PortalGo", "name": "Meta Horizon", "version": ""}, {"regex": "LeafOS", "name": "LeafOS", "version": ""}, {"regex": "Cloud Phone", "name": "Puffin OS", "version": ""}, {"regex": "RisingOS", "name": "risingOS", "version": ""}, {"regex": ".+.cm(\\d).x86_64", "name": "Azure Linux", "version": "$1"}, {"regex": "Linux[/-].+-(azure|Microsoft)", "name": "Azure Linux", "version": ""}, {"regex": "ViziOS/(\\d+[.\\d]+)", "name": "ViziOS", "version": "$1"}, {"regex": "VIZIO(?: SmartCast|-DTV)", "name": "ViziOS", "version": ""}, {"regex": "blackPanther OS", "name": "blackPanther OS", "version": ""}, {"regex": "WoPhone(?:/(\\d+[.\\d]+))?", "name": "WoPhone", "version": "$1"}, {"regex": "KIN\\.(?:One|Two) (\\d+[.\\d]+)", "name": "KIN OS", "version": "$1"}, {"regex": "Star-Blade OS", "name": "Star-Blade OS", "version": ""}, {"regex": "Qtopia/(\\d+[.\\d]+)", "name": "Qtopia", "version": "$1"}, {"regex": "OpenVMS V(\\d+[.\\d]+)", "name": "OpenVMS", "version": "$1"}, {"regex": "AROS", "name": "AROS", "version": ""}, {"regex": "\\(NEXT\\)", "name": "NeXTSTEP", "version": ""}, {"regex": "NEWS-OS (\\d+[.\\d]+)", "name": "NEWS-OS", "version": "$1"}, {"regex": "ULTRIX (\\d+[.\\d]+)", "name": "ULTRIX", "version": "$1"}, {"regex": "Turbolinux/(\\d+[.\\d]+)", "name": "Turbolinux", "version": "$1"}, {"regex": "Joli OS/(\\d+[.\\d]+)", "name": "Joli OS", "version": "$1"}, {"regex": "GENIX (\\d+[.\\d]+)", "name": "GENIX", "version": "$1"}, {"regex": "gNewSense/.*\\((\\d+[.\\d]+)", "name": "gNewSense", "version": "$1"}, {"regex": "Geos (\\d+[.\\d]+)", "name": "GEOS", "version": "$1"}, {"regex": "MOT-.*LinuxOS/", "name": "Motorola EZX", "version": ""}, {"regex": "Baidu Yi", "name": "Baidu Yi", "version": ""}, {"regex": "붉은별|Fedora/(?:\\d+\\.[\\d.-]+)\\.rs(\\d+(?:[_.]\\d+)*)", "name": "Red Star", "version": "$1"}, {"regex": "Linux/(?:\\d+\\.[\\d.-]+)-(?:pve|v(\\d)\\+)", "name": "Proxmox VE", "version": "$1"}, {"regex": "Linux/(?:\\d+\\.[\\d.-]+)-(?:\\d+[.\\d]+)stab(?:\\d+[.\\d]+)", "name": "OpenVZ", "version": ""}, {"regex": "rocky/(\\d+[.\\d]+)", "name": "Rocky <PERSON>", "version": "$1"}, {"regex": "clear-linux-os/(\\d+[.\\d]+)", "name": "Clear Linux OS", "version": "$1"}, {"regex": "alpine/(\\d+[.\\d]+)", "name": "Alpine Linux", "version": "$1"}, {"regex": "scientific/(\\d+[.\\d]+)", "name": "Scientific Linux", "version": "$1"}, {"regex": "eulerosv(\\d)r(\\d+)", "name": "EulerOS", "version": "$1.$2"}, {"regex": "loongnix-server/(\\d+[.\\d]+)", "name": "Loongnix", "version": "$1"}, {"regex": "Linux/(?:\\d+\\.[\\d.-]+)\\.lns(\\d+(?:[_.]\\d+)*)\\.loongarch64", "name": "Loongnix", "version": "$1"}, {"regex": "AOSC OS", "name": "AOSC OS", "version": ""}, {"regex": "SerenityOS|Ladybird/", "name": "SerenityOS", "version": ""}, {"regex": "Helix Phone", "name": "HELIX OS", "version": ""}, {"regex": "Armadillo", "name": "Armadillo OS", "version": ""}, {"regex": "<PERSON>ian", "name": "<PERSON>ian", "version": ""}, {"regex": "Linux.*Liri/", "name": "Liri OS", "version": ""}, {"regex": "FRITZ!OS(?:/0(\\d+[.\\d]+))?", "name": "FRITZ!OS", "version": "$1"}, {"regex": "UOS$", "name": "UOS", "version": ""}, {"regex": "Raspbian/(\\d+[.\\d]+)", "name": "Raspbian", "version": "$1"}, {"regex": "Raspbian", "name": "Raspberry Pi OS", "version": ""}, {"regex": "BrightSign/(?:[A-Z0-9]+)/(\\d+[.\\d]+)", "name": "BrightSignOS", "version": "$1"}, {"regex": "BrightSign/(\\d+[.\\d]+)", "name": "BrightSignOS", "version": "$1"}, {"regex": "LuneOS", "name": "LuneOS", "version": ""}, {"regex": "Linux/(?:\\d+\\.[\\d.-]+)\\.el(\\d+(?:[_.]\\d+)*)uek", "name": "Oracle Linux", "version": "$1"}, {"regex": ".+kali(\\d)", "name": "Kali", "version": "$1"}, {"regex": "TiVoOS/(\\d+[.\\d]+)", "name": "TiVo OS", "version": "$1"}, {"regex": "VIDAA/(\\d+[.\\d]+)", "name": "VIDAA", "version": "$1"}, {"regex": "PICO.+OS(\\d+[.\\d]*)", "name": "PICO OS", "version": "$1"}, {"regex": "RED OS; Linux", "name": "RedOS", "version": ""}, {"regex": "Mikrotik/(\\d)\\.x", "name": "RouterOS", "version": "$1"}, {"regex": "FINNEY U1", "name": "Sirin OS", "version": ""}, {"regex": "Nova; Linux", "name": "Nova", "version": ""}, {"regex": "Android 10.+bliss_maple", "name": "Bliss OS", "version": "12"}, {"regex": ".+tlinux(\\d)", "name": "TencentOS", "version": "$1"}, {"regex": ".+.amzn(\\d)(?:int)?.x86_64|Linux/.+aws", "name": "Amazon Linux", "version": "$1"}, {"regex": "Linux/.+-c9$", "name": "Amazon Linux", "version": "2023"}, {"regex": " COS like Android", "name": "China OS", "version": ""}, {"regex": "Android (\\d+[.\\d]*); ClearPHONE", "name": "ClearOS Mobile", "version": "$1"}, {"regex": "Plasma Mobile", "name": "Plasma Mobile", "version": ""}, {"regex": "KreaTV/0\\.0\\.0\\.0", "name": "KreaTV", "version": ""}, {"regex": "KreaTV/(\\d+[.\\d]+)", "name": "KreaTV", "version": "$1"}, {"regex": "Linux (?:[^;]+); Opera TV(?: Store)?/|^Opera/\\d+\\.\\d+ \\(Linux mips", "name": "Opera TV", "version": ""}, {"regex": "OPR/.+TV Store/(\\d+[.\\d]+)", "name": "Opera TV", "version": "$1"}, {"regex": "Grid OS (\\d+[.\\d]+)", "name": "GridOS", "version": "$1"}, {"regex": "CaixaMagica", "name": "Caixa Mágica", "version": ""}, {"regex": "Mageia; Linux", "name": "Mageia", "version": ""}, {"regex": "(?:WH|WhaleTV)/?(\\d+[.\\d]+)", "name": "Whale OS", "version": "$1"}, {"regex": "Zeasn/.*TBrowser/2\\.0", "name": "Whale OS", "version": "1"}, {"regex": "Zeasn/", "name": "Whale OS", "version": ""}, {"regex": "Tizen[ /]?(\\d+[.\\d]+)?", "name": "Tizen", "version": "$1"}, {"regex": "Maple (?!III)(?:\\d+[.\\d]+)|Maple_?\\d{4}|HbbTV/.+Samsung", "name": "Tizen", "version": ""}, {"regex": "Android (\\d+[.\\d]+)-RT", "name": "YunOS", "version": "$1"}, {"regex": "(?:<PERSON>)?YunOS[ /]?(\\d+[.\\d]+)?", "name": "YunOS", "version": "$1"}, {"regex": "Windows Phone;FBSV/(\\d+[.\\d]+)", "name": "Windows Phone", "version": "$1"}, {"regex": "(?:Windows Phone (?:OS)?|wds)[ /]?(\\d+[.\\d]+)", "name": "Windows Phone", "version": "$1"}, {"regex": "XBLWP7|Windows Phone", "name": "Windows Phone", "version": ""}, {"regex": "Windows CE(?: (\\d+[.\\d]+))?", "name": "Windows CE", "version": "$1"}, {"regex": "(?:IEMobile|Windows ?Mobile)(?: (\\d+[.\\d]+))?", "name": "Windows Mobile", "version": "$1"}, {"regex": "Windows NT 6\\.2; ARM;", "name": "Windows RT", "version": ""}, {"regex": "Windows NT 6\\.3; ARM;", "name": "Windows RT", "version": "8.1"}, {"regex": "Windows IoT 10\\.0", "name": "Windows IoT", "version": "10"}, {"regex": "KAIOS(?:/(\\d+[.\\d]+))?", "name": "KaiOS", "version": "$1"}, {"regex": "HarmonyOS(?:[/ ](\\d+[.\\d]+))?", "name": "HarmonyOS", "version": "$1"}, {"regex": "Hmos/1", "name": "HarmonyOS", "version": "1.0.0"}, {"regex": "RazoDroiD(?: v(\\d+[.\\d]*))?", "name": "RazoDroiD", "version": "$1"}, {"regex": "MildWild(?: CM-(\\d+[.\\d]*))?", "name": "MildWild", "version": "$1"}, {"regex": "CyanogenMod(?:[\\-/](?:CM)?(\\d+[.\\d]*))?", "name": "CyanogenMod", "version": "$1"}, {"regex": "(?:.*_)?MocorDroid(?:(\\d+[.\\d]*))?", "name": "MocorDroid", "version": "$1"}, {"regex": "FydeOS", "name": "FydeOS", "version": ""}, {"regex": "Fire OS(?:/(\\d+[.\\d]*))?", "name": "Fire OS", "version": "$1"}, {"regex": "(?:Andr[o0]id (\\d([\\d.])*);? |Amazon;|smarttv_)AFT|AEO[ACBHKT]|KF[ADFGJKMORSTQ]|.*FIRETVSTICK2018", "name": "Fire OS", "versions": [{"regex": "Andr[o0]id 1[01].+ (?:AFT|KF[ADFGJKMORSTQ])", "version": "8"}, {"regex": "Andr[o0]id 9.+ (?:AEO[AHT]|AFT|KF[ADFGJKMORSTQ])|AFTSO001", "version": "7"}, {"regex": "Andr[o0]id 7|.+FIRETVSTICK2018", "version": "6"}, {"regex": "Andr[o0]id 5\\.1", "version": "5"}, {"regex": "Andr[o0]id 4\\.4\\.3", "version": "4.5.1"}, {"regex": "Andr[o0]id 4\\.4\\.2", "version": "4"}, {"regex": "Andr[o0]id 4\\.2\\.2", "version": "3"}, {"regex": "Andr[o0]id 4\\.0\\.[34]", "version": "3"}, {"regex": "Andr[o0]id 4\\.0", "version": "2"}, {"regex": "Andr[o0]id 2\\.3\\.3", "version": "1"}]}, {"regex": "cordova-amazon-fireos", "name": "Fire OS", "version": ""}, {"regex": "revengeos_x2", "name": "Revenge OS", "version": "2"}, {"regex": "Lineage_(\\d+[.\\d]*)", "name": "Lineage OS", "version": "$1"}, {"regex": "(?:Android (\\d([\\d.])*);? )?lineage_\\w+", "name": "Lineage OS", "versions": [{"regex": "Android 15", "version": "22"}, {"regex": "Android 14", "version": "21"}, {"regex": "Android 13", "version": "20.0"}, {"regex": "Android 12\\.1", "version": "19.1"}, {"regex": "Android 12", "version": "19.0"}, {"regex": "Android 11", "version": "18.0"}, {"regex": "Android 10", "version": "17.0"}, {"regex": "Android 9", "version": "16.0"}, {"regex": "Android 8\\.1", "version": "15.1"}, {"regex": "Android 8", "version": "15.0"}, {"regex": "Android 7\\.1", "version": "14.1"}, {"regex": "Android 7", "version": "14.0"}, {"regex": "Android 6", "version": "13.0"}, {"regex": "Android 5\\.1", "version": "12.1"}, {"regex": "Android 5", "version": "12.0"}, {"regex": "Android 4\\.4", "version": "11.0"}, {"regex": "Android 4\\.3", "version": "10.2"}, {"regex": "Android 4\\.2", "version": "10.1"}, {"regex": "Android 4", "version": "9.1.0"}]}, {"regex": "Android 8(?:[\\d.]*);(?: [\\w-]+;)? rr_fortuna3g", "name": "Resurrection Remix OS", "version": "6"}, {"regex": "RemixOS|Remix (?:Pro|Mini)", "name": "Remix OS", "versions": [{"regex": "RemixOS 5", "version": "1"}, {"regex": "RemixOS 6|Remix Mini", "version": "2"}, {"regex": "Remix Pro", "version": "3"}]}, {"regex": "FreeBSD.+Android", "name": "FreeBSD", "version": ""}, {"regex": "Chrome/(\\d+\\.[.\\d]+) Odd/|SM-R(?:8[6-9]|9)|LEM14", "name": "Wear OS", "version": ""}, {"regex": "SeewoOS x86_64 (\\d+[.\\d]+)", "name": "SeewoOS", "version": "$1"}, {"regex": "(?:CrOS [a-z0-9_]+ |.*Build/R\\d+-)(\\d+[.\\d]+)|Chromebook", "name": "Chrome OS", "version": "$1"}, {"regex": "(?:Android (?:9|1[0-6])[.\\d]*|Linux x86_64); (?:asurada|atlas|brask|brya|cherry|coral|corsola|dedede|drallion|elm|eve|fizz|geralt|grunt|guybrush|hana|hatch|jacuzzi|kalista|kukui|nami|nautilus|nissa|nocturne|octopus|puff|pyro|rammus|reef|rex|sand|sarien|skyrim|snappy|soraka|staryu|strongbad|trogdor|volteer|zork)\\)", "name": "Chrome OS", "version": ""}, {"regex": "Linux; Andr0id[; ](\\d+[.\\d]*)", "name": "Android TV", "version": "$1"}, {"regex": "Android[; ](\\d+[.\\d]*).+(?:(?:Android(?: UHD)?|AT&T|Google|Smart)[ _]?TV|AOSP on r33a0|BRAVIA|wv-atv)", "name": "Android TV", "version": "$1"}, {"regex": "Windows.+Andr0id TV|.+(?:K_?Android_?TV_|AndroidTV|GoogleTV_)", "name": "Android TV", "version": ""}, {"regex": "(?:Android API \\d+|\\d+/tclwebkit(?:\\d+[.\\d]*)|(?:(?<!\\d )Android/\\d{2}|Android (?!1[0-6])\\d{2})(?![^; ]))", "name": "Android", "versions": [{"regex": "Android API 36|36/tclwebkit|Android[ /]36", "version": "16"}, {"regex": "Android API 35|35/tclwebkit|Android[ /]35", "version": "15"}, {"regex": "Android API 34|34/tclwebkit|Android[ /]34", "version": "14"}, {"regex": "Android API 33|33/tclwebkit|Android[ /]33", "version": "13"}, {"regex": "Android API 32|32/tclwebkit|Android[ /]32", "version": "12.1"}, {"regex": "Android API 31|31/tclwebkit|Android[ /]31", "version": "12"}, {"regex": "Android API 30|30/tclwebkit|Android[ /]30", "version": "11"}, {"regex": "Android API 29|29/tclwebkit|Android[ /]29", "version": "10"}, {"regex": "Android API 28|28/tclwebkit|Android[ /]28", "version": "9"}, {"regex": "Android API 27|27/tclwebkit|Android[ /]27", "version": "8.1"}, {"regex": "Android API 26|26/tclwebkit|Android[ /]26", "version": "8"}, {"regex": "Android API 25|25/tclwebkit|Android[ /]25", "version": "7.1"}, {"regex": "Android API 24|24/tclwebkit|Android[ /]24", "version": "7"}, {"regex": "Android API 23|23/tclwebkit|Android[ /]23", "version": "6"}, {"regex": "Android API 22|22/tclwebkit|Android[ /]22", "version": "5.1"}, {"regex": "Android API 21|21/tclwebkit|Android[ /]21", "version": "5"}, {"regex": "Android API (?:20|19)|(?:20|19)/tclwebkit|Android[ /](?:20|19)", "version": "4.4"}, {"regex": "Android API 18|18/tclwebkit|Android[ /]18", "version": "4.3"}, {"regex": "Android API 17|17/tclwebkit|Android[ /]17", "version": "4.2"}, {"regex": "Android API 16|16/tclwebkit|Android[ /]16", "version": "4.1"}, {"regex": "Android API 15|15/tclwebkit|Android[ /]15", "version": "4.0.3"}, {"regex": "Android API 14|14/tclwebkit|Android[ /]14", "version": "4.0.1"}]}, {"regex": "Android Marshmallow", "name": "Android", "version": "6"}, {"regex": "(?:Podbean|Podimo)(?:.*)/Android|Rutube(?:TV)?BlackAndroid", "name": "Android", "version": ""}, {"regex": "(?:Android OS|OMDroid|sdk_gphone64_arm64-userdebug|StarOS)[ /](\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "Pinterest for Android(?: Tablet)?/.*; (\\d(?:[\\d.]*))\\)$", "name": "Android", "version": "$1"}, {"regex": "Android; (\\d+[.\\d]*); Mobile;", "name": "Android", "version": "$1"}, {"regex": "[ ]([\\d.]+)\\) AppleWebKit.*ROBLOX Android App", "name": "Android", "version": "$1"}, {"regex": "(?:(?:Orca-)?(?<!like |/|RadioPublic |Anghami |Callpod Keeper for )Android|Adr|AOSP)[ /]?(?:[a-z]+ )?(\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "(?:Allview_TX1_Quasar|Cosmote_My_mini_Tab) (\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "Android ?(?:jelly bean|Kit Kat|S\\.O\\. <PERSON> Bread|The FireCyano|:) (\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "(?:Orca-Android|FB4A).*FBSV/(\\d+[.\\d]*);", "name": "Android", "version": "$1"}, {"regex": "(?:TwitterAndroid).*[ /](?:[a-z]+ )?(\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "\\(Android:.*\\); API (\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "Android-(\\d+[.\\d]*);", "name": "Android", "version": "$1"}, {"regex": " Adr |.*(?<!like |/ )Android|Silk-Accelerated=[a-z]{4,5}", "name": "Android", "version": ""}, {"regex": "BeyondPod|AntennaPod|Podkicker|DoggCatcher|okhttp|Podcatcher Deluxe|Sonos/.+\\(ACR_|.*WhatsApp/.*A$", "name": "Android", "version": ""}, {"regex": "Linux; diordnA[; ](\\d+[.\\d]*)", "name": "Android", "version": "$1"}, {"regex": "^A/(\\d+[.\\d]*)/", "name": "Android", "version": "$1"}, {"regex": "Sailfish|Jolla", "name": "Sailfish OS", "version": ""}, {"regex": "AmigaOS[ ]?(\\d+[.\\d]+)", "name": "AmigaOS", "version": "$1"}, {"regex": "AmigaOS|AmigaVoyager|Amiga-AWeb", "name": "AmigaOS", "version": ""}, {"regex": "ThreadX(?:_OS)?(?:/(\\d+[.\\d]*))?", "name": "ThreadX", "version": "$1"}, {"regex": "Nucleus(?:(?: |/v?)(\\d+[.\\d]*))?", "name": "MTK / Nucleus", "version": "$1"}, {"regex": "MTK(?:(?: |/v?)(\\d+[.\\d]*))?", "name": "MTK / Nucleus", "version": "$1"}, {"regex": "MRE/(\\d+)\\.(\\d+).*;MAUI", "name": "MRE", "version": "$1.$2"}, {"regex": "Linspire", "name": "Linspire", "version": ""}, {"regex": "LindowsOS", "name": "LindowsOS", "version": ""}, {"regex": "Zenwalk GNU Linux", "name": "Zenwalk", "version": ""}, {"regex": "Linux.+kanotix", "name": "Kanotix", "version": ""}, {"regex": "moonOS/(\\d+.[\\d.]+)", "name": "moonOS", "version": "$1"}, {"regex": "Foresight Linux", "name": "Foresight Linux", "version": ""}, {"regex": "Pardus/(\\d+.[\\d.]+)", "name": "<PERSON><PERSON><PERSON>", "version": "$1"}, {"regex": "Librem 5", "name": "PureOS", "version": ""}, {"regex": "uclient-fetch", "name": "OpenWrt", "version": ""}, {"regex": "RokuOS/.+RokuOS (\\d+.[\\d.]+)", "name": "Roku OS", "version": "$1"}, {"regex": "Roku(?:OS|4640X)?/(?:DVP|Pluto)?-?(\\d+[.\\d]+)?", "name": "Roku OS", "version": "$1"}, {"regex": "Roku; (?:AP|UI); (\\d+[.\\d]+)", "name": "Roku OS", "version": "$1"}, {"regex": "RokuBrowser/.+\\(TV-([a-z0-9]+)-(\\d+.[\\d.]+)\\)", "name": "Roku OS", "version": "$2"}, {"regex": "dvkbuntu", "name": "DVKBuntu", "version": ""}, {"regex": "Helio/(\\d+[.\\d]+)", "name": "Lumin OS", "version": "$1"}, {"regex": "HasCodingOs (\\d+[.\\d]+)", "name": "HasCodingOS", "version": "$1"}, {"regex": "PCLinuxOS/(\\d+[.\\d]+)", "name": "PCLinuxOS", "version": "$1"}, {"regex": "(Ordissimo|webissimo3)", "name": "Ordissimo", "version": ""}, {"regex": "(?:Win|Sistema )Fenix", "name": "Fenix", "version": ""}, {"regex": "TOS; Linux", "name": "TmaxOS", "version": ""}, {"regex": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "version": ""}, {"regex": "Arch ?Linux(?:[ /\\-](\\d+[.\\d]+))?", "name": "Arch Linux", "version": "$1"}, {"regex": "VectorLinux(?: package)?(?:[ /\\-](\\d+[.\\d]+))?", "name": "VectorLinux", "version": "$1"}, {"regex": "sles/(\\d+[.\\d]+)", "name": "SUSE", "version": "$1"}, {"regex": "(?:rhel|Red Hat Enterprise Linux Server)/(\\d+[.\\d]+)", "name": "Red Hat", "version": "$1"}, {"regex": ".+redhat-linux-gnu|rhel|Red Hat Enterprise Linux Server", "name": "Red Hat", "version": ""}, {"regex": "CentOS Stream (\\d)", "name": "CentOS Stream", "version": "$1"}, {"regex": "centos(?: Linux)?/(\\d+[.\\d]+) ", "name": "CentOS", "version": "$1"}, {"regex": ".+.el(\\d+(?:[_.]\\d+)*).(?:centos|x86_64)", "name": "CentOS", "version": "$1"}, {"regex": "CentOS Linux (\\d)", "name": "CentOS", "version": "$1"}, {"regex": "Fedora/.+.fc(\\d+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "Mandriva(?: Linux)?/.+mdv(\\d+[.\\d]+)", "name": "Mandriva", "version": "$1"}, {"regex": "Linux Mint/(\\d)", "name": "Mint", "version": "$1"}, {"regex": "Zorin OS (\\d+)", "name": "ZorinOS", "version": "$1"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>[-/]hardy", "name": "Ubuntu", "version": "8.04"}, {"regex": "debian/stretch", "name": "Debian", "version": "9.13"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>[-/]feisty", "name": "Ubuntu", "version": "7.04"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>[-/]edgy", "name": "Ubuntu", "version": "6.10"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>[-/]dapper", "name": "Ubuntu", "version": "6.06"}, {"regex": "<PERSON><PERSON><PERSON><PERSON>[-/]breezy", "name": "Ubuntu", "version": "5.10"}, {"regex": "Ubuntu[ /](\\d+[.\\d]+)", "name": "Ubuntu", "version": "$1"}, {"regex": "Linux; .*((?:Debian|Knoppix|Mint|Ubuntu|Kubuntu|Xubuntu|Lubuntu|Fedora|Red Hat|Mandriva|Gentoo|Sabayon|Slackware|SUSE|CentOS|BackTrack))[ /](\\d+[.\\d]+)", "name": "$1", "version": "$2"}, {"regex": "Deepin[ /](\\d+[.\\d]+)", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "(Debian|Knoppix|Mint(?! Browser)|Ubuntu|Kubuntu|Xubuntu|Lubuntu|Fedora|Red Hat|Mandriva|Gentoo|Sabayon|Slackware|SUSE|CentOS|BackTrack|Freebox|ASPLinux)(?:(?: Enterprise)? Linux)?(?:[ /\\-](\\d+[.\\d]+))?", "name": "$1", "version": "$2"}, {"regex": "OS ROSA; Linux", "name": "<PERSON>", "version": ""}, {"regex": "(?:Web0S; .*WEBOS|webOS|web0S|Palm webOS|hpwOS)(?:[/]?(\\d+[.\\d]+))?", "name": "webOS", "version": "$1", "versions": [{"regex": "WEBOS(\\d+[.\\d]+)", "version": "$1"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/108", "version": "24"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/94", "version": "23"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/87", "version": "22"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/79", "version": "6"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/68", "version": "5"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/53", "version": "4"}, {"regex": "Web0S; Linux/SmartTV.+Chr[o0]me/38", "version": "3"}, {"regex": "WEBOS1", "version": "1"}, {"regex": "Web0S; Linux/SmartTV.+Safari/538", "version": "2"}, {"regex": "Web0S; Linux/SmartTV.+Safari/537", "version": "1"}]}, {"regex": "(?:PalmOS|Palm OS)(?:[/ ](\\d+[.\\d]+))?|Palm", "name": "palmOS", "version": "$1"}, {"regex": "<PERSON>ino(?:.*v\\. (\\d+[.\\d]+))?", "name": "palmOS", "version": "$1"}, {"regex": "MorphOS(?:[ /](\\d+[.\\d]+))?", "name": "MorphOS", "version": "$1"}, {"regex": "FBW.+FBSV/(\\d+[.\\d]*);", "name": "Windows", "version": "$1"}, {"regex": "Windows.+OS: (\\d+[.\\d]*)", "name": "Windows", "version": "$1"}, {"regex": "Windows; ?(\\d+[.\\d]*);", "name": "Windows", "version": "$1"}, {"regex": "mingw32|winhttp|WhatsApp/.*W$", "name": "Windows", "version": ""}, {"regex": "(?:Windows(?:-Update-Agent)?|Microsoft-(?:CryptoAPI|Delivery-Optimization|WebDAV-MiniRedir|WNS)|WINDOWS_64)/(\\d+\\.\\d+)", "name": "Windows", "version": "$1"}, {"regex": "Windows-(1[01])-", "name": "Windows", "version": "$1"}, {"regex": "CYGWIN_NT-10\\.0|Win(?:dows )?NT 10\\.0|Windows[ /]10", "name": "Windows", "version": "10"}, {"regex": "CYGWIN_NT-6\\.4|Windows NT 6\\.4|Windows 10|win10", "name": "Windows", "version": "10"}, {"regex": "Windows[/-](2016|2019|2022|2025)Server", "name": "Windows", "version": "Server $1"}, {"regex": "Windows/2012ServerR2", "name": "Windows", "version": "Server 2012 R2"}, {"regex": "CYGWIN_NT-6\\.3|Windows NT 6\\.3|Windows[ /]8\\.1", "name": "Windows", "version": "8.1"}, {"regex": "Windows/2012Server", "name": "Windows", "version": "Server 2012"}, {"regex": "CYGWIN_NT-6\\.2|Windows NT 6\\.2|Windows 8|post2008Server", "name": "Windows", "version": "8"}, {"regex": "Windows/2008ServerR2", "name": "Windows", "version": "Server 2008 R2"}, {"regex": "CYGWIN_NT-6\\.1|Windows NT 6\\.1|Windows[ /]7|win7|Windows \\(6\\.1", "name": "Windows", "version": "7"}, {"regex": "CYGWIN_NT-6\\.0|Windows NT 6\\.0|Windows Vista", "name": "Windows", "version": "Vista"}, {"regex": "CYGWIN_NT-5\\.2|Windows NT 5\\.2|Windows Server 2003 / XP x64|Windows/2003Server", "name": "Windows", "version": "Server 2003"}, {"regex": "CYGWIN_NT-5\\.1|Windows NT 5\\.1|(?:Microsoft-)?Windows[ -]XP", "name": "Windows", "version": "XP"}, {"regex": "CYGWIN_NT-5\\.0|Windows NT 5\\.0|Windows 2000", "name": "Windows", "version": "2000"}, {"regex": "CYGWIN_NT-4\\.0|Windows NT 4\\.0|WinNT(?! 10)|Windows NT", "name": "Windows", "version": "NT"}, {"regex": "CYGWIN_ME-4\\.90|Win 9x 4\\.90|Windows ME", "name": "Windows", "version": "ME"}, {"regex": "CYGWIN_98-4\\.10|Win98|Windows 98", "name": "Windows", "version": "98"}, {"regex": "CYGWIN_95-4\\.0|Win32|Win95|Windows 95|Windows_95", "name": "Windows", "version": "95"}, {"regex": "Windows 3\\.1", "name": "Windows", "version": "3.1"}, {"regex": "Windows|.+win32|Win64|MSDW|HandBrake Win Upd|Microsoft BITS|ms-office; MSOffice", "name": "Windows", "version": ""}, {"regex": "OS/Microsoft_Windows_NT_(\\d+\\.\\d+)", "name": "Windows", "version": "$1"}, {"regex": "Hai<PERSON>", "name": "Haiku OS", "version": ""}, {"regex": "Apple ?TV.*CPU (?:iPhone )?OS ((?:9|1[0-8])[_.]\\d+(?:[_.]\\d+)*)", "name": "tvOS", "version": "$1"}, {"regex": "Apple TV; iOS ((?:9|1[0-8])[_.]\\d+(?:[_.]\\d+)*)", "name": "tvOS", "version": "$1"}, {"regex": "iOS(?:; |/)((?:9|1[0-8])\\.\\d+(?:[_.]\\d+)*) (?:model/)?AppleTV", "name": "tvOS", "version": "$1"}, {"regex": "tvOS[ /]?((?:9|1[0-8])\\.\\d+(?:[_.]\\d+)*);?", "name": "tvOS", "version": "$1"}, {"regex": "AppleTV(?:/?(\\d+[.\\d]+))?", "name": "tvOS", "version": "$1"}, {"regex": "(?:Watch1,[12]/|Watch OS,|watchOS[ /]?)(\\d+[.\\d]*)?", "name": "watchOS", "version": "$1"}, {"regex": "Apple Watch(?!;)", "name": "watchOS", "version": ""}, {"regex": "FBMD/iPad;.*FBSV/ ?(1[3-8]|26).(\\d+[.\\d]*);", "name": "iPadOS", "version": "$1.$2"}, {"regex": "iPad(?:OS)?[ /](1[3-8]|26)\\.(\\d+[.\\d]*)", "name": "iPadOS", "version": "$1.$2"}, {"regex": "^iPad(?:\\d+[\\,\\d]*)/(1[3-8]|26)\\.(\\d+[.\\d]*)", "name": "iPadOS", "version": "$1.$2"}, {"regex": "iPad(?:; (?:iOS|iPadOS|iPhone OS)|.+CPU (?:iPad |iPhone )?OS) ((1[3-8]|26)+(?:[_.]\\d+)*)", "name": "iPadOS", "version": "$1"}, {"regex": "iOS/(1[3-8]|26)\\.(\\d+[.\\d]*).+Apple/iPad", "name": "iPadOS", "version": "$1.$2"}, {"regex": "iPhone OS,(1[3-8]|26)\\.(\\d+[.\\d]*).+iPad", "name": "iPadOS", "version": "$1.$2"}, {"regex": "iphoneos(1[3-8]|26)\\.(\\d+[.\\d]*); iPad", "name": "iPadOS", "version": "$1.$2"}, {"regex": "Pinterest for iOS/.*iPad.*; (\\d(?:[\\d.]*))[)]$", "name": "iPadOS", "version": "$1"}, {"regex": "iPad/([89]|1[012])\\.(\\d+[.\\d]*)", "name": "iOS", "version": "$1.$2"}, {"regex": "^(?:iPad|iPhone)(?:\\d+[\\,\\d]*)[/_](\\d+[.\\d]+)", "name": "iOS", "version": "$1"}, {"regex": "iphoneos(1[3-8]|26)\\.(\\d+[.\\d]*); iPhone", "name": "iOS", "version": "$1.$2"}, {"regex": "Pinterest for iOS/.*iPhone.*; (\\d(?:[\\d.]*))[)]$", "name": "iOS", "version": "$1"}, {"regex": "iOS (\\d+[.\\d]+)\\)", "name": "iOS", "version": "$1"}, {"regex": "iPhone OS ([0-9]{1})([0-9]{1})([0-9]{1})", "name": "iOS", "version": "$1.$2.$3"}, {"regex": "(?:CPU OS|iPh(?:one)?[ _]OS|iPhone.+ OS|PodMN.+iPhone|iOS)[ _/](\\d+(?:[_.]\\d+)*)", "name": "iOS", "version": "$1"}, {"regex": "(?:iPhone ?OS|iOS(?: Version)?)(?:/|; |,)(\\d+[.\\d]+)", "name": "iOS", "version": "$1"}, {"regex": "^(?!com\\.apple\\.Safari\\.SearchHelper|Safari|NetworkingExtension).*(?:CFNetwork|Mana)/.+ Darwin/(\\d+[.\\d]+)(?!.*(?:x86_64|i386|PowerMac|Power%20Macintosh))", "name": "iOS", "versions": [{"regex": "Darwin/25\\.0\\.0", "version": "26"}, {"regex": "Darwin/24\\.5\\.0", "version": "18.5"}, {"regex": "Darwin/24\\.4\\.0", "version": "18.4"}, {"regex": "Darwin/24\\.3\\.0", "version": "18.3"}, {"regex": "Darwin/24\\.2\\.0", "version": "18.2"}, {"regex": "Darwin/24\\.1\\.0", "version": "18.1"}, {"regex": "Darwin/24\\.0\\.0", "version": "18.0"}, {"regex": "Darwin/23\\.6\\.0", "version": "17.6"}, {"regex": "Darwin/23\\.5\\.0", "version": "17.5"}, {"regex": "Darwin/23\\.4\\.0", "version": "17.4"}, {"regex": "Darwin/23\\.3\\.0", "version": "17.3"}, {"regex": "Darwin/23\\.2\\.0", "version": "17.2"}, {"regex": "Darwin/23\\.1\\.0", "version": "17.1"}, {"regex": "Darwin/23\\.0\\.0", "version": "17.0"}, {"regex": "Darwin/22\\.6\\.0", "version": "16.6"}, {"regex": "Darwin/22\\.5\\.0", "version": "16.5"}, {"regex": "Darwin/22\\.4\\.0", "version": "16.4"}, {"regex": "Darwin/22\\.3\\.0", "version": "16.3"}, {"regex": "Darwin/22\\.2\\.0", "version": "16.2"}, {"regex": "Darwin/22\\.1\\.0", "version": "16.1"}, {"regex": "Darwin/22\\.0\\.0", "version": "16.0"}, {"regex": "Darwin/21\\.6\\.0", "version": "15.6"}, {"regex": "Darwin/21\\.5\\.0", "version": "15.5"}, {"regex": "Darwin/21\\.4\\.0", "version": "15.4"}, {"regex": "Darwin/21\\.3\\.0", "version": "15.3"}, {"regex": "Darwin/21\\.2\\.0", "version": "15.2"}, {"regex": "Darwin/21\\.1\\.0", "version": "15.1"}, {"regex": "Darwin/21\\.0\\.0", "version": "15.0"}, {"regex": "Darwin/20\\.6\\.0", "version": "14.7"}, {"regex": "Darwin/20\\.5\\.0", "version": "14.6"}, {"regex": "Darwin/20\\.4\\.0", "version": "14.5"}, {"regex": "Darwin/20\\.3\\.0", "version": "14.4"}, {"regex": "Darwin/20\\.2\\.0", "version": "14.3"}, {"regex": "Darwin/20\\.1\\.0", "version": "14.2"}, {"regex": "Darwin/20\\.0\\.0", "version": "14.0"}, {"regex": "Darwin/19\\.6\\.0", "version": "13.6"}, {"regex": "Darwin/19\\.5\\.0", "version": "13.5"}, {"regex": "Darwin/19\\.4\\.0", "version": "13.4"}, {"regex": "Darwin/19\\.3\\.0", "version": "13.3.1"}, {"regex": "Darwin/19\\.2\\.0", "version": "13.3"}, {"regex": "Darwin/19\\.0\\.0", "version": "13.0"}, {"regex": "Darwin/18\\.7\\.0", "version": "12.4"}, {"regex": "Darwin/18\\.6\\.0", "version": "12.3"}, {"regex": "Darwin/18\\.5\\.0", "version": "12.2"}, {"regex": "Darwin/18\\.2\\.0", "version": "12.1"}, {"regex": "Darwin/18\\.0\\.0", "version": "12.0"}, {"regex": "Darwin/17\\.7\\.0", "version": "11.4.1"}, {"regex": "Darwin/17\\.6\\.0", "version": "11.4"}, {"regex": "Darwin/17\\.5\\.0", "version": "11.3"}, {"regex": "Darwin/17\\.4\\.0", "version": "11.2.6"}, {"regex": "Darwin/17\\.3\\.0", "version": "11.2"}, {"regex": "CFNetwork/889", "version": "11.1"}, {"regex": "CFNetwork/887", "version": "11.0"}, {"regex": "CFNetwork/811", "version": "10.3"}, {"regex": "CFNetwork/808\\.3", "version": "10.3"}, {"regex": "CFNetwork/808\\.2", "version": "10.2"}, {"regex": "CFNetwork/808\\.1", "version": "10.1"}, {"regex": "CFNetwork/808\\.0", "version": "10.0"}, {"regex": "CFNetwork/808", "version": "10"}, {"regex": "CFNetwork/758\\.5\\.3", "version": "9.3.5"}, {"regex": "CFNetwork/758\\.4\\.3", "version": "9.3.2"}, {"regex": "CFNetwork/758\\.3\\.15", "version": "9.3"}, {"regex": "CFNetwork/758\\.2\\.[78]", "version": "9.2"}, {"regex": "CFNetwork/758\\.1\\.6", "version": "9.1"}, {"regex": "CFNetwork/758\\.0\\.2", "version": "9.0"}, {"regex": "CFNetwork/711\\.5\\.6", "version": "8.4.1"}, {"regex": "CFNetwork/711\\.4\\.6", "version": "8.4"}, {"regex": "CFNetwork/711\\.3\\.18", "version": "8.3"}, {"regex": "CFNetwork/711\\.2\\.23", "version": "8.2"}, {"regex": "CFNetwork/711\\.1\\.1[26]", "version": "8.1"}, {"regex": "CFNetwork/711\\.0\\.6", "version": "8.0"}, {"regex": "CFNetwork/672\\.1", "version": "7.1"}, {"regex": "CFNetwork/672\\.0", "version": "7.0"}, {"regex": "CFNetwork/609\\.1", "version": "6.1"}, {"regex": "CFNetwork/60[29]", "version": "6.0"}, {"regex": "CFNetwork/548\\.1", "version": "5.1"}, {"regex": "CFNetwork/548\\.0", "version": "5.0"}, {"regex": "CFNetwork/485\\.13", "version": "4.3"}, {"regex": "CFNetwork/485\\.12", "version": "4.2"}, {"regex": "CFNetwork/485\\.10", "version": "4.1"}, {"regex": "CFNetwork/485\\.2", "version": "4.0"}, {"regex": "CFNetwork/467\\.12", "version": "3.2"}, {"regex": "CFNetwork/459", "version": "3.1"}]}, {"regex": "(?:iPhone|iPod_touch)/(\\d+[.\\d]*)(?: hw)?/", "name": "iOS", "version": "$1"}, {"regex": "iOS(\\d+\\.\\d+\\.\\d+)", "name": "iOS", "version": "$1"}, {"regex": "iOS(\\d+)\\.(\\d+)0", "name": "iOS", "version": "$1.$2"}, {"regex": "FBMD/iPhone;.*FBSV/ ?(\\d+[.\\d]+);", "name": "iOS", "version": "$1"}, {"regex": "(?:FBIOS|Messenger(?:Lite)?ForiOS).*FBSV/ ?(\\d+[.\\d]*);", "name": "iOS", "version": "$1"}, {"regex": "iPhone OS,([\\d.]+).+(?:iPhone|iPod)", "name": "iOS", "version": "$1"}, {"regex": "iPad.+; (\\d+[.\\d]+);", "name": "iOS", "version": "$1"}, {"regex": "iPhone.+; Version (\\d+[.\\d]+)", "name": "iOS", "version": "$1"}, {"regex": "OS=iOS;OSVer=(\\d+[.\\d]+);", "name": "iOS", "version": "$1"}, {"regex": "os=Apple-iOS.+osversion=(\\d+[.\\d]+)/", "name": "iOS", "version": "$1"}, {"regex": "(?:Apple-)?(?<!like )(?:iPhone|iPad|iPod)(?:.*Mac OS X.*Version/(\\d+\\.\\d+)|; Opera)?", "name": "iOS", "version": "$1"}, {"regex": "dv\\(iPh.+ov\\((\\d+(?:[_.]\\d+)*)\\);", "name": "iOS", "version": "$1"}, {"regex": "(?:Podcasts/(?:[\\d.]+)|Instacast(?:HD)?/(?:\\d\\.[\\d\\.abc]+)|Pocket Casts, iOS|\\(iOS\\)|iOS; Opera|Overcast|Castro|Podcat|iCatcher|RSSRadio/|MobileSafari/|WhatsApp/.*i$)(?!.*x86_64)", "name": "iOS", "version": ""}, {"regex": "iTunes-(AppleTV|iPod|iPad|iPhone)/(?:[\\d.]+)", "name": "iOS", "version": ""}, {"regex": "iOS[ /]Version ([\\d.]+)", "name": "iOS", "version": "$1"}, {"regex": "Sonos/.+\\(ICRU_", "name": "iOS", "version": ""}, {"regex": "CaptiveNetworkSupport|AirPlay|.*[ \\.\\-/]iOS(?!@)", "name": "iOS", "version": ""}, {"regex": "(?:CFNetwork|Mana|StudioDisplay)/.+<PERSON>(?:/|; )(?:[\\d.]+).+(?:x86_64|i386|Power%20Macintosh)|(?:x86_64-apple-)?darwin(?:[\\d.]+)|C?Python.*Darwin|PowerMac|com\\.apple\\.Safari\\.SearchHelper|^(?:NetworkingExtension|Safari)", "name": "<PERSON>", "versions": [{"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?25\\.0\\.0", "version": "26"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?24\\.5\\.0", "version": "15.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?24\\.4\\.0", "version": "15.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?24\\.3\\.0", "version": "15.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?24\\.2\\.0", "version": "15.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?24\\.1\\.0", "version": "15.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?24\\.0\\.0", "version": "15.0"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.6\\.0", "version": "14.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.5\\.0", "version": "14.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.4\\.0", "version": "14.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.3\\.0", "version": "14.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.2\\.0", "version": "14.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.1\\.0", "version": "14.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?23\\.0\\.0", "version": "14.0"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.6\\.0", "version": "13.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.5\\.0", "version": "13.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.4\\.0", "version": "13.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.3\\.0", "version": "13.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.2\\.0", "version": "13.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.1\\.0", "version": "13.0.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?22\\.0\\.0", "version": "13.0"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.6\\.0", "version": "12.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.5\\.0", "version": "12.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.4\\.0", "version": "12.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.3\\.0", "version": "12.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.2\\.0", "version": "12.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.1\\.0", "version": "12.0.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?21\\.0\\.0", "version": "12.0"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?20\\.6\\.0", "version": "11.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?20\\.5\\.0", "version": "11.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?20\\.4\\.0", "version": "11.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?20\\.3\\.0", "version": "11.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?20\\.2\\.0.*", "version": "11.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?20\\.[01]\\.0", "version": "11.0"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?19\\.6\\.0", "version": "10.15.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?19\\.5\\.0", "version": "10.15.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?19\\.4\\.0", "version": "10.15.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?19\\.3\\.0", "version": "10.15.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?19\\.2\\.0", "version": "10.15.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?19\\.0\\.0", "version": "10.15"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?18\\.7\\.0", "version": "10.14"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?18\\.6\\.0", "version": "10.14.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?18\\.5\\.0", "version": "10.14.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?18\\.2\\.0", "version": "10.14.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?18\\.0\\.0", "version": "10.14"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.7\\.0", "version": "10.13.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.6\\.0", "version": "10.13.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.5\\.0", "version": "10.13.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.4\\.0", "version": "10.13.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.3\\.0", "version": "10.13.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.2\\.0", "version": "10.13.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?17\\.0\\.0", "version": "10.13"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.7\\.0", "version": "10.12.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.6\\.0", "version": "10.12.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.5\\.0", "version": "10.12.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.4\\.0", "version": "10.12.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.3\\.0", "version": "10.12.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.2\\.0", "version": "10.12.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.1\\.0", "version": "10.12.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?16\\.0\\.0", "version": "10.12"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?15\\.6\\.0", "version": "10.11.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?15\\.5\\.0", "version": "10.11.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?15\\.4\\.0", "version": "10.11.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?15\\.3\\.0", "version": "10.11.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?15\\.2\\.0", "version": "10.11.2"}, {"regex": "CFNetwork/760", "version": "10.11"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?14\\.5\\.0", "version": "10.10.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?14\\.4\\.0", "version": "10.10.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?14\\.3\\.0", "version": "10.10.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?14\\.1\\.0", "version": "10.10.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?14\\.0\\.0", "version": "10.10"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?13\\.4\\.0", "version": "10.9.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?13\\.3\\.0", "version": "10.9.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?13\\.2\\.0", "version": "10.9.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?13\\.1\\.0", "version": "10.9.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?13\\.0\\.0", "version": "10.9.0"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?12\\.6\\.0", "version": "10.8.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?12\\.5\\.0", "version": "10.8.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?12\\.4\\.0", "version": "10.8.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?12\\.3\\.0", "version": "10.8.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?12\\.2\\.0", "version": "10.8.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?12\\.1\\.0", "version": "10.8.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?11\\.5\\.0", "version": "10.7.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?11\\.4\\.2", "version": "10.7.5"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?11\\.4\\.0", "version": "10.7.4"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?11\\.3\\.0", "version": "10.7.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?11\\.2\\.0", "version": "10.7.2"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?11\\.1\\.0", "version": "10.7.1"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?10\\.8\\.0", "version": "10.6.8"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?10\\.7\\.[34]", "version": "10.6.7"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?10\\.3\\.0", "version": "10.6.3"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?10\\.0\\.0", "version": "10.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?9\\.8\\.0", "version": "10.5.8"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?9\\.7\\.1", "version": "10.5.7"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?9\\.6\\.2", "version": "10.5.6"}, {"regex": "(?:x86_64-apple-)?<PERSON>(?:/|; )?9\\.5\\.[05]", "version": "10.5.5"}]}, {"regex": "Macintosh;Mac OS X \\((\\d+[.\\d]+)\\);", "name": "<PERSON>", "version": "$1"}, {"regex": "Mac[ +]OS[ +]?X(?:[ /,](?:Version )?(\\d+(?:[_.]\\d+)+))?", "name": "<PERSON>", "version": "$1"}, {"regex": "Mac (?:OS/)?(\\d+(?:[_.]\\d+)+)", "name": "<PERSON>", "version": "$1"}, {"regex": "(?:macOS(?:\\(Catalyst\\))?[ /,]|Mac(?:os)?-)(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1"}, {"regex": "Macintosh; OS X (\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1"}, {"regex": "OSX/(\\d+[.\\d]+)", "name": "<PERSON>", "version": "$1"}, {"regex": "Darwin|Macintosh|Mac[ _]PowerPC|PPC|iMac|MacBook|.*macOS|AppleExchangeWebServices|com\\.apple\\.trustd|Sonos/.+\\(MDCR_|WhatsApp/.*N$", "name": "<PERSON>", "version": ""}, {"regex": "Fuchsia", "name": "Fuchsia", "version": ""}, {"regex": "(?:BB10;.+Version|Black[Bb]erry[0-9a-z]+|Black[Bb]erry.+Version)/(\\d+[.\\d]+)", "name": "BlackBerry OS", "version": "$1"}, {"regex": "RIM Tablet OS (\\d+[.\\d]+)", "name": "BlackBerry Tablet OS", "version": "$1"}, {"regex": "RIM Tablet OS|QNX|Play[Bb]ook", "name": "BlackBerry Tablet OS", "version": ""}, {"regex": "BlackBerry", "name": "BlackBerry OS", "version": ""}, {"regex": "bPod", "name": "BlackBerry OS", "version": ""}, {"regex": "BeOS[ +]?(\\d[.\\d]*)?", "name": "BeOS", "version": "$1"}, {"regex": "Symbian/3.+NokiaBrowser/7\\.3", "name": "Symbian^3", "version": "<PERSON>"}, {"regex": "Symbian/3.+NokiaBrowser/7\\.4", "name": "Symbian^3", "version": "<PERSON>"}, {"regex": "Symbian/3", "name": "Symbian^3", "version": ""}, {"regex": "(?:Series ?60|SymbOS|S60)(?:[ /]?(\\d+[.\\d]+|V\\d+))?", "name": "Symbian OS Series 60", "version": "$1"}, {"regex": "Series40", "name": "Symbian OS Series 40", "version": ""}, {"regex": "SymbianOS/(\\d+[.\\d]+)", "name": "Symbian OS", "version": "$1"}, {"regex": "MeeGo|WeTab", "name": "MeeGo", "version": ""}, {"regex": "Symbian(?: OS)?|SymbOS", "name": "Symbian OS", "version": ""}, {"regex": "Nokia", "name": "Symbian", "version": ""}, {"regex": "(?:Mobile|Tablet);.+Firefox/\\d+\\.\\d+", "name": "Firefox OS", "version": ""}, {"regex": "RISC OS(?:-NC)?(?:[ /](\\d+[.\\d]+))?", "name": "RISC OS", "version": "$1"}, {"regex": "Inferno(?:[ /](\\d+[.\\d]+))?", "name": "Inferno", "version": "$1"}, {"regex": "bada(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "REX; U;", "name": "REX", "version": ""}, {"regex": "(?:Brew(?!-Applet)(?: MP)?|BMP)(?:[ /](\\d+[.\\d]+))?", "name": "<PERSON><PERSON>", "version": "$1"}, {"regex": "GoogleTV(?:[ /](\\d+[.\\d]+))?", "name": "Google TV", "version": "$1"}, {"regex": "WebTV/(\\d+[.\\d]+)", "name": "WebTV", "version": "$1"}, {"regex": "(?:SunOS|Solaris)(?:[/ ](\\d+[.\\d]+))?", "name": "Solar<PERSON>", "version": "$1"}, {"regex": "AIX(?:[/ ]?(\\d+[.\\d]+))?", "name": "AIX", "version": "$1"}, {"regex": "HP-UX(?:[/ ]?(\\d+[.\\d]+))?", "name": "HP-UX", "version": "$1"}, {"regex": "ElectroBSD(?:[/ ]?(\\d+[.\\d]+))?", "name": "ElectroBSD", "version": "$1"}, {"regex": "FreeBSD(?:[/ ]?(\\d+[.\\d]+))?", "name": "FreeBSD", "version": "$1"}, {"regex": "NetBSD(?:[/ ]?(\\d+[.\\d]+))?", "name": "NetBSD", "version": "$1"}, {"regex": "OpenBSD(?:[/ ]?(\\d+[.\\d]+))?", "name": "OpenBSD", "version": "$1"}, {"regex": "DragonFly(?:[/ ]?(\\d+[.\\d]+))?", "name": "DragonFly", "version": "$1"}, {"regex": "Syllable(?:[/ ]?(\\d+[.\\d]+))?", "name": "Syllable", "version": "$1"}, {"regex": "IRIX(?:;64)?(?:[/ ]?(\\d+[.\\d]+))", "name": "IRIX", "version": "$1"}, {"regex": "OSF1(?:[/ ]?v?(\\d+[.\\d]+))?", "name": "OSF1", "version": "$1"}, {"regex": "Nintendo (Wii|Switch)", "name": "Nintendo", "version": "$1"}, {"regex": "PlayStation.+; Linux (\\d+[.\\d]+)", "name": "PlayStation", "version": "$1"}, {"regex": "PlayStation ?(\\d)(?:[/ ](?:Pro )?(\\d+[.\\d]+))?", "name": "PlayStation", "version": "$1.$2"}, {"regex": "Xbox|KIN\\.(?:One|Two)", "name": "Xbox", "version": "360"}, {"regex": "Nitro|Nintendo ([3]?DS[i]?)", "name": "Nintendo Mobile", "version": "$1"}, {"regex": "PlayStation ((?:Portable|Vita))", "name": "PlayStation Portable", "version": "$1"}, {"regex": "OS/2; Warp (\\d+[.\\d]+)", "name": "OS/2", "version": "$1"}, {"regex": "OS/2", "name": "OS/2", "version": ""}, {"regex": "Linux/(\\d+[.\\d]+)", "name": "GNU/Linux", "version": "$1"}, {"regex": "Linux[^a-z]|Cinnamon/(?:\\d+[.\\d]+)|.+(?:pc|unknown)-linux-gnu|X11", "name": "GNU/Linux", "version": ""}, {"regex": "Java ME|(J2ME|Profile)/MIDP|MIDP-(?:\\d+[.\\d]+)/CLDC|Configuration/CLDC|Java; U; MIDP|MMP/\\d\\.\\d", "name": "Java ME", "version": ""}]