declare const _exports: {
    "360": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "8848": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ace": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tunisie Telecom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SFR": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Adronix": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Cherry Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "HTC": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Microsoft": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Nokia": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "CnM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "RIM": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Ghia": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Palm": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Compaq": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HP": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "TiPhone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TiVo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Apple": {
        "regex": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "MicroMax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "3Q": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "4Good": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Acteck": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Alba": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "All Star": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AllCall": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bravis": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Brigmton": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
            "device": string;
        }[];
    };
    "Acer": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Advan": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Advance": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "AGM": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Airis": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Airness": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Airpha": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Aiwa": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Akai": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Arian Space": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Alcatel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Allview": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Allwinner": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "alpsmart": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "altron": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "AMGOO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Amoi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Ainol": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Archos": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Axxion": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MEO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ooredoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MEU": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Arnova": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ARRIS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ask": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "ANS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AOC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Assistant": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Ark": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Garmin-Asus": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Asus": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Audiovox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AVH": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Avvio": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "AXEN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Azumi Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Barnes & Noble": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BGH": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Bitel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "GDL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Blu": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Bluegood": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Blackview": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Bluboo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Black Fox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "bogo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Boway": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HMD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Brondi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "VGO TEL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Vivo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Vinsoc": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Bird": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Becker": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Beeline": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Beetel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BenQ-Siemens": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BenQ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Blaupunkt": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Bmobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "bq": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Bush": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "CAGI": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Capitel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Captiva": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Casio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VOCAL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Casper": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Cat": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Carrefour": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Celcus": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Celkon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Cellution": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Changhong": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "China Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CHCNAV": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Comio": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "CommScope": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Compal": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Artel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "ComTrade Tesla": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "ConCorde": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Condor": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Coolpad": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Clarmin": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CORN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Cosmote": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Cricket": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Crius Mea": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Crosscall": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Walker": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Crown": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "AllDocube": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "allente": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Xsmart": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MyTab": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Cube": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Meta": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "CUBOT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Clout": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Concord": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Crescent": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Crestron": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Cwowdefu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
            "device": string;
        }[];
    };
    "Cyrus": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Datang": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Datsun": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Danew": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Dazen": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Denver": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Dell": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Desay": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DEXP": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "DbPhone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dbtel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DGTEC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
            "device": string;
        }[];
    };
    "Dialog": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dicam": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Digi": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Digicel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Digidragon": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Digihome": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Digiland": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Digit4G": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "COMPUMAX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Digma": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "DoCoMo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bqeel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Doogee": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Doov": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dopod": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Doppio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Doro": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dune HD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DNS": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "E-Boda": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Easypix": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "EBEST": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Horizont": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Echo Mobiles": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ECS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Edenwood": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EE": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "EGL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "EKT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Eks Mobility": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Elephone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Element": {
        "regex": string;
        "model": string;
        "device": string;
    };
    "Elenberg": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Ericy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Essential": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "E-tel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Evercoss": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Eurostar": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "ETOE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Eton": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Essentielb": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Facebook": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gateway": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ricoh": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rikomagic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Rinno": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Riviera": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Senseit": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SUNWIND": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "ZoomSmart": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Smartab": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sony": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "brand": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "brand": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "brand"?: undefined;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
            "brand"?: undefined;
        })[];
    };
    "Ergo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Ericsson": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Energizer": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "eTouch": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Etuline": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Storex": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Everest": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Evertek": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Evolio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Evolveo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Evoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Leotec": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "EVPAD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "evvoli": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EvroMedia": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EKO": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Extrem": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jumper": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "JPay": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ezze": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ezio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Forstar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "InFocus": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Foxconn": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fobem": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fondi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Fairphone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Famoco": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FiGO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FNB": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fuego": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SEG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fujitsu": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "G-TiDE": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Gemini": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Geotel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gigabyte": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gigaset": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Ginzzu": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Gionee": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Grape": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vizio": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Timovi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TIMvision": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GOCLEVER": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "GOtv": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GoMobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Explay": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "General Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Gol Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Goly": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Google": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Gradiente": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Graetz": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Grünberg": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Grundig": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Gtel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "H133": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "H96": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hafury": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HEC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Haier": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "HannSpree": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hasee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Toshiba": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "AWOX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UGINE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sunny": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Qilive": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "JVC": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Loewe": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "LongTV": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Majestic": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Maxwell": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Schneider": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sencor": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "TAUBE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hi-Level": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HKC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HKPro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hoffmann": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Homatics": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Huagan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Stream": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Homtom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hopeland": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hosin": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hoozo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Mightier": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Minix": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Huavi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Turkcell": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "China Telecom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "U-Magic": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hi Nova": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TD Tech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Huawei": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Hyundai": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "i-Joy": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "iBall": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "iRola": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iRulu": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Irbis": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iBerry": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "iHome Life": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iHunt": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "IconBIT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "LYF": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lumus": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "LW": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "M4tel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "IMO Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "iLA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iNew": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iPro": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Infinix": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "InnJoo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Inkti": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Innos": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Innostream": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Insignia": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "INSYS": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "INQ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "AFFIX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Aquarius": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Vsun": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Turbo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Turbo-X": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Intex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "i-mate": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "i-mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "iKoMo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iOcean": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iView": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Impression": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "iLife": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iTel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "iZotron": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "JAY-Tech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jiayu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jolla": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Juniper Systems": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Just5": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SuperTV": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kalley": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Kaan": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kazam": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kazuna": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Keneksi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kempler & Strauss": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Kiano": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "kidiby": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kingbox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kingstar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kingsun": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kocaso": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kogan": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Komu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Koobee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kumai": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "KT-Tech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KDDI": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "PIXPRO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kodak": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "KOPO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Koridy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KRONO": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "K-Touch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Diva": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kyocera": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Mymaga": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Krüger&Matz": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "LAIQ": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AIS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Beko": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Benco": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Laser": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lava": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "SKG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iVA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Landvo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Land Rover": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iOutdoor": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lanin": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lanix": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Lark": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "LCT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Le Pan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Leagoo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Ledstar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Leelbox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "LeEco": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lephone": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Lemco": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lenco": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Aligator": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lenovo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Lexand": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Lexibook": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vargo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "LG": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Lifemaxx": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lingbo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Lingwin": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Logic Instrument": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Logicom": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Konka": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Konrow": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "K-Lite": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Karbonn": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Sagem": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Coby Kyros": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Mpman": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Manta Multimedia": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Masstel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Mastertech": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Talius": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Maxwest": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "True Slim": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Maze Speed": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Maze": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mediacom": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Medion": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "MEEG": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fourel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Meitu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Memup": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mecer": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mione": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mio": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Miray": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Mitsubishi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MIXC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mobiola": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Matrix": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Mobicel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iStar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Mobiistar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mobile Kingdom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MSI": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "MLLED": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mobistel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MobiIoT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mecool": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Modecom": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Mode Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mofut": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BrightSign": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Verssed": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Torque": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Motorola": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Motorola Solutions": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Movic": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Movitel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MTN": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Hammer": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rombica": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "MyPhone (PH)": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "myPhone (PL)": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "MyWigo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Myros": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Myria": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "M.T.T.": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Navon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "C5 Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "NOA": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Nobby": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NoviSea": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Novacom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Novey": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NEC": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Neffos": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nextbit": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Newgen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NetBox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Netgear": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gear Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NeuImage": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NextBook": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NGM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nexian": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Noain": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Noblex": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "NOGA": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Nomu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Noontec": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Nordmende": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Opsson": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nomi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "NUU Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "NYX Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nous": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nvidia": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "O+": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "O2": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Funai": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fusion5": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ZH&K": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Odys": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "astro (MY)": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Astro (UA)": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Obi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Okapi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Okapia": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Olax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Olympia": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Onda": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "OnePlus": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Realme": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "OPPO": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Orange": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Oukitel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "OKWU": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Orion": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Ouki": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Overmax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "OX TAB": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Oysters": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "öwn": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Panacom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Panasonic": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "PCBOX": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "PCD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "PCD Argentina": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ArmPhone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Penta": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pentagram": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "TCL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "phoneOne": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Primepad": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pioneer Computers": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Pioneer": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Pixus": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "PULID": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Purism": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Point Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Point of View": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Pomp": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "PPTV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ProScan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "RCA Tablets": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "RCT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Readboy": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Roku": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rokit": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rover": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "RoverPad": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Roadrover": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "RT Project": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Saba": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "S-TELL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Safaricom": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Santin": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Siemens": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Soundmax": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Samsung": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Sanei": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Selfix": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Senwa": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Visual Land": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UnoPhone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sky": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Skyworth": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Smartfren": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Smartisan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "STF Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "STK": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Stonex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SuperBOX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Super General": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SuperSonic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Supra": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Sumvision": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "SunVan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SWISSMOBILITY": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Thomson": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Tanix": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Panavox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pantech": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Mosimosi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Moxee": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Ployer": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Plum": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Polaroid": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "PolyPad": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Polytron": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Positivo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Prestigio": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Sanyo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vankyo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Quest": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "QMobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "COYOTE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Matco Tools": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Maximus": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TOOGO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Quantum": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Quechua": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Quipus": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "R3Di": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ramos": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Volla": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "I-Plus": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Razer": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Sendo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Silent Circle": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ophone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sigma": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Spice": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "SGIN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "F+": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Sharp": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Simply": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Softbank": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sonim": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Star": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Starmobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "ok.": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Anker": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Altus": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Onida": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Amazon Basics": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Amazon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "SONOS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Syco": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Symphony": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Helio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HERO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Syrox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "System76": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Qtek": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Q-Box": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Q-Touch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "G-Touch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "T-Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TB Touch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Teclast": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tecno Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "TechSmart": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TechPad": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Techwood": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Teracube": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tesco": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "teXet": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Telefunken": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Telego": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Telenor": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Telit": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Telly": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Telma": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Telpo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ThL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TIANYU": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tooky": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tolino": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Top House": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Topelotek": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Toplux": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Touchmate": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "PEAQ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TrekStor": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Trevi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "TVC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "U.S. Cellular": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Uhappy": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unimax": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Unowhy": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UTStarcom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ulefone": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "UMIDIGI": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Uniqcell": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Uniscope": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unistrong": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "United Group": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unnecto": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unnion Technologies": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unonu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UTOK": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Vastking": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ViewSonic": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Vitelcom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fengxiang": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fenoti": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VK Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fortis": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FortuneShip": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vernee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vertu": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Veidoo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Venso": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Venturer": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Verizon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Verykool": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Finlux": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hitachi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mitchell & Brown": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Logik": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Logitech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vestel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Videocon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Vodafone": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Surge": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Volt": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vonino": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Vorago": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Voto": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Voxtel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vulcan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ober": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Walton": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "WellcoM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wexler": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Inco": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Inka": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wink": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Smart": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wiko": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wieppo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Weiimi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Weimei": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wileyfox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wolder": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Wolfgang": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wolki": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wonu": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Woo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Woxter": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "X-View": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "POCO": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Zopo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "QIN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Xiaomi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Xion": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Xolo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yarvik": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yes": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yestel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Yezz": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "YU Fly": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Yu": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yuandao": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yusun": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ytone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zonda": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "ZYQ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Vivax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "MLS": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "MMI": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FLYCAT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Fly": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "FinePower": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Freetel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Western Digital": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zeemi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zenek": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nubia": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Rakuten": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ZTE": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Onix": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Zuum": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zen": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zync": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lemhoov": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MStar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MTC": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Megacable": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MegaFon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Inoi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Vertex": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Starway": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Savio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Schok": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Simbans": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MYFON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "X-TIGI": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Hot Pepper": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Xiaolajiao": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ritmix": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Ross&Moor": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "R-TV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NEXBOX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "True": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Yandex": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "FORME": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Shuttle": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BDF": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HiHi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Boost": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Highscreen": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CVTE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Globex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Atom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Qumo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Qubo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Umax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Mann": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unihertz": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Chuwi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Bezkam": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NG Optics": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KATV1": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ghong": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ghost": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Xoro": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Vinga": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Viumee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nuvo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sunvell": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ugoos": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yxtel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PROFiLO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Proline": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Yota": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mystery": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Ritzviva": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DeWalt": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Runbo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "NewsMy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Newsday": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Daewoo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Vesta": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Spectrum": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kivi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DiverMax": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Divisat": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ArtLine": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DIXON": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Kanji": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Kaiomy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "National": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Shift Phones": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "RTK": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "RugGear": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "RuggeTech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rhino": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ruggex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Maxcom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Luna": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Luxor": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Protruly": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "JFone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Uhans": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Soyes": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zidoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Zfiner": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "EKINOX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iGet": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Phicomm": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Huadoo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gome": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Voyo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ryte": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Vontar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vorke": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wigor": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NEXON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ONN": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "EXO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Q.Bell": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Datawind": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Droidlogic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Droxio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "3GO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Goophone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "PocketBook": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bitmore": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bittium": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kuliao": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sugar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Subor": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Xshitou": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "JKL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gree": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vodacom": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "VKworld": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mito": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "TWM": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TWZ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Oale": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mobo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Macoox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Prixton": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Mafe": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Magnus": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Newland": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Orbic": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "RedLine": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "RED": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "RED-X": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Contixo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "E-Ceros": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "How": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "multibox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Multilaser": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Positivo BGH": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Maxtron": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Philco": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "SEMP TCL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Newman": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Swipe": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Owwo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TD Systems": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ravoz": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AT&T": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "BIHEE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Enot": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kooper": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lesia": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Anry": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tinai": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Winds": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Asano": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Phonemax": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vipro": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Virzo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "OzoneHD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kzen": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Melrose": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Crony": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "T96": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TADAAM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Takara": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tronsmart": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Amigoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lumigon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NEKO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Neomi": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Eltex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Senkatel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bobarry": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wortmann": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BB Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "2E": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Billion": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Telkom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tele2": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ALDI NORD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ALDI SÜD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Alfawise": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Klipad": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "SQOOL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TurboPad": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TurboKids": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Türk Telekom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FRESH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Jinga": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Twoe": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iTruck": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Torex": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Doffler": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Oyyu": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Primux": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iMars": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Logic": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Cloud": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Cloudpad": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Cloudfone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Völfen": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Vsmart": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MiXzo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Openbox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ziox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iDroid": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Aoson": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FireFly Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FISE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "I KALL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "i-Cherry": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "NextTab": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Time2": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sunstech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hotwav": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Tetratab": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dolamee": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Invin": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Doopro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "IQM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nos": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Accent": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Avenzo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Beelink": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MAXVI": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "MDC Store": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MDTV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "meanIT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Kenxinda": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "X-BO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "X88": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "X96": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "X96Q": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Xgody": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Alcor": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iBrit": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ovvi": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fero": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ruio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Transpeed": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VVETIME": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "A1": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Swisstone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "P-UP": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Pacific Research Alliance": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Paladin": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iVooMi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pixelphone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Pixela": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Iris": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Fonos": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Xtouch": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Conquest": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zatec": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Selecline": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Selenga": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Starlight": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Geo Phone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "GEOFOX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tinmo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Verico": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TTEC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TTfone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "eSTAR": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Hometech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ExMobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Exmart": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hipstreet": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hiremco": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Navitech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NorthTech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "UNIWA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ASSE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Andowl": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Silelis": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Bluedot": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Planet Computers": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Play Now": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Reach": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Realix": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MobiWire": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hezire": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hurricane": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iSWAG": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Reeder": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "ELARI": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Epik One": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Altice": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kult": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Beyond": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BioRugged": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VAIO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Spark": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sparx": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SPC": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Winmax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Zebra": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Thuraya": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Bkav": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Brandt": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Bigben": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SMARTEC": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Asanzo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Artizlee": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Mint": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mintt": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Premio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Trifone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bluewave": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Siragon": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Siswoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Poppox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "POPTEL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zentality": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ONVO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Black Bear": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Black Box": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OpelMobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NuAns": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Koolnee": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sansui": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sico": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Smadl": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EXCEED": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tymes": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Solone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FarEasTone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Smailo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Smart Kassel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Smart Tech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Colors": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Honeywell": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Electroneum": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "High Q": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Pluzz": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "IKU Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "IKI Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CG Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "IUNI": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ordissimo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Necnot": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Einstein": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AfriOne": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Connex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Connectce": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ClearPHONE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Clementoni": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Cell-C": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "VC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Cellacom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CellAllure": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EagleSoar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Eagle": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Invens": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Chico Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Claresta": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Camfone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bell": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bellphone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gini": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "MIVO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jesy": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Teknosa": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hyve": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EWIS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Leader Phone": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Cavion": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Canaima": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Canal+": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "GLX": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Seeken": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Jivi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NOVO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Winnovo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "WE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Trio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Seuic": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CENTEK": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Centric": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kata": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Hardkernel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wiseasy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Shtrikh-M": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BBK": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "4ife": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Westpoint": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wintouch": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Aiuto": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Seatel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ditecma": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Datamini": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "3GNET": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yoka TV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Formovie": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Formuler": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Malata": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MBOX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "IT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Navcity": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jeka": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jiake": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iQ&T": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mantra": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Datalogic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "mipo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Naomi Phone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Navitel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Linnex": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SuperTab": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Suzuki": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NEVIR": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Master-G": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "EUROLUX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TORNADO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Philips": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "INCAR": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FaRao Pro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Nabi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "StrawBerry": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "STRONG": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gresso": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pendoo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "PiPO": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Engel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Genesis": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Infomir": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "InfoKit": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Blloc": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Huskee": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "SOLE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SOLO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OINOM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Singtech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TEENO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "v-mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OASYS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Aspera": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vision Touch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NOBUX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hitech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "F150": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BilimLand": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Greentel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gretel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kyowon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LT Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ONYX BOOX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AIRON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "5IVE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GFive": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "KINGZONE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "M-Tech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Fantec": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AURIS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "YUHO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "F2 Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zaith": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Zealot": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Spectralink": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vega": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Bleck": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "BS Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "RoyQueen": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Remdun": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Revo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Axioo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Pico": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "STG Telecom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SMT Telecom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rivo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Four Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "7 Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UZ Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KREZ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "KRIP": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kurio": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Inch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UTime": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "L-Max": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "WIWA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FMT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "mPhone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Cobalt": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "New Balance": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AOYODKG": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Xtratech": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "XGIMI": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VIVIBright": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Raspberry": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tambo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Smooth Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Smotreshka": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gooweel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Elekta": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "YASIN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Blow": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FNF": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iMan": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VAVA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ivvi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dragon Touch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dreamgate": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AOpen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AG Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DIGIFORS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DISH": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VIWA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "M-Horse": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "M-KOPA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ProVision": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sirin Labs": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SK Broadband": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Humax": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Humanware": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Perfeo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CGV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Telia": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Canal Digital": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ZIFRO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SmartBook": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kiowa": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sprint": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Bundy": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Covia": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Elevate": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Stylo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Galaxy Innovations": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Benzo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Vexia": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HiMax": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Packard Bell": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Qnet Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bluebird": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Mara": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BMAX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Geotex": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DING DING": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Trident": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Trimble": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OKSI": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Taiga System": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Glofiish": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Glory Star": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Polar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "PolarLine": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MyGica": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "neoCore": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Novex": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Epson": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Awow": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mitsui": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gocomma": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LEMFO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Urovo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Neon IQ": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Aocos": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Parrot Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Partner Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bubblegum": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Erisson": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Liberton": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Soho Style": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Shivaki": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Meizu": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Athesi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Atmaca Elektronik": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Atvio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "XY Auto": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Alps": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Starwind": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "QTECH": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Prology": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Polestar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Porsche": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Portfolio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NavRoad": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Smarty": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Leff": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DF": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BDQ": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "TuCEL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FEONAL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sunmi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "iData": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iDino": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "IDC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AIDATA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ematic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GLONYX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Zyrex": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Orbita": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TAG Tech": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "DUNNS Mobile": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "inovo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iNOVA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "eNOVA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Amino": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Saiet": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Magicsee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Obabox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iKon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iKonia": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "QLink": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "IRA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Microtech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "G-PLUS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gplus": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Eurocase": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VEON": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "FiGi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "iSafe Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tigers": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Redfox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vinabox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vios": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Beista": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "YOTOPT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ookee": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TOSCIDO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "b2m": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Koslam": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "S2Tel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Massgo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Angelcare": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iPEGTOP": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jedi": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Technopc": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Techstorm": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "OMIX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nexa": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Onkyo": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Royole": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DORLAND": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hamlet": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vorcom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UNNO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VDVD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Autan": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ecom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LOKMAT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Joy": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iWaylink": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "VOGA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Haipai": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OYSIN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Leke": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ROADMAX": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BDsharing": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Pelitt": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Benesse": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Airtel": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "MAXX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TENPLUS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HAOVM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VUCATIMES": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Aoro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "EBEN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "X-AGE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Zonko": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TJC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TJD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HOTREALS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zeeker": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AUX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Neolix": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ATMAN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "EYU": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FOODO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "YEPEN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BROR": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vortex": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Xiaodu": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AXXA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AYYA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Future Mobile Technology": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MBK": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MBI": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "OUJIA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Qware": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Listo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ECON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LNMBBS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SNAMI": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TeachTouch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Facetel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "HLLO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "YUMKEM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TTK-TV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Famous": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "ZIK": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PlusStyle": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "New Bridge": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Intel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Infiniton": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Backcell": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PAPYRE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "I-INN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Emporia": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BLISS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Entity": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Envizen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Denali": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DreamTab": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "DIGICOM": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "InFone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Redway": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iNo Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iReplace": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GEOZON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "actiMirror": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Raylandz": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OTTO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kenbo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VALE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VALEM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ENACOM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Yuno": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Handheld": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "HDC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Victurio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MyMobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Equator": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Imaq": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Imose": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "OneClick": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "D-Tech": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "nJoy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iXTech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "IOTWE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Legend": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BlueSky": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vue Micro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Touch Plus": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Elong Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HexaByte": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HiKing": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hytera": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "PIRANHA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CipherLab": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BrandCode": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lumitel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Mango": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "NTT West": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "JREN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wanmukang": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "Tibuta": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ATOL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Filimo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FILIX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Odotpad": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Orbsmart": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TOX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Topsion": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ceibal": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Wecool": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Weelikeit": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "M3 Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hugerock": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Geanee": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TOPDON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Coopers": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "COOD-E": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lovme": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LOGAN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DASS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Redbean": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ENIE": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dcode": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pritom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unblock Tech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Chainway": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Harper": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Venstar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "CMF": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Nothing": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gazer": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "WizarPos": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Optoma": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zinox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "YUNDOO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Billow": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dany": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "DreamStar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ollee": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dtac": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NINETOLOGY": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NINETEC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NASCO": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "ELE-GATE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Elecson": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NEXT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Next & NextStar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iMuz": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ECOO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dom.ru": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Carbon Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ChiliGreen": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "D-Link": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Scosmos": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "QFX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dinax": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dinalink": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EFT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Youin": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Youwei": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Yooz": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VIDA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iMI": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Marshal": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "V-HOME": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "V-HOPE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BuzzTV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Avaya": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PINE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Duubee": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Blackpcs": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GoldMaster": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Facime": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FPT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iFIT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iNavi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "10moons": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Soda": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Rizzen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Figgers": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DRAGON": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Skyline": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hiberg": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hartens": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AMA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Xcruiser": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Revomovil": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "OSCAL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "UE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hykker": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fluo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Quatro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "White Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ALLINmobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PRIME": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TETC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vizmo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Uniden": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FoxxD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "KZG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iYou": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sewoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Switel": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Farassoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Xwave": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DEALDIG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TecToy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MygPad": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Mobell": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Mobvoi": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Smartex": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Citycall": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Maxfone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fxtec": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Netmak": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VIVIMAGE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "X-Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "JoySurf": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DEYI": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Khadas": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sunmax": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kapsys": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "YELLYOUTH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SSKY": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "XCOM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Winstar": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Waltter": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LPX-G": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VALTECH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sankey": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GIRASOLE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MOVISUN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Samtech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Samtron": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MwalimuPlus": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "OLTO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DSIC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Mascom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vormor": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SOSH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Oangcc": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Everex": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Everis": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Guophone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GVC Pro": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Unitech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "GTMEDIA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Energy Sistem": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "WANSA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "XElectron": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ViBox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BMXC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "V-Gen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Nedaphone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Beafon": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "RelNAT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TeloSystems": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Frunsi": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MIWANG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HeadWolf": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zamolxe": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "REGAL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Great Asia": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PC Smart": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ephone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SYH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "YunSong": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "iRobot": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BRAVE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Attila": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Roam Cat": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DIMO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NuVision": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SANY": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Bookeen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Botech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Pano": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BluSlate": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Lectrus": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LeBest": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BenWee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FFF SmartLife": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Versus": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NGpon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Rupa": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SEBBE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Webfleet": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ROiK": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Kinstone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HIPER": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "TopDevice": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Ctroniq": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Punos": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ANXONIT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vityaz": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VIIPOO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "GOODTEL": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Viper": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HAOQIN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Advantage Air": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "OneLern": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Motiv": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Olkya": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Oking": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Inhon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SKK Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NeuTab": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Kraft": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Zoom": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Linsay": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Disney": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "WAF": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Geant": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Galactic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Zigo": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AEEZO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TPS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Conceptum": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DPA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SkyStream": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Skytech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Supraim": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Bolva": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DIJITSU": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "model": string;
            "device": string;
        })[];
    };
    "Hathway": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "iLepo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SEEWO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TOPSHOWS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KN Mobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Green Orange": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "UOOGOU": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MLAB": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Zeblaze": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "noDROPOUT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Gfone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TechniSat": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Durabook": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DangcapHD": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Panodic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Panoramic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Platoon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Premier": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "A95X": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ibowin": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MultiPOS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BAFF": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "GroBerwert": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BARTEC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "FOSSiBOT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "free": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "model": string;
            "device"?: undefined;
        } | {
            "regex": string;
            "device": string;
            "model": string;
        })[];
    };
    "Nanho": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Vekta": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Accesstyle": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SobieTech": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lime": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VETAS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Canguro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gamma": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Xcell": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Epic": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AMCV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sber": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sveon": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DSDevices": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VOLKANO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "A&K": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BASE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BAUHN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CKK Mobile": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SPURT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Z-Kai": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Arçelik": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TwinMOS": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Getnord": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FreeYond": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "N-one": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Maunfeld": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GTX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SAILF": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Salora": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Dyon": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hisense": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "HiBy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VOLIA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Sagemcom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "XGEM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "MeMobile": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "WeChip": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TriaPlay": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AirTouch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Eyemoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ajib": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "QWATT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HiGrace": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PAGRAER": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DIALN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AUPO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CCIT": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Green Lion": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VANWIN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Globmall": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GlocalMe": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Heimat": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AngelTech": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MAGCH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MAG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "S-Color": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "W&O": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Cuiud": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "C Idea": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Daria": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Wainyok": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Aceline": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "CADENA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Jin Tu": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Whoop": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TECHNOSAT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Lville": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KENSHI": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "KENWOOD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HOLLEBERG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "CEPTER": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SoulLink": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Clovertek": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Padpro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GOLDBERG": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Tuvio": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "WS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FLYCOAY": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KVADRA": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Azeyou": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Azupik": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ExtraLink": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VNPT Technology": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Arival": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VILLAON": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "VIMOQ": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Neoregent": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Retroid Pocket": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Renova": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "GoldStar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ANBERNIC": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ANCEL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Aocwei": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SMUX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AAUW": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Rocket": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "TYD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "NETWIT": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HongTop": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
    "Orange Pi": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "VIKUSHA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Atozee": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Relndoo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Scoole": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "rephone": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BYJU'S": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "BYYBUO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Haixu": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HONKUAHG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SWOFY": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Moondrop": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Vision Technology": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "WOZIFAN": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Newal": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Hanson": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tivax": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Nordfrost": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Ringing Bells": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Atouch": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Metz": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "EGOTEK": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ZZB": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dykemann": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "BYD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ROCH": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DMOAO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hyatta": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PVBox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "XREAL": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SENNA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ATMPC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Jambo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LYOTECH LABS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MESWAO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SERVO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "COLORROOM": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "E-TACHI": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "CPDEVICE": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "XPPen": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Korax": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Ehlel": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Hemilton": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Umiio": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Adreamer": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "RENSO": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "AZOM": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "DUDU AUTO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Temigereev": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Biegedy": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Blackphone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Blackton": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Eudora": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Acepad": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "PLDT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "device": string;
            "model": string;
        }[];
    };
    "MEGAMAX": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "PRISM+": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "SINGER": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Everfine": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Elista": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Horion": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ZIFFLER": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dora": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Weston": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Viendo": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "simfer": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Stilevs": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "TV+": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "G-Guard": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Altibox": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Supermax": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "WildRed": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LUO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "hoco": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "SOWLY": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HAVIT": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "CONSUNG": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "FONTEL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Romsat": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Icone Gold": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KMC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Gazal": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Galatec": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "CUD": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "RAZZ": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "McLaut": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MORTAL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "JUSYEA": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Trecfone": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Spider": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Denka": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Dawlance": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sambox": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nesons": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Cogeco": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Caixun": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "EcoStar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Fision": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "MULTYNET": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "andersson": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "AileTV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "DuoTV": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Indurama": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Magenta": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Nexar": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Qupi": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Sencrom": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "InfinityPro": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "HOMII": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "NOVIS": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "ApoloSign": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "V7": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KGTEL": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "LUNNEN": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "Qiuwoky": {
        "regex": string;
        "device": string;
        "models": {
            "regex": string;
            "model": string;
        }[];
    };
    "ZIOVO": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "XB": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "KTC": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Tricolor": {
        "regex": string;
        "device": string;
        "model": string;
    };
    "Unknown": {
        "regex": string;
        "device": string;
        "models": ({
            "regex": string;
            "device": string;
            "model": string;
        } | {
            "regex": string;
            "model": string;
            "device"?: undefined;
        })[];
    };
};
export = _exports;
