export declare namespace Acer {
    export const regex: string;
    export const device: string;
    export const models: {
        "regex": string;
        "model": string;
    }[];
}
export declare namespace Asus {
    const regex_1: string;
    export { regex_1 as regex };
    const device_1: string;
    export { device_1 as device };
    const models_1: {
        "regex": string;
        "model": string;
    }[];
    export { models_1 as models };
}
export declare namespace Alienware {
    const regex_2: string;
    export { regex_2 as regex };
    const device_2: string;
    export { device_2 as device };
    const models_2: {
        "regex": string;
        "model": string;
    }[];
    export { models_2 as models };
}
export declare namespace Dell {
    const regex_3: string;
    export { regex_3 as regex };
    const device_3: string;
    export { device_3 as device };
    const models_3: {
        "regex": string;
        "model": string;
    }[];
    export { models_3 as models };
}
export declare namespace HP {
    const regex_4: string;
    export { regex_4 as regex };
    const device_4: string;
    export { device_4 as device };
    const models_4: ({
        "regex": string;
        "model": string;
        "device"?: undefined;
    } | {
        "regex": string;
        "model": string;
        "device": string;
    })[];
    export { models_4 as models };
}
export declare namespace Lenovo {
    const regex_5: string;
    export { regex_5 as regex };
    const device_5: string;
    export { device_5 as device };
    const models_5: {
        "regex": string;
        "model": string;
    }[];
    export { models_5 as models };
}
export declare namespace Schneider {
    const regex_6: string;
    export { regex_6 as regex };
    const device_6: string;
    export { device_6 as device };
    export const model: string;
}
export declare namespace Thomson {
    const regex_7: string;
    export { regex_7 as regex };
    const device_7: string;
    export { device_7 as device };
    const model_1: string;
    export { model_1 as model };
}
export declare namespace Toshiba {
    const regex_8: string;
    export { regex_8 as regex };
    const device_8: string;
    export { device_8 as device };
    const models_6: {
        "regex": string;
        "model": string;
    }[];
    export { models_6 as models };
}
