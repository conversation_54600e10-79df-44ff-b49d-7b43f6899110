export declare namespace Apple {
    export const regex: string;
    export const device: string;
    export const models: {
        "regex": string;
        "model": string;
    }[];
}
export declare namespace Cowon {
    const regex_1: string;
    export { regex_1 as regex };
    const device_1: string;
    export { device_1 as device };
    export const model: string;
}
export declare namespace FiiO {
    const regex_2: string;
    export { regex_2 as regex };
    const device_2: string;
    export { device_2 as device };
    const models_1: {
        "regex": string;
        "model": string;
    }[];
    export { models_1 as models };
}
export declare namespace Microsoft {
    const regex_3: string;
    export { regex_3 as regex };
    const device_3: string;
    export { device_3 as device };
    const model_1: string;
    export { model_1 as model };
}
export declare namespace Panasonic {
    const regex_4: string;
    export { regex_4 as regex };
    const device_4: string;
    export { device_4 as device };
    const model_2: string;
    export { model_2 as model };
}
export declare namespace Samsung {
    const regex_5: string;
    export { regex_5 as regex };
    const device_5: string;
    export { device_5 as device };
    const models_2: {
        "regex": string;
        "model": string;
    }[];
    export { models_2 as models };
}
export declare namespace Wizz {
    const regex_6: string;
    export { regex_6 as regex };
    const device_6: string;
    export { device_6 as device };
    const model_3: string;
    export { model_3 as model };
}
export declare namespace Shanling {
    const regex_7: string;
    export { regex_7 as regex };
    const device_7: string;
    export { device_7 as device };
    const models_3: {
        "regex": string;
        "model": string;
    }[];
    export { models_3 as models };
}
export declare namespace Sylvania {
    const regex_8: string;
    export { regex_8 as regex };
    const device_8: string;
    export { device_8 as device };
    const model_4: string;
    export { model_4 as model };
}
export declare namespace KuGou {
    const regex_9: string;
    export { regex_9 as regex };
    const device_9: string;
    export { device_9 as device };
    const model_5: string;
    export { model_5 as model };
}
export declare namespace Surfans {
    const regex_10: string;
    export { regex_10 as regex };
    const device_10: string;
    export { device_10 as device };
    const model_6: string;
    export { model_6 as model };
}
export declare namespace Oilsky {
    const regex_11: string;
    export { regex_11 as regex };
    const device_11: string;
    export { device_11 as device };
    const models_4: {
        "regex": string;
        "model": string;
    }[];
    export { models_4 as models };
}
export declare namespace Diofox {
    const regex_12: string;
    export { regex_12 as regex };
    const device_12: string;
    export { device_12 as device };
    const model_7: string;
    export { model_7 as model };
}
export declare namespace MECHEN {
    const regex_13: string;
    export { regex_13 as regex };
    const device_13: string;
    export { device_13 as device };
    const models_5: {
        "regex": string;
        "model": string;
    }[];
    export { models_5 as models };
}
