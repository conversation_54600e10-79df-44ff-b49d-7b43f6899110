import Axios, {
  AxiosInstance,
  AxiosRequestConfig,
  CustomParamsSerializer
} from "axios";
import {
  AtomHttpError,
  AtomHttpRequestConfig,
  AtomHttpResponse,
  RequestMethods
} from "./types.d";
import { stringify } from "qs";
import NProgress from "../progress";
import { formatToken, getToken } from "@/utils/auth";
import { ErrorCode } from "@/utils/http/code";
import { message } from "@/utils/message";
import { useUserStoreHook } from "@/store/modules/user";

// import { useUserStoreHook } from "@/store/modules/user";

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  baseURL: import.meta.env.VITE_BASE_URL,
  // 请求超时时间
  timeout: 20000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};
export const isRelogin = { show: false };
class AtomHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** token过期后，暂存待执行的请求 */
  private static requests = [];

  /** 防止重复刷新token */
  // private static isRefreshing = false;

  /** 初始化配置对象 */
  private static initConfig: AtomHttpRequestConfig = {};

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: AtomHttpRequestConfig) {
    return new Promise(resolve => {
      AtomHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    AtomHttp.axiosInstance.interceptors.request.use(
      async (config: AtomHttpRequestConfig) => {
        // 开启进度条动画
        // NProgress.start();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (AtomHttp.initConfig.beforeRequestCallback) {
          AtomHttp.initConfig.beforeRequestCallback(config);
          return config;
        }
        /** 请求白名单，放置一些不需要token的接口（通过设置请求白名单，防止token过期后再请求造成的死循环问题） */
        const whiteList = [
          "/refreshToken",
          "/login",
          "/manage",
          "/captchaImage",
          "/userSign",
          "/meetingSign"
        ];
        return whiteList.find(url => url === config.url)
          ? config
          : new Promise(resolve => {
              const token = getToken();
              if (token) {
                config.headers["Authorization"] = formatToken(token);
              }
              resolve(config);
            });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = AtomHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: AtomHttpResponse) => {
        const $config = response.config;
        // 关闭进度条动画
        // NProgress.done();
        // 优先判断post/get等方法是否传入回掉，否则执行初始化设置等回掉
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (AtomHttp.initConfig.beforeResponseCallback) {
          AtomHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        const code = response.data.code || 200;
        // 获取错误信息
        const msg: string =
          ErrorCode[code] || response.data.msg || ErrorCode["default"];

        if (
          response.request.responseType === "blob" ||
          response.request.responseType === "arraybuffer"
        ) {
          return response.data;
        }
        if (code === 401) {
          if (!isRelogin.show) {
            isRelogin.show = false;
            useUserStoreHook().logOut();
          }
          return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
        } else if (code === 500) {
          message(msg, { type: "error" });
          return Promise.reject(new Error(msg));
        } else if (code === 601) {
          message(msg, { type: "error" });
          return Promise.reject(new Error(msg));
        } else if (code !== 200) {
          message(msg, { type: "error" });
          return Promise.reject("error");
        } else {
          return Promise.resolve(response.data);
        }
      },
      (error: AtomHttpError) => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        // 关闭进度条动画
        // NProgress.done();
        let msg = error.message;
        if (msg == "Network Error") {
          msg = "后端接口连接异常";
        } else if (msg.includes("timeout")) {
          msg = "系统接口请求超时";
        } else if (msg.includes("Request failed with status code")) {
          msg = "系统接口" + msg.substring(msg.length - 3) + "异常";
        }

        message(msg, { type: "error", duration: 5 * 1000 });
        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error);
      }
    );
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: AtomHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as AtomHttpRequestConfig;

    // 单独处理自定义请求/响应回掉
    return new Promise((resolve, reject) => {
      AtomHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的post工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: AtomHttpRequestConfig
  ): Promise<P> {
    return this.request<P>("post", url, params, config);
  }

  /** 单独抽离的get工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: AtomHttpRequestConfig
  ): Promise<P> {
    return this.request<P>("get", url, params, config);
  }
}

export const http = new AtomHttp();
