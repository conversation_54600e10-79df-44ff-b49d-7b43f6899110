import Cookies from "js-cookie";
import { storageSession } from "@pureadmin/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { UserInfo } from "@/types/auth";

export type DataInfo = UserInfo;

export const sessionKey = "user-info-meeting";
export const TokenKey = "authorized-token-meeting";

/** 获取`token` */
export function getToken(): string {
  // 此处与`TokenKey`相同，此写法解决初始化时`Cookies`中不存在`TokenKey`报错
  return Cookies.get(TokenKey);
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 */
export function setToken(token: string) {
  Cookies.set(TokenKey, token);
}

export function setAuth(
  userId: number,
  orgCode: string,
  username: string,
  roles: Array<string>,
  auths: Array<string>,
  orgName: string,
  roleNames: Array<string>,
  avatar: string,
  dept: string
) {
  function setSessionKey(
    userId: number,
    orgCode: string,
    username: string,
    roles: Array<string>,
    auths: Array<string>,
    orgName: string,
    roleNames: Array<string>,
    avatar: string,
    dept: string
  ) {
    useUserStoreHook().SET_USER_ID(userId);
    useUserStoreHook().SET_ORG_CODE(orgCode);
    useUserStoreHook().SET_USERNAME(username);
    useUserStoreHook().SET_ROLES(roles);
    useUserStoreHook().SET_AUTHS(auths);
    useUserStoreHook().SET_ORG_NAME(orgName);
    useUserStoreHook().SET_ROLE_NAMES(roleNames);
    useUserStoreHook().SET_AVATAR(avatar);
    useUserStoreHook().SET_DEPT(dept);

    storageSession().setItem(sessionKey, {
      userId,
      orgCode,
      username,
      roles,
      auths,
      orgName,
      roleNames,
      avatar,
      dept
    });
  }

  if (username && roles) {
    setSessionKey(
      userId,
      orgCode,
      username,
      roles,
      auths,
      orgName,
      roleNames,
      avatar,
      dept
    );
  } else {
    const orgCode =
      storageSession().getItem<DataInfo>(sessionKey)?.orgCode ?? "";
    const username =
      storageSession().getItem<DataInfo>(sessionKey)?.username ?? "";
    const roles = storageSession().getItem<DataInfo>(sessionKey)?.roles ?? [];
    const auths = storageSession().getItem<DataInfo>(sessionKey)?.auths ?? [];
    const orgName =
      storageSession().getItem<DataInfo>(sessionKey)?.orgName ?? "";
    const roleNames =
      storageSession().getItem<DataInfo>(sessionKey)?.roleNames ?? [];
    const avatar = storageSession().getItem<DataInfo>(sessionKey)?.avatar ?? "";
    const dept = storageSession().getItem<DataInfo>(sessionKey)?.dept ?? "";
    setSessionKey(
      userId,
      orgCode,
      username,
      roles,
      auths,
      orgName,
      roleNames,
      avatar,
      dept
    );
  }
}

/** 删除`token`以及key值为`user-info`的session信息 */
export function removeToken() {
  Cookies.remove(TokenKey);
  sessionStorage.clear();
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return "Bearer " + token;
};
