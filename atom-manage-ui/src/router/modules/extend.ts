export default {
  path: "/extend",
  redirect: "/welcome",
  meta: {
    title: "首页"
  },
  children: [
    {
      path: "/auth-role/:id",
      name: "AuthRole",
      component: () => import("@/views/system/user/authRole.vue"),
      meta: {
        title: "分配角色",
        visible: false
      }
    },
    {
      path: "/oss-config/index",
      name: "OssConfig",
      component: () => import("@/views/system/oss/config.vue"),
      meta: {
        title: "文件上传配置",
        visible: false
      }
    }
  ]
} as RouteConfigsTable;
