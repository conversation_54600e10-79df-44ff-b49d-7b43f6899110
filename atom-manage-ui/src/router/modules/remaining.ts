const Layout = () => import("@/views/login/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/sso.vue"),
    meta: {
      title: "登录",
      visible: false,
      rank: 101
    }
  },
  {
    path: "/user/home/<USER>",
    name: "UserHomeLayout",
    component: () => import("@/views/user/components/layout.vue"),
    meta: {
      title: "会议端",
      visible: false,
      rank: 109
    }
  },
  {
    path: "/manage",
    name: "Man<PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      visible: false,
      rank: 102
    }
  },
  {
    path: "/joiner/meeting-select",
    name: "MeetingSelect",
    component: () => import("@/views/client/home/<USER>"),
    meta: {
      title: "会议选择",
      visible: false,
      rank: 103
    }
  },
  {
    path: "/joiner/file-manager",
    name: "JoinerFilePreview",
    component: () => import("@/views/client/file/index.vue"),
    meta: {
      title: "文件管理",
      visible: false,
      rank: 103
    }
  },
  {
    path: "/joiner/pdf/:id",
    name: "PdfPreview",
    component: () => import("@/views/client/file/pdf.vue"),
    meta: {
      title: "pdf预览",
      visible: false,
      rank: 106
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "首页",
      visible: false,
      rank: 104
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  {
    path: "/userSign",
    name: "userSign",
    component: () => import("@/views/system/sign/mobile/signature.vue"),
    meta: {
      title: "签名",
      visible: false,
      rank: 107
    }
  },
  {
    path: "/meetingSign",
    name: "meetingSign",
    component: () => import("@/views/system/sign/meetingSign/index.vue"),
    meta: {
      title: "会议签到",
      visible: false,
      rank: 107
    }
  }
] as Array<RouteConfigsTable>;
