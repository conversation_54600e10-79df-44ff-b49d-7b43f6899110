import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import ElementPlus from "element-plus";
import { getServerConfig } from "./config";
import { createApp, Directive } from "vue";
import { MotionPlugin } from "@vueuse/motion";
import { useEcharts } from "@/plugins/echarts";
import { injectResponsiveStorage } from "@/utils/responsive";
import Vue3Lottie from "vue3-lottie";
// import DeviceDetector, { DeviceDetectorResult } from "@/utils/deviceDetector";
const { PROD, VITE_MOBILE_URL } = import.meta.env;
import { UAParser } from "ua-parser-js";
import { useDeviceResultStoreHook } from "@/store/modules/deviceResult";

import VCalendar from "v-calendar";
import "v-calendar/style.css";

import Table from "@pureadmin/table";

// 引入重置样式
import "./style/reset.scss";
// import "vue3-lottie/dist/style.css";
// 导入公共样式
import "./style/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "element-plus/dist/index.css";
import "./style/custom.scss";
// 导入字体图标
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";

// 自定义指令
import * as directives from "@/directives";
// 全局注册`@iconify/vue`图标库
import {
  FontIcon,
  IconifyIconOffline,
  IconifyIconOnline
} from "./components/ReIcon";
import { PureTableBar } from "./components/RePureTableBar";
// 全局注册按钮级别权限组件
import { Auth } from "@/components/ReAuth";
// 字典处理
import { useDict } from "@/utils/dict";
import { parseTime, selectDictLabel } from "@/utils/atom";
import { download } from "@/plugins/download";

const app = createApp(App);

Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("PureTableBar", PureTableBar);
app.component("FontIcon", FontIcon);

app.component("Auth", Auth);

app.config.globalProperties.$download = download;
app.config.globalProperties.$useDict = useDict;
app.config.globalProperties.$parseTime = parseTime;
app.config.globalProperties.$selectDictLabel = selectDictLabel;

getServerConfig(app).then(async config => {
  setupStore(app);
  const deviceResult = UAParser(navigator.userAgent);
  if (PROD) {
    // 如果是手机，跳转到移动端页面
    if (deviceResult.device.is("mobile")) {
      window.location.replace(VITE_MOBILE_URL);
      return;
    }
  }
  // 特殊处理，华为早期鸿蒙平板，设备检测为 桌面版undefined，需要特殊处理
  const platform = navigator.platform;
  if (platform === "Linux aarch64" && deviceResult.device.type === undefined) {
    deviceResult.device.type = "tablet";
  }
  // 设置设备类型
  useDeviceResultStoreHook().SET_DEVICE_RESULT(deviceResult);
  app.use(router);
  await router.isReady();
  injectResponsiveStorage(app, config);
  app.use(MotionPlugin).use(ElementPlus).use(useEcharts).use(Table);
  app.use(Vue3Lottie, { name: "lottie" });
  app.use(VCalendar, {});

  app.mount("#app");
});
