import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { GenTable } from "@/types/tool/gen-table";
import { GenTableColumn } from "@/types/tool/gen-table-column";

export type Table = {
  rows: GenTableColumn[];
  info: GenTable;
  tables: GenTable[];
};

const headers = { datasource: localStorage.getItem("dataName") };

// 查询生成表数据
export function listTable(params: any): Promise<Resp<GenTable[]>> {
  return http.request("get", "/tool/gen/list", { headers, params });
}

// 查询db数据库列表
export function listDbTable(params: any): Promise<Resp<any>> {
  return http.request("get", "/tool/gen/db/list", { headers, params });
}

// 查询表详细信息
export function getGenTable(tableId: number | string): Promise<Resp<Table>> {
  return http.request("get", "/tool/gen/" + tableId, { headers });
}

// 修改代码生成信息
export function updateGenTable(data: any): Promise<Resp<void>> {
  return http.request("put", "/tool/gen", { headers, data });
}

// 导入表
export function importTable(params: any): Promise<Resp<void>> {
  return http.request("post", "/tool/gen/importTable", { headers, params });
}

// 预览生成代码
export function previewTable(tableId: number): Promise<Resp<any>> {
  return http.request("get", "/tool/gen/preview/" + tableId, { headers });
}

// 删除表数据
export function delTable(tableId: number | number[]): Promise<Resp<void>> {
  return http.request("delete", "/tool/gen/" + tableId, { headers });
}

// 生成代码（自定义路径）
export function genCode(tableName: string): Promise<Resp<any>> {
  return http.request("get", "/tool/gen/genCode/" + tableName, { headers });
}

// 同步数据库
export function synchDb(tableName: string): Promise<Resp<any>> {
  return http.request("get", "/tool/gen/synchDb/" + tableName, { headers });
}
