import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import { TopicQuery, Topic } from "@/types/meeting/topic";
import { TopicUpload } from "@/types/meeting/topic-file-audit";

// 查询议题列表
export function listTopic(
  query: TopicQuery,
  page?: PaginationProps
): Promise<Resp<Topic[]>> {
  return http.request("get", `/meeting/topic/list`, {
    params: { ...page, ...query }
  });
}

// 查询议题详细
export function getTopic(id: number): Promise<Resp<Topic>> {
  return http.request("get", `/meeting/topic/` + id);
}

// 新增议题
export function addTopic(data: Topic): Promise<Resp<void>> {
  return http.request("post", `/meeting/topic`, { data: data });
}

// 修改议题
export function updateTopic(data: Topic): Promise<Resp<void>> {
  return http.request("put", `/meeting/topic`, { data: data });
}

// 删除议题
export function delTopic(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/topic/` + id);
}

export function getTopicTypes(): Promise<
  Resp<[{ key: string; name: string }]>
> {
  return http.request("get", `/meeting/topic/types`);
}

// 分页查询所有关联有过本人操作记录的议题
export function listRelatedTopic(
  query: TopicQuery,
  page?: PaginationProps
): Promise<Resp<Topic[]>> {
  return http.request("get", `/meeting/topic/related-topic-list`, {
    params: { ...page, ...query }
  });
}

// 获取当前开放收集时间并且关联当前用户的议题
export function getRelatedTopic(): Promise<Resp<Topic[]>> {
  return http.request("get", `/meeting/topic/related-topic`);
}

// 根据选择的标题id获取关联本人的类型
export function getRelatedType(
  topicId: number
): Promise<Resp<[{ key: string; name: string }]>> {
  return http.request("get", `/meeting/topic/related-type/` + topicId);
}

// 新增上传议题
export function topicUpload(data: TopicUpload): Promise<Resp<void>> {
  return http.request("post", `/meeting/topic/topic-upload`, { data: data });
}

// 提交上传议题
export function submitTopicUpload(
  topicId: number,
  typeId: number
): Promise<Resp<void>> {
  return http.request(
    "put",
    `/meeting/topic/topic-upload/submit/` + topicId + `/` + typeId
  );
}
