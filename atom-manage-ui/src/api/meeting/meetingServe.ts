import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import { MeetingServeQuery, MeetingServe } from "@/types/meeting/meetingServe";
import { Meeting } from "@/types/meeting/meeting";
import { BaseQuery } from "@/types";

// 分页查询会议服务详细
export function pageMeetingServe(
  query: MeetingServeQuery,
  page?: PaginationProps & BaseQuery
): Promise<Resp<Meeting[]>> {
  return http.request("get", `/meeting/meetingServe/page`, {
    params: { ...page, ...query }
  });
}

// 查询会议服务列表
export function listMeetingServe(
  query: MeetingServeQuery
): Promise<Resp<MeetingServe[]>> {
  return http.request("get", `/meeting/meetingServe/list`, {
    params: { ...query }
  });
}

// 查询会议服务详细
export function getMeetingServe(id: number): Promise<Resp<MeetingServe>> {
  return http.request("get", `/meeting/meetingServe/` + id);
}

// 新增会议服务
export function addMeetingServe(data: MeetingServe): Promise<Resp<void>> {
  return http.request("post", `/meeting/meetingServe`, { data: data });
}

// 修改会议服务
export function updateMeetingServe(data: MeetingServe): Promise<Resp<void>> {
  return http.request("put", `/meeting/meetingServe`, { data: data });
}

// 删除会议服务
export function delMeetingServe(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/meetingServe/` + id);
}

// 修改会议服务
export function updateStatus(id: string | number): Promise<Resp<void>> {
  return http.request("put", `/meeting/meetingServe/update/` + id);
}
