import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import { MeetingMemoQuery, MeetingMemo} from "@/types/meeting/meetingMemo";

// 查询会议签名列表
export function listMeetingMemo(query: MeetingMemoQuery): Promise<Resp<MeetingMemo[]>> {
  return http.request("get", `/meeting/meetingMemo/list`, { params: {...query } })
}

// 查询会议签名详细
export function getMeetingMemo(id: number): Promise<Resp<MeetingMemo>> {
  return http.request("get", `/meeting/meetingMemo/` + id)
}

// 新增会议签名
export function addMeetingMemo(data: MeetingMemo): Promise<Resp<void>>  {
  return http.request("post", `/meeting/meetingMemo`, { data: data });
}

// 修改会议签名
export function updateMeetingMemo(data: MeetingMemo): Promise<Resp<void>> {
  return http.request("put", `/meeting/meetingMemo`, { data: data });
}

// 删除会议签名
export function delMeetingMemo(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/meetingMemo/` + id);
}
