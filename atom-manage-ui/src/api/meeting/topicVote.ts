import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import {
  TopicVoteQuery,
  TopicVote,
  TopicVoteAndMeetingLink
} from "@/types/meeting/topicVote";

// 查询议题决列表
export function listTopicVote(
  query: TopicVoteQuery,
  page?: PaginationProps
): Promise<Resp<TopicVote[]>> {
  return http.request("get", `/meeting/topicVote/list`, {
    params: { ...page, ...query }
  });
}

// 查询议题决详细
export function getTopicVote(id: number): Promise<Resp<TopicVote>> {
  return http.request("get", `/meeting/topicVote/` + id);
}

// 新增议题决
export function addTopicVote(
  data: TopicVoteAndMeetingLink
): Promise<Resp<void>> {
  return http.request("post", `/meeting/topicVote`, { data: data });
}

// 删除议题决
export function delTopicVote(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/topicVote/` + id);
}

// linkId查询表决详情
export function getVoteInfoByLinkId(
  linkId: number
): Promise<Resp<TopicVoteAndMeetingLink>> {
  return http.request(
    "get",
    `/meeting/topicVote/getVoteInfoByLinkId/` + linkId
  );
}

// linkId查询相同议题在其他会议的表决详情
export function getOtherVoteInfoByLinkId(
  linkId: number
): Promise<Resp<TopicVoteAndMeetingLink[]>> {
  return http.request(
    "get",
    `/meeting/topicVote/getOtherVoteInfoByLinkId/` + linkId
  );
}
