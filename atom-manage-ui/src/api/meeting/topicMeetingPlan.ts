import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import { TopicMeetingPlanQuery, TopicMeetingPlan} from "@/types/meeting/topicMeetingPlan";

// 查询预约列表
export function listTopicMeetingPlan(query: TopicMeetingPlanQuery, page?: PaginationProps): Promise<Resp<TopicMeetingPlan[]>> {
  return http.request("get", `/meeting/topicMeetingPlan/list`, { params: {...page,...query } })
}

// 查询预约详细
export function getTopicMeetingPlan(id: number): Promise<Resp<TopicMeetingPlan>> {
  return http.request("get", `/meeting/topicMeetingPlan/` + id)
}

// 新增预约
export function addTopicMeetingPlan(data: TopicMeetingPlan): Promise<Resp<void>>  {
  return http.request("post", `/meeting/topicMeetingPlan`, { data: data });
}

// 修改预约
export function updateTopicMeetingPlan(data: TopicMeetingPlan): Promise<Resp<void>> {
  return http.request("put", `/meeting/topicMeetingPlan`, { data: data });
}

// 删除预约
export function delTopicMeetingPlan(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/topicMeetingPlan/` + id);
}

//  预召开会议列表
export function listAllMeetingPlan(): Promise<Resp<TopicMeetingPlan[]>> {
  return http.request("get", `/meeting/topicMeetingPlan/option/list`);
}

//  预召开会议列表 包含当前过期的
export function listAllMeetingPlanWithDelay(
  id: number
): Promise<Resp<TopicMeetingPlan[]>> {
  return http.request(
    "get",
    `/meeting/topicMeetingPlan/option/list/delay/` + id
  );
}
