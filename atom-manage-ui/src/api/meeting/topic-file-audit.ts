import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import {
  TopicFileAuditQuery,
  TopicFileAudit,
  TopicFileAuditRow
} from "@/types/meeting/topic-file-audit";

// 查询议题文件审核列表
export function listTopicFileAudit(
  query: TopicFileAuditQuery,
  page?: PaginationProps
): Promise<Resp<TopicFileAudit[]>> {
  return http.request("get", `/meeting/topicFileAudit/list`, {
    params: { ...page, ...query }
  });
}

export function pageTopicFileAudit(
  query: TopicFileAuditQuery,
  page?: PaginationProps
): Promise<Resp<TopicFileAuditRow[]>> {
  return http.request("get", `/meeting/topicFileAudit/topic-list`, {
    params: { ...page, ...query }
  });
}

// 查询议题文件审核详细
export function getTopicFileAudit(id: number): Promise<Resp<TopicFileAudit>> {
  return http.request("get", `/meeting/topicFileAudit/` + id);
}

// 新增议题文件审核
export function addTopicFileAudit(data: TopicFileAudit): Promise<Resp<void>> {
  return http.request("post", `/meeting/topicFileAudit`, { data: data });
}

// 修改议题文件审核
export function updateTopicFileAudit(
  data: TopicFileAudit
): Promise<Resp<void>> {
  return http.request("put", `/meeting/topicFileAudit`, { data: data });
}

// 删除议题文件审核
export function delTopicFileAudit(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/topicFileAudit/` + id);
}
