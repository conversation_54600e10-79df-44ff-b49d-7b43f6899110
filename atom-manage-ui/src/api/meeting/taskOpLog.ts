import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { TaskOpLog } from "@/types/meeting/taskOpLog";

// 查询督查督办操作流程日志列表
export function getTaskOpLogList(taskId: number): Promise<Resp<TaskOpLog[]>> {
  return http.request("get", `/meeting/taskOpLog/list/taskId/` + taskId);
}

// // 查询督查督办操作流程日志详细
// export function getTaskOpLog(id: number): Promise<Resp<TaskOpLog>> {
//   return http.request("get", `/business/taskOpLog/` + id)
// }
//
// // 新增督查督办操作流程日志
// export function addTaskOpLog(data: TaskOpLog): Promise<Resp<void>>  {
//   return http.request("post", `/business/taskOpLog`, { data: data });
// }
//
// // 修改督查督办操作流程日志
// export function updateTaskOpLog(data: TaskOpLog): Promise<Resp<void>> {
//   return http.request("put", `/business/taskOpLog`, { data: data });
// }
//
// // 删除督查督办操作流程日志
// export function delTaskOpLog(id: number | number[]): Promise<Resp<void>> {
//   return http.request("delete", `/business/taskOpLog/` + id);
// }
