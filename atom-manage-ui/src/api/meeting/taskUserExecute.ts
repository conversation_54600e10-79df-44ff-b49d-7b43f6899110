import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types";
import { PaginationProps } from "@pureadmin/table";
import {
  TaskUserExecuteQuery,
  TaskUserExecute
} from "@/types/meeting/taskUserExecute";

// 查询督查督办执行列表
export function listTaskUserExecute(
  query: TaskUserExecuteQuery,
  page?: PaginationProps
): Promise<Resp<TaskUserExecute[]>> {
  return http.request("get", `/meeting/taskUserExecute/list`, {
    params: { ...page, ...query }
  });
}

// 查询督查督办执行详细
export function getTaskUserExecute(id: number): Promise<Resp<TaskUserExecute>> {
  return http.request("get", `/meeting/taskUserExecute/` + id);
}

// 根据主表taskId查询详情
export function getTaskUserExecuteByTaskId(
  taskId: number
): Promise<Resp<TaskUserExecute>> {
  return http.request("get", `/meeting/taskUserExecute/execute/` + taskId);
}

// 新增/修改督查督办执行
export function addOrUpdateTaskUserExecute(
  data: TaskUserExecute
): Promise<Resp<void>> {
  return http.request("post", `/meeting/taskUserExecute`, { data: data });
}

// 删除督查督办执行
export function delTaskUserExecute(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/taskUserExecute/` + id);
}

// 督查督办执行审核
export function taskUserExecuteAudit(
  data: TaskUserExecute
): Promise<Resp<void>> {
  return http.request("post", `/meeting/taskUserExecute/audit`, { data: data });
}

// 督查督办部门领导补充
export function taskUserExecuteMajorFill(
  data: TaskUserExecute
): Promise<Resp<void>> {
  return http.request("post", `/meeting/taskUserExecute/majorFill`, {
    data: data
  });
}
