import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import { TaskQuery, Task } from "@/types/meeting/task";
import { BaseQuery } from "@/types";
// 查询督查督办主列表
export function listTask(
  query: TaskQuery,
  page?: PaginationProps
): Promise<Resp<Task[]>> {
  return http.request("get", `/meeting/task/list`, {
    params: { ...page, ...query }
  });
}

// 查询督查督办指派列表
export function listTaskAssign(
  query: TaskQuery,
  page?: PaginationProps
): Promise<Resp<Task[]>> {
  return http.request("get", `/meeting/task/list/assign`, {
    params: { ...page, ...query }
  });
}
// 查询督查督办指派列表
export function listTaskExecute(
  query: TaskQuery,
  page?: PaginationProps
): Promise<Resp<Task[]>> {
  return http.request("get", `/meeting/task/list/execute`, {
    params: { ...page, ...query }
  });
}
// 查询督查督办列表（督查督办查阅、涉及人员均可查看）
export function listTaskMajor(
  query: TaskQuery,
  page?: PaginationProps & BaseQuery
): Promise<Resp<Task[]>> {
  return http.request("get", `/meeting/task/list/major`, {
    params: { ...page, ...query }
  });
}

// 查询督查督办主列表
export function listTaskByOrgCode(
  query: TaskQuery,
  page?: PaginationProps
): Promise<Resp<Task[]>> {
  return http.request("get", `/meeting/task/list/orgCode`, {
    params: { ...page, ...query }
  });
}

// 查询督查督办主详细
export function getTask(id: number): Promise<Resp<Task>> {
  return http.request("get", `/meeting/task/` + id);
}

// 新增/更新督查督办主
export function addOrUpdateTask(data: Task): Promise<Resp<void>> {
  return http.request("post", `/meeting/task`, { data: data });
}

// 调整补充意见时效
export function adjustMajorFillExpDays(
  id: number,
  majorFillExpDays: number
): Promise<Resp<void>> {
  return http.request("put", `/meeting/task/majorFillExpDays`, {
    data: { id, majorFillExpDays }
  });
}

// 强制退回
export function forceRevokeTask(id: number): Promise<Resp<void>> {
  return http.request("put", `/meeting/task/forceRevoke/${id}`);
}

// 发布督查督办
export function publishTask(id: number): Promise<Resp<void>> {
  return http.request("put", `/meeting/task/publish/` + id);
}

// 删除督查督办主
export function delTask(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/meeting/task/` + id);
}

// 获取督查督办部门负责人
export function getTaskDeptMajorByDeptId(
  deptId: number
): Promise<Resp<number>> {
  return http.request("get", `/meeting/task/major/` + deptId);
}

// 查询督查督办主列表
export function listVoTask(): Promise<Resp<Task[]>> {
  return http.request("get", `/meeting/task/list/all`);
}
