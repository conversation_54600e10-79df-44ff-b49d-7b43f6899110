import { SysOss } from "@/types/system/sys-oss";
import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";

// 查询OSS对象存储列表
export function listOss(params: any): Promise<Resp<SysOss[]>> {
  return http.request("get", "/system/oss/list", { params });
}

// 删除OSS对象存储
export function delOss(ossIds: string | string[]): Promise<Resp<void>> {
  return http.request("delete", "/system/oss/" + ossIds);
}

export function getSignature(filename: string): Promise<Resp<any>> {
  return http.request("get", "/system/oss/signature", { params: { filename } });
}

export function getSignatureWithKey(
  filename: string,
  key: string
): Promise<Resp<string>> {
  return http.request("get", "/system/oss/signature/" + key, {
    params: { filename }
  });
}

export function delayDelete(ossId: string | number) {
  return http.request("delete", "/system/oss/delay/ " + ossId);
}

export function getShareUrl(ossId: string | number): Promise<Resp<string>> {
  return http.request("get", "/system/oss/share/" + ossId);
}
