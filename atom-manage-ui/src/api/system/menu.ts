import { RoleMenuTree, SysMenu } from "@/types/system/sys-menu";
import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";

// 查询菜单列表
export function listMenu(params?: any): Promise<Resp<SysMenu[]>> {
  return http.request("get", "/system/menu/list", { params });
}

// 查询菜单详细
export function getMenu(menuId: number): Promise<Resp<SysMenu>> {
  return http.request("get", "/system/menu/" + menuId);
}

// 查询菜单下拉树结构
export function treeselect(): Promise<Resp<any[]>> {
  return http.request("get", "/system/menu/treeselect");
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(
  roleId: string
): Promise<Resp<RoleMenuTree>> {
  return http.request("get", "/system/menu/roleMenuTreeselect/" + roleId);
}

// 新增菜单
export function addMenu(data: SysMenu): Promise<Resp<void>> {
  return http.request("post", "/system/menu", { data });
}

// 修改菜单
export function updateMenu(data: SysMenu): Promise<Resp<void>> {
  return http.request("put", "/system/menu", { data });
}

// 删除菜单
export function delMenu(menuId: number): Promise<Resp<void>> {
  return http.request("delete", "/system/menu/" + menuId);
}

// 角色数据权限
export function roleMenuGroup(): Promise<Resp<SysMenu[]>> {
  return http.request("get", "/system/menu/roleMenuGroup");
}

// 菜单编号获取权限信息
export function listRoleByMenuId(
  menuId: string
): Promise<Resp<{ roleId: string }[]>> {
  return http.request("get", `/system/menu/${menuId}/role`);
}
