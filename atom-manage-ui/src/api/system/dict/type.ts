import { SysDictType } from "@/types/system/sys-dict-type";
import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";

// 查询字典类型列表
export function listType(params?: any): Promise<Resp<SysDictType[]>> {
  return http.request("get", "/system/dict/type/list", { params });
}

// 查询字典类型详细
export function getType(dictId: string): Promise<Resp<SysDictType>> {
  return http.request("get", "/system/dict/type/" + dictId);
}

// 新增字典类型
export function addType(data: SysDictType): Promise<Resp<void>> {
  return http.request("post", "/system/dict/type", { data });
}

// 修改字典类型
export function updateType(data): Promise<Resp<void>> {
  return http.request("put", "/system/dict/type", { data });
}

// 删除字典类型
export function delType(dictId: string | string[]): Promise<Resp<void>> {
  return http.request("delete", "/system/dict/type/" + dictId);
}

// 刷新字典缓存
export function refreshCache(): Promise<Resp<void>> {
  return http.request("delete", "/system/dict/type/refreshCache");
}

// 获取字典选择框列表
export function optionselect(): Promise<Resp<SysDictType[]>> {
  return http.request("get", "/system/dict/type/optionselect");
}
