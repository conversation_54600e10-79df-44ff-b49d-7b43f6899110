import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { SysRole } from "@/types/system/sys-role";

// 查询角色列表
export function listRole(params?: any): Promise<Resp<SysRole[]>> {
  return http.request("get", "/system/role/list", { params });
}

// 查询角色详细
export function getRole(roleId: string): Promise<Resp<SysRole>> {
  return http.request("get", `/system/role/${roleId}`);
}

// 新增角色
export function addRole(data: SysRole): Promise<Resp<void>> {
  return http.request("post", "/system/role", { data });
}

// 修改角色
export function updateRole(data: SysRole): Promise<Resp<void>> {
  return http.request("put", "/system/role", { data });
}

// 角色数据权限
export function dataScope(data: SysRole): Promise<Resp<void>> {
  return http.request("put", "/system/role/dataScope", { data });
}

// 角色状态修改
export function changeRoleStatus(
  roleId: number,
  status: string
): Promise<Resp<void>> {
  const data = {
    roleId,
    status
  };
  return http.request("put", "/system/role/changeStatus", { data });
}

// 删除角色
export function delRole(roleId: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/system/role/${roleId}`);
}

// 查询角色已授权用户列表
export function allocatedUserList(
  params: any
): Promise<Resp<{ id: number; userName: string; nickName: string }[]>> {
  return http.request("get", "/system/role/authUser/allocatedList", { params });
}

// 查询角色未授权用户列表
export function unallocatedUserList(
  params: any
): Promise<Resp<{ id: number; userName: string; nickName: string }[]>> {
  return http.request("get", "/system/role/authUser/unallocatedList", {
    params
  });
}

// 取消用户授权角色
export function authUserCancel(data: {
  roleId: number;
  userId: number | number[];
}): Promise<Resp<void>> {
  return http.request("put", "/system/role/authUser/cancel", { data });
}

// 批量取消用户授权角色
export function authUserCancelAll(params: {
  roleId: number;
  userIds: string;
}): Promise<Resp<void>> {
  return http.request("put", "/system/role/authUser/cancelAll", { params });
}

// 授权用户选择
export function authUserSelectAll(params: {
  roleId: number;
  userIds: string;
}): Promise<Resp<void>> {
  return http.request("put", "/system/role/authUser/selectAll", { params });
}

// 根据角色ID查询机构树结构
export function orgTreeSelect(
  roleId: string
): Promise<Resp<{ orgs: any[]; checkedKeys: any[] }>> {
  return http.request("get", `/system/role/orgTree/${roleId}`);
}

// 是否为集团管理员 日历用
export function isMainLeader(): Promise<Resp<boolean>> {
  return http.request("get", `/system/role/isMainLeader`);
}

// 获取全部角色信息，用于下拉选项
export function optionselect(): Promise<Resp<SysRole[]>> {
  return http.request("get", `/system/role/optionselect`);
}

// 查询当前角色所管理角色的角色编号
export function listRoleManaged(roleId: Number): Promise<Resp<number[]>> {
  return http.request("get", `/system/role/${roleId}/managed`);
}
// 保存角色关联
export function saveRoleManaged(
  roleId: Number,
  managedRoleIds: number[]
): Promise<Resp<number[]>> {
  return http.request("post", `/system/role/${roleId}/managed`, {
    data: {
      managedRoles: managedRoleIds,
      managerRole: roleId
    }
  });
}
