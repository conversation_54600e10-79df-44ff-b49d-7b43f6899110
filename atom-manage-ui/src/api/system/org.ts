import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { SysOrg } from "@/types/system/sys-org";
import { SearchUser, SysUser } from "@/types/system/sys-user";

// 查询组织列表
export function listOrg(params?: any): Promise<Resp<SysOrg[]>> {
  return http.request("get", "/system/org/list", { params });
}

export function listOrgChildren(parentId: string): Promise<Resp<SysOrg[]>> {
  return http.request("get", `/system/org/${parentId}/children`);
}
// 查询组织列表
export function treeselectOrg(): Promise<Resp<SysOrg[]>> {
  return http.request("get", "/system/org/treeselect");
}

// 查询组织列表（排除节点）
export function listOrgExcludeChild(orgId: string): Promise<Resp<SysOrg[]>> {
  return http.request("get", `/system/org/list/exclude/${orgId}`);
}

// 查询组织详细
export function getOrg(orgId: string): Promise<Resp<SysOrg>> {
  return http.request("get", `/system/org/${orgId}`);
}

export function listUserByPosition(orgId: string): Promise<Resp<SearchUser[]>> {
  return http.request("get", `/system/org/${orgId}/users`);
}

export function searchUser(
  orgId: number,
  username: string
): Promise<Resp<SearchUser[]>> {
  return http.request("get", `/system/org/${orgId}/searchUser/${username}`);
}

export function searchOrg(
  name: string
): Promise<Resp<{ code: string; name: string }[]>> {
  return http.request("get", `/system/org/search/${name}`);
}

// 获取公司列表
export function listCompanyOrg(): Promise<Resp<any[]>> {
  return http.request("get", `/system/org/listOrg`);
}
