import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import { UserSignQuery, UserSign} from "@/types/system/userSign";

// 查询签名列表
export function listUserSign(query: UserSignQuery, page?: PaginationProps): Promise<Resp<UserSign[]>> {
  return http.request("get", `/system/userSign/list`, { params: {...page,...query } })
}

// 查询签名详细
export function getUserSign(userId: string): Promise<Resp<UserSign>> {
  return http.request("get", `/system/userSign/` + userId)
}

// 新增签名
export function addUserSign(data: UserSign): Promise<Resp<void>>  {
  return http.request("post", `/system/userSign`, { data: data });
}



// 查询姓名
export function getUserName(uid: string|number): Promise<Resp<string>> {
  return http.request("get", `/system/userSign/user/` + uid)
}
