import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";

// 查询缓存详细
export function getCache(): Promise<Resp<any>> {
  return http.request("get", "/monitor/cache");
}

// 查询缓存名称列表
export function listCacheName(): Promise<Resp<string[]>> {
  return http.request("get", "/monitor/cache/getNames");
}

// 查询缓存键名列表
export function listCacheKey(cacheName: string): Promise<Resp<string[]>> {
  return http.request("get", `/monitor/cache/getKeys/${cacheName}`);
}

// 查询缓存内容
export function getCacheValue(
  cacheName: string,
  cacheKey: string
): Promise<Resp<any>> {
  return http.request(
    "get",
    `/monitor/cache/getValue/${cacheName}/${cacheKey}`
  );
}

// 清理指定名称缓存
export function clearCacheName(cacheName: string): Promise<Resp<void>> {
  return http.request("delete", `/monitor/cache/clearCacheName/${cacheName}`);
}

// 清理指定键名缓存
export function clearCacheKey(
  cacheName: string,
  cacheKey: string
): Promise<Resp<void>> {
  return http.request(
    "delete",
    `/monitor/cache/clearCacheKey/${cacheName}/${cacheKey}`
  );
}

// 清理全部缓存
export function clearCacheAll(): Promise<Resp<void>> {
  return http.request("delete", "/monitor/cache/clearCacheAll");
}
