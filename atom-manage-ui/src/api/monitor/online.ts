import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { SysUserOnline } from "@/types/monitor/sys-user-online";

// 查询在线用户列表
export function list(params: any): Promise<Resp<SysUserOnline[]>> {
  return http.request("get", "/monitor/online/list", { params });
}

// 强退用户
export function forceLogout(tokenId: string): Promise<Resp<void>> {
  return http.request("delete", `/monitor/online/${tokenId}`);
}
