import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { SysOperLog } from "@/types/monitor/sys-oper-log";

// 查询操作日志列表
export function listLog(params: any): Promise<Resp<SysOperLog[]>> {
  return http.request("get", "/system/log/list", { params });
}

// 查询操作日志列表
export function list(params: any): Promise<Resp<SysOperLog[]>> {
  return http.request("get", "/monitor/operlog/list", { params });
}

// 删除操作日志
export function delOperlog(operId: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/monitor/operlog/${operId}`);
}

// 清空操作日志
export function cleanOperlog(): Promise<Resp<void>> {
  return http.request("delete", "/monitor/operlog/clean");
}
