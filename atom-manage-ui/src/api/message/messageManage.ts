import { http } from "@/utils/http";
import { Resp } from "@/utils/http/types.d";
import { PaginationProps } from "@pureadmin/table";
import {
  MessageQuery,
  Message,
  MessageViewVo
} from "@/types/message/messageManage";

// 查询消息推送主体列表
export function listMessageManage(
  query: MessageQuery,
  page?: PaginationProps
): Promise<Resp<Message[]>> {
  return http.request("get", `/message/list`, {
    params: { ...page, ...query }
  });
}

// 查询消息推送主体详细
export function getMessageManage(id: number): Promise<Resp<Message>> {
  return http.request("get", `/message/` + id);
}

// 新增消息推送主体
export function addMessageManage(data: Message): Promise<Resp<void>> {
  return http.request("post", `/message`, { data: data });
}

// 修改消息推送主体
export function updateMessageManage(data: Message): Promise<Resp<void>> {
  return http.request("put", `/message`, { data: data });
}

// 删除消息推送主体
export function delMessageManage(id: number | number[]): Promise<Resp<void>> {
  return http.request("delete", `/message/` + id);
}
export function getDesktopList(): Promise<Resp<Message[]>> {
  return http.request("get", `/message/desktopList`);
}

export function readOne(id: number): Promise<Resp<void>> {
  return http.request("put", `/message/readOne/` + id);
}
export function readAll(): Promise<Resp<void>> {
  return http.request("put", `/message/readAll`);
}

export function readAllNotActionMessage(): Promise<Resp<void>> {
  return http.request("put", `/message/readAllNotActionMessage`);
}

export function pullMessageList(): Promise<Resp<void>> {
  return http.request("get", `/message/pullMessageList`);
}

export function getMessageVo(id: number): Promise<Resp<MessageViewVo>> {
  return http.request("get", `/message/view/pc/` + id);
}
