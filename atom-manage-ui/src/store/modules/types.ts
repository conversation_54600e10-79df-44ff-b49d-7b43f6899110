import { RouteRecordName } from "vue-router";

export type cacheType = {
  mode: string;
  name?: RouteRecordName;
};

export type positionType = {
  startIndex?: number;
  length?: number;
};

export type appType = {
  sidebar: {
    opened: boolean;
    withoutAnimation: boolean;
    // 判断是否手动点击Collapse
    isClickCollapse: boolean;
  };
  layout: string;
  device: string;
};

export type multiType = {
  path: string;
  name: string;
  meta: any;
  query?: object;
  params?: object;
};

export type setType = {
  title: string;
  fixedHeader: boolean;
  hiddenSideBar: boolean;
};

export type messageType = {
  list: Array<any>;
};

export type userType = {
  userId?: string;
  orgCode?: string;
  orgName?: string;
  roleNames?: Array<string>;
  username?: string;
  roles?: Array<string>;
  auths?: Array<string>;
  avatar: string;
  dept: string;
};

export type noticeType = {
  noticeList: Array<any>;
};
