import { defineStore } from "pinia";
import { store } from "@/store";
import { IDevice, IResult, IBrowser, IOS, ICPU, IEngine } from "ua-parser-js";
export const deviceResultStore = defineStore({
  id: "deviceResult",
  state: (): {
    ua: string;
    browser: IBrowser;
    device: IDevice;
    os: IOS;
    cpu: ICPU;
    engine: IEngine;
  } => ({
    ua: null,
    browser: null,
    device: null,
    os: null,
    cpu: null,
    engine: null
  }),
  getters: {
    getUA(): string {
      return this.ua;
    },
    getDevice(): IDevice {
      return this.device;
    },
    getOS(): IOS {
      return this.os;
    },
    getBrowser(): IBrowser {
      return this.browser;
    },
    getCPU(): ICPU {
      return this.cpu;
    },
    getEngine(): IEngine {
      return this.engine;
    }
  },
  actions: {
    SET_DEVICE_RESULT(deviceResult: IResult) {
      this.ua = deviceResult.ua;
      this.device = deviceResult.device;
      this.os = deviceResult.os;
      this.browser = deviceResult.browser;
      this.cpu = deviceResult.cpu;
      this.engine = deviceResult.engine;
    }
  }
});

export function useDeviceResultStoreHook() {
  return deviceResultStore(store);
}
