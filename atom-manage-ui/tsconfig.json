{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "strictFunctionTypes": false, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "baseUrl": ".", "allowJs": false, "resolveJsonModule": true, "lib": ["dom", "esnext"], "paths": {"@/*": ["src/*"], "@build/*": ["build/*"]}, "types": ["node", "vite/client", "element-plus/global", "@pureadmin/table/volar", "@pureadmin/descriptions/volar"], "typeRoots": ["./node_modules/@types/", "./types"]}, "include": ["mock/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}