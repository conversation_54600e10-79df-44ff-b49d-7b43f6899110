## 文档

- 前端项目基于 pure-admin 修改，个别使用文档请[点击查看文档](https://yiming_chang.gitee.io/pure-admin-doc)

## 用法

### 安装 pnpm

npm install -g pnpm

### 安装依赖

pnpm install

### 安装一个包

pnpm add 包名

### 卸载一个包

pnpm remove 包名

## ⚠️ 注意

## 命名规范

### 组件路由

采用 <code>under_line</code> 下划线风格

```
/components/hello_world
```

### 文件夹

采用 <code>kebab-case</code> 风格

```
/hello-world
```

### Vue 组件

采用 <code>CamelCased</code> 大驼峰风格

```
/components/HelloWorld.vue
```

### Javascript/Typescript 文件

采用 <code>lowerCase</code> 风格

```
/helloWorld.js
```

### Api 接口/Types 类型定义文件

采用 <code>kebab-case</code> 风格

```
/hello-world.ts
```

### 组件/实例的选项应该有统一的顺序。

```

import component from "component";

defineOptions({ name: 'component' })

const props = defineProps({});

const data = ref();
const computed = computed(() => {});

const watch = watch(() => {}, () => {});

methods();

const created = () => {};

const mounted = () => {};
```

## 要求

1. 子组件不允许直接修改父组件传下来的`props`，请使用`emit`事件.
2. 每个组件必须使用`defineOptions`定义组件名称，并且唯一不可重复.

```vue
//** 模块名称 */ defineOptions({ name: "组件名称" });
```

### 项目主要依赖

[Element UI](https://element.eleme.io/#/zh-CN)
[CSS 样式库](https://tailwindui.com/documentation)
[lodash 工具包](https://www.lodashjs.com/)

### 注意事项

权限指令<code>v-auth</code>
角色指令<code>v-role</code>

### 代码提交规范

参考 [约定式提交](https://www.conventionalcommits.org/zh-hans/v1.0.0)
尽可能遵循以下格式：

```

<类型>[范围(可选)]: <描述>

类型
├── fix/to                # 修复bug，可以是QA发现的BUG，也可以是研发自己发现的BUG
├──├── fix                # 产生diff并自动修复此问题。适合于一次提交直接修复问题
├──└── to                 # 只产生diff不自动修复此问题。适合于多次提交。最终修复问题提交时使用fix
├── feat                  # 新功能、新特性
├── perf                  # 提升性能的代码更改(在不影响代码内部行为的前提下，对程序性能进行优化)
├── revert                # 还原部分代码或功能
├── refactor              # 代码重构(在不影响代码内部行为、功能下的代码修改)
├── merge                 # 代码合并
├── style                 # 代码风格相关无影响运行结果的更改
├── build                 # 影响项目构建或是外部依赖的更改
├── docs                  # 文档或注释更改
├── test                  # 测试用例新增、修改
├── ci                    # 更改持续集成
├── workflow              # 工作流改进
├── release               # 发布新版本
├── chore                 # 依赖更新/脚手架配置修改等
├── types                 # 类型定义文件更改
├── wip                   # 开发中
└── ....


fix(党组织机构): 修复因用户权限范围错误导致的权限异常
fix(DAO):用户查询缺少username属性
feat(Controller):用户查询接口开发
```

> idea 使用`git commit template`插件
