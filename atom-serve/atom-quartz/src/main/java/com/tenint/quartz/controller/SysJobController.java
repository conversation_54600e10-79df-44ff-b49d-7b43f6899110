package com.tenint.quartz.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.constant.Constants;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.common.exception.job.TaskException;
import com.tenint.common.utils.BeanCopyUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.poi.ExcelUtil;
import com.tenint.quartz.domain.SysJob;
import com.tenint.quartz.service.ISysJobService;
import com.tenint.quartz.util.CronUtils;
import com.tenint.quartz.util.ScheduleUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 调度任务信息操作处理
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@RestController
@RequestMapping("job")
public class SysJobController extends BaseController {
    @Autowired
    private ISysJobService jobService;

    /**
     * 查询定时任务列表
     */
    @SaCheckPermission("manage:job:admin")
    @GetMapping("/list")
    public TableDataInfo<SysJob> list(SysJob sysJob, PageQuery pageQuery) {
//        QueryWrapper<SysJob> searchInfo = getSearchInfo(requestHelper, false);
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SysJob> page = pageQuery.build();
        return TableDataInfo.build(page);
    }

    /**
     * 导出定时任务列表
     */
    @SaCheckPermission("manage:job:export")
    @PostMapping("/export")
    public void export(SysJob job, HttpServletResponse response) {
        List<SysJob> list = jobService.selectJobList(job);
        List<SysJob> voList = BeanCopyUtils.copyList(list, SysJob.class);
        ExcelUtil.exportExcel(voList, "定时任务", SysJob.class, response);
    }

    /**
     * 获取定时任务详细信息
     */
    @SaCheckPermission("manage:job:query")
    @GetMapping(value = "/{jobId}")
    public R<SysJob> getInfo(@PathVariable("jobId") Long jobId) {
        return R.ok(jobService.selectJobById(jobId));
    }

    /**
     * 新增定时任务
     */
    @SaCheckPermission("manage:job:add")
    @PostMapping
    public R<Void> add(@RequestBody SysJob job) throws SchedulerException, TaskException {
        if (!CronUtils.isValid(job.getCronExpression())) {
            return R.fail("新增任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI)) {
            return R.fail("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            return R.fail("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
        }
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[] { Constants.HTTP, Constants.HTTPS }))
        {
            return R.fail("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
        }
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR))
        {
            return R.fail("新增任务'" + job.getJobName() + "'失败，目标字符串存在违规");
        }
        else if (!ScheduleUtils.whiteList(job.getInvokeTarget()))
        {
            return R.fail("新增任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
        }
        job.setCreatorId(getUserId());
        return toAjax(jobService.insertJob(job));
    }

    /**
     * 修改定时任务
     */
    @SaCheckPermission("manage:job:edit")
    @PutMapping
    public R<Void> edit(@RequestBody SysJob job) throws SchedulerException, TaskException {
        if (!CronUtils.isValid(job.getCronExpression())) {
            return R.fail("修改任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI)) {
            return R.fail("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            return R.fail("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
        }
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[] { Constants.HTTP, Constants.HTTPS }))
        {
            return R.fail("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
        }
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR))
        {
            return R.fail("修改任务'" + job.getJobName() + "'失败，目标字符串存在违规");
        }
        else if (!ScheduleUtils.whiteList(job.getInvokeTarget()))
        {
            return R.fail("修改任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
        }
        job.setUpdaterId(getUserId());
        return toAjax(jobService.updateJob(job));
    }

    /**
     * 定时任务状态修改
     */
    @SaCheckPermission("manage:job:changeStatus")
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysJob job) throws SchedulerException {
        SysJob newJob = jobService.selectJobById(job.getId());
        newJob.setStatus(job.getStatus());
        return toAjax(jobService.changeStatus(newJob));
    }

    /**
     * 定时任务立即执行一次
     */
    @SaCheckPermission("manage:job:changeStatus")
    @PutMapping("/run")
    public R<Void> run(@RequestBody SysJob job) throws SchedulerException {
        return toAjax(jobService.run(job));
    }

    /**
     * 删除定时任务
     */
    @SaCheckPermission("manage:job:remove")
    @DeleteMapping("/{jobIds}")
    public R<Void> remove(@PathVariable Long[] jobIds) throws SchedulerException, TaskException {
        jobService.deleteJobByIds(jobIds);
        return R.ok();
    }
}
