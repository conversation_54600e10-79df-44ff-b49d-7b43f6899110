package com.tenint.quartz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tenint.quartz.domain.SysJobLog;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 调度任务日志信息 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface SysJobLogMapper extends BaseMapper<SysJobLog> {
    /**
     * 获取quartz调度器日志的计划任务
     *
     * @param jobLog 调度日志信息
     * @return 调度任务日志集合
     */
    public List<SysJobLog> selectJobLogList(SysJobLog jobLog);

    /**
     * 查询所有调度任务日志
     *
     * @return 调度任务日志列表
     */
    public List<SysJobLog> selectJobLogAll();

    /**
     * 通过调度任务日志ID查询调度信息
     *
     * @param id 调度任务日志ID
     * @return 调度任务日志对象信息
     */
    public SysJobLog selectJobLogById(Long id);


    /**
     * 删除任务日志
     *
     * @param id 调度日志ID
     * @return 结果
     */
    public int deleteJobLogById(Long id);

    /**
     * 清空任务日志
     */
    public void cleanJobLog();
}
