<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.quartz.mapper.SysJobMapper">

    <resultMap type="com.tenint.quartz.domain.SysJob" id="SysJobResult">
        <id property="id" column="id"/>
        <result property="jobName" column="job_name"/>
        <result property="jobGroup" column="job_group"/>
        <result property="invokeTarget" column="invoke_target"/>
        <result property="cronExpression" column="cron_expression"/>
        <result property="misfirePolicy" column="misfire_policy"/>
        <result property="concurrent" column="concurrent"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updaterId" column="updater_id"/>
		<result property="updateTime"     column="update_time"     />
		<result property="remark"         column="remark"          />
	</resultMap>
	
	<sql id="selectJobVo">
        select id,
               job_name,
               job_group,
               invoke_target,
               cron_expression,
               misfire_policy,
               concurrent,
               status,
               creator_id,
               create_time,
               remark
        from sys_job
    </sql>

    <select id="selectJobList" parameterType="com.tenint.quartz.domain.SysJob" resultMap="SysJobResult">
        <include refid="selectJobVo"/>
        <where>
            <if test="jobName != null and jobName != ''">
                AND job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                AND job_group = #{jobGroup}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
			</if>
			<if test="invokeTarget != null and invokeTarget != ''">
				AND invoke_target like concat('%', #{invokeTarget}, '%')
			</if>
		</where>
	</select>
	
	<select id="selectJobAll" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
	</select>
	
	<select id="selectJobById" parameterType="String" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
        where id = #{id}
    </select>
	
	<delete id="deleteJobById" parameterType="String">
        delete
        from sys_job
        where id = #{id}
    </delete>

    <delete id="deleteJobByIds" parameterType="String">
        delete from sys_job where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateJob" parameterType="com.tenint.quartz.domain.SysJob">
        update sys_job
        <set>
            <if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
            <if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
            <if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
            <if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
            <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
            <if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
            <if test="status !=null">status = #{status},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updaterId != null and updaterId != ''">updater_id = #{updaterId},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <insert id="insertJob" parameterType="com.tenint.quartz.domain.SysJob" useGeneratedKeys="true" keyProperty="id">
        insert into sys_job(
        <if test="id != null and id != 0">id,</if>
        <if test="jobName != null and jobName != ''">job_name,</if>
        <if test="jobGroup != null and jobGroup != ''">job_group,</if>
        <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
        <if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
        <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
        <if test="concurrent != null and concurrent != ''">concurrent,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="creatorId != null and creatorId != ''">creator_id,</if>
        create_time
 		)values(
        <if test="id != null and id != 0">#{id},</if>
 			<if test="jobName != null and jobName != ''">#{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">#{concurrent},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
        <if test="creatorId != null and creatorId != ''">#{creatorId},</if>
 			sysdate()
 		)
	</insert>

</mapper> 