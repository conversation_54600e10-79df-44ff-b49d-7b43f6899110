package com.tenint;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tenint.common.core.domain.dto.RoleDTO;
import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.model.LoginUser;
import com.tenint.common.enums.DeviceType;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StreamUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.crcc.convert.SysOrgConvert;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.system.service.ISysOrgService;
import com.tenint.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

@SpringBootTest
@RunWith(SpringRunner.class)
public class SysUserServiceTest {


    @Autowired
    private  ISysUserService userService;

    @Autowired
    private  HrApiFacade hrApiFacade;

    @Autowired
    private  ISysOrgService orgService;

    /**
     * 用户组织重置
     */
    @Test
    public void userOrgReset() {

        List<SysUser> allUserList = userService.list();
        Set<SysUser> userSet = allUserList.stream().filter(user -> user.getOrgId() != null && StringUtils.isNotBlank(user.getOrgCode())).collect(Collectors.toSet());

        SysUser adminUser = LoginHelper.getAdminUser();
        CrccOrg crccOrg = hrApiFacade.treeOrgByProviderAndCompanyId(adminUser.getProviderId(), adminUser.getOrgId());
        List<SysOrg> orgList = orgService.treeToList(crccOrg);

        Map<Long, SysOrg> map = orgList.stream().collect(Collectors.toMap(SysOrg::getId, e -> e));

        List<String> list = new ArrayList<>();
        userSet.forEach(user -> {
            SysOrg org = map.get(user.getOrgId());
            if (org != null) {
                if (org.getType() == 3) {
                    CrccOrg deptByPositionId = hrApiFacade.getDeptByPositionId("crcc24", org.getId());
                    List<CrccOrg> crccOrgs = hrApiFacade.listOrgPathByOrgId("crcc24", deptByPositionId.getId());
                    String orgName = getOrgName(crccOrgs, null);
                    user.setOrgCode(deptByPositionId.getCode());
                    user.setOrgId(deptByPositionId.getId());
                    user.setOrgPath(orgName);
                    userService.updateById(user);
                    list.add(user.getNickName());
                }

            }
        });
        System.out.printf(list.toString());
    }


    @Test
    public void setOrgPath() {

        List<SysUser> allUserList = userService.list();
        Set<SysUser> userSet = allUserList.stream().filter(user -> user.getOrgId() != null && StringUtils.isNotBlank(user.getOrgCode())).collect(Collectors.toSet());

        SysUser adminUser = LoginHelper.getAdminUser();
        CrccOrg crccOrg = hrApiFacade.treeOrgByProviderAndCompanyId(adminUser.getProviderId(), adminUser.getOrgId());
        List<SysOrg> orgList = orgService.treeToList(crccOrg);

        Map<Long, SysOrg> map = orgList.stream().collect(Collectors.toMap(SysOrg::getId, e -> e));

        List<String> list = new ArrayList<>();
        userSet.forEach(user -> {

            SysOrg org = map.get(user.getOrgId());
            if (org != null) {
                if (StringUtils.isBlank(user.getOrgPath()) ) {
                    List<CrccOrg> crccOrgs = hrApiFacade.listOrgPathByOrgId("crcc24", org.getId());
                    String orgName = getOrgName(crccOrgs, null);
                    user.setOrgPath(orgName);
                    userService.updateById(user);
                }
            }
        });
        System.out.printf(list.toString());
    }

    public String getOrgName(List<CrccOrg> listOrg, Integer level) {
        if (listOrg.size() == 1) {
            return listOrg.get(0).getName();
        }
        Collections.reverse(listOrg);
        if (level != null) {
            List<CrccOrg> list = listOrg.stream().limit(level).toList();
            return StringUtils.join(list.stream().map(CrccOrg::getName).collect(Collectors.toList()), "/");
        }
        // 翻转listOrg排序
        return StringUtils.join(listOrg.stream().map(CrccOrg::getName).collect(Collectors.toList()), "/");
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(SysUser user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(user.getUserId());
        loginUser.setOrgId(user.getOrgId());
        loginUser.setUsername(user.getUserName());
        loginUser.setOrgCode(user.getOrgCode());
        loginUser.setUserType(user.getUserType());
        return loginUser;
    }
}
