package com.tenint.web.controller.system;

import com.tenint.common.annotation.Log;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.enums.BusinessType;
import com.tenint.system.manager.SysProfileManager;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 个人信息 业务处理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {

    private final SysProfileManager profileManager;

    /**
     * 个人信息
     */
    @GetMapping
    public R<Map<String, Object>> profile() {
        Map<String, Object> profile = profileManager.profile(getUserId());
        return R.ok(profile);
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> updateProfile(@RequestBody SysUser user) {
        profileManager.updateProfile(user);
        return R.ok();
    }

    /**
     * 重置密码
     *
     * @param newPassword 旧密码
     * @param oldPassword 新密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public R<Void> updatePwd(String oldPassword, String newPassword) {
        profileManager.updatePwd(oldPassword, newPassword);
        return R.ok();
    }

    /**
     * 头像上传
     *
     * @param avatar 用户头像
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/avatar")
    public R<Void> avatar(@RequestParam(value = "avatar") String avatar) {
        profileManager.avatar(avatar);
        return R.ok();
    }

}
