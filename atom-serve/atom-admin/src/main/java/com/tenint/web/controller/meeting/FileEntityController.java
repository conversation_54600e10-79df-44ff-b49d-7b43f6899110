package com.tenint.web.controller.meeting;

import java.util.List;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.lang.tree.Tree;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.enums.BusinessType;
import com.tenint.meeting.domain.bo.FileTopicUnionQueryBo;
import com.tenint.meeting.domain.vo.FileTopicUnionVo;
import com.tenint.meeting.domain.vo.MeetingFolderVo;
import com.tenint.meeting.domain.vo.UploadFileVo;
import com.tenint.meeting.enums.JoinUserType;
import com.tenint.meeting.enums.MeetingStatus;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.annotation.RepeatSubmit;
import com.tenint.common.annotation.Log;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.common.utils.poi.ExcelUtil;
import com.tenint.meeting.domain.vo.FileEntityVo;
import com.tenint.meeting.domain.bo.FileEntityBo;
import com.tenint.meeting.manager.FileEntityManager;
import com.tenint.common.core.page.TableDataInfo;

/**
 * 文件Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/meeting/file")
public class FileEntityController extends BaseController {

    private final FileEntityManager fileEntityManager;

    /**
     * 查询文件列表
     */
    @SaCheckPermission("meeting:file:query")
    @GetMapping("/list")
    public R<List<FileEntityVo>> list(FileEntityBo bo) {
        return R.ok(fileEntityManager.listFileAndTopicList(bo));
    }

    /**
     * 查询到自己上传的附件
     */
    @SaCheckPermission("meeting:file:query")
    @GetMapping("/listSelf")
    public TableDataInfo<FileEntityVo> listSelf(FileEntityBo bo, PageQuery pageQuery) {
        return fileEntityManager.pageSelfFileEntityVo(bo, pageQuery);
    }

    /**
     * 查询到分配自己查看的附件
     */
    @SaCheckPermission("meeting:file:query")
    @GetMapping("/listSelfQuery")
    public TableDataInfo<FileEntityVo> listSelfQuery(FileEntityBo bo, PageQuery pageQuery) {
        return fileEntityManager.pageSelfFileEntityQueryVo(bo, pageQuery);
    }

    /**
     * 查询到关联自己的资源目录
     * 参数 用户类型/会议状态
     * 协作人员 - 已提交的会议
     */
    @SaCheckPermission("meeting:file:query")
    @GetMapping("/listSelfJoinFolder")
    public R<Set<Long>> listSelfJoinFolder() {
        return R.ok(fileEntityManager.listSelfFolder(JoinUserType.JOIN.getKey(), MeetingStatus.AUDIT_PASS.getKey()));
    }

    /**
     * 查询到关联自己的资源目录
     * 参数 用户类型/会议状态
     * 参会人员 - 已发布的会议
     */
    @SaCheckPermission("meeting:file:query")
    @GetMapping("/listSelfQueryFolder")
    public R<Set<Long>> listSelfQueryFolder() {
        return R.ok(fileEntityManager.listSelfFolder(JoinUserType.ATTEND.getKey(), MeetingStatus.PUBLISH.getKey()));
    }

    /**
     * 获取文件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("meeting:file:query")
    @GetMapping("/{id}")
    public R<FileEntityVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(fileEntityManager.getVoById(id));
    }

    /**
     * 新增文件
     */
    @SaCheckPermission("meeting:file:option")
    @Log(title = "文件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody FileEntityBo bo) {
        return R.ok(fileEntityManager.saveByBo(bo));
    }

    /**
     * 修改文件
     */
    @SaCheckPermission("meeting:file:option")
    @Log(title = "文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Long> edit(@Validated(EditGroup.class) @RequestBody FileEntityBo bo) {
        return R.ok(fileEntityManager.updateByBo(bo));
    }

    /**
     * 删除文件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("meeting:file:option")
    @Log(title = "文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(fileEntityManager.removeWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 提交文件
     *
     * @param id 主键串
     */
    @SaCheckPermission("meeting:file:option")
    @Log(title = "文件", businessType = BusinessType.SUBMIT)
    @PutMapping("/submit/{id}")
    public R<Void> submit(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(fileEntityManager.submitFile(id));
    }

    /**
     * 审核文件
     *
     * @param id 主键串
     */
    @SaCheckPermission("meeting:file:option")
    @Log(title = "文件", businessType = BusinessType.AUDIT)
    @PutMapping("/audit/{id}/{status}")
    public R<Void> auditFile(@NotEmpty(message = "主键不能为空")
                             @PathVariable Long id, @PathVariable String status) {
        return toAjax(fileEntityManager.auditFile(id, status));
    }

    /**
     * 文件夹树
     */
    @SaCheckPermission(value = "meeting:file:query")
    @GetMapping("/folder/tree")
    public R<List<Tree<Long>>> folder() {
        return R.ok(fileEntityManager.folderTree());
    }


    /**
     * 文件夹树
     */
    @SaCheckPermission(value = "meeting:file:query")
    @GetMapping("/folder/list")
    public R<List<MeetingFolderVo>> folderList() {
        return R.ok(fileEntityManager.listAllFolder());
    }

    /**
     * 添加文件
     */
    @SaCheckPermission(value = "meeting:file:option")
    @PostMapping("/addMeeting")
    public R<Void> fileAdd(@RequestBody UploadFileVo uploadFileVo) {
        fileEntityManager.fileAdd(uploadFileVo);
        return R.ok();
    }

    /**
     * 特殊议题修改
     */
    @PostMapping("/updateSpecialTopic")
    public R<Void> updateSpecialTopic(@RequestBody UploadFileVo uploadFileVo) {
        fileEntityManager.updateSpecialTopic(uploadFileVo);
        return R.ok();
    }

    /**
     * 特殊议题删除
     */
    @DeleteMapping("/delSpecialTopic/{ids}")
    public R<Void> delSpecialTopic(@NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ids) {
        fileEntityManager.delSpecialTopic(ids);
        return R.ok();
    }


    /**
     * 根据id查询
     */
    @GetMapping("/addFile/{id}/{linkId}")
    public R<UploadFileVo> getAddFile(@PathVariable Long id, @PathVariable Long linkId) {
        UploadFileVo file = fileEntityManager.getAddFile(id, linkId);
        return R.ok(file);
    }

    /**
     * 修改排序
     */
    @PostMapping("/updateOrder/{ids}/{type}")
    public R<Void> updateOrder(@PathVariable Long[] ids, @PathVariable String type) {
        fileEntityManager.updateOrder(Arrays.asList(ids), type);
        return R.ok();
    }

    /**
     * 分页查询会议资料议题列表
     *
     * @param bo        条件
     * @param pageQuery 分页
     * @return 结果
     * @apiNote 包含议题的联合查询，不需要指定目录分页查询
     */
    @GetMapping("listUnion")
    public TableDataInfo<FileTopicUnionVo> pageFileTopicUnionVo(FileTopicUnionQueryBo bo, PageQuery pageQuery) {
        return fileEntityManager.pageFileTopicUnionVo(bo, pageQuery);
    }

}
