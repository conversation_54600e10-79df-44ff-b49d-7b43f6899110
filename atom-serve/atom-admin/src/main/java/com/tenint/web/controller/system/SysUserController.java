package com.tenint.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.annotation.Log;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.vo.SysOrgTreeVo;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.common.enums.BusinessType;
import com.tenint.common.excel.ExcelResult;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.poi.ExcelUtil;
import com.tenint.system.convert.SysUserConvert;
import com.tenint.system.domain.bo.CrccUserImportBo;
import com.tenint.system.domain.vo.SysUserExportVo;
import com.tenint.system.domain.vo.SysUserImportVo;
import com.tenint.system.domain.vo.SysUserTreeVo;
import com.tenint.system.domain.vo.SysUserVo;
import com.tenint.system.listener.SysUserImportListener;
import com.tenint.system.manager.SysUserManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
public class SysUserController extends BaseController {

    private final SysUserManager userManager;

    /**
     * 获取用户列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(SysUser user, PageQuery pageQuery) {
        Page<SysUserVo> sysUserPage = userManager.pageUser(user, pageQuery);
        return TableDataInfo.build(sysUserPage);
    }

    /**
     * 获取用户下拉选择,本人除外
     */
    @GetMapping("/selectList")
    public TableDataInfo<SysUserTreeVo> listSelect(SysUser user) {
        List<SysUser> sysUserList = userManager.listUser(user)
                .stream()
                .filter(sysUser -> {
                    Long userId = LoginHelper.getUserId();
                    if(userId == null) return true;
                    return !userId.equals(sysUser.getUserId());
                })
                .toList();

        List<SysUserTreeVo> sysUserTreeVos = SysUserConvert.INSTANCE.convertTreeVoList(sysUserList);
        return TableDataInfo.build(sysUserTreeVos);
    }

    /**
     * 导出用户列表
     */
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:user:export")
    @PostMapping("/export")
    public void export(SysUser user, HttpServletResponse response) {
        List<SysUser> list = userManager.listUser(user);
        List<SysUserExportVo> listVo = BeanUtil.copyToList(list, SysUserExportVo.class);
        for (int i = 0; i < list.size(); i++) {
            SysOrg org = list.get(i).getOrg();
            SysUserExportVo vo = listVo.get(i);
            if (ObjectUtil.isNotEmpty(org)) {
                vo.setOrgName(org.getName());
            }
        }
        ExcelUtil.exportExcel(listVo, "用户数据", SysUserExportVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:user:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<SysUserImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class, new SysUserImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", SysUserImportVo.class, response);
    }

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public R<Map<String, Object>> getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        return R.ok(userManager.getUserById(userId == null ? LoginHelper.getUserId() : userId));
    }

    /**
     * 新增用户
     */
    @SaCheckPermission("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> addUser(@Validated @RequestBody SysUser user) {
        return toAjax(userManager.saveUser(user));
    }

    /**
     * 修改用户
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> updateUser(@Validated @RequestBody SysUser user) {
        return toAjax(userManager.updateUser(user));
    }

    /**
     * 删除用户
     *
     * @param userIds 角色ID串
     */
    @SaCheckPermission("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> removeUser(@PathVariable Long[] userIds) {
        return toAjax(userManager.removeUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @SaCheckPermission("system:user:resetPwd")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Void> resetPwd(@RequestBody SysUser user) {
        return toAjax(userManager.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysUser user) {
        return toAjax(userManager.updateUserStatus(user));
    }


    /**
     * 新增用户
     */
    @SaCheckPermission("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping("/importCrccUser")
    public R<Void> importCrccUser(@Validated @RequestBody CrccUserImportBo user) {
        return toAjax(userManager.importCrccUser(user));
    }


    /**
     * 根据用户编号获取授权角色
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping("/authRole/{userId}")
    public R<Map<String, Object>> authRole(@PathVariable Long userId) {
        return R.ok(userManager.getAuthRoleByUserId(userId));
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户Id
     * @param roleIds 角色ID串
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public R<Void> insertAuthRole(Long userId, Long[] roleIds) {
        userManager.saveAuthRole(userId, roleIds);
        return R.ok();
    }

    /**
     * 获取机构树列表
     */
//    @SaCheckPermission("system:user:list")
    @GetMapping("/orgTree")
    public R<SysOrgTreeVo> orgTree(SysOrg org){
        return R.ok(userManager.listOrgTree(org));
    }

    /**
     * 获取机构树列表
     */
//    @SaCheckPermission("system:user:list")
    @GetMapping("/orgTreeOnly")
    public R<SysOrgTreeVo> orgTreeOnly(){
        return R.ok(userManager.listOrgTreeOnly());
    }

    /**
     * 根据userId获取用户信息
     * @param userIds
     * @return
     */
    @GetMapping("/getUserInfo/{userIds}")
    public R<List<SysUserTreeVo>> listUserInfoByIds(@PathVariable Long[] userIds) {
        List<SysUserTreeVo> userInfo = userManager.listUserInfoByIds(Arrays.asList(userIds));
        return R.ok(userInfo);
    }

    /**
     * 根据orgIds获取用户信息
     * @param orgIds
     * @return
     */
    @GetMapping("/getUserInfoByOrgId/{orgIds}")
    public R<List<SysUserVo>> getUserInfoByOrgId(@PathVariable Long[] orgIds) {
        List<SysUser> userList = userManager.listUserInfoByOrgIds(Arrays.asList(orgIds));
        List<SysUserVo> voList = SysUserConvert.INSTANCE.convert(userList);
        return R.ok(voList);
    }
}
