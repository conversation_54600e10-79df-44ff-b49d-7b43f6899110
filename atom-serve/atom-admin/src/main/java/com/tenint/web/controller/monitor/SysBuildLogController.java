package com.tenint.web.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.common.utils.StringUtils;
import com.tenint.system.domain.SysLogininfor;
import com.tenint.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitor/build")
public class SysBuildLogController {

    private final ISysConfigService configService;

    /**
     * 获取系统访问记录列表
     */
    @SaCheckRole("admin")
    @GetMapping("/log/{type}")
    public R<List<String>> buildLog(@PathVariable String type) {
        if (!Arrays.asList("mobile", "front", "end").contains(type)) {
            return R.fail("未知的编译日志类型");
        }
        String path = configService.getConfigValueByKey("build.log.path." + type);
        if (StringUtils.isBlank(path)) {
            return R.fail("编译目录未配置");
        }
        boolean exist = FileUtil.exist(path);
        if (!exist) {
            return R.fail("编译日志不存在");
        }
        List<String> strings = FileUtil.readUtf8Lines(path);
        return R.ok(strings);
    }
}
