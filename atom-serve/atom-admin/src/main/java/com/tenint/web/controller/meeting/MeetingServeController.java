package com.tenint.web.controller.meeting;

import java.util.Arrays;
import java.util.List;

import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.enums.BusinessType;
import com.tenint.meeting.domain.bo.MeetingServeBo;
import com.tenint.meeting.domain.vo.MeetingServeVo;
import com.tenint.meeting.manager.MeetingServeManager;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.annotation.RepeatSubmit;
import com.tenint.common.annotation.Log;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.common.core.page.TableDataInfo;

/**
 * 会议服务Controller
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/meeting/meetingServe")
public class MeetingServeController extends BaseController {

    private final MeetingServeManager meetingServeManager;

    /**
     * 分页查询会议服务列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/page")
    public TableDataInfo<MeetingServeVo> pageMeetingServeVo(MeetingServeBo bo, PageQuery pageQuery) {
        return meetingServeManager.pageMeetingServeVo(bo, pageQuery);
    }

    /**
     * 查询会议服务列表
     */
    @GetMapping("/list")
    public R<List<MeetingServeVo>> list(MeetingServeBo bo) {
        return R.ok(meetingServeManager.listMeetingServeVo(bo));
    }
    /**
     * 获取会议服务详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MeetingServeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(meetingServeManager.getVoById(id));
    }

     /**
     * 新增会议服务
     */
    @Log(title = "会议服务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MeetingServeBo bo) {
        return toAjax(meetingServeManager.saveByBo(bo));
    }

    /**
     * 修改会议服务
     */
    @Log(title = "会议服务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MeetingServeBo bo) {
        return toAjax(meetingServeManager.updateByBo(bo));
    }

    /**
     * 修改会议服务状态
     */
    @Log(title = "会议服务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/update/{id}")
    public R<Void> editStatus(@PathVariable("id") Long id) {
        return toAjax(meetingServeManager.updateStatus(id));
    }

    /**
     * 删除会议服务
     *
     * @param ids 主键串
     */
    @Log(title = "会议服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(meetingServeManager.removeWithValidByIds(Arrays.asList(ids), true));
    }
}
