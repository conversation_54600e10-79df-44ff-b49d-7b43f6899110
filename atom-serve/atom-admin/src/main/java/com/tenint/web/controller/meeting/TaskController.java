package com.tenint.web.controller.meeting;

import java.util.List;
import java.util.Arrays;

import cn.dev33.satoken.annotation.SaMode;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.enums.BusinessType;
import com.tenint.meeting.domain.TaskDeptMajor;
import com.tenint.meeting.domain.bo.TaskBo;
import com.tenint.meeting.domain.bo.TaskMajorFillExpDaysUpdateBo;
import com.tenint.meeting.domain.query.TaskQuery;
import com.tenint.meeting.domain.vo.TaskVo;
import com.tenint.meeting.manager.TaskDeptMajorManager;
import com.tenint.meeting.manager.TaskManager;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.annotation.RepeatSubmit;
import com.tenint.common.annotation.Log;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.page.TableDataInfo;

/**
 * 督查督办主Controller
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/meeting/task")
public class TaskController extends BaseController {

    private final TaskManager taskManager;
    private final TaskDeptMajorManager taskDeptMajorManager;

    /**
     * 查询督查督办发布列表
     */
    @SaCheckPermission("meeting:task:query")
    @GetMapping("/list")
    public TableDataInfo<TaskVo> list(TaskQuery query, PageQuery pageQuery) {
        return taskManager.pageTaskVoPublish(query, pageQuery);
    }

    /**
     * 查询督查督办指派列表
     *
     * @param query     查询条件
     * @param pageQuery 分页条件
     * @return
     */
    @SaCheckPermission("meeting:taskAssign:query")
    @GetMapping("/list/assign")
    public TableDataInfo<TaskVo> listAssign(TaskQuery query, PageQuery pageQuery) {
        return taskManager.pageTaskVoAssign(query, pageQuery);
    }

    /**
     * 查询督查督办执行列表
     *
     * @param query     查询条件
     * @param pageQuery 分页条件
     * @return
     */
    @SaCheckPermission(value = {"meeting:taskUser:query",}, mode = SaMode.OR)
    @GetMapping("/list/execute")
    public TableDataInfo<TaskVo> listExecute(TaskQuery query, PageQuery pageQuery) {
        return taskManager.pageTaskVoExecute(query, pageQuery);
    }

    /**
     * 查询督查督办列表（督查督办查阅、涉及人员均可查看）
     *
     * @param query     查询条件
     * @param pageQuery 分页条件
     * @return
     */
    @SaCheckPermission(value = {"meeting:taskMajor:query"}, mode = SaMode.OR)
    @GetMapping("/list/major")
    public TableDataInfo<TaskVo> listMajor(TaskQuery query, PageQuery pageQuery) {
        return taskManager.pageTaskVoMajor(query, pageQuery);
    }

    /**
     * 查询当前用户涉及的督查督办列表
     *
     * @param query 条件
     * @param pageQuery 分页
     * @return 结果
     * @apiNote 包含业务 指派、审核、执行、补充意见 不含任务创建发布
     */
    @SaCheckPermission(value = {"meeting:task:query", "meeting:taskUser:query", "meeting:taskAssign:query", "meeting:taskMajor:query"}, mode = SaMode.OR)
    @GetMapping("/list/union")
    public TableDataInfo<TaskVo> pageTaskVoUnion(TaskQuery query, PageQuery pageQuery) {
        return taskManager.pageTaskVoUnion(query, pageQuery);
    }

    /**
     * 获取督查督办主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission(value = {"meeting:task:query", "meeting:taskUser:query", "meeting:taskAssign:query", "meeting:taskMajor:query"}, mode = SaMode.OR)
    @GetMapping("/{id}")
    public R<TaskVo> getInfo(@NotNull(message = "主键不能为空")
                             @PathVariable Long id) {
        TaskVo vo = taskManager.getVoById(id);
        // 查看详细信息后，完成督查督办待阅提醒、领导补充意见提醒
        taskManager.completeTaskNoticeMessage(id);
        return R.ok(vo);
    }

    /**
     * 新增/更新督查督办
     */
    @SaCheckPermission("meeting:task:option")
    @Log(title = "督查督办", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> addOrUpdate(@Validated(AddGroup.class) @RequestBody TaskBo bo) {
        taskManager.saveOrUpdateByBo(bo);
        return R.ok();
    }

    /**
     * 更新督查督办领导补充意见时效
     *
     * @param bo 实体类
     * @return 结果
     */
    @SaCheckPermission("meeting:task:option")
    @Log(title = "督查督办", description = "更新督查督办领导补充意见时效", businessType = BusinessType.UPDATE)
    @PutMapping("majorFillExpDays")
    public R<Void> updateMajorFillExpDays(@RequestBody TaskMajorFillExpDaysUpdateBo bo) {
        taskManager.updateMajorFillExpDays(bo);
        return R.ok();
    }

    /**
     * 强制退回给执行人
     *
     * @param id 督查督办id
     * @return 结果
     */
    @SaCheckPermission("meeting:task:option")
    @Log(title = "督查督办", description = "强制退回执行人", businessType = BusinessType.UPDATE)
    @PutMapping("forceRevoke/{id}")
    public R<Void> forceRevoke(@PathVariable Long id){
        taskManager.forceRevoke(id);
        return R.ok();
    }

    /**
     * 删除督查督办
     *
     * @param ids 主键串
     */
    @SaCheckPermission("meeting:task:option")
    @Log(title = "督查督办", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        taskManager.removeWithValidByIds(Arrays.asList(ids), CollectionUtils.isNotEmpty(Arrays.asList(ids)));
        return R.ok();
    }

    /**
     * 集团管理员发布督查督办
     *
     * @param id
     * @return
     */
    @SaCheckPermission("meeting:task:option")
    @PutMapping("/publish/{id}")
    public R<Void> publishTask(@NotNull(message = "主键不能为空")
                               @PathVariable Long id) {
        taskManager.publishTask(id);
        return R.ok();
    }

    /**
     * 获取督查督办部门负责人
     */
    @SaCheckPermission("meeting:task:query")
    @GetMapping("/major/{deptId}")
    public R<TaskDeptMajor> getTaskDeptMajorUserIdByDeptId(@NotNull(message = "主键不能为空")
                                                           @PathVariable Long deptId) {
        return R.ok(taskDeptMajorManager.getTaskDeptMajorUserIdByDeptId(deptId));
    }


    /**
     * 获取可选择的task
     */
    @GetMapping("/list/all")
    public R<List<TaskVo>> listTask() {
        TaskQuery query = new TaskQuery();
        return R.ok(taskManager.taskVoList(query));
    }

    /**
     * 查询督查督办主列表
     */
    @Deprecated
    @SaCheckPermission(value = {"meeting:task:query", "meeting:taskUser:query", "meeting:taskAssign:query"}, mode = SaMode.OR)
    @GetMapping("/list/orgCode")
    public TableDataInfo<TaskVo> listByOrgCode(TaskQuery query, PageQuery pageQuery) {
        return taskManager.pageTaskByOrgCode(query, pageQuery);
    }
}
