package com.tenint.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.system.domain.SysOssConfig;
import com.tenint.system.manager.SysOssConfigManager;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 对象存储配置Controller
 *
 * <AUTHOR>
 * @date 2021-08-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/oss/config")
public class SysOssConfigController extends BaseController {

    private final SysOssConfigManager ossConfigManager;

    /**
     * 查询对象存储配置列表
     */
    @SaCheckPermission("system:oss:config")
    @GetMapping("/list")
    public TableDataInfo<SysOssConfig> list(SysOssConfig ossConfig, PageQuery pageQuery) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SysOssConfig> page = ossConfigManager.pageConfig(ossConfig, pageQuery);
        return TableDataInfo.build(page);
    }

    /**
     * 获取对象存储配置详细信息
     */
    @GetMapping("/{ossConfigId}")
    public R<SysOssConfig> getInfo(@NotNull(message = "主键不能为空") @PathVariable("ossConfigId") String ossConfigId) {
        SysOssConfig config = ossConfigManager.getById(ossConfigId);
        if (ObjectUtil.isNotNull(config)) {
            config.setSecretKey(null);
        }
        return R.ok(config);
    }

    /**
     * 新增对象存储配置
     */
    @SaCheckPermission("system:oss:add")
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssConfig bo) {
        return toAjax(ossConfigManager.saveOssConfig(bo) ? 1 : 0);
    }

    /**
     * 修改对象存储配置
     */
    @SaCheckPermission("system:oss:edit")
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssConfig bo) {
        return toAjax(ossConfigManager.updateOssConfig(bo) ? 1 : 0);
    }

    /**
     * 删除对象存储配置
     */
    @SaCheckPermission("system:oss:remove")
    @DeleteMapping("/{ossConfigIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ossConfigIds) {
        return toAjax(ossConfigManager.deleteWithValidByIds(Arrays.asList(ossConfigIds), true) ? 1 : 0);
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:oss:edit")
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysOssConfig bo) {
        return toAjax(ossConfigManager.updateOssConfigStatus(bo));
    }
}
