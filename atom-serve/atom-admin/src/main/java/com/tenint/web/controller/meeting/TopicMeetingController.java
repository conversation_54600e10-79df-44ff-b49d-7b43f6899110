package com.tenint.web.controller.meeting;

import java.util.List;
import java.util.Arrays;

import cn.dev33.satoken.annotation.SaMode;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.common.enums.BusinessType;
import com.tenint.meeting.domain.TopicMeetingLink;
import com.tenint.meeting.domain.bo.*;
import com.tenint.meeting.domain.vo.FileCommentVo;
import com.tenint.meeting.domain.vo.TopicMeetingAuditVo;
import com.tenint.meeting.domain.vo.TopicMeetingFlowRecordVo;
import com.tenint.meeting.domain.vo.TopicMeetingVo;
import com.tenint.meeting.enums.TopicMeetingAuditEnum;
import com.tenint.meeting.manager.FileCommentManager;
import com.tenint.meeting.manager.TopicMeetingAuditManager;
import com.tenint.meeting.manager.TopicMeetingFlowManager;
import com.tenint.meeting.manager.TopicMeetingManager;
import com.tenint.meeting.service.ITopicMeetingLinkService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.annotation.RepeatSubmit;
import com.tenint.common.annotation.Log;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.validate.AddGroup;
import org.springframework.web.multipart.MultipartFile;

/**
 * 议题收集Controller
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/meeting/topicMeeting")
public class TopicMeetingController extends BaseController {

    private final TopicMeetingManager topicMeetingManager;
    private final TopicMeetingAuditManager topicMeetingAuditManager;
    private final TopicMeetingFlowManager topicMeetingFlowManager;
    private final FileCommentManager topicFileCommentManager;
    private final ITopicMeetingLinkService meetingLinkService;

    /**
     * 查询我创建的议题收集列表（分页）
     */
    @SaCheckPermission("meeting:topicMeeting:query")
    @GetMapping("/list/my")
    public TableDataInfo<TopicMeetingVo> myList(TopicMeetingBo bo, PageQuery pageQuery) {
        return topicMeetingManager.pageMyTopicMeetingVo(bo, pageQuery);
    }

    /**
     * 获取议题收集详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission(value = {"meeting:topicMeetingAudit:query","meeting:topicMeeting:query"},mode = SaMode.OR)
    @GetMapping("/{id}")
    public R<TopicMeetingVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(topicMeetingManager.getTopicMeeting(id));
    }

    /**
     * 新增/更新议题收集
     * @param bo
     * @return
     */
    @SaCheckPermission("meeting:topicMeeting:option")
    @Log(title = "议题收集", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> saveOrUpdate(@Validated(AddGroup.class) @RequestBody TopicMeetingBo bo) {
        topicMeetingManager.saveOrUpdateTopicMeeting(bo, TopicMeetingAuditEnum.EDIT.getKey());
        return R.ok();
    }

    /**
     * 删除议题收集
     *
     * @param ids 主键串
     */
    @SaCheckPermission("meeting:topicMeeting:option")
    @Log(title = "议题收集", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(topicMeetingManager.removeWithValidByIds(Arrays.asList(ids)));
    }

    /**
     * 提交议题收集
     */
    @SaCheckPermission("meeting:topicMeeting:option")
    @Log(title = "议题收集", businessType = BusinessType.SUBMIT)
    @PutMapping("/submit/{id}")
    public R<Void> submitInfo(@NotEmpty(message = "主键不能为空")
                          @PathVariable("id") Long id) {
        return toAjax(topicMeetingManager.submitTopicMeeting(id));
    }

    /**
     * 重新提交议题收集
     */
    @SaCheckPermission("meeting:topicMeeting:option")
    @Log(title = "议题收集", businessType = BusinessType.SUBMIT)
    @PutMapping("/re-submit")
    public R<Void> reSubmitInfo(@Validated(AddGroup.class) @RequestBody TopicMeetingBo bo) {
        topicMeetingManager.reSubmitTopicMeeting(bo);
        return R.ok();
    }

    /**
     * 根据预会议id查询审核通过的议题(分管领导审核通过的)
     * @param planId
     * @return
     */
    @SaCheckPermission(value = {"meeting:topicMeetingPlan:query","meeting:topicMeeting:query"},mode = SaMode.OR)
    @GetMapping("/topic/audited/{planId}")
    public R<List<TopicMeetingVo>> getAuditedByMeetingPlanId(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long planId) {
        return R.ok(topicMeetingManager.getAuditedTopicByPlanId(planId));
    }

    /**
     * 根据预会议id查询议题-分页(审核通过的)
     * @return
     */
    @SaCheckPermission(value = {"meeting:topicMeetingPlan:query","meeting:topicMeeting:query"},mode = SaMode.OR)
    @GetMapping("/topic/list")
    public TableDataInfo<TopicMeetingVo> listAuditedTopicByPlanId(TopicMeetingBo bo, PageQuery pageQuery) {
        return topicMeetingManager.listAuditedTopicByPlanId(bo, pageQuery);
    }

    /**
     * 查询议题收集待审核列表 部门/分管领导审核页面
     */
    @SaCheckPermission("meeting:topicMeetingAudit:query")
    @GetMapping("/list/leader/audit")
    public TableDataInfo<TopicMeetingAuditVo> listAuditLeaderShip(TopicMeetingAuditBo bo, PageQuery pageQuery) {
        return topicMeetingAuditManager.pageTopicMeetingAuditWithLeaderShip(bo, pageQuery);
    }

    /**
     * 查询议题收集已审核列表 部门/分管领导审核页面
     */
    @SaCheckPermission("meeting:topicMeetingAudit:query")
    @GetMapping("/list/leader/audited")
    public TableDataInfo<TopicMeetingAuditVo> listAuditedLeaderShip(TopicMeetingAuditBo bo, PageQuery pageQuery) {
        return topicMeetingAuditManager.pageTopicMeetingAuditedWithLeaderShip(bo, pageQuery);
    }

    /**
     * 审核议题收集
     * @param auditInfoBo
     * @return
     */
    @SaCheckPermission("meeting:topicMeetingAudit:option")
    @PostMapping("/audit")
    public R<Void> auditTopicMeeting(@RequestBody TopicMeetingAuditInfoBo auditInfoBo) {
        topicMeetingAuditManager.auditTopicMeeting(auditInfoBo);
        return R.ok();
    }

    /**
     * 根据议题id查询议题审核记录
     * @param topicId
     * @return
     */
    @SaCheckPermission("meeting:topicMeetingAudit:query")
    @GetMapping("/flow/list/{topicId}")
    public R<TopicMeetingFlowRecordVo> getFlowRecordByTopicId(@NotEmpty(message = "主键不能为空")
                                                      @PathVariable("topicId") Long topicId) {
        return R.ok(topicMeetingFlowManager.getFlowRecordByTopicId(topicId));
    }

    /**
     * 更新议题 分管领导 审核通过的议题
     * @param editBo
     * @return
     */
    @PostMapping("/link/update")
    public R<Void> updateTopic(@RequestBody TopicMeetingLinkEditBo editBo) {
        topicMeetingManager.updateTopicAfterGroupPass(editBo);
        return R.ok();
    }

    /**
     * 退回议题 分管领导 审核通过的议题
     * @param recallBo
     * @return
     */
    @PostMapping("/link/recall")
    public R<Void> recallTopic(@RequestBody TopicMeetingLinkRecallBo recallBo) {
        topicMeetingAuditManager.recallTopicMeetingAfterGroupPass(recallBo);
        return R.ok();
    }

    /**
     * 更新议题排序 分管领导 审核通过的议题
     * @param planId 预会议id
     * @param ids 需要排序的议题id
     * @param pageQuery 分页信息
     * @return 结果
     */
    @PostMapping("/link/order/{ids}")
    public R<Void> updateTopicOrder(@PathVariable Long[] ids,Long planId, PageQuery pageQuery) {
        topicMeetingManager.updateAuditedTopicOrder(planId, Arrays.asList(ids), pageQuery.getCurrentPage(), pageQuery.getPageSize());
        return R.ok();
    }


    /**
     * 保存原文件增加批注后的附件
     * @param file
     * @param linkId
     * @return
     */
    @RepeatSubmit
    @PostMapping("/upload/comment")
    public R<Void> uploadCommentPdf(MultipartFile file,
                                    @RequestParam("linkId") Long linkId, @RequestParam("linkFileId") Long linkFileId,
                                    @RequestParam("originalName") String originalName) {
        topicFileCommentManager.uploadCommentPdf(file, linkId, linkFileId, originalName);
        return R.ok();
    }

    /**
     * 查找历次文件批注记录
     * @param linkId 关联的业务id
     * @param linkFileId 关联的文件id
     * @return
     */
    @GetMapping("/list/comment")
    public R<List<FileCommentVo>> listComment(@RequestParam("linkId") Long linkId, @RequestParam("linkFileId") Long linkFileId) {

        List<FileCommentVo> commentVoList = topicFileCommentManager.listComment(linkId, linkFileId);
        return R.ok(commentVoList);
    }

    /**
     * 删除批注文件
     *
     * @param id 主键
     */
    @Log(title = "议题收集", businessType = BusinessType.DELETE)
    @DeleteMapping("/comment/{id}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(topicFileCommentManager.removeCommentFile(id));
    }

    /**
     * 更新批注文件的文件名称
     * @param commentNameBo
     * @return
     */
    @Log(title = "议题收集", businessType = BusinessType.UPDATE)
    @PostMapping("/comment/update")
    public R<Void> updateCommentFileName(@RequestBody FileCommentNameBo commentNameBo) {
        topicFileCommentManager.updateCommentFileName(commentNameBo);
        return R.ok();
    }

    @GetMapping("/topic/meeting/{meetingId}")
    public R<List<TopicMeetingLink>> getListTopic(@PathVariable("meetingId") Long meetingId){
        return R.ok(meetingLinkService.getLinksByMeetingId(meetingId));
    }
}
