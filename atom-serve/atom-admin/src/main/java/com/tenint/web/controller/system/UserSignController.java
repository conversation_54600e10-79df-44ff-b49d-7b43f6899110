package com.tenint.web.controller.system;

import java.util.List;
import java.util.Arrays;

import cn.dev33.satoken.annotation.SaIgnore;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.annotation.RepeatSubmit;
import com.tenint.common.annotation.Log;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.system.domain.vo.UserSignVo;
import com.tenint.system.domain.bo.UserSignBo;
import com.tenint.system.manager.UserSignManager;
import com.tenint.common.core.page.TableDataInfo;

/**
 * 签名Controller
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/userSign")
public class UserSignController extends BaseController {

    private final UserSignManager userSignManager;

    /**
     * 查询签名列表
     */
    @SaCheckPermission("system:userSign:query")
    @GetMapping("/list")
    public TableDataInfo<UserSignVo> list(UserSignBo bo, PageQuery pageQuery) {
        return userSignManager.pageUserSignVo(bo, pageQuery);
    }
    /**
     * 获取签名详细信息
     *
     * @param userId 用户id
     */
    @SaCheckPermission("system:userSign:query")
    @GetMapping("/{userId}")
    public R<UserSignVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long userId) {
        return R.ok(userSignManager.getVoById(userId));
    }
    /**
     * 新增/修改签名
     */
//    @SaCheckPermission("system:userSign:option")
    @RepeatSubmit()
    @PostMapping()
    @SaIgnore
    public R<Void> addOrUpdate(@Validated(AddGroup.class) @RequestBody UserSignBo bo) {
        return toAjax(userSignManager.addOrUpdate(bo));
    }

    @GetMapping("/user/{uid}")
    @SaIgnore
    public R<String> getName(@PathVariable("uid") Long uid) {
        return R.ok("查询成功",userSignManager.getNameByUid(uid));
    }


}
