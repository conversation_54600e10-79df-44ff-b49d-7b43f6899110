package com.tenint.web.controller.meeting;

import java.util.List;
import java.util.Arrays;

import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.enums.BusinessType;
import com.tenint.meeting.domain.bo.TopicUploadBo;
import com.tenint.meeting.domain.query.TopicQuery;
import com.tenint.meeting.domain.vo.TopicTypeVo;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tenint.common.annotation.RepeatSubmit;
import com.tenint.common.annotation.Log;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tenint.common.core.controller.BaseController;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.common.utils.poi.ExcelUtil;
import com.tenint.meeting.domain.vo.TopicVo;
import com.tenint.meeting.domain.bo.TopicBo;
import com.tenint.meeting.manager.TopicManager;
import com.tenint.common.core.page.TableDataInfo;

/**
 * 议题Controller
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Deprecated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meeting/topic")
public class TopicController extends BaseController {

    private final TopicManager topicManager;

    /**
     * 查询议题列表
     */
    @GetMapping("/list")
    public TableDataInfo<TopicVo> list(TopicBo bo, PageQuery pageQuery) {
        return topicManager.pageTopicVo(bo, pageQuery);
    }
    /**
     * 获取议题详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("meeting:topic:query")
    @GetMapping("/{id}")
    public R<TopicVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(topicManager.getVoById(id));
    }

     /**
     * 新增议题
     */
    @SaCheckPermission("meeting:topic:add")
    @Log(title = "议题", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopicBo bo) {
        return toAjax(topicManager.saveByBo(bo));
    }

    /**
     * 修改议题
     */
    @SaCheckPermission("meeting:topic:edit")
    @Log(title = "议题", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopicBo bo) {
        return toAjax(topicManager.updateByBo(bo));
    }

    /**
     * 删除议题
     *
     * @param ids 主键串
     */
    @SaCheckPermission("meeting:topic:remove")
    @Log(title = "议题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(topicManager.removeWithValidByIds(Arrays.asList(ids), true));
    }


    /**
     * 获取议题类型
     *
     */
    @SaCheckPermission("meeting:topic:query")
    @GetMapping("/types")
    public R<List<TopicTypeVo>> getTypes() {
        return R.ok(topicManager.getTypes());
    }

    /**
     * 获取当前开放收集时间并且关联当前用户的议题
     */
//    @SaCheckPermission("meeting:topic:query")
//    @GetMapping("/related-topic")
//    public R<List<TopicVo>> getRelatedTopic() {
//        return R.ok(topicManager.getRelatedTopic());
//    }

    /**
     * 根据选择的标题id获取关联本人的类型
     */
    @SaCheckPermission("meeting:topic:query")
    @GetMapping("/related-type/{topicId}")
    public R<List<TopicTypeVo>> getRelatedType(@PathVariable("topicId") Long topicId) {
        return R.ok(topicManager.getRelatedTypeByTopicId(topicId));
    }

    /**
     * 分页查询所有关联有过本人操作记录的议题
     */
    @SaCheckPermission("meeting:topic:query")
    @GetMapping("/related-topic-list")
    public TableDataInfo<TopicVo> relatedTopic(TopicQuery query, PageQuery pageQuery) {
        return TableDataInfo.build(topicManager.pageRelatedTopic(query, pageQuery));
    }

    /**
     * 上传议题 - 新增
     */
    @Log(title = "上传议题 - 新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/topic-upload")
    public R<Void> topicUpload(@Validated(AddGroup.class) @RequestBody TopicUploadBo uploadBo) {
        topicManager.saveTopicUpload(uploadBo);
        return R.ok();
    }

    /**
     * 上传议题 - 提交
     */
    @Log(title = "上传议题 - 提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/topic-upload/submit/{topicId}/{typeId}")
    public R<Void> topicUpload(@PathVariable("topicId") Long topicId, @PathVariable("typeId") Long typeId) {
        topicManager.submitTopicUpload(topicId, typeId);
        return R.ok();
    }

}
