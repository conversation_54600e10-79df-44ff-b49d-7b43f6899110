projectFeignUrl: https://api01.crcc.cn/api/project/graphql

--- # 数据源配置
spring:
  #  jpa:
  #    open-in-view: false
  #    properties:
  #      hibernate:
  #        dialect: org.hibernate.dialect.DmDialect
  #    show-sql: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: dm.jdbc.driver.DmDriver
#          url: jdbc:dm://180.169.77.20:11238/MEETING?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&rewriteBatchedStatements=true
          url: jdbc:dm://192.168.2.24:5238/MEETING?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&rewriteBatchedStatements=true
#          url: jdbc:dm://101.91.214.82:55236/MEETING?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&rewriteBatchedStatements=true
          username: MEETING
          password: ChenBo19850306
        # 从库数据源
      #        slave:
      #          type: ${spring.datasource.type}
      #          lazy: true
      #          driverClassName: com.mysql.cj.jdbc.Driver
      #          url:
      #          username:
      #          password:

      #        oracle:
      #          driverClassName: oracle.jdbc.OracleDriver
      #          url: *************************************
      #          username: ROOT
      #          password: root
      #          drid:
      #            validationQuery: SELECT 1 FROM DUAL
      #        postgres:
      #          driverClassName: org.postgresql.Driver
      #          url: ******************************************************************************************************************************************
      #          username: root
      #          password: root
      #        sqlserver:
      #          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #          url: *****************************************************************************************************
      #          username: SA
      #          password: root

      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 10000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 60000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 600000
        # 连接测试query（配置检测连接是否有效）
        connectionTestQuery: SELECT 1

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 14
    #    # 密码
#    password: doudou..315
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

mybatis-plus:
  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

redisson:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: tenint
    # 最小空闲连接数
    connectionMinimumIdleSize: 2
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

oss:
  # 前端直传成功回调
  callback:
     enabled: false
     url: http://127.0.0.1:8086/system/oss/callback
   # 是否开启文件上传确认（使用redisson的延时队列实现上传文件后自动删除，保存相关业务时如果没有将上传文件的编号从队列中清理，将在指定时间后删除文件）
  expire:
    # 是否开启
    enable: false
    # 阻塞队列名称
    queueName: oss:expire:queue

# 系统基础配置
system:
  isRootAllow: true   # 是否允许root
  loginEncrypt: false
  defaultRootId: 1   # root 的默认id
  # 文件路径 示例（ Windows配置D:/uploadPath，Linux配置 /home/<USER>
  localResource: File
  # 获取ip地址开关
  addressEnabled: false
  # 是否开启定时任务（项目启动时自动开启）
  quartzAutoStartup: false

meilisearch:
  host: http://localhost:7700
  api-key: 7ctkeO0FTyoSHiMW5PW

#crcc-oauth2:
#  crcc:
#    redirect-uri: 'http://localhost:8888/login/oauth2/code/crcc'
#    login-url: 'http://localhost:3000/login'


crcc-oauth2:
  crcc:
    client-id: crcc24-rongmeiti
    client-secret: lET595A7iq2NkgXwP33oQSlFUzFTyn3Hjcl2JOe5
    client-name: Crcc Client
    provider: oidc-provider
    scope: openid+profile
    redirect-uri: 'http://localhost:8888/login/oauth2/code/crcc'
    login-url: 'http://localhost:3000/login'
    mobile-login-url: 'http://localhost:3333/mobile-meeting/login'
    client-authentication-method: basic
    authorization-grant-type: authorization_code
  hr:
    client-id: crcc24-rongmeiti
    client-secret: mR0hzn9ZPc8JsCytRV8FeKOpDez5vLXd67ZB0hDQ
    client-name: HR api Client
    provider: hr-api-provider
    scope: api_hr:crcc24,api_project:crcc24,api_gongcheng:crcc24
    client-authentication-method: basic
    authorization-grant-type: client_credentials
