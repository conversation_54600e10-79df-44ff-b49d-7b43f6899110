# 基础JDK镜像与证书配置
FROM openjdk:17-slim AS jdk-base

# 设置维护者信息
LABEL maintainer="Ftrybe"

# 安装所需工具，下载证书，之后清理不必要的包和文件
RUN apt-get update && apt-get install -y curl unzip && \
    curl -o CFCAGLOBALCA.zip "https://www.cfca.com.cn/upload/file/CFCAGLOBALCA20230418.zip" && \
    unzip CFCAGLOBALCA.zip && \
    keytool -import -noprompt -trustcacerts -file "CFCA 全球服务器证书链/CFCA_EV_ROOT.cer" -alias CFCA_EV_ROOT -cacerts -storepass changeit && \
    keytool -import -noprompt -trustcacerts -file "CFCA 全球服务器证书链/CFCA_EV_OCA.cer" -alias CFCA_EV_OCA -cacerts -storepass changeit && \
    keytool -import -noprompt -trustcacerts -file "CFCA 全球服务器证书链/CFCA_OV_OCA.cer" -alias CFCA_OV_OCA -cacerts -storepass changeit && \
    rm -rf CFCAGLOBALCA.zip /var/lib/apt/lists/* && \
    apt-get remove -y curl unzip && apt-get autoremove -y

# 应用构建镜像
FROM jdk-base AS app-builder

# 设置工作目录
WORKDIR /atom/server

# 设置环境变量
ENV SERVER_PORT=8888 LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS=""

# 暴露端口
EXPOSE ${SERVER_PORT}

# 创建必要的文件夹
RUN mkdir -p /atom/server/logs \
             /atom/server/temp \
             /atom/skywalking/agent

# 添加应用程序jar包
COPY ./target/atom-admin.jar ./app.jar

# 设置容器启动后执行的命令
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dserver.port=${SERVER_PORT}", "-jar", "app.jar", "-XX:+HeapDumpOnOutOfMemoryError", "-Xlog:gc*,:time,tags,level", "-XX:+UseZGC", "${JAVA_OPTS}"]
