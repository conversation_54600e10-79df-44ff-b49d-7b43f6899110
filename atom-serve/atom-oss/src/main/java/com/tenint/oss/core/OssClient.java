package com.tenint.oss.core;

import cn.hutool.core.util.IdUtil;
import com.tenint.common.utils.DateUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.file.FileUtils;
import com.tenint.oss.constant.OssConstant;
import com.tenint.oss.entity.UploadResult;
import com.tenint.oss.enums.AccessPolicyType;
import com.tenint.oss.enums.PolicyType;
import com.tenint.oss.exception.OssException;
import com.tenint.oss.properties.OssProperties;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.waiters.WaiterResponse;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.waiters.S3Waiter;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.time.Duration;
import java.util.*;

/**
 * S3 存储协议 所有兼容S3协议的云厂商均支持
 * 阿里云 腾讯云 七牛云 minio
 *
 * <AUTHOR>
 */
@Slf4j
public class OssClient {

    private final String configKey;

    private final OssProperties properties;

    private final S3Client client;

    private final S3Presigner presigner;

    public OssClient(String configKey, OssProperties ossProperties) {
        this.configKey = configKey;
        this.properties = ossProperties;
        try {
            AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(properties.getAccessKey(), properties.getSecretKey());
            Region region = Region.of(StringUtils.isNotBlank(properties.getRegion()) ? properties.getRegion() : Region.US_EAST_1.id());
            StaticCredentialsProvider staticCredentialsProvider = StaticCredentialsProvider.create(awsBasicCredentials);
            String prefix = Objects.equals(properties.getIsHttps(), "Y") ? "https://" : "http://";
            URI endpointOverride = URI.create(prefix + properties.getEndpoint());
            this.client = S3Client.builder()
                    .region(region)
                    .endpointOverride(endpointOverride)
                    .credentialsProvider(staticCredentialsProvider)
                    .build();

            createBucket();

            this.presigner = S3Presigner.builder()
                    .region(region)
                    .endpointOverride(endpointOverride)
                    .credentialsProvider(staticCredentialsProvider)
                    .build();

        } catch (Exception e) {
            if (e instanceof OssException) {
                throw e;
            }
            throw new OssException("配置错误! 请检查系统配置:[" + e.getMessage() + "]");
        }
    }

    public Map<String, String> getObjectMetadata(String bucket, String key) {
        HeadObjectRequest request = HeadObjectRequest.builder()
                .bucket(bucket)
                .key(key)
                .build();
        HeadObjectResponse headObjectResponse = client.headObject(request);

        return headObjectResponse.metadata();
    }

    public InputStream getObjectContent(String path) {
        path = path.replace(getUrl() + "/", "");
        GetObjectRequest getObjectRequest = GetObjectRequest.builder().bucket(properties.getBucketName()).key(path).build();
        ResponseBytes<GetObjectResponse> objectAsBytes = client.getObjectAsBytes(getObjectRequest);
        return objectAsBytes.asInputStream();
    }

    /**
     * 创建桶
     */
    private void createBucket() {
        try {
            S3Waiter s3Waiter = this.client.waiter();

            String bucketName = properties.getBucketName();

            HeadBucketRequest headerRequest = HeadBucketRequest.builder().bucket(bucketName).build();
            try {
                client.headBucket(headerRequest);
            } catch (NoSuchBucketException e) {
                CreateBucketRequest createBucketRequest = CreateBucketRequest.builder()
                        .bucket(properties.getBucketName())
                        .acl(BucketCannedACL.PUBLIC_READ)
                        .build();

                client.createBucket(createBucketRequest);

                PutBucketPolicyRequest policyRequest = PutBucketPolicyRequest.builder()
                        .bucket(bucketName)
                        .policy(getPolicy(bucketName, PolicyType.READ))
                        .build();

                client.putBucketPolicy(policyRequest);
                WaiterResponse<HeadBucketResponse> waiterResponse = s3Waiter.waitUntilBucketExists(headerRequest);
                waiterResponse.matched().response().ifPresent(System.out::println);
                System.out.println(bucketName + " is ready");
            }


        } catch (Exception e) {
            throw new OssException("创建Bucket失败, 请核对配置信息:[" + e.getMessage() + "]");
        }
    }

    public boolean checkPropertiesSame(OssProperties properties) {
        return this.properties.equals(properties);
    }

    /**
     * 上传
     *
     * @param bytes       字节
     * @param path        路径
     * @param contentType 内容类型
     * @return {@link UploadResult}
     */
    public UploadResult upload(byte[] bytes, String path, String contentType, String fileName) {
        try {

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(properties.getBucketName())
                    .contentType(contentType)
                    .contentLength((long) bytes.length)
                    .key(path)
                    .build();

            PutObjectResponse response = client.putObject(putObjectRequest, RequestBody.fromBytes(bytes));
            log.info("上传结果: " + path + " :" + response.eTag());

        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
        return UploadResult.builder()
                .url(getUrl() + "/" + path)
                .privateUrl(getPrivateUrl(path, fileName).toString())
                .filename(path).build();
    }

    public UploadResult multipartUpload(File file) {
        return multipartUpload(file, null, null);
    }

    /**
     * Upload an object in parts
     */
    public UploadResult multipartUpload(File file, String key, String fileName) {
        if (StringUtils.isBlank(key)) {
            key = getPath(properties.getPrefix(), FileUtils.getSuffix(file.getName()));
        }

        try {
            int mB = 1024 * 1024;
            CreateMultipartUploadRequest createMultipartUploadRequest = CreateMultipartUploadRequest.builder()
                    .bucket(properties.getBucketName())
                    .key(key)
                    .build();

            CreateMultipartUploadResponse response = client.createMultipartUpload(createMultipartUploadRequest);
            String uploadId = response.uploadId();

            int partSize = 10 * mB;

            long contentLength = file.length();
            int filePosition = 0;

            Collection<CompletedPart> completedParts = new ArrayList<>();
            try (FileInputStream fis = new FileInputStream(file)) {
                for (int i = 1; filePosition < contentLength; i++) {
                    partSize = (int) Math.min(partSize, (contentLength - filePosition));

                    UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .partNumber(i)
                            .build();

                    String etag = client.uploadPart(uploadPartRequest, RequestBody.fromInputStream(fis, partSize)).eTag();

                    CompletedPart part = CompletedPart.builder().partNumber(i).eTag(etag).build();
                    completedParts.add(part);

                    filePosition += partSize;
                }
            }


            CompletedMultipartUpload completedMultipartUpload = CompletedMultipartUpload.builder()
                    .parts(completedParts)
                    .build();


            CompleteMultipartUploadRequest completeMultipartUploadRequest =
                    CompleteMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .multipartUpload(completedMultipartUpload)
                            .build();

            client.completeMultipartUpload(completeMultipartUploadRequest);
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }

        return UploadResult.builder()
                .url(getUrl() + "/" + key)
                .privateUrl(getPrivateUrl(key, fileName).toString())
                .filename(key).build();
    }

    public ListObjectsV2Response listObject(String bucket) {
        ListObjectsV2Request request = ListObjectsV2Request
                .builder()
                .bucket(bucket)

                .build();
        ListObjectsV2Response listObjectsV2Response = client.listObjectsV2(request);
        return listObjectsV2Response;
    }

    public void delete(String path) {
        path = path.replace(getUrl() + "/", "");
        ArrayList<ObjectIdentifier> toDelete = new ArrayList<>();
        toDelete.add(ObjectIdentifier.builder().key(path).build());
        try {
            DeleteObjectsRequest dor = DeleteObjectsRequest.builder()
                    .bucket(properties.getBucketName())
                    .delete(Delete.builder().objects(toDelete).build())
                    .build();
            client.deleteObjects(dor);
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 获取私有URL链接
     *
     * @param objectKey 对象KEY
     * @param duration    授权时间
     */
    public URL getPrivateUrl(String objectKey, Duration duration, String filename) {
        if (StringUtils.isBlank(filename)) {
            filename = FileUtils.getName(objectKey);
        }
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(properties.getBucketName())
                .responseContentDisposition("attachment; filename=\"" + filename + "\"")
                .key(objectKey)
                .build();

        GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(duration)
                .getObjectRequest(getObjectRequest)
                .build();

        PresignedGetObjectRequest presignedGetObjectRequest = presigner.presignGetObject(getObjectPresignRequest);
        return presignedGetObjectRequest.url();
    }

    public URL getPrivateUrl(String objectKey, String filename) {
        return getPrivateUrl(objectKey, Duration.ofHours(12), filename);
    }

    /**
     * 获取当前桶权限类型
     *
     * @return 当前桶权限类型code
     */
    public AccessPolicyType getAccessPolicy() {
        return AccessPolicyType.getByType(properties.getAccessPolicy());
    }


    public UploadResult uploadByDefaultKey(byte[] data, String fileName, String contentType) {
        String suffix = FileUtils.getSuffix(fileName);
        return upload(data, getPath(properties.getPrefix(), suffix), contentType, fileName);
    }

    public String getUrl() {
        String domain = properties.getDomain();
        if (StringUtils.isNotBlank(domain)) {
            return domain;
        }
        String endpoint = properties.getEndpoint();
        String header = OssConstant.IS_HTTPS.equals(properties.getIsHttps()) ? "https://" : "http://";
        if (endpoint.contains("http")) {
            String trim = endpoint.trim();
            if (trim.indexOf("http://") == 0) {
                endpoint = endpoint.substring(7, trim.length());
            } else if (trim.indexOf("https://") == 0) {
                endpoint = endpoint.substring(8, trim.length());
            }
        }
        // 云服务商直接返回
        if (StringUtils.containsAny(endpoint, OssConstant.CLOUD_SERVICE)) {
            return header + properties.getBucketName() + "." + endpoint;
        }
        // minio 单独处理
        return header + endpoint + "/" + properties.getBucketName();
    }

    public String getPath(String prefix, String suffix) {
        // 生成uuid
        String uuid = IdUtil.fastSimpleUUID();
        // 文件路径
        String path = DateUtils.datePath() + "/" + uuid;
        if (StringUtils.isNotBlank(prefix)) {
            path = prefix + "/" + path;
        }
        if (suffix.startsWith(".")) {
            return path + suffix;
        }
        return path + "." + suffix;
    }


    public String getConfigKey() {
        return configKey;
    }

    private static String getPolicy(String bucketName, PolicyType policyType) {
        StringBuilder builder = new StringBuilder();
        builder.append("{\n\"Version\": \"2012-10-17\"");
        builder.append("\n\"Statement\": [\n{\n\"Action\": [\n");
        if (policyType == PolicyType.WRITE) {
            builder.append("\"s3:GetBucketLocation\",\n\"s3:ListBucketMultipartUploads\"\n");
        } else if (policyType == PolicyType.READ_WRITE) {
            builder.append("\"s3:GetBucketLocation\",\n\"s3:ListBucket\",\n\"s3:ListBucketMultipartUploads\"\n");
        } else {
            builder.append("\"s3:GetBucketLocation\"\n");
        }
        builder.append("],\n\"Effect\": \"Allow\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
        builder.append(bucketName);
        builder.append("\"\n},\n");
        if (policyType == PolicyType.READ) {
            builder.append("{\n\"Action\": [\n\"s3:ListBucket\"\n],\n\"Effect\": \"Deny\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
            builder.append(bucketName);
            builder.append("\"\n},\n");
        }
        builder.append("{\n\"Action\": ");
        switch (policyType) {
            case WRITE:
                builder.append("[\n\"s3:AbortMultipartUpload\",\n\"s3:DeleteObject\",\n\"s3:ListMultipartUploadParts\",\n\"s3:PutObject\"\n],\n");
                break;
            case READ_WRITE:
                builder.append("[\n\"s3:AbortMultipartUpload\",\n\"s3:DeleteObject\",\n\"s3:GetObject\",\n\"s3:ListMultipartUploadParts\",\n\"s3:PutObject\"\n],\n");
                break;
            default:
                builder.append("\"s3:GetObject\",\n");
                break;
        }
        builder.append("\"Effect\": \"Allow\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
        builder.append(bucketName);
        builder.append("/*\"\n}\n]}\n");
        return builder.toString();
    }

}
