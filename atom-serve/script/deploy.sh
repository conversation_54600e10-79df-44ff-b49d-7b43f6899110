#!/bin/bash

# 定义前端项目路径和Minio相关的常量
FRONTEND_DIR="../../atom-manage-ui"
MINIO_URL="http://101.91.214.82:59000"
MINIO_ALIAS="zt24j_meeting"

# 设置Minio Client别名
set_minio_alias() {
    mc alias set ${MINIO_ALIAS} ${MINIO_URL} deployment KMzchoVILz84MpgcLxhK
    echo "Minio别名设置完成。"
}

# 推送文件到Minio
push_to_minio() {
    local src_path=$1
    local dest_path="deployment/"
    mc cp --debug ${src_path} ${MINIO_ALIAS}/${dest_path}
    echo "文件已推送至Minio。"
}

# 从Minio拉取文件
pull_from_minio() {
    local src_path="deployment/$1"
    local dest_path="/tmp/$1"
    mc cp --debug ${MINIO_ALIAS}/${src_path} ${dest_path}
    echo "文件已从Minio拉取。"
}

# 解压文件到指定目录
extract_to_directory() {
    local archive_path=$1
    local target_dir=$2
    rm -rf /tmp/deployment
    unzip ${archive_path} -d /tmp/deployment
    rm -rf ${target_dir}/*
    mv -f /tmp/deployment/* ${target_dir}/
    echo "文件已解压到指定目录。"
}

# 重新部署后端
redeploy_backend() {
    cd /opt/tongweb/bin
    ./commandstool.sh redeploy kdyims
    cd -
    echo "后端部署完成。"
}

# 重启Nginx
restart_nginx() {
    sudo systemctl restart nginx
    echo "Nginx已重启。"
}

# 编译并打包前端代码
build_frontend() {
    cd ${FRONTEND_DIR}
    rm -rf ./dist ./dist.zip
    pnpm build
    zip -vr dist.zip dist
    du -sh dist.zip
    cd -
    echo "前端代码编译并打包完成。"
}

# 编译后端代码
build_backend() {
    mvn -f ../pom.xml clean package -Dmaven.test.skip=true -Pprod -pl atom-admin -am
    echo "后端代码编译完成。"
}

# 显示菜单并根据用户选择调用相应的函数
echo "请选择操作："
echo "1. 设置Minio Client alias"
echo "2. 推送后端服务到Minio"
echo "3. 推送前端压缩包到Minio"
echo "====================================="
echo "4. 从minio拉取并解压前端代码到Nginx部署目录"
echo "5. 从minio拉取并解压war包到东方通部署目录"
echo "====================================="
echo "6. 重新部署后端"
echo "7. 重启Nginx"
echo "====================================="
echo "8. 编译并打包前端代码"
echo "9. 编译后端代码"
read -p "请输入选择（1-9）：" choice

case $choice in
    1) set_minio_alias ;;
    2) push_to_minio "../atom-admin/target/atom-admin.war" ;;
    3) push_to_minio "${FRONTEND_DIR}/dist.zip" ;;
    4)
        pull_from_minio "dist.zip"
        extract_to_directory "/tmp/dist.zip" "/data/docker/nginx/html/admin"
        ;;
    5)
        pull_from_minio "atom-admin.war"
        extract_to_directory "/tmp/atom-admin.war" "/data/server/"
        ;;
    6) redeploy_backend ;;
    7) restart_nginx ;;
    8) build_frontend ;;
    9) build_backend ;;
    *) echo "无效的选择。" ;;
esac
