version: '3'

services:
  # 此镜像仅用于测试 正式环境需自行安装数据库
  # SID: XE user: system password: oracle
  oracle:
    image: tekintian/oracle12c:latest
    container_name: oracle
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      DBCA_TOTAL_MEMORY: 16192
    ports:
      - "18080:8080"
      - "1521:1521"
    volumes:
      # 数据挂载
      - "/docker/oracle/data:/u01/app/oracle"
    network_mode: "host"

  # 此镜像仅用于测试 正式环境需自行安装数据库
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2017-latest
    container_name: sqlserver
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      ACCEPT_EULA: "Y"
      SA_PASSWORD: "Ruoyi@123"
    ports:
      - "1433:1433"
    volumes:
      # 数据挂载
      - "/docker/sqlserver/data:/var/opt/mssql"
    network_mode: "host"

  postgres:
    image: postgres:14.2
    container_name: postgres
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - /docker/postgres/data:/var/lib/postgresql/data
    network_mode: "host"

  postgres13:
    image: postgres:13.6
    container_name: postgres13
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
      POSTGRES_DB: postgres
    ports:
      - "5433:5432"
    volumes:
      - /docker/postgres13/data:/var/lib/postgresql/data
    network_mode: "host"
  dm8:
    image: atom/dm:8
    container_name: dm8
    ports:
      - "5236:5236"
    network_mode: "host"

  mysql:
    image: mysql:8.0.33
    container_name: mysql
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # root 密码
      MYSQL_ROOT_PASSWORD: root
      # 初始化数据库(后续的初始化sql会在这个库执行)
      MYSQL_DATABASE: ry-vue
    ports:
      - "3306:3306"
    volumes:
      # 数据挂载
      - /docker/mysql/data/:/var/lib/mysql/
      # 配置挂载
      - /docker/mysql/conf/:/etc/mysql/conf.d/
    command:
      # 将mysql8.0默认密码策略 修改为 原先 策略 (mysql8.0对其默认策略做了更改 会导致密码无法匹配)
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
    privileged: true
    network_mode: "host"

