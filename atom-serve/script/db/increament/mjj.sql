-- 1
alter table T_MEETING_USER_FILE
    add IS_DISTRIBUTE CHAR(1);

comment
on column T_MEETING_USER_FILE.IS_DISTRIBUTE is '是否分发  1：是  0：否';

-- 2
alter table "T_MEETING" modify "MEETING_TYPE" BIGINT;

-- 3
alter table T_FILE_ENTITY
    add FILE_STATUS varchar(10);

comment
on column T_FILE_ENTITY.FILE_STATUS is '附件提交状态 0编辑 1提交 2通过 9拒绝';

-- 4
alter table T_MEETING drop DEAD_TIME;

alter table .T_MEETING
    add DEAD_TIME TIMESTAMP;

comment
on column T_MEETING.DEAD_TIME is '上传资料截止时间';

-- 5
alter table T_MEETING
    add IS_RE_UPLOAD CHAR(1);

comment
on column T_MEETING.IS_RE_UPLOAD is '是否开启重新上传';

-- 6
alter table MEETING.T_MEETING_JOINER
    add USER_TYPE VARCHAR(5);

comment
on column MEETING.T_MEETING_JOINER.USER_TYPE is '用户类型';

-- 7 添加角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, DATA_SCOPE, MENU_CHECK_STRICTLY, ORG_CHECK_STRICTLY, STATUS, IS_ACTIVE, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1769581471934193666, '参会人员', 'meeting_attend', 10, '1', 1, 1, '0', '1', 1, '2024-03-18', 1, '2024-04-17', null);
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, DATA_SCOPE, MENU_CHECK_STRICTLY, ORG_CHECK_STRICTLY, STATUS, IS_ACTIVE, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1779769411197734913, '协作人员', 'meeting_join', 11, '1', 1, 1, '0', '1', 1, '2024-04-15', 1, '2024-04-15', null);

-- 8 添加菜单
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779711761017790465, '会议文件上传', 1764895135628181506, 2, '/meeting/file/joiner', 'meeting/fileJoiner/index', '', 1, 0, 'C', '0', '0', 'meeting:file:list', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, 'FileJoiner');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779768439650127874, '文件查询', 1779711761017790465, 1, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:query', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779768564304842754, '文件新增', 1779711761017790465, 2, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:add', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779768641572311042, '文件修改', 1779711761017790465, 3, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:edit', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779768766889725954, '文件删除', 1779711761017790465, 4, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:remove', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779768844979277825, '文件导出', 1779711761017790465, 5, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:export', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779770809519968258, '协作会议', 1764895135628181506, 1, '', '', '', 1, 0, 'F', '0', '0', 'meeting:manager:cooperate', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779811530801258497, '文件提交', 1764883148399624192, 6, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:submit', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1779811613131251713, '文件提交', 1779711761017790465, 6, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:submit', '', 1, '2024-04-15', 1, '2024-04-15', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1780438202638409730, '会议文件查询', 1764895135628181506, 3, '/file/query', 'meeting/fileQuery/index', '', 1, 0, 'C', '0', '0', 'meeting:file:list', '', 1, '2024-04-17', 1, '2024-04-17', '', '0', '', ' ', null, 'FileQuery');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1780438450882486274, '文件查询', 1780438202638409730, 1, '', '', '', 1, 0, 'F', '0', '0', 'meeting:file:query', '', 1, '2024-04-17', 1, '2024-04-17', '', '0', '', ' ', null, '');

-- 9 新增表
create table T_FILE_READ
(
    ID           BIGINT not null,
    CREATOR_ID   BIGINT,
    CREATE_TIME  timestamp,
    UPDATER_ID   BIGINT,
    UPDATE_TIME  timestamp,
    IS_ACTIVE    CHAR(1),
    MEETING_ID   BIGINT,
    MEETING_TYPE BIGINT,
    FILE_ID      BIGINT,
    USER_ID      BIGINT,
    IS_READ      BIGINT
);

comment
on table T_FILE_READ is '文件阅读表-发布后会议的阅读人员才会进此表';

comment
on column T_FILE_READ.ID is '主键';

comment
on column T_FILE_READ.CREATOR_ID is '创建人id';

comment
on column T_FILE_READ.CREATE_TIME is '创建时间';

comment
on column T_FILE_READ.UPDATER_ID is '更新人id';

comment
on column T_FILE_READ.UPDATE_TIME is '更新时间';

comment
on column T_FILE_READ.IS_ACTIVE is '有效位';

comment
on column T_FILE_READ.MEETING_ID is '关联会议id';

comment
on column T_FILE_READ.MEETING_TYPE is '会议资源目录';

comment
on column T_FILE_READ.FILE_ID is '附件id';

comment
on column T_FILE_READ.USER_ID is '阅读人员id';

comment
on column T_FILE_READ.IS_READ is '阅读次数';

-- 7.25新增
alter table T_TOPIC_USER
    add USER_ID BIGINT(19);

comment
on column T_TOPIC_USER.USER_ID is '用户id';

ALTER TABLE T_TOPIC_USER RENAME COLUMN TARGET_TYPE TO TYPE_NAME;
ALTER TABLE T_TOPIC_USER MODIFY TYPE_NAME VARCHAR(255);

ALTER TABLE T_TOPIC_USER RENAME COLUMN TARGET_ID TO TYPE_ID;

alter table T_TOPIC_FILE
    add USER_ID BIGINT;

comment
on column T_TOPIC_FILE.USER_ID is '用户id';

-- 9月新增
-- 1
create table T_TOPIC_MEETING
(
    ID             BIGINT not null,
    PLAN_ID        BIGINT,
    NOTICE_CODE    VARCHAR(100),
    REMARK         VARCHAR(400),
    IS_ACTIVE      CHAR(1),
    CREATOR_ID     BIGINT,
    CREATE_TIME    DATETIME,
    UPDATER_ID     BIGINT,
    UPDATE_TIME    DATETIME,
    MEET_TYPE      BIGINT,
    STATUS         VARCHAR(2),
    TOPIC_TITLE    VARCHAR(200),
    REPORT_USER_ID BIGINT
);

comment
on table T_TOPIC_MEETING is '议题收集';

comment
on column T_TOPIC_MEETING.ID is '主键';

comment
on column T_TOPIC_MEETING.PLAN_ID is '预召开会议id';

comment
on column T_TOPIC_MEETING.NOTICE_CODE is '事项编码';

comment
on column T_TOPIC_MEETING.REMARK is '补充说明';

comment
on column T_TOPIC_MEETING.IS_ACTIVE is '有效位';

comment
on column T_TOPIC_MEETING.CREATOR_ID is '创建人id';

comment
on column T_TOPIC_MEETING.CREATE_TIME is '创建时间';

comment
on column T_TOPIC_MEETING.UPDATER_ID is '更新人id';

comment
on column T_TOPIC_MEETING.UPDATE_TIME is '更新时间';

comment
on column T_TOPIC_MEETING.MEET_TYPE is '会议类型';

comment
on column T_TOPIC_MEETING.STATUS is '议题状态';

comment
on column T_TOPIC_MEETING.TOPIC_TITLE is '议题名称';

comment
on column T_TOPIC_MEETING.REPORT_USER_ID is '汇报人id';


-- 2
create table T_TOPIC_MEETING_FILE
(
    ID            BIGINT not null,
    TOPIC_MEET_ID BIGINT,
    FILE_TYPE     VARCHAR(2),
    OSS_ID        BIGINT,
    NAME          VARCHAR(255),
    IS_ACTIVE     CHAR(1),
    CREATOR_ID    BIGINT,
    CREATE_TIME   DATETIME,
    UPDATER_ID    BIGINT,
    UPDATE_TIME   DATETIME
);

comment
on table T_TOPIC_MEETING_FILE is '议题收集附件表';

comment
on column T_TOPIC_MEETING_FILE.ID is '主键';

comment
on column T_TOPIC_MEETING_FILE.TOPIC_MEET_ID is '关联的议题收集id';

comment
on column T_TOPIC_MEETING_FILE.FILE_TYPE is '文件类型 1 正文附件 2 普通附件';

comment
on column T_TOPIC_MEETING_FILE.NAME is '附件名称';

comment
on column T_TOPIC_MEETING_FILE.IS_ACTIVE is '有效位';

comment
on column T_TOPIC_MEETING_FILE.CREATOR_ID is '创建人id';

comment
on column T_TOPIC_MEETING_FILE.CREATE_TIME is '创建时间';

comment
on column T_TOPIC_MEETING_FILE.UPDATER_ID is '更新人id';

comment
on column T_TOPIC_MEETING_FILE.UPDATE_TIME is '更新时间';

-- 3
create table T_TOPIC_MEETING_AUDIT
(
    ID            INTEGER not null,
    USER_TYPE     VARCHAR(2),
    USER_ID       BIGINT,
    STATUS        VARCHAR(2),
    REMARK        VARCHAR(200),
    TOPIC_MEET_ID BIGINT,
    IS_ACTIVE     CHAR(1),
    CREATOR_ID    BIGINT,
    CREATE_TIME   DATETIME,
    UPDATER_ID    BIGINT,
    UPDATE_TIME   DATETIME
);

comment
on table T_TOPIC_MEETING_AUDIT is '会议收集审核人员';

comment
on column T_TOPIC_MEETING_AUDIT.ID is '主键';

comment
on column T_TOPIC_MEETING_AUDIT.USER_TYPE is '用户类型 1 部门领导 2 分管领导';

comment
on column T_TOPIC_MEETING_AUDIT.USER_ID is '用户id';

comment
on column T_TOPIC_MEETING_AUDIT.STATUS is '审批状态';

comment
on column T_TOPIC_MEETING_AUDIT.REMARK is '备注';

comment
on column T_TOPIC_MEETING_AUDIT.TOPIC_MEET_ID is '关联的议题收集id';

comment
on column T_TOPIC_MEETING_AUDIT.IS_ACTIVE is '有效位';

comment
on column T_TOPIC_MEETING_AUDIT.CREATOR_ID is '创建人id';

comment
on column T_TOPIC_MEETING_AUDIT.CREATE_TIME is '创建时间';

comment
on column T_TOPIC_MEETING_AUDIT.UPDATER_ID is '更新人id';

comment
on column T_TOPIC_MEETING_AUDIT.UPDATE_TIME is '更新时间';

--
alter table T_TOPIC_MEETING
    add REPORT_USER_NAME VARCHAR2(30);

comment
on column T_TOPIC_MEETING.REPORT_USER_NAME is '汇报人姓名';

alter table T_TOPIC_MEETING
    add TOPIC_ORDER INTEGER default 0;

comment
on column T_TOPIC_MEETING.TOPIC_ORDER is '议题排序';

alter table T_TOPIC_MEETING MODIFY MEET_TYPE VARCHAR2(2);

alter table T_TOPIC_MEETING
    add CREATOR_DEPT VARCHAR2(150);

comment
on column T_TOPIC_MEETING.CREATOR_DEPT is '上传人单位';

alter table T_TOPIC_MEETING_AUDIT MODIFY ID BIGINT;

UPDATE SYS_MENU SET MENU_NAME = '议题收集', PARENT_ID = 1764895135628181506, ORDER_NUM = 0, PATH = '/meeting/topic-plan', COMPONENT = 'meeting/topicMeeting/index', QUERY_PARAM = null, IS_FRAME = 1, IS_CACHE = 0, MENU_TYPE = 'C', VISIBLE = '0', STATUS = '0', PERMS = 'meeting:topicMeeting:query', ICON = '', CREATOR_ID = 1, CREATE_TIME = '2024-07-16', UPDATER_ID = 1, UPDATE_TIME = '2024-08-30', REMARK = '议题菜单', SHOW_PARENT = '0', TRANSITION = null, HIDDEN_TAG = null, DYNAMIC_LEVEL = null, ROUTER_NAME = 'TopicMeeting' WHERE MENU_ID = 1813114655556677632;

-- 文件批注表
create table T_FILE_COMMENT
(
    ID            BIGINT not null,
    LINK_ID       BIGINT,
    LINK_FILE_ID  NUMBER(20),
    OSS_ID        NUMBER(20),
    ORIGINAL_NAME VARCHAR2(755) default '',
    CREATE_TIME   TIMESTAMP,
	CREATOR_ID BIGINT,
	UPDATE_TIME TIMESTAMP,
	UPDATER_ID BIGINT,
	LINK_TYPE VARCHAR2(2)
);

comment
on table T_FILE_COMMENT is '议题批注文件';

comment
on column T_FILE_COMMENT.ID is '主键';

comment
on column T_FILE_COMMENT.LINK_ID is '关联的业务id';

comment
on column T_FILE_COMMENT.LINK_FILE_ID is '关联的原文件id';

comment
on column T_FILE_COMMENT.OSS_ID is '当前文件的ossid';

comment
on column T_FILE_COMMENT.ORIGINAL_NAME is '文件名';

comment
on column T_FILE_COMMENT.CREATE_TIME is '创建时间';

comment
on column T_FILE_COMMENT.CREATOR_ID is '创建人id';

comment
on column T_FILE_COMMENT.UPDATE_TIME is '更新时间';

comment
on column T_FILE_COMMENT.UPDATER_ID is '更新人id';

comment
on column T_FILE_COMMENT.LINK_TYPE is '关联的业务类型';


-- 督查督办
create table T_TASK
(
    ID            BIGINT not null,
    TASK_TYPE     VARCHAR2(2),
    TASK_TITLE    VARCHAR2(255),
    DEPT_ID       BIGINT,
    USER_ID       BIGINT,
    COMPLETE_TIME TIMESTAMP,
    TASK_CONTENT VARCHAR2(500),
    IS_ACTIVE VARCHAR2(2),
    CREATE_TIME TIMESTAMP,
    CREATOR_ID BIGINT,
    UPDATE_TIME TIMESTAMP,
    UPDATER_ID BIGINT,
    DEPT_NAME VARCHAR2(300),
    TASK_STATUS VARCHAR2(2)
);

comment
on table T_TASK is '督查督办主表';

comment
on column T_TASK.ID is '主键';

comment
on column T_TASK.TASK_TYPE is '任务类型 1来自会议 2新建任务';

comment
on column T_TASK.TASK_TITLE is '督办事项名称';

comment
on column T_TASK.DEPT_ID is '主责部门id';

comment
on column T_TASK.USER_ID is '部门负责人id';

comment
on column T_TASK.COMPLETE_TIME is '办结时间';

comment
on column T_TASK.TASK_CONTENT is '任务内容';

comment
on column T_TASK.CREATE_TIME is '创建时间';

comment
on column T_TASK.CREATOR_ID is '创建人id';

comment
on column T_TASK.UPDATE_TIME is '更新时间';

comment
on column T_TASK.UPDATER_ID is '更新人id';

comment
on column T_TASK.DEPT_NAME is '分管部门名称';

comment
on column T_TASK.TASK_STATUS is '任务状态';



create table T_TASK_DETAIL
(
    ID             BIGINT not null,
    TASK_ID        BIGINT,
    TASK_TYPE      VARCHAR2(2),
    SOURCE_TYPE    VARCHAR2(2),
    MEETING_ID     BIGINT,
    SOURCE_CONTENT VARCHAR2(400),
    IS_ACTIVE      CHAR(1),
    CREATOR_ID     BIGINT,
    CREATE_TIME    TIMESTAMP,
    UPDATER_ID BIGINT,
    UPDATE_TIME TIMESTAMP
);

comment
on table T_TASK_DETAIL is '督查督办明细子表';

comment
on column T_TASK_DETAIL.ID is '主键';

comment
on column T_TASK_DETAIL.TASK_ID is '任务主表id';

comment
on column T_TASK_DETAIL.TASK_TYPE is '任务类型 1来自会议 2新建任务';

comment
on column T_TASK_DETAIL.SOURCE_TYPE is '任务来源 1引用文件 2手动输入';

comment
on column T_TASK_DETAIL.MEETING_ID is '关联的会议ID';

comment
on column T_TASK_DETAIL.SOURCE_CONTENT is '任务来源-手动输入';

comment
on column T_TASK_DETAIL.CREATOR_ID is '创建人id';

comment
on column T_TASK_DETAIL.CREATE_TIME is '创建时间';

comment
on column T_TASK_DETAIL.UPDATER_ID is '更新人';

comment
on column T_TASK_DETAIL.UPDATE_TIME is '更新时间';



create table T_TASK_FILE
(
    ID          BIGINT not null,
    TASK_ID     BIGINT,
    FILE_TYPE   VARCHAR2(2),
    OSS_ID      BIGINT,
    NAME        VARCHAR2(255),
    IS_ACTIVE   CHAR(1),
    CREATOR_ID  BIGINT,
    CREATE_TIME TIMESTAMP,
    UPDATER_ID BIGINT,
    UPDATE_TIME TIMESTAMP
);

comment
on table T_TASK_FILE is '督查督办附件表';

comment
on column T_TASK_FILE.ID is '主键';

comment
on column T_TASK_FILE.TASK_ID is '督查督办ID';

comment
on column T_TASK_FILE.FILE_TYPE is '附件类型 1 任务附件 2 新建任务-引用文件';

comment
on column T_TASK_FILE.NAME is '附件名称';

comment
on column T_TASK_FILE.CREATOR_ID is '创建人';

comment
on column T_TASK_FILE.CREATE_TIME is '创建时间';

comment
on column T_TASK_FILE.UPDATER_ID is '更新人';

comment
on column T_TASK_FILE.UPDATE_TIME is '更新时间';



create table T_TASK_DEPT_MAJOR
(
    ID          BIGINT not null,
    DEPT_ID     BIGINT,
    DEPT_NAME   VARCHAR2(300),
    USER_ID     BIGINT,
    IS_ACTIVE   CHAR(1),
    CREATOR_ID  BIGINT,
    CREATE_TIME TIMESTAMP,
    UPDATER_ID BIGINT,
    UPDATE_TIME TIMESTAMP
);

comment
on table T_TASK_DEPT_MAJOR is '督查督办部门负责人';

comment
on column T_TASK_DEPT_MAJOR.ID is '主键';

comment
on column T_TASK_DEPT_MAJOR.DEPT_ID is '部门id';

comment
on column T_TASK_DEPT_MAJOR.DEPT_NAME is '部门名称';

comment
on column T_TASK_DEPT_MAJOR.USER_ID is '用户id';

comment
on column T_TASK_DEPT_MAJOR.CREATOR_ID is '创建人id';

comment
on column T_TASK_DEPT_MAJOR.CREATE_TIME is '创建时间';

comment
on column T_TASK_DEPT_MAJOR.UPDATER_ID is '更新人id';

comment
on column T_TASK_DEPT_MAJOR.UPDATE_TIME is '更新时间';

