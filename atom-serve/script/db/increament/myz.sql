create table MEETING.T_TOPIC_MEETING_PLAN
(
    ID          number(19) not null,
    TITLE       VARCHAR(255),
    LAST_TIME   timestamp,
    TYPE        varchar(255),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_TOPIC_MEETING_PLAN is '预约表';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.TITLE is '标题';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.LAST_TIME is '截至时间';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.TYPE is '类型';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.CREATOR_ID is '创建id';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.CREATE_TIME is '创建时间';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.UPDATER_ID is '修改人';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_TOPIC_MEETING_PLAN.IS_ACTIVE is '是否删除';






-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1828695335363985408, '预召开会议', '1764895135628181506', '1', '/meeting/topicMeetingPlan','TopicMeetingPlan', 'meeting/topicMeetingPlan/index', 1, 0, 'C', '0', '0', 'business:topicMeetingPlan:list', '', 1, sysdate, null, null, '预约菜单', '0');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, router_name, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1828695335363985409, '查询', 1828695335363985408, '1',  '#','', '', 1,  0, 'F', '0', '0', 'meeting:topicMeetingPlan:query',        '', 1, sysdate, null, null, '','0');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1828695335363985410, '新增', 1828695335363985408, '2',  '#','', '', 1,  0, 'F', '0', '0', 'meeting:topicMeetingPlan:add',          '', 1, sysdate, null, null, '','0');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1828695335363985411, '修改', 1828695335363985408, '3',  '#','', '', 1,  0, 'F', '0', '0', 'meeting:topicMeetingPlan:edit',         '', 1, sysdate, null, null, '','0');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1828695335363985412, '删除', 1828695335363985408, '4',  '#','', '', 1,  0, 'F', '0', '0', 'meeting:topicMeetingPlan:remove',       '', 1, sysdate, null, null, '','0');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1828695335363985413, '导出', 1828695335363985408, '5',  '#','', '', 1,  0, 'F', '0', '0', 'meeting:topicMeetingPlan:export',       '', 1, sysdate, null, null, '','0');

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1829063139106750464, '签名', '1', '1', '/sign','sign', 'system/sign/index', 1, 0, 'C', '0', '0', 'system:userSign:list', '', 1, sysdate, null, null, '签名菜单', '0');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, router_name, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1829063139106750465, '签名查询', 1829063139106750464, '1',  '#','', '', 1,  0, 'F', '0', '0', 'system:userSign:query',        '', 1, sysdate, null, null, '','0');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, router_name, component, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent)
values(1829063139106750466, '签名新增', 1829063139106750464, '2',  '#','', '', 1,  0, 'F', '0', '0', 'system:userSign:add',          '', 1, sysdate, null, null, '','0');




create table MEETING.SYS_USER_SIGN
(
    ID          number(19) not null,
    USER_ID     number(19),
    SIGN_PIC    CLOB,
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.SYS_USER_SIGN is '签名表';

comment
on column MEETING.SYS_USER_SIGN.ID is '主键';

comment
on column MEETING.SYS_USER_SIGN.USER_ID is '用户id';

comment
on column MEETING.SYS_USER_SIGN.SIGN_PIC is '签名base64码';

comment
on column MEETING.SYS_USER_SIGN.CREATOR_ID is '创建id';

comment
on column MEETING.SYS_USER_SIGN.CREATE_TIME is '创建时间';

comment
on column MEETING.SYS_USER_SIGN.UPDATER_ID is '修改人';

comment
on column MEETING.SYS_USER_SIGN.UPDATE_TIME is '修改时间';

comment
on column MEETING.SYS_USER_SIGN.IS_ACTIVE is '是否删除';



alter table MEETING.T_MEETING
    add USER_NAME VARCHAR(255);

comment
on column MEETING.T_MEETING.USER_NAME is '主持人姓名';


alter table MEETING.T_MEETING
    add IS_VOTE varchar(20);

comment
on column MEETING.T_MEETING.IS_VOTE is '是否开启投票功能';


alter table MEETING.T_FILE_ENTITY
    add MEETING_ID number(19);

comment
on column MEETING.T_FILE_ENTITY.MEETING_ID is '会议ID';


alter table MEETING.T_MEETING
    add MEETING_STATUS VARCHAR(20);

comment
on column MEETING.T_MEETING.MEETING_STATUS is '会议状态';


alter table MEETING.T_MEETING
    add MEETING_PLAN_ID number(19);

comment
on column MEETING.T_MEETING.MEETING_PLAN_ID is '预会议ID';




create table MEETING.T_MEETING_USER_SIGN
(
    ID          number(19) not null,
    USER_ID     number(19),
    SIGN_PIC    CLOB,
    MEETING_ID  number(19),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_MEETING_USER_SIGN is '会议签名表';

comment
on column MEETING.T_MEETING_USER_SIGN.ID is '主键';

comment
on column MEETING.T_MEETING_USER_SIGN.USER_ID is '用户id';

comment
on column MEETING.T_MEETING_USER_SIGN.MEETING_ID is '会议id';

comment
on column MEETING.T_MEETING_USER_SIGN.SIGN_PIC is '签名base64码';

comment
on column MEETING.T_MEETING_USER_SIGN.CREATOR_ID is '创建id';

comment
on column MEETING.T_MEETING_USER_SIGN.CREATE_TIME is '创建时间';

comment
on column MEETING.T_MEETING_USER_SIGN.UPDATER_ID is '修改人';

comment
on column MEETING.T_MEETING_USER_SIGN.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_MEETING_USER_SIGN.IS_ACTIVE is '是否删除';





create table MEETING.T_TOPIC_MEETING_LINK
(
    ID               number(19) not null,
    PLAN_ID          number(19),
    MEETING_ID       number(19),
    TOPIC_ID         number(19),
    IS_ACTIVE        CHAR(1),
    CREATE_TIME      timestamp,
    TOPIC_TITLE      VARCHAR(200),
    REPORT_USER_ID   number(19),
    REPORT_USER_NAME VARCHAR(30),
    TOPIC_ORDER      INTEGER default 0
);

comment
on table MEETING.T_TOPIC_MEETING_LINK is '议题会议表';

comment
on column MEETING.T_TOPIC_MEETING_LINK.ID is '主键';

comment
on column MEETING.T_TOPIC_MEETING_LINK.PLAN_ID is '预召开会议id';

comment
on column MEETING.T_TOPIC_MEETING_LINK.TOPIC_ID is '议题id';

comment
on column MEETING.T_TOPIC_MEETING_LINK.MEETING_ID is '会议id';

comment
on column MEETING.T_TOPIC_MEETING_LINK.IS_ACTIVE is '有效位';

comment
on column MEETING.T_TOPIC_MEETING_LINK.CREATE_TIME is '创建时间';


comment
on column MEETING.T_TOPIC_MEETING_LINK.TOPIC_TITLE is '议题名称';

comment
on column MEETING.T_TOPIC_MEETING_LINK.REPORT_USER_ID is '汇报人id';

comment
on column MEETING.T_TOPIC_MEETING_LINK.REPORT_USER_NAME is '汇报人姓名';

comment
on column MEETING.T_TOPIC_MEETING_LINK.TOPIC_ORDER is '议题排序';





INSERT INTO MEETING.T_FILE_ENTITY (ID, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, PARENT_ID, ENTITY_TYPE, NAME, FILE_SIZE, ANCESTORS, IS_ACTIVE, STATUS, FLAG, REMARK, OSS_ID, FILE_ORDER, FILE_TYPE, ORG_ID, FILE_STATUS, MEETING_ID) VALUES (1, 1, '2024-03-13 13:12:39.000000', 1, '2024-03-26 17:53:19.490000', 0, 'folder', '党委常委会议', null, '0', '1', null, '0', null, null, 1, null, null, null, null);
INSERT INTO MEETING.T_FILE_ENTITY (ID, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, PARENT_ID, ENTITY_TYPE, NAME, FILE_SIZE, ANCESTORS, IS_ACTIVE, STATUS, FLAG, REMARK, OSS_ID, FILE_ORDER, FILE_TYPE, ORG_ID, FILE_STATUS, MEETING_ID) VALUES (2, 1, '2024-03-13 13:12:39.000000', 1, '2024-03-13 13:12:43.000000', 0, 'folder', '总经理办公会议', null, '0', '1', null, '0', null, null, 2, null, null, null, null);
INSERT INTO MEETING.T_FILE_ENTITY (ID, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, PARENT_ID, ENTITY_TYPE, NAME, FILE_SIZE, ANCESTORS, IS_ACTIVE, STATUS, FLAG, REMARK, OSS_ID, FILE_ORDER, FILE_TYPE, ORG_ID, FILE_STATUS, MEETING_ID) VALUES (3, 1, '2024-03-13 13:12:39.000000', 1, '2024-03-13 13:12:43.000000', 0, 'folder', '董事会', null, '0', '1', null, '0', null, null, 3, null, null, null, null);
INSERT INTO MEETING.T_FILE_ENTITY (ID, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, PARENT_ID, ENTITY_TYPE, NAME, FILE_SIZE, ANCESTORS, IS_ACTIVE, STATUS, FLAG, REMARK, OSS_ID, FILE_ORDER, FILE_TYPE, ORG_ID, FILE_STATUS, MEETING_ID) VALUES (4, 1, '2024-03-13 13:12:39.000000', 1, '2024-03-13 13:12:39.000000', 0, 'folder', '党委中心组学习', null, '0', '1', null, '0', null, null, 4, null, null, null, null);


-- 2024-9-9
INSERT INTO MEETING.SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, DATA_SCOPE, MENU_CHECK_STRICTLY, ORG_CHECK_STRICTLY, STATUS, IS_ACTIVE, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1831262487412510721, '列席人员', 'meeting_invitation', 12, '1', 1, 1, '0', '1', 1, '2024-09-04', 1, '2024-09-04', null);


-- 2024-09-18
alter table MEETING.T_TOPIC_MEETING_LINK
    add CREATOR_DEPT VARCHAR(200);

comment
on column MEETING.T_TOPIC_MEETING_LINK.CREATOR_DEPT is '上传单位';




-- 2024-09-25
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1838759851593338881, '会议地址管理', 1, 1, '/meeting-place', 'system/dict/meetingPlace/index', '', 1, 0, 'C', '0', '0', 'system:dict:list', '', 1, '2024-09-25', 1, '2024-09-25', '', '0', '', ' ', null, 'meeting-place');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1838870646633336834, '新增', 1838759851593338881, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '', 1, '2024-09-25', 1, '2024-09-25', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1838870756591210498, '删除', 1838759851593338881, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '', 1, '2024-09-25', 1, '2024-09-25', '', '0', '', ' ', null, '');
INSERT INTO SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1838870882827177985, '修改', 1838759851593338881, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '', 1, '2024-09-25', 1, '2024-09-25', '', '0', '', ' ', null, '');

INSERT INTO SYS_DICT_TYPE (DICT_ID, DICT_NAME, DICT_TYPE, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, PARENT_ID, REMARK) VALUES (1838749616451391489, '会议地点', 'meeting_place', '0', 1, '2024-09-25 09:17:25.535000', 1, '2024-09-25 09:17:25.535000', 0, null);

INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751002895355906, 1, '辅楼6楼报告厅', '1', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:22:56.000000', 1, '2024-09-25 13:49:41.162000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751254721368065, 2, '11楼1119会议室(视频会议室)', '2', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:23:56.128000', 1, '2024-09-25 09:23:56.128000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751601628057601, 3, '15楼1514会议室', '3', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:25:18.842000', 1, '2024-09-25 09:25:18.842000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751664609726465, 4, '16楼1614会议室', '4', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:25:33.862000', 1, '2024-09-25 09:25:33.862000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751723430645761, 5, '17楼1711会议室', '5', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:25:47.877000', 1, '2024-09-25 09:25:47.877000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751769370857474, 6, '18楼1812会议室', '6', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:25:58.000000', 1, '2024-09-25 09:26:14.907000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751938661355522, 7, '6楼会议室', '7', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:26:39.204000', 1, '2024-09-25 09:26:39.204000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838751990465204226, 8, '6楼接待室', '8', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:26:51.556000', 1, '2024-09-25 09:26:51.556000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838752037806313474, 9, '17楼1712接待室', '9', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:27:02.840000', 1, '2024-09-25 09:27:02.840000', '0000100024');
INSERT INTO SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1838752093930295297, 10, '18楼1813接待室', '10', 'meeting_place', '', '', 'N', '0', 1, '2024-09-25 09:27:16.221000', 1, '2024-09-25 09:27:16.221000', '0000100024');


-- 2024-10-14
create table MEETING.T_MEETING_GROUP
(
    ID          number(19) not null,
    NAME        VARCHAR(200),
    USER_ID     number(19),
    USER_NAME   VARCHAR(200),
    ORG_CODE    VARCHAR(100),
    REMARK      VARCHAR(500),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_MEETING_GROUP is '用户组表';

comment
on column MEETING.T_MEETING_GROUP.ID is '主键';

comment
on column MEETING.T_MEETING_GROUP.NAME is '名字';

comment
on column MEETING.T_MEETING_GROUP.USER_ID is '用户id';

comment
on column MEETING.T_MEETING_GROUP.USER_NAME is '用户名字';

comment
on column MEETING.T_MEETING_GROUP.ORG_CODE is '机构ID';

comment
on column MEETING.T_MEETING_GROUP.REMARK is '备注';

comment
on column MEETING.T_MEETING_GROUP.CREATOR_ID is '创建id';

comment
on column MEETING.T_MEETING_GROUP.CREATE_TIME is '创建时间';

comment
on column MEETING.T_MEETING_GROUP.UPDATER_ID is '修改人';

comment
on column MEETING.T_MEETING_GROUP.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_MEETING_GROUP.IS_ACTIVE is '是否删除';

create table MEETING.T_MEETING_GROUP_USER
(
    ID          number(19) not null,
    USER_ID     number(19),
    GROUP_ID     number(19),
    USER_NAME   VARCHAR(200),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_MEETING_GROUP_USER is '用户组用户';

comment
on column MEETING.T_MEETING_GROUP_USER.ID is '主键';

comment
on column MEETING.T_MEETING_GROUP_USER.GROUP_ID is '分组主键';

comment
on column MEETING.T_MEETING_GROUP_USER.USER_ID is '用户id';

comment
on column MEETING.T_MEETING_GROUP_USER.USER_NAME is '用户名字';

comment
on column MEETING.T_MEETING_GROUP_USER.CREATOR_ID is '创建id';

comment
on column MEETING.T_MEETING_GROUP_USER.CREATE_TIME is '创建时间';

comment
on column MEETING.T_MEETING_GROUP_USER.UPDATER_ID is '修改人';

comment
on column MEETING.T_MEETING_GROUP_USER.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_MEETING_GROUP_USER.IS_ACTIVE is '是否删除';




-- 2024-10-25
create table MEETING.T_TASK_USER
(
    ID          number(19) not null,
    TASK_NAME   VARCHAR(200),
    TASK_ID    number(19),
    USER_ID     number(19),
    USER_NAME   VARCHAR(200),
    ORG_CODE    VARCHAR(100),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_TASK_USER is '执行人员表';

comment
on column MEETING.T_TASK_USER.ID is '主键';

comment
on column MEETING.T_TASK_USER.TASK_NAME is 'NAME';

comment
on column MEETING.T_TASK_USER.TASK_ID is '督办主表id';

comment
on column MEETING.T_TASK_USER.USER_ID is '用户id';

comment
on column MEETING.T_TASK_USER.USER_NAME is '用户名字';

comment
on column MEETING.T_TASK_USER.ORG_CODE is '机构id';

comment
on column MEETING.T_TASK_USER.CREATOR_ID is '创建id';

comment
on column MEETING.T_TASK_USER.CREATE_TIME is '创建时间';

comment
on column MEETING.T_TASK_USER.UPDATER_ID is '修改人';

comment
on column MEETING.T_TASK_USER.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_TASK_USER.IS_ACTIVE is '是否删除';


create table MEETING.T_TASK_USER_GROUP
(
    ID          number(19) not null,
    USER_ID     number(19),
    MAIN_ID     number(19),
    ORG_CODE    VARCHAR(200),
    USER_NAME   VARCHAR(200),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_TASK_USER_GROUP is '执行人员详情表';

comment
on column MEETING.T_TASK_USER_GROUP.ID is '主键';

comment
on column MEETING.T_TASK_USER_GROUP.USER_ID is '用户id';

comment
on column MEETING.T_TASK_USER_GROUP.MAIN_ID is '主表id';

comment
on column MEETING.T_TASK_USER_GROUP.ORG_CODE is '机构id';

comment
on column MEETING.T_TASK_USER_GROUP.USER_NAME is '用户名字';

comment
on column MEETING.T_TASK_USER_GROUP.CREATOR_ID is '创建id';

comment
on column MEETING.T_TASK_USER_GROUP.CREATE_TIME is '创建时间';

comment
on column MEETING.T_TASK_USER_GROUP.UPDATER_ID is '修改人';

comment
on column MEETING.T_TASK_USER_GROUP.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_TASK_USER_GROUP.IS_ACTIVE is '是否删除';


create table MEETING.T_TASK_USER_EXECUTE
(
    ID                number(19) not null,
    TASK_ID           number(19),
    EXECUTE_WORKABLE  VARCHAR(1000),
    EXECUTE_DELAY     VARCHAR(1000),
    EXECUTE_PLAN      VARCHAR(1000),
    PLAN_TIME         timestamp,
    SUCCESS_TIME      timestamp,
    REMARK            VARCHAR(1000),
    STATUS            VARCHAR(2),
    CREATOR_ID        number(19),
    CREATE_TIME       timestamp,
    UPDATER_ID        number(19),
    UPDATE_TIME       timestamp,
    IS_ACTIVE         varchar(1)
);

comment
on table MEETING.T_TASK_USER_EXECUTE is '督查督办执行表';

comment
on column MEETING.T_TASK_USER_EXECUTE.ID is '主键';

comment
on column MEETING.T_TASK_USER_EXECUTE.TASK_ID is '督办主表id';

comment
on column MEETING.T_TASK_USER_EXECUTE.EXECUTE_WORKABLE is '落实情况';

comment
on column MEETING.T_TASK_USER_EXECUTE.EXECUTE_DELAY is '延迟原因';

comment
on column MEETING.T_TASK_USER_EXECUTE.EXECUTE_PLAN is '当前进度，后续计划';

comment
on column MEETING.T_TASK_USER_EXECUTE.PLAN_TIME is '计划完成时间';

comment
on column MEETING.T_TASK_USER_EXECUTE.SUCCESS_TIME is '实际完成时间';

comment
on column MEETING.T_TASK_USER_EXECUTE.REMARK is '备注';

comment
on column MEETING.T_TASK_USER_EXECUTE.STATUS is '状态';

comment
on column MEETING.T_TASK_USER_EXECUTE.CREATOR_ID is '创建id';

comment
on column MEETING.T_TASK_USER_EXECUTE.CREATE_TIME is '创建时间';

comment
on column MEETING.T_TASK_USER_EXECUTE.UPDATER_ID is '修改人';

comment
on column MEETING.T_TASK_USER_EXECUTE.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_TASK_USER_EXECUTE.IS_ACTIVE is '是否删除';



alter table MEETING.T_TASK
    add ORG_CODE VARCHAR(100);

comment
on column MEETING.T_TASK.ORG_CODE is '组织机构code';

INSERT INTO MEETING.SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1848237815749971969, '督查督办执行', 1846017873127776258, 2, '/task-user', 'meeting/taskUser/index', '', 1, 0, 'C', '0', '0', 'meeting:taskUser:query', '', 1, '2024-10-21', 1, '2024-10-23', '', '0', '', ' ', null, 'TaskUser');
INSERT INTO MEETING.SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1848238005483507713, '操作', 1848237815749971969, 1, '', '', '', 1, 0, 'F', '0', '0', 'meeting:taskUser:option', '', 1, '2024-10-21', 1, '2024-10-21', '', '0', '', ' ', null, '');
INSERT INTO MEETING.SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1848238430286811137, '督查督办管理', 1846017873127776258, 3, '/task/manage', 'meeting/taskManage/index', '', 1, 0, 'C', '0', '0', 'meeting:task:manage', '', 1, '2024-10-21', 1, '2024-10-25', '', '0', '', ' ', null, 'taskManage');

INSERT INTO MEETING.SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, DATA_SCOPE, MENU_CHECK_STRICTLY, ORG_CHECK_STRICTLY, STATUS, IS_ACTIVE, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1848963473799131138, '督查督办执行人', 'task_execute', 10, '1', 1, 1, '0', '1', 1, '2024-10-23', 1, '2024-10-23', null);



-- 2024-10-31
alter table MEETING.T_TASK
    add TASK_GIST varchar(1000);

comment
on column MEETING.T_TASK.TASK_GIST is '立项依据';

alter table MEETING.T_TASK
    add APPROVAL_TIME timestamp;

comment
on column MEETING.T_TASK.APPROVAL_TIME is '立项时间';


--2024-12-17

create table MEETING.T_MEETING_NOTIFY
(
    ID          number(19) not null,
    MEETING_ID  number(19) ,
    TITLE       varchar(2000),
    USER_ID     number(19),
    USER_NAME  varchar(50),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_MEETING_NOTIFY is '会议消息通知表';

comment
on column MEETING.T_MEETING_NOTIFY.ID is '主键';

comment
on column MEETING.T_MEETING_NOTIFY.MEETING_ID is '会议id';
comment
on column MEETING.T_MEETING_NOTIFY.TITLE is '标题';

comment
on column MEETING.T_MEETING_NOTIFY.USER_ID is '用户id';

comment
on column MEETING.T_MEETING_NOTIFY.USER_NAME is '用户姓名';

comment
on column MEETING.T_MEETING_NOTIFY.CREATOR_ID is '创建id';

comment
on column MEETING.T_MEETING_NOTIFY.CREATE_TIME is '创建时间';

comment
on column MEETING.T_MEETING_NOTIFY.UPDATER_ID is '修改人';

comment
on column MEETING.T_MEETING_NOTIFY.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_MEETING_NOTIFY.IS_ACTIVE is '是否删除';

-- 添加字典
INSERT INTO MEETING.SYS_DICT_TYPE (DICT_ID, DICT_NAME, DICT_TYPE, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, PARENT_ID, REMARK) VALUES (1868831414904438786, '会议消息模板', 'meeting_notify', '0', 1, '2024-12-17 09:31:45.179000', 1, '2024-12-17 09:31:45.179000', 0, '消息内容通知模板');
INSERT INTO MEETING.SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1868832315232763906, 1, '兹定于{{date}} {{time}}，在{{meetingLocation}}召开{{meetingTitle}}{{meetingType}}会议，现将会议议题印发给您，请准时出席。', '1', 'meeting_notify', '兹定于xx年xx月xx日，在xx召开xx会议，现将会议议题印发给您，请准时出席。', '', 'N', '0', 1, '2024-12-17 09:35:19.000000', 1, '2024-12-17 13:14:38.148000', '');
INSERT INTO MEETING.SYS_DICT_DATA (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE, CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1868832614617989122, 2, '定于{{date}} {{time}}，在{{meetingLocation}}召开{{meetingTitle}}{{meetingType}}会议，现将会议议题印发给您，请您准时参加。', '2', 'meeting_notify', '定于xx年xx月xx日，在xx召开xx会议，现将会议议题印发给您，请您准时参加。', '', 'N', '0', 1, '2024-12-17 09:36:31.000000', 1, '2024-12-17 13:14:44.456000', '');



create table MEETING.T_MEETING_SERVE
(
    ID          number(19) not null,
    MEETING_ID  number(19) ,
    TITLE       varchar(2000),
    USER_ID      number(19),
    IS_FINISH   varchar(2),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_MEETING_SERVE is '会议服务表';

comment
on column MEETING.T_MEETING_SERVE.ID is '主键';

comment
on column MEETING.T_MEETING_SERVE.MEETING_ID is '会议id';

comment
on column MEETING.T_MEETING_SERVE.TITLE is '标题';

comment
on column MEETING.T_MEETING_SERVE.IS_FINISH is '是否完成';

comment
on column MEETING.T_MEETING_SERVE.USER_ID is '执行人';


comment
on column MEETING.T_MEETING_SERVE.CREATOR_ID is '创建id';

comment
on column MEETING.T_MEETING_SERVE.CREATE_TIME is '创建时间';

comment
on column MEETING.T_MEETING_SERVE.UPDATER_ID is '修改人';

comment
on column MEETING.T_MEETING_SERVE.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_MEETING_SERVE.IS_ACTIVE is '是否删除';

alter table T_MEETING_JOINER
    add IS_AGREE varchar(2) DEFAULT '0';

comment
on column T_MEETING_JOINER.IS_AGREE is '是否通过';

INSERT INTO MEETING.SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1871090828713775105, '会议表决', 1764895135628181506, 4, '/meeting/register', 'meeting/register/index', '', 1, 0, 'C', '0', '0', 'meeting:manager:register', '', 1, '2024-12-23', 1, '2024-12-25', '', '0', '', ' ', null, 'register');
INSERT INTO MEETING.SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, DATA_SCOPE, MENU_CHECK_STRICTLY, ORG_CHECK_STRICTLY, STATUS, IS_ACTIVE, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1870025198677852162, '记录角色', 'meeting_recorder', 11, '1', 1, 1, '0', '1', 1, '2024-12-20', 1, '2024-12-25', null);
INSERT INTO MEETING.SYS_ROLE_MENU (ID, ROLE_ID, MENU_ID) VALUES (1871746787119251458, 1870025198677852162, 1764895135628181506);
INSERT INTO MEETING.SYS_ROLE_MENU (ID, ROLE_ID, MENU_ID) VALUES (1871746787144417282, 1870025198677852162, 1871090828713775105);
INSERT INTO MEETING.SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, DATA_SCOPE, MENU_CHECK_STRICTLY, ORG_CHECK_STRICTLY, STATUS, IS_ACTIVE, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK) VALUES (1872534679676538882, '服务角色', 'meeting_serve', 11, '1', 1, 1, '0', '1', 1, '2024-12-27', 1, '2024-12-27', null);

alter table T_MEETING
    add USER_ID number(19);

comment
on column T_MEETING.USER_ID is '主持人ID';

alter table T_MEETING
    add REGISTER_ID number(19);

comment
on column T_MEETING.REGISTER_ID is '登记人ID';



create table MEETING.T_MEETING_MEMO
(
    ID          NUMBER(19) not null,
    MEMO_WORD    CLOB,
    MEETING_ID  NUMBER(19),
    CREATOR_ID  NUMBER(19),
    CREATE_TIME timestamp,
    UPDATER_ID NUMBER(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE VARCHAR(1)
);

comment
on table MEETING.T_MEETING_MEMO is '会议签名表';

comment
on column MEETING.T_MEETING_MEMO.ID is '主键';

comment
on column MEETING.T_MEETING_MEMO.MEMO_WORD is '便签内容';

comment
on column MEETING.T_MEETING_MEMO.MEETING_ID is '会议id';

comment
on column MEETING.T_MEETING_MEMO.CREATOR_ID is '创建id';

comment
on column MEETING.T_MEETING_MEMO.CREATE_TIME is '创建时间';

comment
on column MEETING.T_MEETING_MEMO.UPDATER_ID is '修改人';

comment
on column MEETING.T_MEETING_MEMO.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_MEETING_MEMO.IS_ACTIVE is '是否删除';


INSERT INTO MEETING.SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1845666763362603010, '会议分组', 1764895135628181506, 0, '/meeting/meetingGroup', 'meeting/meetingGroup/index', '', 1, 0, 'C', '0', '0', 'meeting:meetingGroup:query', '', 1, '2024-10-14', 1, '2024-10-17', '', '0', '', ' ', null, 'meetingGroup');
INSERT INTO MEETING.SYS_MENU (MENU_ID, MENU_NAME, PARENT_ID, ORDER_NUM, PATH, COMPONENT, QUERY_PARAM, IS_FRAME, IS_CACHE, MENU_TYPE, VISIBLE, STATUS, PERMS, ICON, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, REMARK, SHOW_PARENT, TRANSITION, HIDDEN_TAG, DYNAMIC_LEVEL, ROUTER_NAME) VALUES (1846852021815889922, '操作', 1845666763362603010, 1, '', '', '', 1, 0, 'F', '0', '0', 'meeting:meetingGroup:option', '', 1, '2024-10-17', 1, '2024-10-17', '', '0', '', ' ', null, '');



-- 2025/1/13
-- 议题表决
create table MEETING.T_TOPIC_VOTE
(
    ID          number(19) not null,
    MEETING_ID  number(19) ,
    USER_ID     number(19),
    TOPIC_ID    number(19),
    USER_TYPE   varchar(2)
    STATUS      varchar(2),
    REMARK      varchar(1000),
    CREATOR_ID  number(19),
    CREATE_TIME timestamp,
    UPDATER_ID  number(19),
    UPDATE_TIME timestamp,
    IS_ACTIVE   varchar(1)
);

comment
on table MEETING.T_TOPIC_VOTE is '议题表决表';

comment
on column MEETING.T_TOPIC_VOTE.ID is '主键';

comment
on column MEETING.T_TOPIC_VOTE.MEETING_ID is '会议id';

comment
on column MEETING.T_TOPIC_VOTE.USER_ID is '用户id';

comment
on column MEETING.T_TOPIC_VOTE.USER_TYPE is '用户类型';

comment
on column MEETING.T_TOPIC_VOTE.TOPIC_ID is '议题id';

comment
on column MEETING.T_TOPIC_VOTE.STATUS is '表决意见，1 同意；2 原则同意；3 不同意；4 保留意见';


comment
on column MEETING.T_TOPIC_VOTE.REMARK is '表决意见';


comment
on column MEETING.T_TOPIC_VOTE.CREATOR_ID is '创建id';

comment
on column MEETING.T_TOPIC_VOTE.CREATE_TIME is '创建时间';

comment
on column MEETING.T_TOPIC_VOTE.UPDATER_ID is '修改人';

comment
on column MEETING.T_TOPIC_VOTE.UPDATE_TIME is '修改时间';

comment
on column MEETING.T_TOPIC_VOTE.IS_ACTIVE is '是否删除';


alter table MEETING.T_TOPIC_MEETING_LINK
    add STATUS VARCHAR(2);

comment
on column MEETING.T_TOPIC_MEETING_LINK.STATUS is '议题状态 1 同意；2原则同意；3不同意；4保留意见';
