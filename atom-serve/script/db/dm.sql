-- 创建sys_dict_type表，用于存储字典类型信息
DROP TABLE IF EXISTS SYS_DICT_TYPE;
CREATE TABLE SYS_DICT_TYPE
(
    DICT_ID     NUMBER(20,0) NOT NULL,
    DICT_NAME   VARCHAR2(300) DEFAULT '',
    DICT_TYPE   VARCHAR2(300) DEFAULT '',
    STATUS      CHAR(1) DEFAULT '0',
    CREATOR_ID  NUMBER(20) DEFAULT '',
    CREATE_TIME TIMESTAMP,
    UPDATER_ID  NUMBER(20) DEFAULT '',
    UPDATE_TIME TIMESTAMP,
    PARENT_ID   NUMBER(20,0) default 0,
    REMARK      VARCHAR2(1000) DEFAULT NULL,
    CONSTRAINT PK_DICT_ID PRIMARY KEY (DICT_ID),
    CONSTRAINT UK_DICT_TYPE UNIQUE (DICT_TYPE)
);
COMMENT
ON TABLE SYS_DICT_TYPE IS '字典类型表';
COMMENT
ON COLUMN SYS_DICT_TYPE.DICT_ID IS '字典主键';
COMMENT
ON COLUMN SYS_DICT_TYPE.DICT_NAME IS '字典名称';
COMMENT
ON COLUMN SYS_DICT_TYPE.DICT_TYPE IS '字典类型';
COMMENT
ON COLUMN SYS_DICT_TYPE.STATUS IS '状态（0正常 1停用）';
COMMENT
ON COLUMN SYS_DICT_TYPE.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN SYS_DICT_TYPE.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_DICT_TYPE.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN SYS_DICT_TYPE.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_DICT_TYPE.PARENT_ID IS '父类编号';
COMMENT
ON COLUMN SYS_DICT_TYPE.REMARK IS '备注';


insert into sys_dict_type
values (1, '用户性别', 'sys_user_sex', '0', 0, null, null, null, 0,'用户性别列表');
insert into sys_dict_type
values (2, '菜单状态', 'sys_show_hide', '0', 0, null, null, null, 0,'菜单状态列表');
insert into sys_dict_type
values (3, '系统开关', 'sys_normal_disable', '0', 0, null, null, null, 0,'系统开关列表');
insert into sys_dict_type
values (6, '系统是否', 'sys_yes_no', '0', 0, null, null, null, 0,'系统是否列表');
insert into sys_dict_type
values (7, '通知类型', 'sys_notice_type', '0', 0, null, null, null, 0,'通知类型列表');
insert into sys_dict_type
values (8, '通知状态', 'sys_notice_status', '0', 0, null, null, null, 0,'通知状态列表');
insert into sys_dict_type
values (9, '操作类型', 'sys_oper_type', '0', 0, null, null, null, 0,'操作类型列表');
insert into sys_dict_type
values (10, '系统状态', 'sys_common_status', '0', 0, null, null, null, 0,'登录状态列表');

-- 创建SYS_DICT_DATA表，用于存储字典数据信息
DROP TABLE IF EXISTS SYS_DICT_DATA;
CREATE TABLE SYS_DICT_DATA
(
    DICT_CODE   NUMBER(20,0) NOT NULL,
    DICT_SORT   NUMBER(4) DEFAULT 0,
    DICT_LABEL  VARCHAR2(300) DEFAULT '',
    DICT_VALUE  VARCHAR2(300) DEFAULT '',
    DICT_TYPE   VARCHAR2(300) DEFAULT '',
    CSS_CLASS   VARCHAR2(100) DEFAULT NULL,
    LIST_CLASS  VARCHAR2(100) DEFAULT NULL,
    IS_DEFAULT  CHAR(1) DEFAULT 'N',
    STATUS      CHAR(1) DEFAULT '0',
    CREATOR_ID  NUMBER(20) DEFAULT '',
    CREATE_TIME TIMESTAMP,
    UPDATER_ID  NUMBER(20) DEFAULT '',
    UPDATE_TIME TIMESTAMP,
    REMARK      VARCHAR2(1000) DEFAULT NULL,
    CONSTRAINT PK_DICT_CODE PRIMARY KEY (DICT_CODE)
);
COMMENT
ON TABLE SYS_DICT_DATA IS '字典数据表';
COMMENT
ON COLUMN SYS_DICT_DATA.DICT_CODE IS '字典编码';
COMMENT
ON COLUMN SYS_DICT_DATA.DICT_SORT IS '字典排序';
COMMENT
ON COLUMN SYS_DICT_DATA.DICT_LABEL IS '字典标签';
COMMENT
ON COLUMN SYS_DICT_DATA.DICT_VALUE IS '字典键值';
COMMENT
ON COLUMN SYS_DICT_DATA.DICT_TYPE IS '字典类型';
COMMENT
ON COLUMN SYS_DICT_DATA.CSS_CLASS IS '样式属性（其他样式扩展）';
COMMENT
ON COLUMN SYS_DICT_DATA.LIST_CLASS IS '表格回显样式';
COMMENT
ON COLUMN SYS_DICT_DATA.IS_DEFAULT IS '是否默认（Y是 N否）';
COMMENT
ON COLUMN SYS_DICT_DATA.STATUS IS '状态（0正常 1停用）';
COMMENT
ON COLUMN SYS_DICT_DATA.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN SYS_DICT_DATA.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_DICT_DATA.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN SYS_DICT_DATA.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_DICT_DATA.REMARK IS '备注';



insert into sys_dict_data
values (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 0, null, null, null, '性别男');
insert into sys_dict_data
values (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 0, null, null, null, '性别女');
insert into sys_dict_data
values (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 0, null, null, null, '性别未知');
insert into sys_dict_data
values (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 0, null, null, null, '显示菜单');
insert into sys_dict_data
values (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 0, null, null, null, '隐藏菜单');
insert into sys_dict_data
values (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 0, null, null, null, '正常状态');
insert into sys_dict_data
values (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 0, null, null, null, '停用状态');
insert into sys_dict_data
values (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 0, null, null, null, '系统默认是');
insert into sys_dict_data
values (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 0, null, null, null, '系统默认否');
insert into sys_dict_data
values (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 0, null, null, null, '通知');
insert into sys_dict_data
values (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 0, null, null, null, '公告');
insert into sys_dict_data
values (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 0, null, null, null, '正常状态');
insert into sys_dict_data
values (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 0, null, null, null, '关闭状态');
insert into sys_dict_data
values (29, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 0, null, null, null, '其他操作');
insert into sys_dict_data
values (18, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 0, null, null, null, '新增操作');
insert into sys_dict_data
values (19, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 0, null, null, null, '修改操作');
insert into sys_dict_data
values (20, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 0, null, null, null, '删除操作');
insert into sys_dict_data
values (21, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 0, null, null, null, '授权操作');
insert into sys_dict_data
values (22, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 0, null, null, null, '导出操作');
insert into sys_dict_data
values (23, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 0, null, null, null, '导入操作');
insert into sys_dict_data
values (24, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 0, null, null, null, '强退操作');
insert into sys_dict_data
values (25, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 0, null, null, null, '生成操作');
insert into sys_dict_data
values (26, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 0, null, null, null, '清空操作');
insert into sys_dict_data
values (27, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 0, null, null, null, '正常状态');
insert into sys_dict_data
values (28, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 0, null, null, null, '停用状态');

-- 创建SYS_JOB表，用于存储定时任务
DROP TABLE IF EXISTS SYS_JOB;
CREATE TABLE SYS_JOB
(
    ID              NUMBER(20,0) NOT NULL,
    JOB_NAME        VARCHAR2(755) DEFAULT '' NOT NULL,
    JOB_GROUP       VARCHAR2(755) DEFAULT 'DEFAULT' NOT NULL,
    INVOKE_TARGET   VARCHAR2(700) NOT NULL,
    CRON_EXPRESSION VARCHAR2(255) DEFAULT '' NULL,
    MISFIRE_POLICY  VARCHAR2(20) DEFAULT '3' NULL,
    CONCURRENT      CHAR(1) DEFAULT '1' NULL,
    STATUS          CHAR(1) DEFAULT '0' NULL,
    CREATOR_ID      NUMBER(20) DEFAULT '' NULL,
    CREATE_TIME     TIMESTAMP NULL,
    UPDATER_ID      NUMBER(20) DEFAULT '' NULL,
    UPDATE_TIME     TIMESTAMP NULL,
    REMARK          VARCHAR2(500) DEFAULT '' NULL,
    CONSTRAINT PK_SYS_JOB PRIMARY KEY (ID, JOB_NAME, JOB_GROUP)
);
COMMENT
ON TABLE SYS_JOB IS '定时任务调度表';
COMMENT
ON COLUMN SYS_JOB.ID IS '任务ID';
COMMENT
ON COLUMN SYS_JOB.JOB_NAME IS '任务名称';
COMMENT
ON COLUMN SYS_JOB.JOB_GROUP IS '任务组名';
COMMENT
ON COLUMN SYS_JOB.INVOKE_TARGET IS '调用目标字符串';
COMMENT
ON COLUMN SYS_JOB.CRON_EXPRESSION IS 'cron执行表达式';
COMMENT
ON COLUMN SYS_JOB.MISFIRE_POLICY IS '计划执行错误策略（1立即执行;2执行一次 3放弃执行）';
COMMENT
ON COLUMN SYS_JOB.CONCURRENT IS '是否并发执行（0允许;1禁止）';
COMMENT
ON COLUMN SYS_JOB.STATUS IS '状态（0正常;1暂停）';
COMMENT
ON COLUMN SYS_JOB.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN SYS_JOB.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_JOB.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN SYS_JOB.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_JOB.REMARK IS '备注信息';

-- 创建SYS_JOB_LOG表，用于存储定时任务调度日志信息
DROP TABLE IF EXISTS SYS_JOB_LOG;
CREATE TABLE SYS_JOB_LOG
(
    ID             NUMBER(20,0) NOT NULL,
    JOB_NAME       VARCHAR2(755) NOT NULL,
    JOB_GROUP      VARCHAR2(755) NOT NULL,
    INVOKE_TARGET  VARCHAR2(700) NOT NULL,
    JOB_MESSAGE    VARCHAR2(1000) NULL,
    STATUS         CHAR(1) DEFAULT '0' NULL,
    EXCEPTION_INFO VARCHAR2(2000) DEFAULT '' NULL,
    CREATE_TIME    TIMESTAMP NULL,
    CONSTRAINT PK_SYS_JOB_LOG PRIMARY KEY (ID)
);
COMMENT
ON TABLE SYS_JOB_LOG IS '定时任务调度日志表';
COMMENT
ON COLUMN SYS_JOB_LOG.ID IS '任务日志ID';
COMMENT
ON COLUMN SYS_JOB_LOG.JOB_NAME IS '任务名称';
COMMENT
ON COLUMN SYS_JOB_LOG.JOB_GROUP IS '任务组名';
COMMENT
ON COLUMN SYS_JOB_LOG.INVOKE_TARGET IS '调用目标字符串';
COMMENT
ON COLUMN SYS_JOB_LOG.JOB_MESSAGE IS '日志信息';
COMMENT
ON COLUMN SYS_JOB_LOG.STATUS IS '执行状态（0正常;1失败）';
COMMENT
ON COLUMN SYS_JOB_LOG.EXCEPTION_INFO IS '异常信息';
COMMENT
ON COLUMN SYS_JOB_LOG.CREATE_TIME IS '创建时间';
DROP TABLE IF EXISTS SYS_ORG;
CREATE TABLE SYS_ORG
(
    ORG_ID       NUMBER(20) NOT NULL,
    ORG_NAME     VARCHAR2(500),
    ABBREVIATION VARCHAR2(500),
    LEVEL        NUMBER(10),
    ORDER_NUM    NUMBER(10) DEFAULT 0,
    ANCESTORS    VARCHAR2(500),
    IS_ACTIVE    CHAR(1) deafult '1',
    ADDRESS      VARCHAR2(255),
    TEL          VARCHAR2(255),
    LEADER       VARCHAR2(255),
    PARENT_ID    NUMBER(20),
    CREATE_TIME  DATE,
    UPDATE_TIME  DATE,
    CODE         VARCHAR2(255),
    CREATOR_ID   NUMBER(20),
    UPDATER_ID   NUMBER(20),
    ORG_TYPE     VARCHAR2(50),
    status       CHAR(1) DEFAULT '0'
        PRIMARY KEY (ORG_ID)
) COMMENT ON TABLE SYS_ORG IS '组织';
COMMENT
ON COLUMN SYS_ORG.ORG_ID IS '组织ID';
COMMENT
ON COLUMN SYS_ORG.ORG_NAME IS '组织全称';
COMMENT
ON COLUMN SYS_ORG.ABBREVIATION IS '组织简称';
COMMENT
ON COLUMN SYS_ORG.LEVEL IS '层级';
COMMENT
ON COLUMN SYS_ORG.ORDER_NUM IS '排序';
COMMENT
ON COLUMN SYS_ORG.ANCESTORS IS '祖级列表';
COMMENT
ON COLUMN SYS_ORG.IS_ACTIVE IS '是否有效';
COMMENT
ON COLUMN SYS_ORG.ADDRESS IS '联系地址';
COMMENT
ON COLUMN SYS_ORG.TEL IS '联系电话';
COMMENT
ON COLUMN SYS_ORG.LEADER IS '机构负责人';
COMMENT
ON COLUMN SYS_ORG.PARENT_ID IS '父节点编号';
COMMENT
ON COLUMN SYS_ORG.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_ORG.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_ORG.CODE IS '组织机构编号-用户编写';
COMMENT
ON COLUMN SYS_ORG.CREATOR_ID IS '创建人编号';
COMMENT
ON COLUMN SYS_ORG.UPDATER_ID IS '更新人编号';
COMMENT
ON COLUMN SYS_ORG.ORG_TYPE IS '组织类型';
COMMENT
ON COLUMN SYS_ORG.STATUS IS '状态0正常 1 禁用';

insert into SYS_ORG
values (100, '美华系统', '美华', 0, 0, null, '1', '长宁区', '1890000000', '', null, null, null, '', null, null, null,
        '0');


create table SYS_NOTICE (
    NOTICE_ID         number(20,0)      not null,
    NOTICE_TITLE      varchar2(50)    not null,
    NOTICE_TYPE       char(1)         not null,
    NOTICE_CONTENT    clob            default null,
    STATUS            char(1)         default '0',
    CREATOR_ID        number(20,0)    default null,
    CREATE_TIME       date,
    UPDATER_ID         number(20,0)    default null,
    UPDATE_TIME       date,
    REMARK            varchar2(255)   default null
);

alter table SYS_NOTICE add constraint PK_SYS_NOTICE primary key (notice_id);

COMMENT ON TABLE  SYS_NOTICE                   IS '通知公告表';
COMMENT ON COLUMN SYS_NOTICE.NOTICE_ID         IS '公告主键';
COMMENT ON COLUMN SYS_NOTICE.NOTICE_TITLE      IS '公告标题';
COMMENT ON COLUMN SYS_NOTICE.NOTICE_TYPE       IS '公告类型（1通知 2公告）';
COMMENT ON COLUMN SYS_NOTICE.NOTICE_CONTENT    IS '公告内容';
COMMENT ON COLUMN SYS_NOTICE.STATUS            IS '公告状态（0正常 1关闭）';
COMMENT ON COLUMN SYS_NOTICE.CREATOR_ID         IS '创建者';
COMMENT ON COLUMN SYS_NOTICE.CREATE_TIME       IS '创建时间';
COMMENT ON COLUMN SYS_NOTICE.UPDATER_ID         IS '更新者';
COMMENT ON COLUMN SYS_NOTICE.UPDATE_TIME       IS '更新时间';
COMMENT ON COLUMN SYS_NOTICE.REMARK            IS '备注';
-- 对象存储
DROP TABLE IF EXISTS SYS_OSS;
CREATE TABLE SYS_OSS
(
    ID            NUMBER(20,0) NOT NULL,
    FILE_NAME     VARCHAR2(755) DEFAULT '' NOT NULL,
    ORIGINAL_NAME VARCHAR2(755) DEFAULT '' NOT NULL,
    FILE_SUFFIX   VARCHAR2(30) DEFAULT '' NOT NULL,
    URL           VARCHAR2(755) NOT NULL,
    CREATE_TIME   TIMESTAMP,
    CREATOR_ID    NUMBER(20) DEFAULT '',
    UPDATE_TIME   TIMESTAMP,
    UPDATER_ID    NUMBER(20) DEFAULT '',
    FILE_SIZE      NUMERIC  DEFAULT 0,
    PUBLIC_READ   CHAR(1)   DEFAULT '0',
    SERVICE       VARCHAR2(100),
    CONSTRAINT PK_SYS_OSS PRIMARY KEY (ID)
);
COMMENT
ON TABLE SYS_OSS IS '对象存储';
COMMENT
ON COLUMN SYS_OSS.ID IS '对象存储主键';
COMMENT
ON COLUMN SYS_OSS.FILE_NAME IS '文件名';
COMMENT
ON COLUMN SYS_OSS.ORIGINAL_NAME IS '原名';
COMMENT
ON COLUMN SYS_OSS.FILE_SUFFIX IS '文件后缀名';
COMMENT
ON COLUMN SYS_OSS.URL IS 'URL地址';
COMMENT
ON COLUMN SYS_OSS.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_OSS.CREATOR_ID IS '上传人';
COMMENT
ON COLUMN SYS_OSS.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_OSS.FILE_SIZE IS '文件大小';
COMMENT
ON COLUMN SYS_OSS.UPDATER_ID IS '更新人';
COMMENT
ON COLUMN SYS_OSS.SERVICE IS '服务商';
comment
on column SYS_OSS.PUBLIC_READ is '是否公共读';

-- 对象存储配置表
DROP TABLE IF EXISTS SYS_OSS_CONFIG;
CREATE TABLE SYS_OSS_CONFIG
(
    ID            NUMBER(20,0) NOT NULL,
    CREATOR_ID    NUMBER(20),
    CREATE_TIME   TIMESTAMP,
    UPDATER_ID    NUMBER(20),
    UPDATE_TIME   TIMESTAMP,
    CONFIG_KEY    VARCHAR2(255),
    ACCESS_KEY    VARCHAR2(255),
    SECRET_KEY    VARCHAR2(255),
    BUCKET_NAME   VARCHAR2(255),
    PREFIX        VARCHAR2(255),
    ENDPOINT      VARCHAR2(255),
    SITE_DOMAIN   VARCHAR2(255),
    IS_HTTPS      CHAR(1),
    REGION        VARCHAR2(255),
    STATUS        CHAR(1),
    EXT1          VARCHAR2(255),
    REMARK        VARCHAR2(500),
    IS_STS        CHAR(1),
    ACCESS_POLICY VARCHAR2(30),
    CONSTRAINT PK_SYS_OSS_CONFIG PRIMARY KEY (ID)
);
COMMENT
ON TABLE SYS_OSS_CONFIG IS '对象存储配置表';
COMMENT
ON COLUMN SYS_OSS_CONFIG.ID IS '主键';
COMMENT
ON COLUMN SYS_OSS_CONFIG.CREATOR_ID IS '创建者编号';
COMMENT
ON COLUMN SYS_OSS_CONFIG.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_OSS_CONFIG.UPDATER_ID IS '更新者编号';
COMMENT
ON COLUMN SYS_OSS_CONFIG.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_OSS_CONFIG.CONFIG_KEY IS '配置key';
COMMENT
ON COLUMN SYS_OSS_CONFIG.ACCESS_KEY IS 'accessKey';
COMMENT
ON COLUMN SYS_OSS_CONFIG.SECRET_KEY IS '秘钥';
COMMENT
ON COLUMN SYS_OSS_CONFIG.BUCKET_NAME IS '桶名称';
COMMENT
ON COLUMN SYS_OSS_CONFIG.PREFIX IS '前缀';
COMMENT
ON COLUMN SYS_OSS_CONFIG.ENDPOINT IS '访问站点';
COMMENT
ON COLUMN SYS_OSS_CONFIG.SITE_DOMAIN IS '自定义域名';
COMMENT
ON COLUMN SYS_OSS_CONFIG.IS_HTTPS IS '是否https（0否;1是）';
COMMENT
ON COLUMN SYS_OSS_CONFIG.REGION IS '域';
COMMENT
ON COLUMN SYS_OSS_CONFIG.STATUS IS '状态(0正常;1停用)';
COMMENT
ON COLUMN SYS_OSS_CONFIG.EXT1 IS '扩展字段';
COMMENT
ON COLUMN SYS_OSS_CONFIG.REMARK IS '备注';
COMMENT
ON COLUMN SYS_OSS_CONFIG.IS_STS IS '是否支持前端直传';
COMMENT
ON COLUMN SYS_OSS_CONFIG.ACCESS_POLICY IS '桶权限类型(0=private 1=public 2=custom)';


DROP TABLE IF EXISTS SYS_ROLE;
CREATE TABLE SYS_ROLE
(
    ROLE_ID             NUMBER(20) NOT NULL,
    ROLE_NAME           VARCHAR2(30) NOT NULL,
    ROLE_KEY            VARCHAR2(100) NOT NULL,
    ROLE_SORT           NUMBER(4) NOT NULL,
    DATA_SCOPE          CHAR(1) DEFAULT '1',
    MENU_CHECK_STRICTLY NUMBER(1) DEFAULT 1,
    ORG_CHECK_STRICTLY  NUMBER(1) DEFAULT 1,
    STATUS              CHAR(1) NOT NULL,
    IS_ACTIVE           CHAR(1) DEFAULT '1',
    CREATOR_ID          NUMBER(20) DEFAULT '',
    CREATE_TIME         DATE,
    UPDATER_ID          NUMBER(20) DEFAULT '',
    UPDATE_TIME         DATE,
    REMARK              VARCHAR2(500) DEFAULT NULL,
    PRIMARY KEY (ROLE_ID)
);

COMMENT
ON TABLE SYS_ROLE IS '角色信息表';
COMMENT
ON COLUMN SYS_ROLE.ROLE_ID IS '角色ID';
COMMENT
ON COLUMN SYS_ROLE.ROLE_NAME IS '角色名称';
COMMENT
ON COLUMN SYS_ROLE.ROLE_KEY IS '角色权限字符串';
COMMENT
ON COLUMN SYS_ROLE.ROLE_SORT IS '显示顺序';
COMMENT
ON COLUMN SYS_ROLE.DATA_SCOPE IS '数据范围（1：全部数据权限 2：自定数据权限 3：本机构数据权限 4：本机构及以下数据权限）';
COMMENT
ON COLUMN SYS_ROLE.MENU_CHECK_STRICTLY IS '菜单树选择项是否关联显示';
COMMENT
ON COLUMN SYS_ROLE.ORG_CHECK_STRICTLY IS '机构树选择项是否关联显示';
COMMENT
ON COLUMN SYS_ROLE.STATUS IS '角色状态（0正常 1停用）';
COMMENT
ON COLUMN SYS_ROLE.IS_ACTIVE IS '删除标志（1代表活动 0代表删除）';
COMMENT
ON COLUMN SYS_ROLE.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN SYS_ROLE.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_ROLE.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN SYS_ROLE.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_ROLE.REMARK IS '备注';

insert into sys_role
values ('1', '超级管理员', 'admin', 1, 1, 1, 1, '0', '1', null, null, null, null, '超级管理员');


DROP TABLE IF EXISTS SYS_ROLE_MENU;
CREATE TABLE IF NOT EXISTS SYS_ROLE_MENU
(
    ID NUMBER
(
    20
) NOT NULL,
    ROLE_ID NUMBER
(
    20
) NULL,
    MENU_ID NUMBER
(
    20
) NULL,
    PRIMARY KEY
(
    ID
)
    );

COMMENT
ON TABLE SYS_ROLE_MENU IS '角色功能菜单关系表';
COMMENT
ON COLUMN SYS_ROLE_MENU.ID IS '编号';
COMMENT
ON COLUMN SYS_ROLE_MENU.ROLE_ID IS '角色编号';
COMMENT
ON COLUMN SYS_ROLE_MENU.MENU_ID IS '功能代号';



DROP TABLE IF EXISTS SYS_ROLE_ORG;
CREATE TABLE SYS_ROLE_ORG
(
    ID NUMBER(20) NOT NULL,
    ROLE_ID NUMBER(20),
    ORG_ID NUMBER(20),
    PRIMARY KEY (ID)
);
COMMENT ON TABLE SYS_ROLE_ORG IS '角色机构关联';
COMMENT ON COLUMN SYS_ROLE_ORG.ID IS '主键';
COMMENT ON COLUMN SYS_ROLE_ORG.ROLE_ID IS '角色编号';
COMMENT ON COLUMN SYS_ROLE_ORG.ORG_ID IS '机构编号';


-- 参数配置表
DROP TABLE IF EXISTS SYS_CONFIG;
CREATE TABLE SYS_CONFIG
(
    ID           NUMBER(20) NOT NULL,
    CONFIG_NAME  VARCHAR2(90) NOT NULL,
    CONFIG_KEY   VARCHAR2(100) NOT NULL,
    CONFIG_VALUE VARCHAR2(500),
    IS_SYS       VARCHAR2(11),
    CREATOR_ID   NUMBER(20),
    CREATE_TIME  DATETIME,
    UPDATER_ID   NUMBER(20),
    UPDATE_TIME  DATETIME,
    REMARK       VARCHAR2(500)
);
COMMENT
ON TABLE SYS_CONFIG IS '参数配置表';
COMMENT
ON COLUMN SYS_CONFIG.ID IS '编号';
COMMENT
ON COLUMN SYS_CONFIG.CONFIG_NAME IS '名称';
COMMENT
ON COLUMN SYS_CONFIG.CONFIG_KEY IS '参数键';
COMMENT
ON COLUMN SYS_CONFIG.CONFIG_VALUE IS '参数值';
COMMENT
ON COLUMN SYS_CONFIG.IS_SYS IS '系统内置Y：是；N：否';
COMMENT
ON COLUMN SYS_CONFIG.CREATOR_ID IS '创建人';
COMMENT
ON COLUMN SYS_CONFIG.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_CONFIG.REMARK IS '备注';
COMMENT
ON COLUMN SYS_CONFIG.UPDATER_ID IS '更新人ID';
COMMENT
ON COLUMN SYS_CONFIG.UPDATE_TIME IS '更新时间';

insert into sys_config
values (1, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', null, null, null, null,
        '初始化密码 123456');
DROP TABLE IF EXISTS SYS_MENU;
CREATE TABLE SYS_MENU
(
    MENU_ID       NUMBER(20) NOT NULL,
    MENU_NAME     VARCHAR2(50) NOT NULL,
    PARENT_ID     NUMBER(20) DEFAULT 0,
    ORDER_NUM     NUMBER(10) DEFAULT 0,
    PATH          VARCHAR2(200),
    ROUTER_NAME   VARCHAR2(300),
    COMPONENT     VARCHAR2(255),
    QUERY_PARAM   VARCHAR2(255),
    IS_FRAME      NUMBER(10) DEFAULT 1,
    IS_CACHE      NUMBER(10) DEFAULT 0,
    MENU_TYPE     CHAR,
    VISIBLE       CHAR DEFAULT '0',
    STATUS        CHAR DEFAULT '0',
    PERMS         VARCHAR2(100),
    ICON          VARCHAR2(100) DEFAULT '#',
    CREATOR_ID    NUMBER(20),
    CREATE_TIME   DATE,
    UPDATER_ID    NUMBER(20),
    UPDATE_TIME   DATE,
    REMARK        VARCHAR2(500),
    SHOW_PARENT   CHAR(1),
    TRANSITION    VARCHAR2(255),
    HIDDEN_TAG    CHAR(1),
    DYNAMIC_LEVEL NUMBER(10),
    PRIMARY KEY (MENU_ID)
) COMMENT ON TABLE SYS_MENU IS '菜单权限表';
COMMENT
ON COLUMN SYS_MENU.MENU_ID IS '菜单ID';
COMMENT
ON COLUMN SYS_MENU.MENU_NAME IS '菜单名称';
COMMENT
ON COLUMN SYS_MENU.PARENT_ID IS '父菜单ID';
COMMENT
ON COLUMN SYS_MENU.ORDER_NUM IS '显示顺序';
COMMENT
ON COLUMN SYS_MENU.PATH IS '路由地址';
COMMENT
ON COLUMN SYS_MENU.COMPONENT IS '组件路径';
COMMENT
ON COLUMN SYS_MENU.QUERY_PARAM IS '路由参数';
COMMENT
ON COLUMN SYS_MENU.IS_FRAME IS '是否为外链（0是;1否）';
COMMENT
ON COLUMN SYS_MENU.IS_CACHE IS '是否缓存（0缓存;1不缓存）';
COMMENT
ON COLUMN SYS_MENU.MENU_TYPE IS '菜单类型（M目录;C菜单;F按钮）';
COMMENT
ON COLUMN SYS_MENU.VISIBLE IS '菜单状态（0显示;1隐藏）';
COMMENT
ON COLUMN SYS_MENU.STATUS IS '菜单状态（0正常;1停用）';
COMMENT
ON COLUMN SYS_MENU.PERMS IS '权限标识';
COMMENT
ON COLUMN SYS_MENU.ICON IS '菜单图标';
COMMENT
ON COLUMN SYS_MENU.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN SYS_MENU.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_MENU.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN SYS_MENU.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_MENU.REMARK IS '备注';
COMMENT
ON COLUMN SYS_MENU.SHOW_PARENT IS '是否显示父级菜单';
COMMENT
ON COLUMN SYS_MENU.TRANSITION IS '动画效果';
COMMENT
ON COLUMN SYS_MENU.HIDDEN_TAG IS '当前菜单名称或自定义信息禁止添加到标签页';
COMMENT
ON COLUMN SYS_MENU.DYNAMIC_LEVEL IS '显示在标签页的最大数量，需满足后面的条件：不显示在菜单中的路由并且是通过query或params传参模式打开的页面';
COMMENT
ON COLUMN SYS_MENU.ROUTER_NAME IS '路由名称，必须唯一并且和当前路由component字段对应的页面里用defineOptions包起来的name保持一致';




-- ----------------------------
-- 初始化-菜单信息表数据
-- ----------------------------
-- 一级菜单

-- ----------------------------
-- 初始化-菜单信息表数据
-- ----------------------------
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1, '系统管理', 0, 1, '/system', null, '', 1, 0, 'M', '0', '0', '', 'ep:setting', 0, '2023-04-19 05:59:47', 1, '2023-04-19 15:50:53', '系统管理目录', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (2, '系统监控', 0, 2, '/monitor', null, '', 1, 0, 'M', '0', '0', '', 'ep:monitor', 0, '2023-04-19 05:59:47', null, null, '系统监控目录', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (3, '系统工具', 0, 3, '/tool', null, '', 1, 0, 'M', '0', '0', '', 'fa-solid:toolbox', 0, '2023-04-19 05:59:47', 1, '2023-05-04 14:29:15', '系统工具目录', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (100, '用户管理', 1, 1, '/user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:20:57', '用户管理菜单', '0', null, '0', null, 'User');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (101, '角色管理', 1, 2, '/role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:21:06', '角色管理菜单', '0', null, '0', null, 'Role');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (102, '菜单管理', 1, 3, '/menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:21:14', '菜单管理菜单', '0', null, '0', null, 'Menu');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (105, '字典管理', 1, 6, '/dict', 'system/dict/index', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:21:23', '字典管理菜单', '0', null, '0', null, 'Dict');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (106, '参数设置', 1, 7, '/config', 'system/config/index', '', 1, 0, 'C', '0', '0', 'system:config:list', '', 0, '2023-04-19 05:59:47', 1, '2023-05-06 16:52:33', '参数设置菜单', '0', null, '0', null, 'Config');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (107, '通知公告', 1, 8, '/notice', 'system/notice/index', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:21:36', '通知公告菜单', '0', null, '0', null, 'Notice');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (108, '日志管理', 1, 9, '/log', '', '', 1, 0, 'M', '0', '0', '', 'log', 0, '2023-04-19 05:59:47', null, null, '日志管理菜单', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (109, '在线用户', 2, 1, '/online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:22:14', '在线用户菜单', '0', null, '0', null, 'Online');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (112, '缓存列表', 2, 6, '/cacheList', 'monitor/cache/list', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:22:28', '缓存列表菜单', '0', null, '0', null, 'CacheList');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (113, '缓存监控', 2, 5, '/cache', 'monitor/cache/index', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:22:22', '缓存监控菜单', '0', null, '0', null, 'Cache');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (114, '表单构建', 3, 1, '/build', 'tool/build/index', '', 1, 0, 'C', '1', '0', 'tool:build:list', 'build', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:22:36', '表单构建菜单', '0', null, '0', null, 'Build');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (115, '代码生成管理', 3, 10, '/gen', '', '', 1, 0, 'M', '0', '0', '', '', 1, '2023-05-04 14:24:47', 1, '2023-05-06 11:25:18', '', '0', '', '', null, 'Gen');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (118, '文件管理', 1, 10, '/oss', 'system/oss/index', '', 1, 0, 'C', '0', '0', 'system:oss:list', 'upload', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:22:05', '文件管理菜单', '0', null, '0', null, 'Oss');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (500, '操作日志', 108, 1, '/operlog', 'monitor/operlog/index', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:21:47', '操作日志菜单', '0', null, '0', null, 'OperLog');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (501, '登录日志', 108, 2, '/logininfor', 'monitor/logininfor/index', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:21:59', '登录日志菜单', '0', null, '0', null, 'Logininfor');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1001, '用户查询', 100, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1002, '用户新增', 100, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1003, '用户修改', 100, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1004, '用户删除', 100, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1005, '用户导出', 100, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1006, '用户导入', 100, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1007, '重置密码', 100, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1008, '角色查询', 101, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1009, '角色新增', 101, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1010, '角色修改', 101, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1011, '角色删除', 101, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1012, '角色导出', 101, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1013, '菜单查询', 102, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1014, '菜单新增', 102, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1015, '菜单修改', 102, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1016, '菜单删除', 102, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 0, '2023-04-19 05:59:47', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1026, '字典查询', 105, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1027, '字典新增', 105, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1028, '字典修改', 105, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1029, '字典删除', 105, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1030, '字典导出', 105, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1031, '参数查询', 106, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1032, '参数新增', 106, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1033, '参数修改', 106, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1034, '参数删除', 106, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1035, '参数导出', 106, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1036, '公告查询', 107, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1037, '公告新增', 107, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1038, '公告修改', 107, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1039, '公告删除', 107, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1040, '操作查询', 500, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1041, '操作删除', 500, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1042, '日志导出', 500, 4, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1043, '登录查询', 501, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1044, '登录删除', 501, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1045, '日志导出', 501, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1046, '在线查询', 109, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1047, '批量强退', 109, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1048, '单条强退', 109, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1050, '账户解锁', 501, 4, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1055, '生成查询', 115, 1, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1056, '生成修改', 115, 2, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1057, '生成删除', 115, 3, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1058, '导入代码', 115, 2, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1059, '预览代码', 115, 4, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1060, '生成代码', 115, 5, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1501, '代码生成', 115, 2, '/gen/index', 'tool/gen/index', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 0, '2023-04-19 05:59:47', 1, '2023-05-06 12:22:44', '代码生成菜单', '1', null, '0', null, 'Gen');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1502, '生成修改', 115, 1, '/gen-edit/index/:id', 'tool/gen/editTable', '', 1, 0, 'C', '1', '0', '', '', 1, '2023-05-04 14:27:18', 1, '2023-05-06 12:51:06', '', '0', '', '', null, 'GenEdit');
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1600, '文件查询', 118, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:query', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1601, '文件上传', 118, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:upload', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1602, '文件下载', 118, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:download', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1603, '文件删除', 118, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:remove', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1604, '配置添加', 118, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:add', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, creator_id, create_time, updater_id, update_time, remark, show_parent, transition, hidden_tag, dynamic_level, router_name) values (1605, '配置编辑', 118, 6, '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:edit', '#', 0, '2023-04-19 05:59:48', null, null, '', '0', null, '0', null, null);



DROP TABLE IF EXISTS SYS_USER;
CREATE TABLE SYS_USER
(
    USER_ID     NUMBER(20) NOT NULL,
    ORG_ID      NUMBER(20) DEFAULT NULL,
    USER_NAME   VARCHAR2(30) NOT NULL,
    NICK_NAME   VARCHAR2(30) NOT NULL,
    USER_TYPE   VARCHAR2(10) DEFAULT 'SYS_USER',
    EMAIL       VARCHAR2(50) DEFAULT '',
    MOBILE      VARCHAR2(11) DEFAULT '',
    SEX         CHAR(1) DEFAULT '0',
    AVATAR      VARCHAR2(100) DEFAULT '',
    PASSWORD    VARCHAR2(255) DEFAULT '',
    SLAT        VARCHAR2(255) DEFAULT '',
    STATUS      CHAR(1) DEFAULT '0',
    IS_ACTIVE   CHAR(1) DEFAULT '1',
    LOGIN_IP    VARCHAR2(128) DEFAULT '',
    LOGIN_DATE  DATE,
    CREATOR_ID  NUMBER(20) DEFAULT NULL,
    CREATE_TIME DATE,
    UPDATER_ID  NUMBER(20) DEFAULT NULL,
    UPDATE_TIME DATE,
    REMARK      VARCHAR2(500) DEFAULT NULL,
    PRIMARY KEY (USER_ID)
);

COMMENT
ON TABLE SYS_USER IS '用户信息表';
COMMENT
ON COLUMN SYS_USER.USER_ID IS '用户ID';
COMMENT
ON COLUMN SYS_USER.ORG_ID IS '机构ID';
COMMENT
ON COLUMN SYS_USER.USER_NAME IS '用户账号';
COMMENT
ON COLUMN SYS_USER.NICK_NAME IS '用户昵称';
COMMENT
ON COLUMN SYS_USER.USER_TYPE IS '用户类型（sys_user系统用户）';
COMMENT
ON COLUMN SYS_USER.EMAIL IS '用户邮箱';
COMMENT
ON COLUMN SYS_USER.MOBILE IS '手机号码';
COMMENT
ON COLUMN SYS_USER.SEX IS '用户性别（0男 1女 2未知）';
COMMENT
ON COLUMN SYS_USER.AVATAR IS '头像地址';
COMMENT
ON COLUMN SYS_USER.PASSWORD IS '密码';
COMMENT
ON COLUMN SYS_USER.SLAT IS '加密盐';
COMMENT
ON COLUMN SYS_USER.STATUS IS '帐号状态（0正常 1停用）';
COMMENT
ON COLUMN SYS_USER.IS_ACTIVE IS '删除标志（1代表存在 0代表删除）';
COMMENT
ON COLUMN SYS_USER.LOGIN_IP IS '最后登录IP';
COMMENT
ON COLUMN SYS_USER.LOGIN_DATE IS '最后登录时间';
COMMENT
ON COLUMN SYS_USER.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN SYS_USER.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN SYS_USER.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN SYS_USER.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN SYS_USER.REMARK IS '备注';


insert into sys_user
values (1, 100, 'root', '管理员', 'sys_user', '', '', '0', '',
        '$2a$10$X2QhBsnf9U0kofdSrnLxd.C1U.h5JUVVKkVnVgwFUWA1QQI867on6', '$2a$10$X2QhBsnf9U0kofdSrnLxd.', '0', '1', '',
        null, '', null, '', null, '');

-- 用户角色关联表
DROP TABLE IF EXISTS SYS_USER_ROLE;
CREATE TABLE SYS_USER_ROLE
(
    ID      NUMBER(20) NOT NULL,
    USER_ID NUMBER(20),
    ROLE_ID NUMBER(20)
);
COMMENT
ON TABLE SYS_USER_ROLE IS '用户角色关联表';
COMMENT
ON COLUMN SYS_USER_ROLE.ID IS 'ID';
COMMENT
ON COLUMN SYS_USER_ROLE.USER_ID IS '用户ID';
COMMENT
ON COLUMN SYS_USER_ROLE.ROLE_ID IS '角色ID';



-- ----------------------------
-- 初始化-用户和角色关联表数据
-- ----------------------------
insert into SYS_USER_ROLE
values (1, 1, 1);

-- 代码生成业务表
DROP TABLE IF EXISTS GEN_TABLE;
CREATE TABLE GEN_TABLE
(
    ID                NUMBER(20) NOT NULL,
    TABLE_NAME        VARCHAR2(200) DEFAULT '',
    TABLE_COMMENT     VARCHAR2(1000) DEFAULT '',
    SUB_TABLE_NAME    VARCHAR2(1000),
    SUB_TABLE_FK_NAME VARCHAR2(255),
    CLASS_NAME        VARCHAR2(100) DEFAULT '',
    TPL_CATEGORY      VARCHAR2(200) DEFAULT 'crud',
    IS_NEED_EXCEL     CHAR(1) DEFAULT '0',
    IS_NEED_VALIDATOR CHAR(1) DEFAULT '0',
    IS_NEED_SATOKEN   CHAR(1) DEFAULT '0',
    PACKAGE_NAME      VARCHAR2(300),
    MODULE_NAME       VARCHAR2(30),
    BUSINESS_NAME     VARCHAR2(90),
    FUNCTION_NAME     VARCHAR2(100),
    FUNCTION_AUTHOR   VARCHAR2(100),
    GEN_TYPE          CHAR(1) DEFAULT '0',
    GEN_PATH          VARCHAR2(200) DEFAULT '/',
    OPTIONS           VARCHAR2(1000),
    CREATOR_ID        NUMBER(20) DEFAULT '',
    CREATE_TIME       DATETIME,
    UPDATER_ID        NUMBER(20) DEFAULT '',
    UPDATE_TIME       DATETIME,
    REMARK            VARCHAR2(500),
    IS_SORT_DELETE    CHAR(1)
);
COMMENT
ON TABLE GEN_TABLE IS '代码生成业务表';
COMMENT
ON COLUMN GEN_TABLE.ID IS '编号';
COMMENT
ON COLUMN GEN_TABLE.TABLE_NAME IS '表名称';
COMMENT
ON COLUMN GEN_TABLE.TABLE_COMMENT IS '表描述';
COMMENT
ON COLUMN GEN_TABLE.SUB_TABLE_NAME IS '关联子表的表名';
COMMENT
ON COLUMN GEN_TABLE.SUB_TABLE_FK_NAME IS '子表关联的外键名';
COMMENT
ON COLUMN GEN_TABLE.CLASS_NAME IS '实体类名称';
COMMENT
ON COLUMN GEN_TABLE.TPL_CATEGORY IS '使用的模板（crud单表操作;tree树表操作）';
COMMENT
ON COLUMN GEN_TABLE.IS_NEED_EXCEL IS '开启导入导出功能（0不开启;1开启）';
COMMENT
ON COLUMN GEN_TABLE.IS_NEED_VALIDATOR IS '开启后段数据验证（0不开启;1开启）';
COMMENT
ON COLUMN GEN_TABLE.IS_NEED_SATOKEN IS '开启satoken';
COMMENT
ON COLUMN GEN_TABLE.PACKAGE_NAME IS '生成包路径';
COMMENT
ON COLUMN GEN_TABLE.MODULE_NAME IS '生成模块名';
COMMENT
ON COLUMN GEN_TABLE.BUSINESS_NAME IS '生成业务名';
COMMENT
ON COLUMN GEN_TABLE.FUNCTION_NAME IS '生成功能名';
COMMENT
ON COLUMN GEN_TABLE.FUNCTION_AUTHOR IS '生成功能作者';
COMMENT
ON COLUMN GEN_TABLE.GEN_TYPE IS '生成代码方式（0zip压缩包;1自定义路径）';
COMMENT
ON COLUMN GEN_TABLE.GEN_PATH IS '生成路径（不填默认项目路径）';
COMMENT
ON COLUMN GEN_TABLE.OPTIONS IS '其它生成选项';
COMMENT
ON COLUMN GEN_TABLE.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN GEN_TABLE.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN GEN_TABLE.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN GEN_TABLE.UPDATE_TIME IS '更新时间';
COMMENT
ON COLUMN GEN_TABLE.REMARK IS '备注';
COMMENT
ON COLUMN GEN_TABLE.IS_SORT_DELETE IS '是否软删除';

-- 代码生成业务表字段
DROP TABLE IF EXISTS GEN_TABLE_COLUMN;
CREATE TABLE GEN_TABLE_COLUMN
(
    ID             NUMBER(20) NOT NULL,
    TABLE_ID       NUMBER(20),
    COLUMN_NAME    VARCHAR2(200),
    COLUMN_COMMENT VARCHAR2(500),
    COLUMN_TYPE    VARCHAR2(100),
    JAVA_TYPE      VARCHAR2(500),
    JAVA_FIELD     VARCHAR2(200),
    IS_PK          CHAR(1),
    IS_INCREMENT   CHAR(1),
    IS_REQUIRED    CHAR(1),
    IS_INSERT      CHAR(1),
    IS_EDIT        CHAR(1),
    IS_LIST        CHAR(1),
    IS_QUERY       CHAR(1),
    QUERY_TYPE     VARCHAR2(200) DEFAULT 'EQ',
    HTML_TYPE      VARCHAR2(200),
    DICT_TYPE      VARCHAR2(200) DEFAULT '',
    SORT           NUMBER(10),
    CREATOR_ID     NUMBER(20) DEFAULT '',
    CREATE_TIME    DATETIME,
    UPDATER_ID     NUMBER(20) DEFAULT '',
    UPDATE_TIME    DATETIME
);
COMMENT
ON TABLE GEN_TABLE_COLUMN IS '代码生成业务表字段';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.ID IS '编号';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.TABLE_ID IS '归属表编号';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.COLUMN_NAME IS '列名称';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.COLUMN_COMMENT IS '列描述';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.COLUMN_TYPE IS '列类型';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.JAVA_TYPE IS 'JAVA类型';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.JAVA_FIELD IS 'JAVA字段名';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_PK IS '是否主键（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_INCREMENT IS '是否自增（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_REQUIRED IS '是否必填（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_INSERT IS '是否为插入字段（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_EDIT IS '是否编辑字段（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_LIST IS '是否列表字段（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.IS_QUERY IS '是否查询字段（1是）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.QUERY_TYPE IS '查询方式（等于、不等于、大于、小于、范围）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.HTML_TYPE IS '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.DICT_TYPE IS '字典类型';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.SORT IS '排序';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.CREATOR_ID IS '创建者';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.CREATE_TIME IS '创建时间';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.UPDATER_ID IS '更新者';
COMMENT
ON COLUMN GEN_TABLE_COLUMN.UPDATE_TIME IS '更新时间';

    -- 系统访问记录
DROP TABLE IF EXISTS SYS_LOGININFOR;
CREATE TABLE SYS_LOGININFOR
(
    INFO_ID        NUMBER(20) NOT NULL,
    USER_NAME      VARCHAR2(50) DEFAULT '',
    IPADDR         VARCHAR2(128) DEFAULT '',
    LOGIN_LOCATION VARCHAR2(255) DEFAULT '',
    BROWSER        VARCHAR2(50) DEFAULT '',
    OS             VARCHAR2(50) DEFAULT '',
    STATUS         CHAR(1) DEFAULT '0',
    MSG            VARCHAR2(255) DEFAULT '',
    LOGIN_TIME     DATETIME
);
COMMENT
ON TABLE SYS_LOGININFOR IS '系统访问记录';
COMMENT
ON COLUMN SYS_LOGININFOR.INFO_ID IS '访问ID';
COMMENT
ON COLUMN SYS_LOGININFOR.USER_NAME IS '用户账号';
COMMENT
ON COLUMN SYS_LOGININFOR.IPADDR IS '登录IP地址';
COMMENT
ON COLUMN SYS_LOGININFOR.LOGIN_LOCATION IS '登录地点';
COMMENT
ON COLUMN SYS_LOGININFOR.BROWSER IS '浏览器类型';
COMMENT
ON COLUMN SYS_LOGININFOR.OS IS '操作系统';
COMMENT
ON COLUMN SYS_LOGININFOR.STATUS IS '登录状态（0成功 1失败）';
COMMENT
ON COLUMN SYS_LOGININFOR.MSG IS '提示消息';
COMMENT
ON COLUMN SYS_LOGININFOR.LOGIN_TIME IS '访问时间';


-- 系统操作日志
DROP TABLE IF EXISTS SYS_OPER_LOG;
CREATE TABLE SYS_OPER_LOG
(
    OPER_ID        NUMBER(20) NOT NULL,
    TITLE          VARCHAR2(150) DEFAULT '' NULL,
    BUSINESS_TYPE  NUMBER(2) DEFAULT 0 NULL,
    METHOD         VARCHAR2(100) DEFAULT '' NULL,
    REQUEST_METHOD VARCHAR2(10) DEFAULT '' NULL,
    OPERATOR_TYPE  NUMBER(1) DEFAULT 0 NULL,
    OPER_NAME      VARCHAR2(100) DEFAULT '' NULL,
    ORG_NAME       VARCHAR2(50) DEFAULT '' NULL,
    OPER_URL       VARCHAR2(255) DEFAULT '' NULL,
    OPER_IP        VARCHAR2(128) DEFAULT '' NULL,
    OPER_LOCATION  VARCHAR2(255) DEFAULT '' NULL,
    OPER_PARAM     VARCHAR2(3878) DEFAULT '' NULL,
    COST_TIME      NUMERIC(20)  DEFAULT 0 NULL,
    JSON_RESULT    VARCHAR2(3878) DEFAULT '' NULL,
    STATUS         NUMBER(1) DEFAULT 0 NULL,
    ERROR_MSG      VARCHAR2(3878) DEFAULT '' NULL,
    OPER_TIME      TIMESTAMP NULL,
    DESCRIPTION    VARCHAR2(100) NULL
);

ALTER TABLE SYS_OPER_LOG
    ADD CONSTRAINT PK_SYS_OPER_LOG PRIMARY KEY (OPER_ID);


COMMENT
ON TABLE SYS_OPER_LOG IS '操作日志记录';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_ID IS '日志主键';
COMMENT
ON COLUMN SYS_OPER_LOG.TITLE IS '模块标题';
COMMENT
ON COLUMN SYS_OPER_LOG.BUSINESS_TYPE IS '业务类型（0其它 1新增 2修改 3删除）';
COMMENT
ON COLUMN SYS_OPER_LOG.METHOD IS '方法名称';
COMMENT
ON COLUMN SYS_OPER_LOG.REQUEST_METHOD IS '请求方式';
COMMENT
ON COLUMN SYS_OPER_LOG.OPERATOR_TYPE IS '操作类别（0其它 1后台用户 2手机端用户）';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_NAME IS '操作人员';
COMMENT
ON COLUMN SYS_OPER_LOG.ORG_NAME IS '机构名称';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_URL IS '请求URL';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_IP IS '主机地址';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_LOCATION IS '操作地点';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_PARAM IS '请求参数';
COMMENT
ON COLUMN SYS_OPER_LOG.COST_TIME IS '消耗时间';
COMMENT
ON COLUMN SYS_OPER_LOG.JSON_RESULT IS '返回参数';
COMMENT
ON COLUMN SYS_OPER_LOG.STATUS IS '操作状态（0正常 1异常）';
COMMENT
ON COLUMN SYS_OPER_LOG.ERROR_MSG IS '错误消息';
COMMENT
ON COLUMN SYS_OPER_LOG.OPER_TIME IS '操作时间';
COMMENT
ON COLUMN SYS_OPER_LOG.DESCRIPTION IS '业务描述';

