<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.FileTopicUnionMapper">

    <resultMap type="com.tenint.meeting.domain.vo.FileTopicUnionVo" id="FileTopicUnionResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="ossId" column="oss_id"/>
        <result property="fileType" column="file_type"/>
        <result property="fileOrder" column="file_order"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectFileTopicUnionVoPage" resultType="com.tenint.meeting.domain.vo.FileTopicUnionVo">
        -- meeting_id 存在：与会议有关联的目录
        -- plan_id 存在：通过议题收集关联的议题，否则为会议资料上传的特殊议题
        -- file_type = topic 上传过特殊议题的目录
        select t.* from (
        -- 1.1、通过议题收集的议题（与会议有关的）
        SELECT l.topic_id AS id,
        f.id AS PARENT_ID,
        l.topic_title AS name,
        r.OSS_ID,
        'topic' as FILE_TYPE,
        l.TOPIC_ORDER as FILE_ORDER,
        l.IS_ACTIVE,
        l.CREATE_TIME
        FROM t_file_entity f
        inner JOIN T_TOPIC_MEETING_LINK l ON l.topic_id != f.id and f.meeting_id = l.meeting_id
        left join T_TOPIC_MEETING_FILE r on l.TOPIC_ID = r.TOPIC_MEET_ID and r.FILE_TYPE = '1' and r.IS_ACTIVE = '1'
        WHERE f.entity_type = 'folder'
        and f.IS_ACTIVE = '1' -- 目录必须存在
        AND f.meeting_id IS NOT NULL
        AND l.PLAN_ID IS NOT NULL
        UNION ALL
        -- 1.2、通过会议资料上传的特殊议题（与会议有关的）
        SELECT l.id AS id,
        f.id AS PARENT_ID,
        l.topic_title AS name,
        r.OSS_ID,
        'topic' as FILE_TYPE,
        l.TOPIC_ORDER as FILE_ORDER,
        l.IS_ACTIVE,
        l.CREATE_TIME
        FROM t_file_entity f
        inner JOIN T_TOPIC_MEETING_LINK l ON l.topic_id = f.id and f.meeting_id = l.meeting_id
        left join T_TOPIC_MEETING_FILE r on l.ID = r.TOPIC_MEET_ID and r.FILE_TYPE = '1' and r.IS_ACTIVE = '1'
        WHERE f.entity_type = 'folder'
        and f.IS_ACTIVE = '1' -- 目录必须存在
        AND f.meeting_id IS NOT NULL
        AND l.PLAN_ID IS NULL
        UNION ALL
        -- 1.3、通过会议资料上传的特殊议题（与会议无关的）
        SELECT l.id AS id,
        f.id AS PARENT_ID,
        l.topic_title AS name,
        r.OSS_ID,
        'topic' as FILE_TYPE,
        l.TOPIC_ORDER as FILE_ORDER,
        l.IS_ACTIVE,
        l.CREATE_TIME
        FROM t_file_entity f
        inner JOIN T_TOPIC_MEETING_LINK l ON l.topic_id = f.id
        left join T_TOPIC_MEETING_FILE r on l.ID = r.TOPIC_MEET_ID and r.FILE_TYPE = '1' and r.IS_ACTIVE = '1'
        WHERE f.entity_type = 'folder'
        and f.IS_ACTIVE = '1' -- 目录必须存在
        AND f.file_type = 'topic'
        AND f.MEETING_ID is null
        AND l.PLAN_ID IS NULL
        UNION ALL
        -- 2: 资料
        SELECT id,
        PARENT_ID,
        name,
        OSS_ID,
        FILE_TYPE,
        FILE_ORDER,
        IS_ACTIVE,
        CREATE_TIME
        FROM t_file_entity
        WHERE entity_type = 'file') t
        where t.IS_ACTIVE = '1'
        <if test="ew != null">
            <if test="ew.nonEmptyOfWhere">
                AND
            </if>
            ${ew.sqlSegment}
        </if>
    </select>

</mapper>
