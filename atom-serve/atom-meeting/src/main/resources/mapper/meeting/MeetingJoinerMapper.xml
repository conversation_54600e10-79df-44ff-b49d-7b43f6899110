<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.MeetingJoinerMapper">
    <resultMap type="com.tenint.meeting.domain.MeetingJoiner" id="MeetingJoinerResult">
        <result property="id" column="id"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updaterId" column="updater_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="isActive" column="is_active"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="userId" column="user_id"/>
        <result property="attendanceStatus" column="attendance_status"/>
        <result property="userType" column="user_type"/>
        <result property="isAgree" column="is_agree"/>
        <result property="joinerOrder" column="joiner_order"/>
    </resultMap>

    <select id="selectJoinerMeetingVoByUserIdAndStatus" resultType="com.tenint.meeting.domain.vo.JoinerMeetingVo">
        select m.id, m.meeting_title, m.meeting_location, m.meeting_type, m.meeting_begin_time, m.meeting_end_time,
        m.status,
        mj.attendance_status from t_meeting m
        inner join t_meeting_joiner mj on m.id = mj.meeting_id
        where mj.user_id = #{userId}
        <if test="status != null">
            and m.status in
            <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginDate != null and beginDate != ''">
            and m.meeting_begin_time &gt;= #{beginDate}
        </if>
        and m.is_active = 1
        and mj.is_active = 1
    </select>
</mapper>
