<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.TopicMeetingAuditMapper">

    <resultMap type="com.tenint.meeting.domain.TopicMeetingAudit" id="TopicMeetingAuditResult">
            <result property="id" column="id"/>
            <result property="userType" column="user_type"/>
            <result property="userId" column="user_id"/>
            <result property="status" column="status"/>
            <result property="remark" column="remark"/>
            <result property="topicMeetId" column="topic_meet_id"/>
            <result property="isActive" column="is_active"/>
            <result property="creatorId" column="creator_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updaterId" column="updater_id"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="pageTopicMeetingAuditWithLeaderShip"
            resultType="com.tenint.meeting.domain.dto.TopicMeetingAuditDTO">
        SELECT TTM.ID topicId, TTM.TOPIC_TITLE, TTM.MEET_TYPE, TTM.CREATE_TIME, TTM.STATUS, TTM.CREATOR_DEPT, TTM.REPORT_USER_NAME, TTM.UPDATE_TIME
        FROM T_TOPIC_MEETING TTM INNER JOIN T_TOPIC_MEETING_AUDIT TTMA ON TTM.ID = TTMA.TOPIC_MEET_ID
        WHERE
        TTM.IS_ACTIVE = '1' AND TTMA.IS_ACTIVE = '1' AND TTMA.USER_ID = #{userId}
        AND ((TTMA.USER_TYPE = '1' AND TTM.STATUS = '1') OR (TTMA.USER_TYPE = '2' AND TTM.STATUS = '20'))
        <if test="ew!=null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
    </select>
    <select id="pageTopicMeetingAudited"
            resultType="com.tenint.meeting.domain.dto.TopicMeetingAuditDTO">
        SELECT
            TTM.ID AS topicId,
            TTM.TOPIC_TITLE,
            TTM.MEET_TYPE,
            TTM.CREATE_TIME,
            TTM.STATUS,
            TTM.CREATOR_DEPT,
            TTM.REPORT_USER_NAME,
            TTM.UPDATE_TIME
        FROM
            T_TOPIC_MEETING TTM
        WHERE
        TTM.IS_ACTIVE = '1'
        AND EXISTS (
        SELECT 1
        FROM T_TOPIC_MEETING_FLOW TTMF
        WHERE
        TTMF.TOPIC_MEET_ID = TTM.ID
        AND TTMF.USER_ID = #{userId}
        AND TTMF.FLOW_TYPE = '20'
        AND TTMF.IS_ACTIVE = '1'
        )
        <if test="ew != null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
    </select>
    <select id="pageTopicMeetingAuditWithGroup"
            resultType="com.tenint.meeting.domain.dto.TopicMeetingAuditDTO">
        SELECT TTM.ID topicId, TTM.TOPIC_TITLE, TTM.MEET_TYPE, TTM.CREATE_TIME, TTM.STATUS, TTM.CREATOR_DEPT, TTM.REPORT_USER_NAME, TTM.UPDATE_TIME
        FROM T_TOPIC_MEETING TTM INNER JOIN T_TOPIC_MEETING_AUDIT TTMA ON TTM.ID = TTMA.TOPIC_MEET_ID
        WHERE TTMA.USER_ID = #{userId} AND TTMA.USER_TYPE = '1' AND TTM.STATUS = '1' AND TTM.IS_ACTIVE = '1' AND TTMA.IS_ACTIVE = '1'
        <if test="ew!=null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
        UNION ALL
        SELECT TTM.ID topicId, TTM.TOPIC_TITLE, TTM.MEET_TYPE, TTM.CREATE_TIME, TTM.STATUS, TTM.CREATOR_DEPT, TTM.REPORT_USER_NAME, TTM.UPDATE_TIME
        FROM T_TOPIC_MEETING TTM INNER JOIN T_TOPIC_MEETING_AUDIT TTMA ON TTM.ID = TTMA.TOPIC_MEET_ID
        WHERE TTMA.USER_ID = #{userId} AND TTMA.USER_TYPE = '2' AND TTM.STATUS = '20' AND TTM.IS_ACTIVE = '1' AND TTMA.IS_ACTIVE = '1'
        <if test="ew!=null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
        UNION ALL
        SELECT TTM.ID topicId, TTM.TOPIC_TITLE, TTM.MEET_TYPE, TTM.CREATE_TIME, TTM.STATUS, TTM.CREATOR_DEPT, TTM.REPORT_USER_NAME, TTM.UPDATE_TIME
        FROM T_TOPIC_MEETING TTM
        WHERE TTM.STATUS = '30' AND TTM.IS_ACTIVE = '1'
        <if test="ew!=null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
    </select>
    <select id="pageTopicMeetingAuditWithAdmin"
            resultType="com.tenint.meeting.domain.dto.TopicMeetingAuditDTO">
        SELECT TTM.ID topicId, TTM.TOPIC_TITLE, TTM.MEET_TYPE, TTM.CREATE_TIME, TTM.STATUS, TTM.CREATOR_DEPT, TTM.REPORT_USER_NAME, TTM.UPDATE_TIME
        FROM T_TOPIC_MEETING TTM WHERE TTM.STATUS IN ('1', '20', '30') AND TTM.IS_ACTIVE = '1'
        <if test="ew!=null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
    </select>
    <select id="pageTopicMeetingAuditedWithAdmin"
            resultType="com.tenint.meeting.domain.dto.TopicMeetingAuditDTO">
        SELECT TTM.ID topicId, TTM.TOPIC_TITLE, TTM.MEET_TYPE, TTM.CREATE_TIME, TTM.STATUS, TTM.CREATOR_DEPT, TTM.REPORT_USER_NAME, TTM.UPDATE_TIME
        FROM T_TOPIC_MEETING TTM WHERE TTM.STATUS !='0' AND TTM.STATUS !='1' AND TTM.IS_ACTIVE = '1'
        <if test="ew!=null and ew.SqlSegment != null and ew.SqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
    </select>
</mapper>
