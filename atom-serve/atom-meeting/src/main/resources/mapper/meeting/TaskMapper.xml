<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.TaskMapper">

    <resultMap type="com.tenint.meeting.domain.Task" id="TaskResult">
            <result property="id" column="id"/>
            <result property="taskType" column="task_type"/>
            <result property="taskTitle" column="task_title"/>
            <result property="deptId" column="dept_id"/>
            <result property="userId" column="user_id"/>
            <result property="completeTime" column="complete_time"/>
            <result property="taskContent" column="task_content"/>
            <result property="isActive" column="is_active"/>
            <result property="createTime" column="create_time"/>
            <result property="creatorId" column="creator_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="updaterId" column="updater_id"/>
            <result property="orgCode" column="org_code"/>
            <result property="approvalTime" column="approval_time" />
            <result property="taskGist" column="task_gist" />
            <result property="publishTime" column="publish_time" />
            <result property="majorFillExpDays" column="major_fill_exp_days" />
            <result property="otherMajorIds" column="other_major_ids" />
    </resultMap>

</mapper>
