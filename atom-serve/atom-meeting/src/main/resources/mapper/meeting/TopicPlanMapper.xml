<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.TopicPlanDetailMapper">

    <resultMap type="com.tenint.meeting.domain.TopicPlanDetail" id="TopicPlanResult">
        <result property="id" column="id"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updaterId" column="updater_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="planId" column="plan_id"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="typeName" column="type_name"/>
        <result property="typeId" column="type_id"/>
        <result property="isActive" column="is_active"/>
    </resultMap>

    <resultMap id="topicFileAuditResultMap" type="com.tenint.meeting.domain.vo.TopicFileAuditRowVo">
        <id property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="createTime" column="create_time"/>
        <collection property="planList" ofType="com.tenint.meeting.domain.vo.TopicPlanDetailVo">
            <id property="id" column="plan_detail_id"/>
            <result property="typeName" column="plan_type_name"/>
            <result property="typeId" column="type_id"/>
            <result property="beginTime" column="plan_start_time"/>
            <result property="endTime" column="plan_end_time"/>
        </collection>
    </resultMap>

    <select id="selectPageJoinPlan" resultMap="topicFileDetailAuditResultMap">
        SELECT
        t.id AS plan_id,
        t.TITLE as plan_name,
        t.create_time,
        tp.id AS plan_detail_id,
        tp.type_id as plan_type_id,
        tp.type_name as plan_type_name,
        tp.begin_time as plan_start_time,
        tp.end_time as plan_end_time
        FROM T_TOPIC t
        LEFT JOIN t_topic_plan_detail tp ON t.id = tp.plan_id
        WHERE t.is_active = 1 and tp.is_active = 1
        <if test="wq!=null and wq.planName != ''">
            AND t.TITLE LIKE CONCAT('%', #{wq.planName}, '%')
        </if>
        <if test="wq!=null and wq.typeId != null">
            AND t.type_id = #{wq.typeId}
        </if>
    </select>
</mapper>
