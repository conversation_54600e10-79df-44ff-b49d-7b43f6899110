<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.TaskUserExecuteMapper">

    <resultMap type="com.tenint.meeting.domain.TaskUserExecute" id="TaskUserExecuteResult">
            <result property="id" column="id"/>
            <result property="taskId" column="task_id"/>
            <result property="executeWorkable" column="execute_workable"/>
            <result property="executeDelay" column="execute_delay"/>
            <result property="executePlan" column="execute_plan"/>
            <result property="planTime" column="plan_time"/>
            <result property="successTime" column="success_time"/>
            <result property="remark" column="remark"/>
            <result property="status" column="status"/>
            <result property="creatorId" column="creator_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updaterId" column="updater_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="isActive" column="is_active"/>
    </resultMap>

</mapper>
