<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.meeting.mapper.MeetingMapper">

    <resultMap type="com.tenint.meeting.domain.Meeting" id="MeetingResult">
            <result property="id" column="id"/>
            <result property="creatorId" column="creator_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updaterId" column="updater_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="isActive" column="is_active"/>
            <result property="meetingTitle" column="meeting_title"/>
            <result property="meetingBrief" column="meeting_brief"/>
            <result property="meetingBeginTime" column="meeting_begin_time"/>
            <result property="meetingEndTime" column="meeting_end_time"/>
            <result property="meetingNo" column="meeting_no"/>
            <result property="meetingType" column="meeting_type"/>
            <result property="userName" column="user_name" />
            <result property="isVote" column="is_vote" />
            <result property="meetingStatus" column="meeting_status" />
            <result property="meetingPlanId" column="meeting_plan_id" />
            <result property="userId" column="user_id" />
            <result property="registerId" column="register_id" />
    </resultMap>

</mapper>
