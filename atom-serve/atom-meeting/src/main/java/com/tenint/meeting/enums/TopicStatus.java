package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "TOPIC_STATUS",label = "议题收集状态",style = true)
@Getter
@AllArgsConstructor
public enum TopicStatus implements KeyLabelStyleEnum {

    PREPARE("0", "编辑", "primary"),

    START("1", "收集中", "primary"),

    END("9", "结束收集", "primary");


    private final String key,label,eleStyle;
}
