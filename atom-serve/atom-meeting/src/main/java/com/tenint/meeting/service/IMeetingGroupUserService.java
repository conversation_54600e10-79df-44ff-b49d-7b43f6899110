package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.MeetingGroupUser;
import com.tenint.meeting.domain.bo.MeetingGroupUserBo;

import java.util.Collection;
import java.util.List;
/**
 * 用户组用户Service接口
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
public interface IMeetingGroupUserService extends IService<MeetingGroupUser> {

    /**
     * 查询用户组用户列表
     */
    Page<MeetingGroupUser> pageMeetingGroupUser(MeetingGroupUserBo bo, PageQuery pageQuery);

    /**
     * 查询用户组用户列表
     */
    List<MeetingGroupUser> listMeetingGroupUser(MeetingGroupUserBo bo);

    /**
     * 校验并批量删除用户组用户信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量添加
     */
    void addListGroupUser(List<MeetingGroupUser> groupUsers,Long groupId);

    /**
     * 根据用户id查询用户
     */
    List<Long> getListByGroupId(Long groupId);

    /**
     * 根据id删除用户
     */
    void deleteByGroupId(Long groupId);
}
