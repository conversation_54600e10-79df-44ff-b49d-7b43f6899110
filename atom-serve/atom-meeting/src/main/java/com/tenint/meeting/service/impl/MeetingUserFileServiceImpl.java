package com.tenint.meeting.service.impl;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.MeetingUserFile;
import com.tenint.meeting.domain.bo.MeetingUserFileBo;
import com.tenint.meeting.mapper.MeetingUserFileMapper;
import com.tenint.meeting.service.IMeetingUserFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会议用户文件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RequiredArgsConstructor
@Service
public class MeetingUserFileServiceImpl extends ServiceImpl<MeetingUserFileMapper, MeetingUserFile> implements IMeetingUserFileService {


    /**
     * 查询会议用户文件信息列表
     */
    @Override
    public Page<MeetingUserFile> pageMeetingUserFile(MeetingUserFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MeetingUserFile> lqw = buildQueryWrapper(bo);
        Page<MeetingUserFile> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询会议用户文件信息列表
     */
    @Override
    public List<MeetingUserFile> listMeetingUserFile(MeetingUserFileBo bo) {
        LambdaQueryWrapper<MeetingUserFile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建会议用户文件信息列表查询条件
     */
    private LambdaQueryWrapper<MeetingUserFile> buildQueryWrapper(MeetingUserFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeetingUserFile> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getUserId() != null, MeetingUserFile::getUserId, bo.getUserId());
            lqw.eq(bo.getFileId() != null, MeetingUserFile::getFileId, bo.getFileId());
        return lqw;
    }


    /**
     * 批量删除会议用户文件信息
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> fileIds, Boolean isValid) {
        if(!isValid){
           return false;
        }
        LambdaQueryWrapper<MeetingUserFile> lqw = Wrappers.lambdaQuery();
        lqw.in(MeetingUserFile::getFileId, fileIds);
        return baseMapper.delete(lqw) > 0;
    }

    @Override
    public List<MeetingUserFile> listUserFileByFileId(List<Long> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MeetingUserFile> lqw = Wrappers.lambdaQuery();
        lqw.in(MeetingUserFile::getFileId, fileIds);
        return list(lqw);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOrUpdateBatch(Long fileId, List<Long> userList) {

        if (CollUtil.isEmpty(userList)) {
            LambdaQueryWrapper<MeetingUserFile> lqw = Wrappers.lambdaQuery();
            lqw.eq(MeetingUserFile::getFileId, fileId);
            return remove(lqw);
        }

        List<MeetingUserFile> userFiles = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MeetingUserFile::getFileId, fileId).list();

        List<MeetingUserFile> userFileList = userList.stream().map(item -> {
            MeetingUserFile userFile = new MeetingUserFile();
            userFile.setFileId(fileId);
            userFile.setUserId(item);
            return userFile;
        }).toList();

        if (CollUtil.isEmpty(userFiles)) {
            userFileList.forEach(userFile -> userFile.setFileId(fileId));
            return saveBatch(userFileList);
        }else {
            List<Long> oldIds = userFiles.stream().map(MeetingUserFile::getId).toList();
            List<Long> newIds = userFileList.stream().map(MeetingUserFile::getId).filter(Objects::nonNull).toList();
            // 需要新增的
            List<MeetingUserFile> newList = userFileList.stream().filter(item -> Objects.isNull(item.getId())).toList();
            newList.forEach(userFile -> userFile.setFileId(fileId));
            // 需要更新的
            List<MeetingUserFile> updateList = userFileList.stream().filter(item -> Objects.nonNull(item.getId()))
                    .filter(item -> oldIds.contains(item.getId())).toList();
            // 需要删除的
            List<MeetingUserFile> removeList = userFiles.stream().filter(item -> !newIds.contains(item.getId())).toList();

            saveBatch(newList);
            updateBatchById(updateList);
            removeBatchByIds(removeList);
            return true;
        }

    }

}
