package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TaskUserGroup;
import com.tenint.meeting.domain.bo.TaskUserGroupBo;

import java.util.Collection;
import java.util.List;
/**
 * 执行人员详情Service接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface ITaskUserGroupService extends IService<TaskUserGroup> {

    /**
     * 查询执行人员详情列表
     */
    Page<TaskUserGroup> pageTaskUserGroup(TaskUserGroupBo bo, PageQuery pageQuery);

    /**
     * 查询执行人员详情列表
     */
    List<TaskUserGroup> listTaskUserGroup(TaskUserGroupBo bo);

    /**
     * 校验并批量删除执行人员详情信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量添加
     */
    void bathAddUserGroup(List<TaskUserGroup> userGroups,Long mainId);

    /**
     * 删除
     */
    void removeByMainId(Long mainId);

    /**
     * 根据用户id查询用户
     */
    Long getListByMainId(Long mainId);


}
