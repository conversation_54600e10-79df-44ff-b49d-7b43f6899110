package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.MeetingServe;
import com.tenint.meeting.domain.bo.MeetingServeBo;
import com.tenint.meeting.mapper.MeetingServeMapper;
import com.tenint.meeting.service.IMeetingServeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 会议服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@RequiredArgsConstructor
@Service
public class MeetingServeServiceImpl extends ServiceImpl<MeetingServeMapper, MeetingServe> implements IMeetingServeService {


    /**
     * 查询会议服务列表
     */
    @Override
    public Page<MeetingServe> pageMeetingServe(MeetingServeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MeetingServe> lqw = buildQueryWrapper(bo);
        Page<MeetingServe> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询会议服务列表
     */
    @Override
    public List<MeetingServe> listMeetingServe(MeetingServeBo bo) {
        LambdaQueryWrapper<MeetingServe> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建会议服务列表查询条件
     */
    private LambdaQueryWrapper<MeetingServe> buildQueryWrapper(MeetingServeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeetingServe> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getMeetingId() != null, MeetingServe::getMeetingId, bo.getMeetingId());
            lqw.eq(bo.getUserId() != null, MeetingServe::getUserId, bo.getUserId());
            lqw.eq(StringUtils.isNotBlank(bo.getTitle()), MeetingServe::getTitle, bo.getTitle());
            lqw.orderByDesc(MeetingServe::getCreateTime);
        return lqw;
    }


    /**
     * 批量删除会议服务
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<Long> getListByUserId(Long userId) {
        return baseMapper.selectList(Wrappers.<MeetingServe>lambdaQuery().eq(MeetingServe::getUserId, userId))
                .stream().map(MeetingServe::getMeetingId).distinct().toList();
    }

}
