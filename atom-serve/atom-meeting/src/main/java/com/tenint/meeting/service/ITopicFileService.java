package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicFile;
import com.tenint.meeting.domain.bo.TopicFileBo;
import java.util.Collection;
import java.util.List;
/**
 * 议题附件Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface ITopicFileService extends IService<TopicFile> {

    /**
     * 查询议题附件列表
     */
    Page<TopicFile> pageTopicFile(TopicFileBo bo, PageQuery pageQuery);

    /**
     * 查询议题附件列表
     */
    List<TopicFile> listTopicFile(TopicFileBo bo);

    /**
     * 校验并批量删除议题附件信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

}
