package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 会议消息通知业务对象 t_meeting_notify
 *
 * <AUTHOR>
 * @date 2024-12-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingNotifyBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 会议id
     */
    @NotNull(message = "会议id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingId;


    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String title;


    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String userName;


}
