package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tenint.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serial;
/**
 * 消息推送主体对象 t_message
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Data
@TableName("t_message")
public class Message extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * $column.columnComment
    */
    private Long id;

   /**
    * 用户id
    */
    private Long userId;

   /**
    * 是否已读
    */
    private String isRead;

   /**
    * 消息推送内容
    */
    private String description;

    /**
     * 1：消息，2：待办
     * @see com.tenint.meeting.enums.MessageModuleEnum
     */
    private String module;

    /**
     * 1：消息，2：待办
     * @see MessageTypeEnum
     */    /**
     *
     */
    private String type;

    /**
     * 完成的
     */
    private Boolean completed;

   /**
    * $column.columnComment
    */
    @TableField(fill = FieldFill.INSERT)
    private String isActive;

    private Long businessId;

}
