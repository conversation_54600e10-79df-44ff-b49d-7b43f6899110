package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 会议消息通知视图对象 t_meeting_notify
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@ExcelIgnoreUnannotated
public class MeetingNotifyVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 会议id
     */
    @ExcelProperty(value = "会议id")
    private Long meetingId;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "用户姓名")
    private String userName;


}

