package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 文件业务对象 t_file_entity
 *
 * <AUTHOR>
 * @date 2024-03-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class FileEntityBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 父节点
     */
    private Long parentId;


    /**
     *  文件/文件夹
     */
    @NotBlank(message = " 文件/文件夹不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String entityType;


    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String name;


    /**
     * 文件大小
     */
    private BigDecimal fileSize;


    /**
     * 祖级列表
     */
    private String ancestors;


    /**
     * 状态
     */
    private String status;


    /**
     * 文件标记, 什么重点文件啥的
     */
    private String flag;


    /**
     * 备注
     */
    private String remark;


    /**
     * 管理的文件信息
     */
    private Long ossId;


    /**
     * 排序
     */
    private Long fileOrder;


    /**
     * 文件类型文件类型（如.txt, .jpg等）。对于目录，此字段可以为NULL
     */
    private String fileType;

    /**
     * 关联人员
     */
    private List<Long> userList;

    /**
     * 上传日期搜索条件
     */
    private String date;

    /**
     * 文件状态
     */
    private String fileStatus;

    private List<Long> parentIds;

    private Long meetingId;

    private String[] dateRange;
}
