package com.tenint.meeting.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TopicPlanDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tenint.meeting.domain.query.TopicAuditQuery;
import com.tenint.meeting.domain.vo.TopicFileAuditRowVo;
import com.tenint.meeting.domain.vo.TopicPlanRowVo;
import com.tenint.meeting.domain.vo.TopicPlanVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 议题计划时间Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface TopicPlanDetailMapper extends BaseMapper<TopicPlanDetail> {

    /**
     * 查询计划下的计划详细
     * @param planId
     * @return
     */
    default List<TopicPlanDetail> selectListByPlanId(Long planId) {
       return selectList(Wrappers.lambdaQuery(TopicPlanDetail.class).eq(TopicPlanDetail::getPlanId, planId));
    }

    /**
     * @param Ids
     * @return
     */
    default List<TopicPlanDetail> selectListByPlanIds(List<Long> planIds) {
       return selectList(Wrappers.lambdaQuery(TopicPlanDetail.class).in(TopicPlanDetail::getPlanId, planIds));
    }

    /**
     * 根据根据会议计划删除计划详细
     * @param planIds 议题主键
     */
    default void deleteByTopicIds(Collection<Long> planIds) {
        delete(Wrappers.lambdaQuery(TopicPlanDetail.class).in(TopicPlanDetail::getPlanId, planIds));
    }

    /**
     * 查询关联的议题下的所有会议计划
     *
     * @param
     * @param build
     * @return
     */
    Page<TopicFileAuditRowVo> selectPageJoinPlan(@Param("wq") TopicAuditQuery wq, Page<TopicPlanDetail> build);

    List<TopicPlanRowVo> selectListByAvailable(@Param("nowDate") Date nowDate);
}
