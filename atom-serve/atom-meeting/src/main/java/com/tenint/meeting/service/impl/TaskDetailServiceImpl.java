package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.TaskDetail;
import com.tenint.meeting.domain.bo.TaskDetailBo;
import com.tenint.meeting.mapper.TaskDetailMapper;
import com.tenint.meeting.service.ITaskDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 督查督办明细子Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@RequiredArgsConstructor
@Service
public class TaskDetailServiceImpl extends ServiceImpl<TaskDetailMapper, TaskDetail> implements ITaskDetailService {


    /**
     * 查询督查督办明细子列表
     */
    @Override
    public Page<TaskDetail> pageTaskDetail(TaskDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TaskDetail> lqw = buildQueryWrapper(bo);
        Page<TaskDetail> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询督查督办明细子列表
     */
    @Override
    public List<TaskDetail> listTaskDetail(TaskDetailBo bo) {
        LambdaQueryWrapper<TaskDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建督查督办明细子列表查询条件
     */
    private LambdaQueryWrapper<TaskDetail> buildQueryWrapper(TaskDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TaskDetail> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getTaskId() != null, TaskDetail::getTaskId, bo.getTaskId());
            lqw.eq(StringUtils.isNotBlank(bo.getTaskType()), TaskDetail::getTaskType, bo.getTaskType());
            lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), TaskDetail::getSourceType, bo.getSourceType());
            lqw.eq(bo.getMeetingId() != null, TaskDetail::getMeetingId, bo.getMeetingId());
            lqw.eq(StringUtils.isNotBlank(bo.getSourceContent()), TaskDetail::getSourceContent, bo.getSourceContent());
        return lqw;
    }


    /**
     * 批量删除督查督办明细子
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void removeDetailByTaskId(List<Long> taskIds) {
        LambdaQueryChainWrapper<TaskDetail> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .in(TaskDetail::getTaskId, taskIds);
        remove(wrapper.getWrapper());
    }

}
