package com.tenint.meeting.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * 参会人员会议信息Vo
 * <AUTHOR>
 * @date 2024/03/13
 */
@Data
public class JoinerMeetingVo {
    private Long id;
    /**
     * 会议标题
     */
    @ExcelProperty(value = "会议标题")
    private String meetingTitle;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 会议日期
     */
    private LocalDate meetingDate;


    /**
     * 会议时间
     */
    private String[] meetingTime;


    /**
     * 会议状态
     */
    private String status;

    /**
     * 签到状态
     */
    private Boolean attendanceStatus;


    /**
     * 会议发起人/部门
     */
    private String sponsor;

    /**
     * 会议开始时间
     */
    @JsonIgnore
    private Date meetingBeginTime;

    /**
     * 会议结束时间
     */
    @JsonIgnore
    private Date meetingEndTime;


    public LocalDate getMeetingDate() {
        if (meetingBeginTime == null) {
            return null;
        }
        return meetingBeginTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public String[] getMeetingTime() {
        if (meetingBeginTime == null || meetingEndTime == null) {
            return null;
        }
        return new String[]{meetingBeginTime.toInstant().atZone(ZoneId.systemDefault()).toLocalTime().toString(),
                meetingEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalTime().toString()};
    }
}

