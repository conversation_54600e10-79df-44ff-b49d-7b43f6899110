package com.tenint.meeting.enums;


import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "MEETING_STATUS_TOPIC",label = "会议进行状态",style = true)
@Getter
@AllArgsConstructor
public enum MeetingStatusTopic implements KeyLabelStyleEnum {

    /**
     * 未开始
     */
    UNSTART("0", "未开始", "primary"),

    /**
     * 提交
     */
    INPROGRESS("1", "进行中", "primary"),

    /**
     * 会议结束
     */
    OVER("9", "会议结束", "success");


    private final String key,label,eleStyle;
}
