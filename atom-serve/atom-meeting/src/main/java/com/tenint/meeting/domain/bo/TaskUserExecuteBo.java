package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 督查督办执行业务对象 t_task_user_execute
 *
 * <AUTHOR>
 * @date 2024-10-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TaskUserExecuteBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;



    /**
     * 督办主表id
     */
    @NotNull(message = "督办主表id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long taskId;


    /**
     * 落实情况
     */
    @NotBlank(message = "落实情况不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String executeWorkable;


    /**
     * 延迟原因
     */
    private String executeDelay;


    /**
     * 当前进度，后续计划
     */
    private String executePlan;


    /**
     * 计划完成时间
     */
    private Date planTime;


    /**
     * 实际完成时间
     */
    private Date successTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 部门负责人审核意见
     */
    private String auditRemark;

    /**
     * 分管领导补充意见
     */
    private String majorRemark;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否是负责人执行（未指派直接执行完成）
     */
    private Boolean noAssignExecute;

    /**
     * 部门负责人审核是否通过（审核用）
     */
    private Boolean auditPass;

}
