package com.tenint.meeting.domain.vo;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 督查督办执行视图对象 t_task_user_execute
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
@ExcelIgnoreUnannotated
public class TaskUserExecuteVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 督办主表id
     */
    @ExcelProperty(value = "督办主表id")
    private Long taskId;

    /**
     * 落实情况
     */
    @ExcelProperty(value = "落实情况")
    private String executeWorkable;

    /**
     * 延迟原因
     */
    @ExcelProperty(value = "延迟原因")
    private String executeDelay;

    /**
     * 当前进度，后续计划
     */
    @ExcelProperty(value = "当前进度，后续计划")
    private String executePlan;

    /**
     * 计划完成时间
     */
    @ExcelProperty(value = "计划完成时间")
    private Date planTime;

    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间")
    private Date successTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    private String userName;


}

