package com.tenint.meeting.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.constant.CacheNames;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.convert.TopicMeetingPlanConvert;
import com.tenint.meeting.domain.TopicMeetingPlan;
import com.tenint.meeting.domain.bo.TopicMeetingPlanBo;
import com.tenint.meeting.domain.vo.TopicMeetingPlanVo;
import com.tenint.meeting.service.ITopicMeetingPlanService;
import com.tenint.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 预约综合Service层
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingPlanManager {

    private final ITopicMeetingPlanService topicMeetingPlanService;

    /**
     * 查询预约
     */
    public TopicMeetingPlanVo getVoById(Long id) {
        TopicMeetingPlan topicMeetingPlan = topicMeetingPlanService.getById(id);
        TopicMeetingPlanVo topicMeetingPlanVo = TopicMeetingPlanConvert.INSTANCE.convert(topicMeetingPlan);
        return topicMeetingPlanVo;
    }

    /**
     * 根据ids查询预会议
     * @param ids 预会议ids
     * @return 结果
     */
    public List<TopicMeetingPlanVo> getVoListByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<TopicMeetingPlan> plans = topicMeetingPlanService.listByIds(ids);
        List<TopicMeetingPlanVo> vos = TopicMeetingPlanConvert.INSTANCE.convertList(plans);
        return vos;
    }

    /**
     * 根据议题查询关联的预会议vo
     * @param topicId 议题id
     * @return 结果
     */
    public List<TopicMeetingPlanVo> getVoListByTopicId(Long topicId) {
        List<TopicMeetingPlan> plans = topicMeetingPlanService.getListByTopicId(topicId);
        List<TopicMeetingPlanVo> vos = TopicMeetingPlanConvert.INSTANCE.convertList(plans);
        return vos;
    }

    /**
     * 查询预约列表
     */
    public TableDataInfo<TopicMeetingPlanVo> pageTopicMeetingPlanVo(TopicMeetingPlanBo bo, PageQuery pageQuery) {
        Page<TopicMeetingPlan> page = topicMeetingPlanService.pageTopicMeetingPlan(bo, pageQuery);
        Page<TopicMeetingPlanVo> pageVo = TopicMeetingPlanConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询预约列表
     */
    public List<TopicMeetingPlanVo> listTopicMeetingPlanVo(TopicMeetingPlanBo bo) {
        List<TopicMeetingPlan> list = topicMeetingPlanService.listTopicMeetingPlan(bo);
        List<TopicMeetingPlanVo> listVo = TopicMeetingPlanConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增预约
     */
    public Boolean saveByBo(TopicMeetingPlanBo bo) {
        TopicMeetingPlan topicMeetingPlan = TopicMeetingPlanConvert.INSTANCE.convert(bo);
        return topicMeetingPlanService.save(topicMeetingPlan);
    }

    /**
     * 修改预约
     */
    public Boolean updateByBo(TopicMeetingPlanBo bo) {
        validateTopicMeetingPlanExists(bo.getId());
        TopicMeetingPlan topicMeetingPlan = TopicMeetingPlanConvert.INSTANCE.convert(bo);
        return topicMeetingPlanService.updateById(topicMeetingPlan);
    }


    /**
     * 校验并批量删除预约信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return topicMeetingPlanService.removeWithValidByIds(ids, isValid);
    }


    private void validateTopicMeetingPlanExists(Long id) {
        if (topicMeetingPlanService.getById(id) == null) {
            throw new ServiceException("预约数据不存在");
        }
    }

    /**
     * 获取所有预召开会议
     * @return
     */
    public List<TopicMeetingPlanVo> listAllMeetingPlan() {

        List<TopicMeetingPlan> planList = topicMeetingPlanService.lambdaQuery()
                .select(TopicMeetingPlan::getId, TopicMeetingPlan::getTitle, TopicMeetingPlan::getType, TopicMeetingPlan::getLastTime)
                .ge(TopicMeetingPlan::getLastTime, new Date())
                .orderByDesc(TopicMeetingPlan::getCreateTime).list();
        return TopicMeetingPlanConvert.INSTANCE.convertList(planList);
    }

    /**
     * 获取所有预召开会议 包含当前过期的
     * @param delayPlanId
     * @return
     */
    public List<TopicMeetingPlanVo> listAllMeetingPlanWithDelay(Long delayPlanId) {

        List<TopicMeetingPlan> planList = topicMeetingPlanService.lambdaQuery()
                .select(TopicMeetingPlan::getId, TopicMeetingPlan::getTitle, TopicMeetingPlan::getType, TopicMeetingPlan::getLastTime)
                .ge(TopicMeetingPlan::getLastTime, new Date())
                .or().eq(TopicMeetingPlan::getId,delayPlanId)
                .orderByDesc(TopicMeetingPlan::getCreateTime)
                .list();

//        TopicMeetingPlan delay = topicMeetingPlanService.getById(delayPlanId);
//        planList.add(delay);

        return TopicMeetingPlanConvert.INSTANCE.convertList(planList);
    }

}
