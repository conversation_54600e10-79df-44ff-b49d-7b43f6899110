package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题预会议关联对象 t_topic_plan_link
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@TableName("t_topic_plan_link")
public class TopicPlanLink {

   /**
    * 主键
    */
    @TableId(value = "id")
    private Long id;

   /**
    * 议题id
    */
    private Long topicId;

   /**
    * 预会议id
    */
    private Long planId;

    /**
     * 议题排序
     */
    private Integer topicOrder;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
