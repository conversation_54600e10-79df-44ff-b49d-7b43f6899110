package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.core.domain.BaseEntity;

/**
 * 会议服务业务对象 t_meeting_serve
 *
 * <AUTHOR>
 * @date 2024-12-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingServeBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 会议id
     */
    @NotNull(message = "会议id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingId;


    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String title;


    /**
     * 服务时间
     */
//    @NotNull(message = "服务时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 是否完成
     */
    private String isFinish;
}
