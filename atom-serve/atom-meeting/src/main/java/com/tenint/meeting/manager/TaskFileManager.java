package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.common.utils.redis.OssDelayedUtils;
import com.tenint.meeting.convert.TaskFileConvert;
import com.tenint.meeting.domain.TaskFile;
import com.tenint.meeting.domain.bo.TaskFileBo;
import com.tenint.meeting.domain.vo.TaskFileVo;
import com.tenint.meeting.service.ITaskFileService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Collection;
import java.util.Objects;

/**
 * 督查督办附件综合Service层
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RequiredArgsConstructor
@Service
public class TaskFileManager {

    private final ITaskFileService taskFileService;

    /**
     * 查询督查督办附件
     */
    public TaskFileVo getVoById(Long id) {
        TaskFile taskFile = taskFileService.getById(id);
        TaskFileVo taskFileVo = TaskFileConvert.INSTANCE.convert(taskFile);
        return taskFileVo;
    }

    /**
     * 查询督查督办附件列表
     */
    public TableDataInfo<TaskFileVo> pageTaskFileVo(TaskFileBo bo, PageQuery pageQuery) {
        Page<TaskFile> page = taskFileService.pageTaskFile(bo, pageQuery);
        Page<TaskFileVo> pageVo = TaskFileConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询督查督办附件列表
     */
    public List<TaskFileVo> listTaskFileVo(TaskFileBo bo) {
        List<TaskFile> list = taskFileService.listTaskFile(bo);
        List<TaskFileVo> listVo = TaskFileConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增督查督办附件
     */
    public Boolean saveByBo(TaskFileBo bo) {
        TaskFile taskFile = TaskFileConvert.INSTANCE.convert(bo);
        return taskFileService.save(taskFile);
    }

    /**
     * 修改督查督办附件
     */
    public Boolean updateByBo(TaskFileBo bo) {
        validateTaskFileExists(bo.getId());
        TaskFile taskFile = TaskFileConvert.INSTANCE.convert(bo);
        return taskFileService.updateById(taskFile);
    }


    /**
     * 校验并批量删除督查督办附件信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return taskFileService.removeWithValidByIds(ids, isValid);
    }


    private void validateTaskFileExists(Long id) {
        if (taskFileService.getById(id) == null) {
            throw new ServiceException("督查督办附件数据不存在");
        }
    }

    /**
     * 根据任务主表保存附件
     * @param fileBoList
     * @param taskId
     * @param fileType
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateFileByTask(List<TaskFileBo> fileBoList, Long taskId, String fileType) {

        List<TaskFile> taskFiles = TaskFileConvert.INSTANCE.convertBoList(fileBoList);
        if (CollectionUtils.isEmpty(taskFiles)) {
            return;
        }
        List<TaskFile> remainList = taskFileService.listFileByTaskIdWithType(taskId, fileType);
        if (CollectionUtils.isEmpty(remainList)) {
            taskFiles.forEach(item -> {item.setTaskId(taskId); item.setFileType(fileType);});
            taskFileService.saveBatch(taskFiles);
            List<String> ossId = taskFiles.stream().map(item -> String.valueOf(item.getId())).toList();
            OssDelayedUtils.closeDelete(ossId);
        }else {
            List<TaskFile> insertList = taskFiles.stream().filter(item -> Objects.isNull(item.getId())).toList();
            insertList.forEach(item -> {item.setTaskId(taskId); item.setFileType(fileType);});
            List<Long> remainIds = remainList.stream().map(TaskFile::getId).toList();
            List<TaskFile> updateList = taskFiles.stream().filter(item -> remainIds.contains(item.getId())).toList();
            List<Long> fileIds = taskFiles.stream().map(TaskFile::getId).filter(Objects::nonNull).toList();
            List<TaskFile> removeList = remainList.stream().filter(item -> !fileIds.contains(item.getId())
                    && Objects.nonNull(item.getId())).toList();
            taskFileService.saveBatch(insertList);
            if (CollectionUtils.isNotEmpty(updateList)) {
                taskFileService.updateBatchById(updateList);
            }
            if (CollectionUtils.isNotEmpty(removeList)) {
                taskFileService.removeBatchByIds(removeList);
            }
            List<String> ossId = insertList.stream().map(item -> String.valueOf(item.getId())).toList();
            OssDelayedUtils.closeDelete(ossId);
        }
    }

    /**
     * 查询关联的任务附件
     * @param taskId
     * @param fileType
     * @return
     */
    public List<TaskFileVo> listFileByTaskId(Long taskId, String fileType) {

        List<TaskFile> fileList = taskFileService.listFileByTaskIdWithType(taskId, fileType);
        return TaskFileConvert.INSTANCE.convertList(fileList);
    }

    /**
     * 根据任务id删除关联的附件记录
     * @param taskIds
     * @param isValid
     */
    public void removeFileByTaskId(List<Long> taskIds, Boolean isValid) {
        if (isValid) {
            taskFileService.removeFileByTaskId(taskIds);
        }
    }

    /**
     * 根据任务id和类型删除关联的附件记录
     * @param taskId
     * @param fileType
     */
    public void removeFileByTaskIdWithType(Long taskId, String fileType) {
        taskFileService.removeFileByTaskIdWithType(taskId, fileType);
    }
}
