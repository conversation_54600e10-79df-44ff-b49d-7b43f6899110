package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.meeting.domain.TopicFileAudit;
import com.tenint.meeting.mapper.TopicFileAuditMapper;
import com.tenint.meeting.service.ITopicFileAuditService;
import com.tenint.meeting.domain.bo.TopicFileAuditBo;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 议题文件审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@RequiredArgsConstructor
@Service
public class TopicFileAuditServiceImpl extends ServiceImpl<TopicFileAuditMapper, TopicFileAudit> implements ITopicFileAuditService {


    /**
     * 查询议题文件审核列表
     */
    @Override
    public Page<TopicFileAudit> pageTopicFileAudit(TopicFileAuditBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopicFileAudit> lqw = buildQueryWrapper(bo);
        Page<TopicFileAudit> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题文件审核列表
     */
    @Override
    public List<TopicFileAudit> listTopicFileAudit(TopicFileAuditBo bo) {
        LambdaQueryWrapper<TopicFileAudit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题文件审核列表查询条件
     */
    private LambdaQueryWrapper<TopicFileAudit> buildQueryWrapper(TopicFileAuditBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopicFileAudit> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getUserId() != null, TopicFileAudit::getUserId, bo.getUserId());
            lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TopicFileAudit::getStatus, bo.getStatus());
            lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), TopicFileAudit::getRemarks, bo.getRemarks());
            lqw.eq(bo.getTopicId() != null, TopicFileAudit::getTopicId, bo.getTopicId());
            lqw.eq(bo.getFileId() != null, TopicFileAudit::getFileId, bo.getFileId());
        return lqw;
    }


    /**
     * 批量删除议题文件审核
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
