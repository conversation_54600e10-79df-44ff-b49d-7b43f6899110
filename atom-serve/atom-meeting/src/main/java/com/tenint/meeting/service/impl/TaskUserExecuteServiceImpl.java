package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.TaskUserExecute;
import com.tenint.meeting.domain.bo.TaskUserExecuteBo;
import com.tenint.meeting.mapper.TaskUserExecuteMapper;
import com.tenint.meeting.service.ITaskUserExecuteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 督查督办执行Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RequiredArgsConstructor
@Service
public class TaskUserExecuteServiceImpl extends ServiceImpl<TaskUserExecuteMapper, TaskUserExecute> implements ITaskUserExecuteService {


    /**
     * 查询督查督办执行列表
     */
    @Override
    public Page<TaskUserExecute> pageTaskUserExecute(TaskUserExecuteBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TaskUserExecute> lqw = buildQueryWrapper(bo);
        Page<TaskUserExecute> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询督查督办执行列表
     */
    @Override
    public List<TaskUserExecute> listTaskUserExecute(TaskUserExecuteBo bo) {
        LambdaQueryWrapper<TaskUserExecute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建督查督办执行列表查询条件
     */
    private LambdaQueryWrapper<TaskUserExecute> buildQueryWrapper(TaskUserExecuteBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TaskUserExecute> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getTaskId() != null, TaskUserExecute::getTaskId, bo.getTaskId());
            lqw.eq(StringUtils.isNotBlank(bo.getExecuteWorkable()), TaskUserExecute::getExecuteWorkable, bo.getExecuteWorkable());
            lqw.eq(StringUtils.isNotBlank(bo.getExecuteDelay()), TaskUserExecute::getExecuteDelay, bo.getExecuteDelay());
            lqw.eq(StringUtils.isNotBlank(bo.getExecutePlan()), TaskUserExecute::getExecutePlan, bo.getExecutePlan());
            lqw.eq(bo.getPlanTime() != null, TaskUserExecute::getPlanTime, bo.getPlanTime());
            lqw.eq(bo.getSuccessTime() != null, TaskUserExecute::getSuccessTime, bo.getSuccessTime());
            lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TaskUserExecute::getStatus, bo.getStatus());
        return lqw;
    }


    /**
     * 批量删除督查督办执行
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void removeTaskExecuteByTaskId(List<Long> taskIds) {
        LambdaQueryChainWrapper<TaskUserExecute> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .in(TaskUserExecute::getTaskId, taskIds);
        remove(wrapper.getWrapper());
    }

}
