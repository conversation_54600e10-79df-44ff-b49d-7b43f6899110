package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "FILE_ENTITY_STATUS",label = "文件类型",style = true)
@Getter
@AllArgsConstructor
public enum FileFlag implements KeyLabelStyleEnum {

    FIXED("0", "不可修改的", "success");



    private final String key,label,eleStyle;

}
