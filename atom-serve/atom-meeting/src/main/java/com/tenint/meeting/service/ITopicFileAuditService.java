package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicFileAudit;
import com.tenint.meeting.domain.bo.TopicFileAuditBo;
import java.util.Collection;
import java.util.List;
/**
 * 议题文件审核Service接口
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface ITopicFileAuditService extends IService<TopicFileAudit> {

    /**
     * 查询议题文件审核列表
     */
    Page<TopicFileAudit> pageTopicFileAudit(TopicFileAuditBo bo, PageQuery pageQuery);

    /**
     * 查询议题文件审核列表
     */
    List<TopicFileAudit> listTopicFileAudit(TopicFileAuditBo bo);

    /**
     * 校验并批量删除议题文件审核信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

}
