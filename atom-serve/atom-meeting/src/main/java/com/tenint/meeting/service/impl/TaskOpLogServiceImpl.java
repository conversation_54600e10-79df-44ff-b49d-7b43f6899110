package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.meeting.domain.TaskOpLog;
import com.tenint.meeting.domain.TaskUser;
import com.tenint.meeting.mapper.TaskOpLogMapper;
import com.tenint.meeting.service.ITaskOpLogService;
import com.tenint.meeting.domain.bo.TaskOpLogBo;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 督查督办操作流程日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class TaskOpLogServiceImpl extends ServiceImpl<TaskOpLogMapper, TaskOpLog> implements ITaskOpLogService {


    /**
     * 查询督查督办操作流程日志列表
     */
    @Override
    public Page<TaskOpLog> pageTaskOpLog(TaskOpLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TaskOpLog> lqw = buildQueryWrapper(bo);
        Page<TaskOpLog> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询督查督办操作流程日志列表
     */
    @Override
    public List<TaskOpLog> listTaskOpLog(TaskOpLogBo bo) {
        LambdaQueryWrapper<TaskOpLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建督查督办操作流程日志列表查询条件
     */
    private LambdaQueryWrapper<TaskOpLog> buildQueryWrapper(TaskOpLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TaskOpLog> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getTaskId() != null, TaskOpLog::getTaskId, bo.getTaskId());
            lqw.eq(bo.getUserId() != null, TaskOpLog::getUserId, bo.getUserId());
            lqw.like(StringUtils.isNotBlank(bo.getUserName()), TaskOpLog::getUserName, bo.getUserName());
            lqw.like(StringUtils.isNotBlank(bo.getOpName()), TaskOpLog::getOpName, bo.getOpName());
        return lqw;
    }


    /**
     * 批量删除督查督办操作流程日志
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void removeByTaskId(Collection<Long> taskIds) {
        LambdaQueryChainWrapper<TaskOpLog> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .in(TaskOpLog::getTaskId, taskIds);
        remove(wrapper.getWrapper());
    }

}
