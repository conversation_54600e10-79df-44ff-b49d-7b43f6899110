package com.tenint.meeting.domain.bo;

import com.tenint.common.constant.CacheNames;
import com.tenint.common.core.domain.BaseEntity;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 待办配置业务对象 sys_todo_config
 *
 * <AUTHOR>
 * @date 2023-10-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MessageConfigBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 模块
     */
    @NotBlank(message = "消息模块不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String messageModule;


    /**
     * 待办类型
     */
    @NotBlank(message = "消息类型不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String messageType;


    /**
     * 组件路径
     */
    private String componentUrl;


    /**
     * 组件类型 dialog router custom
     */
    @NotBlank(message = "组件类型 dialog router custom不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String componentType;


    /**
     * 参数 跳转或者对话框弹出需要的参数信息
     */
    private String componentParams;


    /**
     * 关联的业务表
     */
    private String businessTable;


    /**
     * 对话框字段映射
     */
    private String dialogField;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String clientType;

    /**
     * 完成相关代办
     */
    private Boolean associated;


    public String cacheKey() {
        return CacheNames.MESSAGE_CONFIG + ":" + clientType + ":" + messageType;
    }
}
