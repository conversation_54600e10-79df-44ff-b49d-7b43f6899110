package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 会议签名业务对象 t_meeting_user_sign
 *
 * <AUTHOR>
 * @date 2024-09-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingUserSignBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;



    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 签名base64码
     */
    @NotBlank(message = "签名base64码不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String signPic;


    /**
     * 会议id
     */
    @NotNull(message = "会议id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingId;


}
