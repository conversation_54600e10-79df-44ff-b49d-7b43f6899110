package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;


/**
 * 议题收集视图对象 t_topic_meeting_link
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
@ExcelIgnoreUnannotated
public class TopicMeetingLinkVo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 预召开会议id
     */
    private Long planId;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 议题id
     */
    private Long topicId;


    /**
     * 有效位
     */
    private String isActive;


    /**
     * 议题名称
     */
    private String topicTitle;

    /**
     * 汇报人id
     */
    private Long reportUserId;

    /**
     * 汇报人姓名
     */
    private String reportUserName;

    /**
     * 议题排序
     */
    private Long topicOrder;

    private Date createTime;

    private String creatorDept;

    /**
     * 议题状态
     */
    private String status;

}

