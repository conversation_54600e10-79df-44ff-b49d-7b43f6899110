package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@DictEnum(label = "消息处理组件类型" , key = "MESSAGE_COMPONENT_TYPE")
public enum MessageComponentTypeEnum implements KeyLabelEnum {
    DIALOG("dialog", "对话框"),

    ROUTER("router", "路由"),


    CUSTOM("custom", "自定义"),

    NONE("none", "无");


    public String key, label;

}
