package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicMeetingLink;
import com.tenint.meeting.domain.bo.MeetingBo;
import com.tenint.meeting.domain.bo.TopicMeetingLinkBo;

import java.util.Collection;
import java.util.List;
/**
 * 议题收集Service接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface ITopicMeetingLinkService extends IService<TopicMeetingLink> {

    /**
     * 查询议题收集列表
     */
    Page<TopicMeetingLink> pageTopicMeetingLink(TopicMeetingLinkBo bo, PageQuery pageQuery);

    /**
     * 查询议题收集列表
     */
    List<TopicMeetingLink> listTopicMeetingLink(TopicMeetingLinkBo bo);

    /**
     * 校验并批量删除议题收集信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量添加
     */
    void saveBatch(MeetingBo bo, Long meetingId);

    /**
     * 根据会议id查询议题link
     */
    List<TopicMeetingLink> getLinksByMeetingId(Long meetingId);

    /**
     * 根据会议id删除
     */
    void deleteByMeetingId(List<Long> ids);

    /**
     * 根据议题id删除
     * @param ids
     */
    void deleteByTopicId(List<Long> ids);

    /**
     * 添加特殊议题（在会议资料中直接上传的）
     */
    TopicMeetingLink insertSpecialTopicMeetingLink(Long topicId, Long meetingId, String titleName, Long reportUserId, Long fileOrder);

    /**
     * 更新特殊议题（在会议资料中直接上传的）
     *
     * @param linkId
     * @param topicId
     * @param meetingId
     * @param titleName
     * @param reportUserId
     * @param fileOrder
     * @return
     */
    TopicMeetingLink updateSpecialTopicMeetingLink(Long linkId, Long topicId, Long meetingId, String titleName, Long reportUserId, Long fileOrder);
}
