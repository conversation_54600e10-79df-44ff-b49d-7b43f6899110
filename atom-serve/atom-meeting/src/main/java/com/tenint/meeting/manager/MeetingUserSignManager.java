package com.tenint.meeting.manager;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.meeting.domain.MeetingUserSign;
import com.tenint.meeting.service.IMeetingUserSignService;
import com.tenint.meeting.domain.bo.MeetingUserSignBo;
import com.tenint.meeting.domain.vo.MeetingUserSignVo;
import com.tenint.meeting.convert.MeetingUserSignConvert;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;

/**
 * 会议签名综合Service层
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@RequiredArgsConstructor
@Service
public class MeetingUserSignManager {

    private final IMeetingUserSignService meetingUserSignService;

    /**
     * 查询会议签名
     */
    public MeetingUserSignVo getVoById(Long id) {
        MeetingUserSign meetingUserSign = meetingUserSignService.getById(id);
        MeetingUserSignVo meetingUserSignVo = MeetingUserSignConvert.INSTANCE.convert(meetingUserSign);
        return meetingUserSignVo;
    }

    /**
     * 查询会议签名列表
     */
    public TableDataInfo<MeetingUserSignVo> pageMeetingUserSignVo(MeetingUserSignBo bo, PageQuery pageQuery) {
        Page<MeetingUserSign> page = meetingUserSignService.pageMeetingUserSign(bo, pageQuery);
        Page<MeetingUserSignVo> pageVo = MeetingUserSignConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询会议签名列表
     */
    public List<MeetingUserSignVo> listMeetingUserSignVo(MeetingUserSignBo bo) {
        List<MeetingUserSign> list = meetingUserSignService.listMeetingUserSign(bo);
        List<MeetingUserSignVo> listVo = MeetingUserSignConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增/修改会议签名
     */
    public Boolean saveByBo(MeetingUserSignBo bo) {
        MeetingUserSign meetingUserSign = MeetingUserSignConvert.INSTANCE.convert(bo);
        MeetingUserSign sign = this.getSignByMeetingAndUserId(bo.getMeetingId(), bo.getUserId());
        if (ObjectUtil.isNull(sign)){ // 新增
            return meetingUserSignService.save(meetingUserSign);
        }else {
            sign.setSignPic(bo.getSignPic());
            return meetingUserSignService.updateById(sign);
        }
    }

    /**
     * 修改会议签名
     */
    public Boolean updateByBo(MeetingUserSignBo bo) {
        validateMeetingUserSignExists(bo.getId());
        MeetingUserSign meetingUserSign = MeetingUserSignConvert.INSTANCE.convert(bo);
        return meetingUserSignService.updateById(meetingUserSign);
    }


    /**
     * 校验并批量删除会议签名信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return meetingUserSignService.removeWithValidByIds(ids, isValid);
    }


    private void validateMeetingUserSignExists(Long id) {
        if (meetingUserSignService.getById(id) == null) {
            throw new ServiceException("会议签名数据不存在");
        }
    }

    public MeetingUserSign getSignByMeetingAndUserId(Long meetingId,Long UserId){
        return meetingUserSignService.getOne(Wrappers.<MeetingUserSign>lambdaQuery().eq(MeetingUserSign::getMeetingId, meetingId)
                .eq(MeetingUserSign::getUserId, UserId));
    }

}
