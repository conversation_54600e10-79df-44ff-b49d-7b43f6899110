package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;


/**
 * 执行人员视图对象 t_task_user
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
@ExcelIgnoreUnannotated
public class TaskUserVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * NAME
     */
    @ExcelProperty(value = "NAME")
    private String taskName;


    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 督办主表id
     */
    @ExcelProperty(value = "督办主表id")
    private Long taskId;

    /**
     * 用户名字
     */
    @ExcelProperty(value = "用户名字")
    private String userName;

    /**
     * 机构code
     */
    private String orgCode;

    private Long userIds;

    // 督查督办名字
    private String name;

    // 领导名字
    private String majorName;

}

