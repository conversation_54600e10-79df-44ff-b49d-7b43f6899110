package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题批注文件对象 t_topic_file_comment
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
@TableName("t_file_comment")
public class FileComment extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 关联的业务id
    */
    private Long linkId;

    /**
     * 关联的业务类型
     */
    private String linkType;

    /**
    * 关联的原文件id
    */
    private Long linkFileId;

   /**
    * 当前文件的ossid
    */
    private Long ossId;

   /**
    * 文件名
    */
    private String originalName;


}
