package com.tenint.meeting.domain.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 会议签到表视图对象
 *
 * <AUTHOR>
 * @date 2024-12-14
 */
@Data
public class MeetingSignTableVo {

    /**
     * 会议ID
     */
    private Long meetingId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 会议标题
     */
    private String meetingTitle;

    /**
     * 会议日期
     */
    private Date meetingDate;

    /**
     * 参会人员列表
     */
    private List<SignTableAttendeeVo> attendees;

    /**
     * 列席人员列表
     */
    private List<SignTableAttendeeVo> observers;

    /**
     * 签到表参与者信息
     */
    @Data
    public static class SignTableAttendeeVo {
        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 姓名
         */
        private String name;

        /**
         * 职务
         */
        private String title;

        /**
         * 备注
         */
        private String remark;

        /**
         * 签名base64码
         */
        private String signatureBase64;

        /**
         * 用户类型（参会/列席）
         */
        private String userType;
    }
}
