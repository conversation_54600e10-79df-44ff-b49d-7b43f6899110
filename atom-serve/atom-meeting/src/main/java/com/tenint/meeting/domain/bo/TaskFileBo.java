package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 督查督办附件业务对象 t_task_file
 *
 * <AUTHOR>
 * @date 2024-10-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TaskFileBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;



    /**
     * 督查督办ID
     */
    @NotNull(message = "督查督办ID不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long taskId;


    /**
     * 附件类型 1 任务附件 2 新建任务-引用文件
     */
    @NotBlank(message = "附件类型 1 任务附件 2 新建任务-引用文件不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String fileType;


    /**
     * $column.columnComment
     */
    @NotNull(message = "$column.columnComment不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long ossId;


    /**
     * 附件名称
     */
    @NotBlank(message = "附件名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String name;


}
