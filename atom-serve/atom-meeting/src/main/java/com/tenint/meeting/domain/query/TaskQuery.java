package com.tenint.meeting.domain.query;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.meeting.domain.bo.TaskFileBo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 督查督办列表查询对象
 */
@Data
public class TaskQuery {

    /**
     * 主键
     */
    private Long id ;

    /**
     * 任务类型 1来自会议 2新建任务
     */
    private String taskType;

    /**
     * 来自会议 - 关联会议
     */
    private Long meetingId;

    /**
     * 新建任务 - 任务来源 1引用文件 2手动输入
     */
    private String sourceType;


    /**
     * 新建任务 - 任务来源 - 手动输入
     */
    private String sourceContent;

    /**
     * 督办事项名称
     */
    private String taskTitle;

    /**
     * 主责部门id
     */
    private Long deptId;

    /**
     * 分管领导id
     */
    private Long majorUserId;

    /**
     * 部门负责人id
     */
    private Long userId;

    /**
     * 协助部室id
     */
    private Long coDeptId;

    /**
     * 协助部门领导id
     */
    private Long coMajorUserId;

    /**
     * 协助部门负责人id
     */
    private Long coUserId;

    /**
     * 办结时间
     */
    private Date completeTime;


    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 任务状态
     */
    private String taskStatus;


    private String orgCode;

    /**
     * 立项时间
     */
    private Date approvalTime;

    /**
     * 立项依据
     */
    private String taskGist;

    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTimeStart;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTimeEnd;

    /**
     * 页签
     */
    private Tab tab;

    @Getter
    @AllArgsConstructor
    public enum Tab {
        /**
         * 全部
         */
        ALL,

        // * 指派列表
        /**
         * 待指派
         */
        ASSIGN,
        /**
         * 待审核
         */
        AUDITING,
        /**
         * 已审核
         */
        AUDITED,

        // * 执行列表
        /**
         * 待执行
         */
        EXECUTING,

        /**
         * 已执行
         */
        EXECUTED,

        // * 混合查询（指派、执行、审核）
        /**
         * 待处理
         */
        PENDING,

    }

}
