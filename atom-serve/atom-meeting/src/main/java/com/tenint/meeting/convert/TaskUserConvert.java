package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TaskUser;
import com.tenint.meeting.domain.bo.TaskUserBo;
import com.tenint.meeting.domain.vo.TaskUserVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TaskUserConvert {

    TaskUserConvert INSTANCE = Mappers.getMapper( TaskUserConvert.class );


    TaskUser convert(TaskUserBo bean);

    TaskUserVo convert(TaskUser bean);

    List<TaskUserVo> convertList(List<TaskUser> list);

    Page<TaskUserVo> convertPage(Page<TaskUser> page);

}
