package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TopicMeetingAudit;
import com.tenint.meeting.domain.bo.TopicMeetingAuditBo;
import com.tenint.meeting.domain.dto.TopicMeetingAuditDTO;
import com.tenint.meeting.domain.vo.TopicMeetingAuditVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TopicMeetingAuditConvert {

    TopicMeetingAuditConvert INSTANCE = Mappers.getMapper( TopicMeetingAuditConvert.class );


    TopicMeetingAudit convert(TopicMeetingAuditBo bean);

    TopicMeetingAuditVo convert(TopicMeetingAudit bean);

    List<TopicMeetingAuditVo> convertList(List<TopicMeetingAudit> list);

    Page<TopicMeetingAuditVo> convertPage(Page<TopicMeetingAudit> page);

    Page<TopicMeetingAuditVo> convertDTOPage(Page<TopicMeetingAuditDTO> page);

}
