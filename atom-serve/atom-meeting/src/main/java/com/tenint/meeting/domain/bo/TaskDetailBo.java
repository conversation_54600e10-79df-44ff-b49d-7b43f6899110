package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 督查督办明细子业务对象 t_task_detail
 *
 * <AUTHOR>
 * @date 2024-10-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TaskDetailBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 任务主表id
     */
    @NotNull(message = "任务主表id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long taskId;


    /**
     * 任务类型 1来自会议 2新建任务
     */
    @NotBlank(message = "任务类型 1来自会议 2新建任务不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String taskType;


    /**
     * 任务来源 1引用文件 2手动输入
     */
    @NotBlank(message = "任务来源 1引用文件 2手动输入不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String sourceType;


    /**
     * 关联的会议ID
     */
    @NotNull(message = "关联的会议ID不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingId;


    /**
     * 任务来源-手动输入
     */
    @NotBlank(message = "任务来源-手动输入不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String sourceContent;


}
