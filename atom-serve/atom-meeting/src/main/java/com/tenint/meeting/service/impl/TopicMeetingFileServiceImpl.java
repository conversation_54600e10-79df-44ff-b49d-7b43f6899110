package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.tenint.common.utils.StringUtils;

import com.tenint.common.utils.redis.OssDelayedUtils;
import com.tenint.meeting.domain.FileEntity;
import com.tenint.meeting.domain.TopicMeetingFile;
import com.tenint.meeting.domain.bo.TopicMeetingFileBo;
import com.tenint.meeting.mapper.TopicMeetingFileMapper;
import com.tenint.meeting.service.ITopicMeetingFileService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 议题收集附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingFileServiceImpl extends ServiceImpl<TopicMeetingFileMapper, TopicMeetingFile> implements ITopicMeetingFileService {


    /**
     * 查询议题收集附件列表
     */
    @Override
    public Page<TopicMeetingFile> pageTopicMeetingFile(TopicMeetingFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopicMeetingFile> lqw = buildQueryWrapper(bo);
        Page<TopicMeetingFile> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题收集附件列表
     */
    @Override
    public List<TopicMeetingFile> listTopicMeetingFile(TopicMeetingFileBo bo) {
        LambdaQueryWrapper<TopicMeetingFile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题收集附件列表查询条件
     */
    private LambdaQueryWrapper<TopicMeetingFile> buildQueryWrapper(TopicMeetingFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopicMeetingFile> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getTopicMeetId() != null, TopicMeetingFile::getTopicMeetId, bo.getTopicMeetId());
            lqw.eq(StringUtils.isNotBlank(bo.getFileType()), TopicMeetingFile::getFileType, bo.getFileType());
            lqw.eq(bo.getOssId() != null, TopicMeetingFile::getOssId, bo.getOssId());
            lqw.like(StringUtils.isNotBlank(bo.getName()), TopicMeetingFile::getName, bo.getName());
        return lqw;
    }


    /**
     * 批量删除议题收集附件
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Map<String, List<TopicMeetingFile>> collectFileMapByTopicId(Long topicId) {

        List<TopicMeetingFile> fileList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TopicMeetingFile::getTopicMeetId, topicId).list();
        return fileList.stream().collect(Collectors.groupingBy(TopicMeetingFile::getFileType));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOrUpdateTopicFile(Long topicId, List<TopicMeetingFile> fileList) {

        List<TopicMeetingFile> remainFileList = new LambdaQueryChainWrapper<>(baseMapper)
                .select(TopicMeetingFile::getId)
                .eq(TopicMeetingFile::getTopicMeetId, topicId).list();
        if (CollectionUtils.isEmpty(remainFileList)){
            fileList.forEach(item -> item.setTopicMeetId(topicId));
            return saveBatch(fileList);
        }

        List<TopicMeetingFile> insertFileList = fileList.stream().filter(item -> Objects.isNull(item.getId())).toList();
        insertFileList.forEach(item -> item.setTopicMeetId(topicId));

        List<Long> remainIds = remainFileList.stream().map(TopicMeetingFile::getId).toList();
        List<TopicMeetingFile> fileListWithRecord = fileList.stream().filter(item -> !Objects.isNull(item.getId())).toList();

        List<Long> fileIds = fileList.stream().map(TopicMeetingFile::getId).toList();
        List<Long> deleteFileIds = CollectionUtils.isEmpty(fileIds) ? remainIds : remainIds.stream().filter(item -> !fileIds.contains(item)).toList();
        List<TopicMeetingFile> updateFileList = fileListWithRecord.stream().filter(item -> remainIds.contains(item.getId())).toList();

        if (CollectionUtils.isNotEmpty(updateFileList)) {
            updateBatchById(updateFileList);
        }
        if (CollectionUtils.isNotEmpty(deleteFileIds)) {
            removeBatchByIds(deleteFileIds);
            //todo 同步删除关联的批注文件
        }
        if (CollectionUtils.isNotEmpty(insertFileList)) {
            saveBatch(insertFileList);
            List<String> ossIds = insertFileList.stream().map(TopicMeetingFile::getOssId).map(String::valueOf).toList();
            OssDelayedUtils.closeDelete(ossIds);
        }
        return true;
    }

    @Override
    public void removeTopicFileByTopicId(List<Long> topicIds) {

        if (CollectionUtils.isNotEmpty(topicIds)) {
            LambdaQueryChainWrapper<TopicMeetingFile> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                    .in(TopicMeetingFile::getTopicMeetId, topicIds);
            remove(wrapper.getWrapper());
            //todo 同步删除关联的批注文件
        }
    }

}
