package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.FileEntity;
import com.tenint.meeting.domain.bo.FileEntityBo;
import com.tenint.meeting.domain.bo.MeetingBo;
import com.tenint.meeting.domain.vo.UploadFileVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IFileEntityService extends IService<FileEntity> {

    /**
     * 查询文件列表
     */
    Page<FileEntity> pageFileEntity(FileEntityBo bo, PageQuery pageQuery);

    /**
     * 查询自己上传的文件列表
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<FileEntity> pageSelfFileEntity(FileEntityBo bo, PageQuery pageQuery);

    /**
     * 查询文件列表
     */
    List<FileEntity> listFileEntity(FileEntityBo bo);

    /**
     * 校验并批量删除文件信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据文件类型查询文件列表
     * @param entityType 文件类型
     * @return 文件列表
     */
    List<FileEntity> listByEntityType(String entityType);

    /**
     * 根据会议类型/parentId 查询文件 且创建人为自己的
     * @param meetingType
     * @param userId
     * @return
     */
    List<FileEntity> listByParentId(Long meetingType, Long userId);

    /**
     * 根据会议类型/parentId 查询文件
     * @param meetingType
     * @return
     */
    List<FileEntity> listByParentId(Long meetingType);

    List<FileEntity> listByFlagAndParentId(String flag, long parentId);

    /**
     * 通过id判断是否已存在当前年份和月份下的文件夹
     */
    FileEntity getFolderById(Long id, String meetingName,Long meetingId);

    /**
     * 批量添加文件
     *
     * @return
     */
    Boolean addFile(FileEntity entity,List<FileEntityBo> fileList,Long meetingId);

    // 修改会议名字，附件
    Boolean updateFileAndNameByMeeting(MeetingBo bo);

    // 根据会议id查询附件
    List<FileEntityBo> getListByMeetingId(Long meetingId);

    /**
     * 删除文件
     */
    Boolean removeByMeetingId(List<Long> meetingId);

    /**
     * 添加附件类型
     */
    Boolean insertBatch(UploadFileVo uploadFileVo);

    /**
     * 文件选择上传文件
     */
    Boolean fileInsertBatch(FileEntity fileEntity, UploadFileVo uploadFileVo);
}
