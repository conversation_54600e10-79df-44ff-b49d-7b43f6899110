package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 待办配置视图对象 sys_todo_config
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@ExcelIgnoreUnannotated
public class MessageConfigVo {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 模块
     */
    @ExcelProperty(value = "模块")
    private String messageModule;

    /**
     * 待办类型
     */
    @ExcelProperty(value = "类型")
    private String messageType;

    /**
     * 组件路径
     */
    @ExcelProperty(value = "组件路径")
    private String componentUrl;

    /**
     * 组件类型 dialog router custom
     */
    @ExcelProperty(value = "组件类型 dialog router custom")
    private String componentType;

    /**
     * 参数 跳转或者对话框弹出需要的参数信息
     */
    @ExcelProperty(value = "参数 跳转或者对话框弹出需要的参数信息")
    private String componentParams;

    /**
     * 关联的业务表
     */
    @ExcelProperty(value = "关联的业务表")
    private String businessTable;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 对话框参数
     */
    private String dialogField;

    /**
     * 设备类型
     */
    private String clientType;

    /**
     * 完成相关代办
     */
    private Boolean associated;

}
