package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.Topic;
import com.tenint.meeting.domain.bo.TopicBo;
import com.tenint.meeting.domain.dto.TopicUploadDTO;
import com.tenint.meeting.domain.query.TopicQuery;

import java.util.Collection;
import java.util.List;
/**
 * 议题Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface ITopicService extends IService<Topic> {

    /**
     * 查询议题列表
     */
    Page<Topic> pageTopic(TopicBo bo, PageQuery pageQuery);

    /**
     * 查询议题列表
     */
    List<Topic> listTopic(TopicBo bo);

    /**
     * 校验并批量删除议题信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取当前开放收集时间并且关联当前用户的议题
     */
    List<Topic> getRelatedTopic();

    /**
     * 分页查询所有关联有过操作记录
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<TopicUploadDTO> pageRelatedTopic(TopicQuery query, PageQuery pageQuery);
}
