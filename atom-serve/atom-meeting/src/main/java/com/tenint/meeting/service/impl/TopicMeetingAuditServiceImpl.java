package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.TopicMeetingAudit;
import com.tenint.meeting.domain.bo.TopicMeetingAuditBo;
import com.tenint.meeting.domain.dto.TopicMeetingAuditDTO;
import com.tenint.meeting.mapper.TopicMeetingAuditMapper;
import com.tenint.meeting.service.ITopicMeetingAuditService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会议收集审核人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingAuditServiceImpl extends ServiceImpl<TopicMeetingAuditMapper, TopicMeetingAudit> implements ITopicMeetingAuditService {


    /**
     * 构建会议收集审核人员列表查询条件
     */
    private QueryWrapper<TopicMeetingAudit> buildQueryWrapper(TopicMeetingAuditBo bo) {

        QueryWrapper<TopicMeetingAudit> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(bo.getTopicTitle()), "TTM.TOPIC_TITLE", bo.getTopicTitle());
        if (CollectionUtils.isNotEmpty(bo.getDateRange()) && bo.getDateRange().stream().allMatch(StringUtils::isNotBlank)) {
            wrapper.between("TTM.CREATE_TIME", bo.getDateRange().get(0),  bo.getDateRange().get(1));
        }
        wrapper.apply(StringUtils.isNotBlank(bo.getMeetType()),
                "exists(select 1 from t_topic_plan_link,t_topic_meeting_plan " +
                        "where t_topic_plan_link.plan_id = t_topic_meeting_plan.id " +
                        "and t_topic_plan_link.topic_id = TTM.ID " +
                        "and t_topic_meeting_plan.is_active = '1' " +
                        "and t_topic_plan_link.is_active = '1' " +
                        "and t_topic_meeting_plan.type = {0})",
                bo.getMeetType());
        return wrapper;
    }

    /**
     * 批量删除会议收集审核人员
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Map<String, TopicMeetingAudit> collectAuditUserByTopicId(Long topicId) {

        List<TopicMeetingAudit> auditUserList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TopicMeetingAudit::getTopicMeetId, topicId).list();
        return auditUserList.stream().collect(Collectors.toMap(TopicMeetingAudit::getUserType, item -> item, (a, b) -> a));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOrUpdateAuditUser(Long topicId, Map<String, List<Long>> userMap) {

        List<TopicMeetingAudit> remainAuditUserList = new LambdaQueryChainWrapper<>(baseMapper)
                .select(TopicMeetingAudit::getId, TopicMeetingAudit::getUserId, TopicMeetingAudit::getUserType)
                .eq(TopicMeetingAudit::getTopicMeetId, topicId).list();
        if (CollectionUtils.isEmpty(remainAuditUserList)) {
            List<TopicMeetingAudit> auditList = generateAuditUserList(topicId, userMap);
            return saveBatch(auditList);
        }

        List<TopicMeetingAudit> update = new ArrayList<>();
        List<TopicMeetingAudit> delete = new ArrayList<>();
        Map<String, List<Long>> insert = new HashMap<>();
        Map<String, List<TopicMeetingAudit>> remainMap = remainAuditUserList.stream()
                .collect(Collectors.groupingBy(TopicMeetingAudit::getUserType));
        remainMap.forEach((key, value) -> {
            List<Long> userIds = userMap.get(key);
            List<TopicMeetingAudit> updateAuditUserList = value.stream().filter(item -> userIds.contains(item.getUserId())).toList();
            update.addAll(updateAuditUserList);

            List<Long> remainUserList = value.stream().map(TopicMeetingAudit::getUserId).toList();
            List<Long> insertUserIds = userIds.stream().filter(item -> !remainUserList.contains(item)).toList();
            insert.put(key, insertUserIds);
            List<Long> deleteUserIds = remainUserList.stream().filter(item -> !userIds.contains(item)).toList();
            List<TopicMeetingAudit> deleteAuditUserList = value.stream().filter(item -> deleteUserIds.contains(item.getUserId())).toList();
            delete.addAll(deleteAuditUserList);
        });

        if (CollectionUtils.isNotEmpty(update)) {
            updateBatchById(update);
        }
        if (CollectionUtils.isNotEmpty(delete)) {
            removeBatchByIds(delete);
        }
        if (CollectionUtils.isNotEmpty(insert)) {
            List<TopicMeetingAudit> auditList = generateAuditUserList(topicId, insert);
            saveBatch(auditList);
        }

        return true;
    }

    @Override
    public void removeAuditUserByTopicId(List<Long> topicIds) {

        if (CollectionUtils.isNotEmpty(topicIds)) {
            LambdaQueryChainWrapper<TopicMeetingAudit> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                    .in(TopicMeetingAudit::getTopicMeetId, topicIds);
            remove(wrapper.getWrapper());
        }
    }

    @Override
    public Page<TopicMeetingAuditDTO> pageTopicMeetingAuditWithLeaderShip(TopicMeetingAuditBo bo, PageQuery pageQuery) {

        QueryWrapper<TopicMeetingAudit> wrapper = buildQueryWrapper(bo);
        Page<TopicMeetingAuditDTO> page = baseMapper.pageTopicMeetingAuditWithLeaderShip(wrapper, LoginHelper.getUserId(), pageQuery.build());
        return page;
    }

    @Deprecated
    @Override
    public Page<TopicMeetingAuditDTO> pageTopicMeetingAuditWithGroup(TopicMeetingAuditBo bo, PageQuery pageQuery) {

        QueryWrapper<TopicMeetingAudit> wrapper = buildQueryWrapper(bo);
        Page<TopicMeetingAuditDTO> page = baseMapper.pageTopicMeetingAuditWithGroup(wrapper, LoginHelper.getUserId(), pageQuery.build());
        return page;
    }

    @Override
    public Page<TopicMeetingAuditDTO> pageTopicMeetingAudited(TopicMeetingAuditBo bo, PageQuery pageQuery) {

        QueryWrapper<TopicMeetingAudit> wrapper = buildQueryWrapper(bo);
        Page<TopicMeetingAuditDTO> page = baseMapper.pageTopicMeetingAudited(wrapper, LoginHelper.getUserId(), pageQuery.build());
        return page;
    }

    @Override
    public Page<TopicMeetingAuditDTO> pageTopicMeetingAuditWithAdmin(TopicMeetingAuditBo bo, PageQuery pageQuery) {
        QueryWrapper<TopicMeetingAudit> wrapper = buildQueryWrapper(bo);
        Page<TopicMeetingAuditDTO> page = baseMapper.pageTopicMeetingAuditWithAdmin(wrapper, pageQuery.build());
        return page;
    }

    @Override
    public Page<TopicMeetingAuditDTO> pageTopicMeetingAuditedWithAdmin(TopicMeetingAuditBo bo, PageQuery pageQuery) {
        QueryWrapper<TopicMeetingAudit> wrapper = buildQueryWrapper(bo);
        Page<TopicMeetingAuditDTO> page = baseMapper.pageTopicMeetingAuditedWithAdmin(wrapper, pageQuery.build());
        return page;
    }

    @NotNull
    private static List<TopicMeetingAudit> generateAuditUserList(Long topicId, Map<String, List<Long>> userMap) {

        List<TopicMeetingAudit> auditList = userMap.entrySet().stream()
                .flatMap(item -> item.getValue().stream().map(it -> {
                    TopicMeetingAudit audit = new TopicMeetingAudit();
                    audit.setTopicMeetId(topicId);
                    audit.setUserId(it);
//                    audit.setStatus(TopicMeetingAuditFlowEnum.EDIT_POINT.getKey());
                    audit.setUserType(item.getKey());
                    return audit;
                }))
                .toList();
        return auditList;
    }

}
