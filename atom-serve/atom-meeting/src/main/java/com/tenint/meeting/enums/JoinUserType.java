package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "JOIN_USER_TYPE",label = "关联人员的类型")
@Getter
@AllArgsConstructor
public enum JoinUserType implements KeyLabelEnum {

    JOIN("0", "协作人员" ),

    READ("1", "列席人员"),

    ATTEND("2", "参会人员");

    private final String key,label;
}
