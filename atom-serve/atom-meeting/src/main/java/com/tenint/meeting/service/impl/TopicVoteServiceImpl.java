package com.tenint.meeting.service.impl;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.convert.TopicVoteConvert;
import com.tenint.meeting.domain.Topic;
import com.tenint.meeting.domain.TopicMeetingLink;
import com.tenint.meeting.domain.TopicVote;
import com.tenint.meeting.domain.bo.TopicVoteBo;
import com.tenint.meeting.domain.dto.TopicVoteAndMeetingLinkDTO;
import com.tenint.meeting.domain.vo.TopicVoteVo;
import com.tenint.meeting.mapper.TopicVoteMapper;
import com.tenint.meeting.service.ITopicMeetingLinkService;
import com.tenint.meeting.service.ITopicVoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 议题表决Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
public class TopicVoteServiceImpl extends ServiceImpl<TopicVoteMapper, TopicVote> implements ITopicVoteService {


    private final ITopicMeetingLinkService linkService;

    /**
     * 查询议题表决列表
     */
    @Override
    public Page<TopicVote> pageTopicVote(TopicVoteBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopicVote> lqw = buildQueryWrapper(bo);
        Page<TopicVote> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题表决列表
     */
    @Override
    public List<TopicVote> listTopicVote(TopicVoteBo bo) {
        LambdaQueryWrapper<TopicVote> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题表决列表查询条件
     */
    private LambdaQueryWrapper<TopicVote> buildQueryWrapper(TopicVoteBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopicVote> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getMeetingId() != null, TopicVote::getMeetingId, bo.getMeetingId());
            lqw.eq(bo.getUserId() != null, TopicVote::getUserId, bo.getUserId());
            lqw.eq(bo.getTopicId() != null, TopicVote::getTopicId, bo.getTopicId());
            lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TopicVote::getStatus, bo.getStatus());
        return lqw;
    }


    /**
     * 批量删除议题表决
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(TopicVoteAndMeetingLinkDTO linkDTO) {
        // 更具会议id和 topicId查询是否有记录
        List<TopicVoteVo> votes = linkDTO.getVotes();
        TopicMeetingLink link = linkDTO.getLink();
        TopicVoteVo topicVoteBo = votes.get(0);
        linkService.lambdaUpdate().eq(TopicMeetingLink::getId,link.getId())
                .set(TopicMeetingLink::getStatus,link.getStatus())
                .update();
        baseMapper.delete(Wrappers.<TopicVote>lambdaQuery().eq(TopicVote::getMeetingId, topicVoteBo.getMeetingId())
                .eq(TopicVote::getTopicId, topicVoteBo.getTopicId()));
        List<TopicVote> topicVotes = TopicVoteConvert.INSTANCE.convertListVo(votes);
        baseMapper.insertOrUpdate(topicVotes);
    }

    @Override
    public List<TopicVote> getVotesByMeetingIdAndTopicId(Long meetingId, Long topicId) {
        return baseMapper.selectList(Wrappers.<TopicVote>lambdaQuery()
                .eq(TopicVote::getMeetingId, meetingId)
                .eq(TopicVote::getTopicId, topicId));
    }

}
