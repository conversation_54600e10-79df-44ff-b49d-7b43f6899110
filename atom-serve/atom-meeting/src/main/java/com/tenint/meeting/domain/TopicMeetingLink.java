package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import java.util.Date;

import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题收集对象 t_topic_meeting_link
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
@TableName("t_topic_meeting_link")
public class TopicMeetingLink {

   /**
    * 主键
    */
    private Long id;

   /**
    * 预召开会议id
    */
    private Long planId;

   /**
    * 会议id
    */
    private Long meetingId;

    /**
    * 议题id
    */
    private Long topicId;


   /**
    * 有效位
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


   /**
    * 议题名称
    */
    private String topicTitle;

   /**
    * 汇报人id
    */
    private Long reportUserId;

   /**
    * 汇报人姓名
    */
    private String reportUserName;

   /**
    * 议题排序
    */
    private Long topicOrder;

    private Date createTime;

    private String creatorDept;

    /**
     * 议题状态
     */
    private String status;


}
