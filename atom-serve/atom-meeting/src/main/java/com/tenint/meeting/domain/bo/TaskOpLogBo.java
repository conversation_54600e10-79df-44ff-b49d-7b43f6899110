package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 督查督办操作流程日志业务对象 t_task_op_log
 *
 * <AUTHOR>
 * @date 2025-01-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
public class TaskOpLogBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 督办主表id
     */
    @NotNull(message = "督办主表id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long taskId;


    /**
     * 操作人id
     */
    @NotNull(message = "操作人id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 操作人姓名
     */
    @NotBlank(message = "操作人姓名不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String userName;


    /**
     * 操作情况
     */
    @NotBlank(message = "操作情况不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String opName;


    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String opType;


}
