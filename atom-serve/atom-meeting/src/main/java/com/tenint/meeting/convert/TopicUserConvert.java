package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TopicUser;
import com.tenint.meeting.domain.vo.TopicTypeVo;
import com.tenint.meeting.domain.vo.TopicUserVo;
import com.tenint.meeting.domain.bo.TopicUserBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TopicUserConvert {

    TopicUserConvert INSTANCE = Mappers.getMapper( TopicUserConvert.class );


    TopicUser convert(TopicUserBo bean);

    TopicUserVo convert(TopicUser bean);

    @Mapping(source = "typeId", target = "key")
    @Mapping(source = "typeName", target = "name")
    TopicTypeVo convertToType(TopicUser bean);

    List<TopicUserVo> convertList(List<TopicUser> list);

    List<TopicTypeVo> convertToTypeList(List<TopicUser> list);

    Page<TopicUserVo> convertPage(Page<TopicUser> page);

}
