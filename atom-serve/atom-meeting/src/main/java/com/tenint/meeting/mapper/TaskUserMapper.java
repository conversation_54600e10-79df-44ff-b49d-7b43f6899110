package com.tenint.meeting.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tenint.meeting.domain.TaskUser;
import com.tenint.meeting.domain.TaskUserGroup;
import org.apache.ibatis.annotations.Param;

/**
 * 执行人员Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface TaskUserMapper extends BaseMapper<TaskUser> {

    TaskUser getTaskByUserIdOrTaskId(@Param("userId") Long userId,@Param("taskId") Long taskId);
}
