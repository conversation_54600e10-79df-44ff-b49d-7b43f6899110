package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 会议签名视图对象 t_meeting_user_sign
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
@ExcelIgnoreUnannotated
public class MeetingUserSignVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 签名base64码
     */
    @ExcelProperty(value = "签名base64码")
    private String signPic;

    /**
     * 会议id
     */
    @ExcelProperty(value = "会议id")
    private Long meetingId;


}

