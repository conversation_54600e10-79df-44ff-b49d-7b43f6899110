package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 参会人员对象 t_meeting_joiner
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@TableName("t_meeting_joiner")
public class MeetingJoiner extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键id
    */
    @TableId(value = "id")
    private Long id;

   /**
    * 1：有效  0：删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;

   /**
    * 会议主键id
    */
    private Long meetingId;

   /**
    * 参会用户编号
    */
    private Long userId;

    /**
     * 签到状态
     */
    private String attendanceStatus;

    /**
     * 关联用户类型
     */
    private String userType;

    /**
     * 是否通过
     */
    private String isAgree;

    /**
     * 参会列席人员顺序
     */
    private Integer joinerOrder;

}
