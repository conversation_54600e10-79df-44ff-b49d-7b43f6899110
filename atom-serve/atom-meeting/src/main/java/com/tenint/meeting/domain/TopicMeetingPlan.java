package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.core.domain.BaseEntity;
/**
 * 预约对象 t_topic_meeting_plan
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@TableName("t_topic_meeting_plan")
public class TopicMeetingPlan extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * $column.columnComment
    */
    private Long id;

   /**
    * 标题
    */
    private String title;

   /**
    * 截至时间
    */
    private Date lastTime;

   /**
    * 类型
    */
    private String type;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
