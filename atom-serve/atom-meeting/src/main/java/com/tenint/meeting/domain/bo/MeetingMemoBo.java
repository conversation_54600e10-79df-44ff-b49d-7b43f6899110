package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 会议签名业务对象 t_meeting_memo
 *
 * <AUTHOR>
 * @date 2024-12-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingMemoBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 便签内容
     */
    @NotBlank(message = "便签内容不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String memoWord;


    /**
     * 会议id
     */
    @NotNull(message = "会议id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingId;


}
