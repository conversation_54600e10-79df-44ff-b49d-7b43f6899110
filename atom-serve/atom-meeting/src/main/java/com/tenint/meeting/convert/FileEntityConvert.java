package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.FileEntity;
import com.tenint.meeting.domain.vo.FileEntityVo;
import com.tenint.meeting.domain.bo.FileEntityBo;
import com.tenint.meeting.domain.vo.MeetingFolderVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface FileEntityConvert {

    FileEntityConvert INSTANCE = Mappers.getMapper( FileEntityConvert.class );


    FileEntity convert(FileEntityBo bean);

    FileEntityVo convert(FileEntity bean);

    FileEntityBo convertBo(FileEntity bean);

    List<FileEntity> convertBoList(List<FileEntityBo> list);

    List<FileEntityVo> convertList(List<FileEntity> list);

    Page<FileEntityVo> convertPage(Page<FileEntity> page);

    List<FileEntityBo> convertListBo(List<FileEntity> list);


    List<MeetingFolderVo> convertFolder(List<FileEntity> page);

}
