package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@DictEnum(label = "消息" , key = "MESSAGE_CLIENT_TYPE")
public enum MessageClientTypeEnum implements KeyLabelEnum{
    PC("PC端", "pc"),
    WAP("WAP端", "wap");

    public final String label,key;
}
