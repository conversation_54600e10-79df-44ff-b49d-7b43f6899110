package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.constant.ConfigConstants;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.convert.TaskDeptMajorConvert;
import com.tenint.meeting.domain.Task;
import com.tenint.meeting.domain.TaskDeptMajor;
import com.tenint.meeting.domain.bo.TaskDeptMajorBo;
import com.tenint.meeting.domain.vo.TaskDeptMajorVo;
import com.tenint.meeting.service.ITaskDeptMajorService;
import com.tenint.system.manager.SysUserManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 督查督办部门负责人综合Service层
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@RequiredArgsConstructor
@Service
public class TaskDeptMajorManager {

    private final ITaskDeptMajorService taskDeptMajorService;
    private final SysUserManager sysUserManager;

    /**
     * 查询督查督办部门负责人
     */
    public TaskDeptMajorVo getVoById(Long id) {
        TaskDeptMajor taskDeptMajor = taskDeptMajorService.getById(id);
        TaskDeptMajorVo taskDeptMajorVo = TaskDeptMajorConvert.INSTANCE.convert(taskDeptMajor);
        return taskDeptMajorVo;
    }

    /**
     * 查询督查督办部门负责人列表
     */
    @Deprecated
    public TableDataInfo<TaskDeptMajorVo> pageTaskDeptMajorVo(TaskDeptMajorBo bo, PageQuery pageQuery) {
        Page<TaskDeptMajor> page = taskDeptMajorService.pageTaskDeptMajor(bo, pageQuery);
        Page<TaskDeptMajorVo> pageVo = TaskDeptMajorConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询督查督办部门负责人列表
     */
    @Deprecated
    public List<TaskDeptMajorVo> listTaskDeptMajorVo(TaskDeptMajorBo bo) {
        List<TaskDeptMajor> list = taskDeptMajorService.listTaskDeptMajor(bo);
        List<TaskDeptMajorVo> listVo = TaskDeptMajorConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增督查督办部门负责人
     */
    public Boolean saveByBo(TaskDeptMajorBo bo) {
        TaskDeptMajor taskDeptMajor = TaskDeptMajorConvert.INSTANCE.convert(bo);
        return taskDeptMajorService.save(taskDeptMajor);
    }

    /**
     * 修改督查督办部门负责人
     */
    public Boolean updateByBo(TaskDeptMajorBo bo) {
        validateTaskDeptMajorExists(bo.getId());
        TaskDeptMajor taskDeptMajor = TaskDeptMajorConvert.INSTANCE.convert(bo);
        return taskDeptMajorService.updateById(taskDeptMajor);
    }


    /**
     * 校验并批量删除督查督办部门负责人信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return taskDeptMajorService.removeWithValidByIds(ids, isValid);
    }


    private void validateTaskDeptMajorExists(Long id) {
        if (taskDeptMajorService.getById(id) == null) {
            throw new ServiceException("督查督办部门负责人数据不存在");
        }
    }

    /**
     * 根据任务涉及人员赋予操作角色权限
     * @param task 任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleTaskAuth(Task task) {
        // 转换为 主责部门人员以及协助部室人员list
        List<TaskDeptMajor> taskDeptMajors = TaskDeptMajorConvert.INSTANCE.convertList(task);
        if(taskDeptMajors.isEmpty()){
            throw new ServiceException("督查督办部门负责人数据不存在");
        }
        // 保存所选的部门领导负责人信息用于以后默认选中
        taskDeptMajorService.saveOrUpdateBatch(taskDeptMajors);
        // 第一个为主责部门数据
        TaskDeptMajor deptMajor = taskDeptMajors.get(0);
        // 分配角色
        // 督查督办负责人
        sysUserManager.saveAuthRoleBackGround(Collections.singletonList(deptMajor.getUserId()), ConfigConstants.TASK_ASSIGN);

        // 督查督办查阅，所有涉及人员均可查看）
        List<Long> majorViewUserIds = new ArrayList<>();
        // 发布人
        majorViewUserIds.add(task.getCreatorId());
        // 部门领导
        majorViewUserIds.add(deptMajor.getMajorUserId());
        // 部门负责人
        majorViewUserIds.add(deptMajor.getUserId());

        // 第二条为协助部门数据（可选）
        if(taskDeptMajors.size() == 2){
            TaskDeptMajor coDeptMajor = taskDeptMajors.get(1);
            if(coDeptMajor != null){
                // 协助部门领导
                if(coDeptMajor.getMajorUserId() != null){
                    majorViewUserIds.add(coDeptMajor.getMajorUserId());
                }
                // 协助部门负责人
                if(coDeptMajor.getUserId() != null){
                    majorViewUserIds.add(coDeptMajor.getUserId());
                }
            }
        }

        // 关联其他领导人（可选）
        if(StringUtils.isNotBlank(task.getOtherMajorIds())){
            List<Long> otherMajorIdList = Arrays.stream(task.getOtherMajorIds().split(",")).map(Long::parseLong).toList();
            majorViewUserIds.addAll(otherMajorIdList);
        }
        sysUserManager.saveAuthRoleBackGround(majorViewUserIds, ConfigConstants.TASK_MAJOR_VIEW);
    }

    /**
     * 根据部门id查询是否有录入部门负责人
     * @param deptId
     * @return
     */
    public TaskDeptMajor getTaskDeptMajorUserIdByDeptId(Long deptId) {

        TaskDeptMajor deptMajor = taskDeptMajorService.lambdaQuery()
                .eq(TaskDeptMajor::getDeptId, deptId).orderByDesc(TaskDeptMajor::getCreateTime)
                .last("limit 1")
                .one();
        return deptMajor;
    }

}
