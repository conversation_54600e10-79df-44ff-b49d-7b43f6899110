package com.tenint.meeting.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.meeting.domain.MessageConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 待办配置Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Mapper
public interface MessageConfigMapper extends BaseMapper<MessageConfig> {

    default MessageConfig selectByType(String todoType) {
        LambdaQueryWrapper<MessageConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MessageConfig::getMessageType, todoType);
        wrapper.last("limit 1");
        return selectOne(wrapper);
    }

    /**
     * 验证是否存在表
     * @param tableName
     * @return int
     */
    int existsTable(@Param("tableName") String tableName);

    /**
     * 根据表明找字段名称
     * @param tableName
     * @return {@link List}<{@link String}>
     */
    List<String> selectDbTableColumnsByName(@Param("tableName") String tableName);


    /**
     * @param clientType
     * @param messageType
     * @return 待办配置对象sys_todo_config
     */
   default MessageConfig selectByClientTypeAndMessageType(String clientType, String messageType) {
        LambdaQueryWrapper<MessageConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MessageConfig::getClientType, clientType);
        wrapper.eq(MessageConfig::getMessageType, messageType);
        return selectOne(wrapper);
    }

   default List<MessageConfig> selectByClientType(String clientType) {
        LambdaQueryWrapper<MessageConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MessageConfig::getClientType, clientType);
        return selectList(wrapper);
   }
}
