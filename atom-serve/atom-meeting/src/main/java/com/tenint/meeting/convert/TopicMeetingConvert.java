package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.bo.TopicMeetingBo;
import com.tenint.meeting.domain.vo.TopicMeetingVo;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TopicMeetingConvert {

    TopicMeetingConvert INSTANCE = Mappers.getMapper( TopicMeetingConvert.class );


    TopicMeeting convert(TopicMeetingBo bean);

    TopicMeetingVo convert(TopicMeeting bean);

    List<TopicMeetingVo> convertList(List<TopicMeeting> list);

    Page<TopicMeetingVo> convertPage(Page<TopicMeeting> page);

}
