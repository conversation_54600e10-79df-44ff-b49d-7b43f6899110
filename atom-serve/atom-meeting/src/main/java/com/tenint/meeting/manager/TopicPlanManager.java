package com.tenint.meeting.manager;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.meeting.convert.TopicPlanConvert;
import com.tenint.meeting.convert.TopicPlanDetailConvert;
import com.tenint.meeting.domain.TopicPlan;
import com.tenint.meeting.domain.TopicPlanDetail;
import com.tenint.meeting.domain.TopicUser;
import com.tenint.meeting.domain.vo.TopicPlanRowVo;
import com.tenint.meeting.service.*;
import com.tenint.meeting.domain.bo.TopicPlanBo;
import com.tenint.meeting.domain.vo.TopicPlanVo;

import com.tenint.common.utils.StringUtils;
import com.tenint.system.domain.SysOss;
import com.tenint.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 议题计划时间综合Service层
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@RequiredArgsConstructor
@Service
public class TopicPlanManager {

    private final ITopicPlanService topicPlanService;

    private final IFileEntityService fileEntityService;

    private final ITopicPlanDetailService topicPlanDetailService;

    private final ITopicUserService topicUserService;

    private final ITopicFileService topicFileService;

    private final ITopicFileAuditService topicFileAuditService;

    private final ISysOssService ossService;

    /**
     * 查询议题计划时间
     */
    public TopicPlanVo getVoById(Long id) {
        TopicPlan topicPlan = topicPlanService.getById(id);
        TopicPlanVo topicPlanVo = TopicPlanConvert.INSTANCE.convert(topicPlan);

        List<TopicPlanDetail> planDetailList = topicPlanDetailService.listByPlanId(id);

        if (ObjectUtil.isNotEmpty(planDetailList)) {
            topicPlanVo.setPlanList(TopicPlanDetailConvert.INSTANCE.convertList(planDetailList));

            // 处理用户信息
            List<TopicUser> userList = topicUserService.listByTopicId(id);
            if (ObjectUtil.isNotEmpty(userList)) {
                Map<Long, List<Long>> userIdMap = userList.stream().collect(Collectors.groupingBy(TopicUser::getTypeId, Collectors.mapping(TopicUser::getUserId, Collectors.toList())));
                topicPlanVo.getPlanList().forEach( plan -> {
                    Long typeId = plan.getTypeId();
                    List<Long> userIds = userIdMap.get(typeId);
                    plan.setUserIds(userIds);
                });
            }


        }

        String ossIds = topicPlan.getOssIds();
        if (StringUtils.isNotBlank(ossIds) && !"null".equals(ossIds)) {
            List<String> ids = StringUtils.splitTo(ossIds, ",", String::valueOf);
            List<SysOss> ossList = ossService.listByIds(ids);
            topicPlanVo.setFileList(ossList);
        }
        return topicPlanVo;
    }

    /**
     * 查询议题计划时间列表
     */
    public TableDataInfo<TopicPlanVo> pageTopicPlanVo(TopicPlanBo bo, PageQuery pageQuery) {
        Page<TopicPlan> page = topicPlanService.pageTopicPlan(bo, pageQuery);
        Page<TopicPlanVo> pageVo = TopicPlanConvert.INSTANCE.convertPage(page);

        List<TopicPlanVo> records = pageVo.getRecords();

        if (ObjectUtil.isNotEmpty(records)) {
            List<Long> planIds = records.stream().map(TopicPlanVo::getId).toList();
            Map<Long, List<TopicPlanDetail>> map = topicPlanDetailService.getMapWithTopicIdByPlanIds(planIds);

            records.forEach(planVo -> {
                List<TopicPlanDetail> planList = map.get(planVo.getId());
                if (ObjectUtil.isNotEmpty(planList)) {
                    planVo.setPlanList(TopicPlanDetailConvert.INSTANCE.convertList(planList));
                }
            });
        }

        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询议题计划时间列表
     */
    public List<TopicPlanVo> listTopicPlanVo(TopicPlanBo bo) {
        List<TopicPlan> list = topicPlanService.listTopicPlan(bo);
        List<TopicPlanVo> listVo = TopicPlanConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增议题计划时间
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveByBo(TopicPlanBo bo) {
        TopicPlan topicPlan = TopicPlanConvert.INSTANCE.convert(bo);

//         bo.setStatus(TopicStatus.PREPARE.getKey());
//
//        if (ObjectUtil.isNotEmpty(bo.getFileList())) {
//            String ossIds = bo.getFileList().stream().map(SysOss::getId).map(String::valueOf).collect(Collectors.joining(","));
//            topic.setOssIds(ossIds);
//        }

        boolean save = topicPlanService.save(topicPlan);
        topicPlanDetailService.saveOrUpdateBatchByPlanId(topicPlan.getId(), bo.getPlanList());

        return save;
    }

    /**
     * 修改议题计划时间
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TopicPlanBo bo) {
        validateTopicPlanExists(bo.getId());
        TopicPlan topicPlan = TopicPlanConvert.INSTANCE.convert(bo);

//         if (ObjectUtil.isNotEmpty(bo.getFileList())) {
//            String ossIds = bo.getFileList().stream().map(SysOss::getId).map(String::valueOf).collect(Collectors.joining(","));
//            topicPlan.setOssIds(ossIds);
//        }
        topicPlanDetailService.saveOrUpdateBatchByPlanId(topicPlan.getId(), bo.getPlanList());
        return topicPlanService.updateById(topicPlan);
    }


    /**
     * 校验并批量删除议题计划时间信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        Boolean status = topicPlanService.removeWithValidByIds(ids, isValid);

        // 关联信息删除
        topicPlanDetailService.removeByPlanIds(ids, isValid);
        return status;
    }


    private void validateTopicPlanExists(Long id) {
        if (topicPlanService.getById(id) == null) {
            throw new ServiceException("议题计划时间数据不存在");
        }
    }


    /**
     *  获取当前时间可选的计划
     * @return
     */
    public List<TopicPlanRowVo> listByAvailable() {
        return topicPlanService.listByAvailable();
    }
}
