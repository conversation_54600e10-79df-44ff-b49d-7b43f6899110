package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.bo.TopicMeetingBo;

import java.util.Collection;
import java.util.List;
/**
 * 议题收集Service接口
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface ITopicMeetingService extends MPJBaseService<TopicMeeting> {

    /**
     * 查询议题收集列表
     */
    Page<TopicMeeting> pageTopicMeeting(TopicMeetingBo bo, PageQuery pageQuery);

    /**
     * 查询议题收集列表
     */
    List<TopicMeeting> listTopicMeeting(TopicMeetingBo bo);

    /**
     * 校验并批量删除议题收集信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据给出的议题id 过滤出自己创建的或自己汇报的议题id
     *
     * @param topicIds 需要检查的topicIds
     */
    List<Long> filterMyTopicMeetingIds(List<Long> topicIds);
}
