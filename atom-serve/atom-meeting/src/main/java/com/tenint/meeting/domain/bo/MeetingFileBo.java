package com.tenint.meeting.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 附件业务对象 t_meeting_file
 *
 * <AUTHOR>
 * @date 2024-03-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingFileBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String name;


    /**
     * 会议主键id
     */
    @NotNull(message = "会议主键id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingId;


    /**
     * oss编号
     */
    @NotNull(message = "oss编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long ossId;


}
