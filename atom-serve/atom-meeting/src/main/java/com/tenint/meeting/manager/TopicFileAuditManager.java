package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.meeting.domain.TopicFileAudit;
import com.tenint.meeting.domain.query.TopicAuditQuery;
import com.tenint.meeting.domain.vo.TopicFileAuditRowVo;
import com.tenint.meeting.service.ITopicFileAuditService;
import com.tenint.meeting.domain.bo.TopicFileAuditBo;
import com.tenint.meeting.domain.vo.TopicFileAuditVo;
import com.tenint.meeting.convert.TopicFileAuditConvert;

import com.tenint.meeting.service.ITopicPlanDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;

/**
 * 议题文件审核综合Service层
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RequiredArgsConstructor
@Service
public class TopicFileAuditManager {

    private final ITopicFileAuditService topicFileAuditService;

    private final ITopicPlanDetailService topicPlanService;

    /**
     * 查询议题文件审核
     */
    public TopicFileAuditVo getVoById(Long id) {
        TopicFileAudit topicFileAudit = topicFileAuditService.getById(id);
        TopicFileAuditVo topicFileAuditVo = TopicFileAuditConvert.INSTANCE.convert(topicFileAudit);
        return topicFileAuditVo;
    }

    /**
     * 查询议题文件审核列表
     */
    public TableDataInfo<TopicFileAuditVo> pageTopicFileAuditVo(TopicFileAuditBo bo, PageQuery pageQuery) {
        Page<TopicFileAudit> page = topicFileAuditService.pageTopicFileAudit(bo, pageQuery);
        Page<TopicFileAuditVo> pageVo = TopicFileAuditConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询议题文件审核列表
     */
    public List<TopicFileAuditVo> listTopicFileAuditVo(TopicFileAuditBo bo) {
        List<TopicFileAudit> list = topicFileAuditService.listTopicFileAudit(bo);
        List<TopicFileAuditVo> listVo = TopicFileAuditConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增议题文件审核
     */
    public Boolean saveByBo(TopicFileAuditBo bo) {
        TopicFileAudit topicFileAudit = TopicFileAuditConvert.INSTANCE.convert(bo);
        return topicFileAuditService.save(topicFileAudit);
    }

    /**
     * 修改议题文件审核
     */
    public Boolean updateByBo(TopicFileAuditBo bo) {
        validateTopicFileAuditExists(bo.getId());
        TopicFileAudit topicFileAudit = TopicFileAuditConvert.INSTANCE.convert(bo);
        return topicFileAuditService.updateById(topicFileAudit);
    }


    /**
     * 校验并批量删除议题文件审核信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return topicFileAuditService.removeWithValidByIds(ids, isValid);
    }


    private void validateTopicFileAuditExists(Long id) {
        if (topicFileAuditService.getById(id) == null) {
            throw new ServiceException("议题文件审核数据不存在");
        }
    }

    public TableDataInfo<TopicFileAuditRowVo> pageTopicAuditVo(TopicAuditQuery query, PageQuery pageQuery) {

        Page<TopicFileAuditRowVo> list = topicPlanService.pageTopicJoinPlan(query, pageQuery);

        return TableDataInfo.build(list);
    }
}
