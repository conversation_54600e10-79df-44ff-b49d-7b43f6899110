package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "TOPIC_AUDIT_INFO_STATUS",label = "议题审核状态")
@Getter
@AllArgsConstructor
public enum TopicMeetingAuditInfoEnum implements KeyLabelEnum {

    PASS("pass", "通过" ),

    RECALL("2", "退回"),

    ;

    private final String key,label;
}
