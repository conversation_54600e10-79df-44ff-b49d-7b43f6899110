package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.MeetingServe;
import com.tenint.meeting.domain.bo.MeetingServeBo;
import com.tenint.meeting.domain.vo.MeetingServeVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MeetingServeConvert {

    MeetingServeConvert INSTANCE = Mappers.getMapper( MeetingServeConvert.class );


    MeetingServe convert(MeetingServeBo bean);

    MeetingServeVo convert(MeetingServe bean);

    List<MeetingServeVo> convertList(List<MeetingServe> list);

    Page<MeetingServeVo> convertPage(Page<MeetingServe> page);

}
