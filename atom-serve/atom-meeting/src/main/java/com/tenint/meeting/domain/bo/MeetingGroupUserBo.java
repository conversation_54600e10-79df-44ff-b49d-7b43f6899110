package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 用户组用户业务对象 t_meeting_group_user
 *
 * <AUTHOR>
 * @date 2024-10-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingGroupUserBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 用户id
     */
    private Long userId;


    /**
     * 用户名字
     */
    private String userName;


    /**
     * 主表id
     */
    private Long groupId;


}
