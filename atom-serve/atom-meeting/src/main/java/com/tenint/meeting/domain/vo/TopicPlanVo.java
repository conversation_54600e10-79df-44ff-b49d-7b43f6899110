package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import com.tenint.system.domain.SysOss;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * 议题计划时间视图对象 t_topic_plan
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@ExcelIgnoreUnannotated
public class TopicPlanVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 主题
     */
    @ExcelProperty(value = "主题")
    private String title;


    /**
     * 计划列表
     */
    private List<TopicPlanDetailVo> planList = new ArrayList<>();

    /**
     * 文件列表
     */
    private List<SysOss> fileList;
}

