package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.core.domain.BaseEntity;

/**
 * 督查督办主业务对象 t_task
 *
 * <AUTHOR>
 * @date 2024-10-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TaskBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 任务类型 1来自会议 2新建任务
     */
    @NotBlank(message = "任务类型 1来自会议 2新建任务不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String taskType;

    /**
     * 来自会议 - 关联会议
     */
    private Long meetingId;

    /**
     * 新建任务 - 任务来源 1引用文件 2手动输入
     */
    private String sourceType;

    /**
     * 新建任务 - 任务来源 - 引用文件 文件
     */
    private List<TaskFileBo> sourceFileList;

    /**
     * 新建任务 - 任务来源 - 手动输入
     */
    private String sourceContent;

    /**
     * 督办事项名称
     */
    @NotBlank(message = "督办事项名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String taskTitle;


    /**
     * 主责部门id
     */
    @NotNull(message = "主责部门id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long deptId;

    /**
     * 分管领导id
     */
    @NotNull(message = "分管领导id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long majorUserId;

    /**
     * 部门负责人id
     */
    @NotNull(message = "部门负责人id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 协助部室id
     */
    private Long coDeptId;

    /**
     * 协助部门领导id
     */
    private Long coMajorUserId;

    /**
     * 协助部门负责人id
     */
    private Long coUserId;

    /**
     * 办结时间
     */
    @NotNull(message = "办结时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Date completeTime;


    /**
     * 任务内容
     */
    @NotBlank(message = "任务内容不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String taskContent;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 附件
     */
    private List<TaskFileBo> fileList;

    private String orgCode;

    /**
     * 立项时间
     */
    private Date approvalTime;

    /**
     * 立项依据
     */
    private String taskGist;

    /**
     * 分管领导补充意见时效（天）
     */
    @NotNull(message = "分管领导补充意见时效不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Integer majorFillExpDays;

    /**
     * 关联的部门领导ids
     */
    private String otherMajorIds;

}
