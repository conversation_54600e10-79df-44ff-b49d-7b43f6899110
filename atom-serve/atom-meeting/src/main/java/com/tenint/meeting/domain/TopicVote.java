package com.tenint.meeting.domain;


import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题表决对象 t_topic_vote
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@TableName("t_topic_vote")
public class TopicVote extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 会议id
    */
    private Long meetingId;

   /**
    * 用户id
    */
    private Long userId;

   /**
    * 议题id
    */
    private Long topicId;

   /**
    * 表决意见，1 同意；2 原则同意；3 不同意；4 保留意见
    */
    private String status;

    /**
    *  用户类型
    */
    private String userType;

    /**
     * 表决意见
     */
    private String remark;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
