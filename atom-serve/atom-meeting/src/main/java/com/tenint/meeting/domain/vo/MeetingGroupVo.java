package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.List;


/**
 * 用户组视图对象 t_meeting_group
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
@ExcelIgnoreUnannotated
public class MeetingGroupVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 名字
     */
    @ExcelProperty(value = "名字")
    private String name;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名字
     */
    @ExcelProperty(value = "用户名字")
    private String userName;

    /**
     * 机构ID
     */
    @ExcelProperty(value = "机构ID")
    private String orgCode;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 用户组人员
     */
    private List<Long> groupUsers;

}

