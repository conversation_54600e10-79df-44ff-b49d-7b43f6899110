package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.core.domain.BaseEntity;

/**
 * 议题收集流转记录业务对象 t_topic_meeting_flow
 *
 * <AUTHOR>
 * @date 2024-09-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicMeetingFlowBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String userName;


    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String status;


    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String remark;


    /**
     * 关联会议收集id
     */
    @NotNull(message = "关联会议收集id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long topicMeetId;


    /**
     * 操作时间
     */
    @NotNull(message = "操作时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Date operateTime;


    /**
     * 记录类型 创建类型 审批类型
     */
    @NotBlank(message = "记录类型 创建类型 审批类型不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String flowType;


}
