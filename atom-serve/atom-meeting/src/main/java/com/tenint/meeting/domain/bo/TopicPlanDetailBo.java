package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import java.util.List;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 议题计划时间业务对象 t_topic_plan
 *
 * <AUTHOR>
 * @date 2024-07-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicPlanDetailBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 计划编号
     */
    private Long planId;


    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Date beginTime;


    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Date endTime;


    /**
     * 当前计划的类型名称
     */
    @NotBlank(message = "当前计划的类型名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String typeName;


    /**
     * 关联编号
     */
    @NotNull(message = "关联编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long typeId;


    /**
     * 能填报的人员列表
     */
    private List<Long> userIds;


}
