package com.tenint.meeting.enums;
import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务来源类型
 * <AUTHOR>
 */

@DictEnum(key = "TASK_SOURCE_TYPE",label = "任务类型")
@Getter
@AllArgsConstructor
public enum TaskSourceTypeEnum implements KeyLabelEnum {

    FILE("1", "引用文件" ),

    INPUT("2", "手动输入"),

    ;
    private final String key,label;
}
