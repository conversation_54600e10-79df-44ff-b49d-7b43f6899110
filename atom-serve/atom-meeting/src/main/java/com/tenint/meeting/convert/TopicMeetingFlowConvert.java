package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.TopicMeetingFlow;
import com.tenint.meeting.domain.bo.TopicMeetingFlowBo;
import com.tenint.meeting.domain.vo.TopicMeetingFlowRecordVo;
import com.tenint.meeting.domain.vo.TopicMeetingFlowVo;
import com.tenint.meeting.enums.TopicMeetingAuditEnum;
import com.tenint.meeting.enums.TopicMeetingAuditFlowEnum;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TopicMeetingFlowConvert {

    TopicMeetingFlowConvert INSTANCE = Mappers.getMapper( TopicMeetingFlowConvert.class );


    TopicMeetingFlow convert(TopicMeetingFlowBo bean);

    @Mapping(source = "id", target = "topicMeetId")
    @Mapping(source = "reportUserId", target = "userId")
    @Mapping(source = "reportUserName", target = "userName")
    @Mapping(source = "createTime", target = "operateTime")
    TopicMeetingFlow convertWithTopicCreate(TopicMeeting topicMeeting);

    @AfterMapping
    default void convertFlow( @MappingTarget TopicMeetingFlow flow, TopicMeeting topicMeeting) {
        if (TopicMeetingAuditEnum.SUBMIT.getKey().equals(topicMeeting.getStatus())) {
            flow.setFlowType(TopicMeetingAuditFlowEnum.CREATE.getKey());
        }
    }

    @Mapping(source = "id", target = "topicId")
    TopicMeetingFlowRecordVo convert(TopicMeeting topicMeeting);

    TopicMeetingFlowVo convert(TopicMeetingFlow flow);

    List<TopicMeetingFlowVo> convertList(List<TopicMeetingFlow> list);

    Page<TopicMeetingFlowRecordVo> convertPage(Page<TopicMeetingFlow> page);

}
