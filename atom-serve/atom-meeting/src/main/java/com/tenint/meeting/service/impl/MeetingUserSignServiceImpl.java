package com.tenint.meeting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.meeting.domain.MeetingUserSign;
import com.tenint.meeting.mapper.MeetingUserSignMapper;
import com.tenint.meeting.service.IMeetingUserSignService;
import com.tenint.meeting.domain.bo.MeetingUserSignBo;

import java.util.*;

import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.file.PdfSignTableUtils;
import com.tenint.meeting.domain.Meeting;
import com.tenint.meeting.domain.MeetingJoiner;
import com.tenint.meeting.service.IMeetingService;
import com.tenint.meeting.service.IMeetingJoinerService;
import com.tenint.system.service.ISysUserService;
import com.tenint.system.service.ISysOrgService;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.meeting.enums.JoinUserType;

import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 会议签名Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@RequiredArgsConstructor
@Service
public class MeetingUserSignServiceImpl extends ServiceImpl<MeetingUserSignMapper, MeetingUserSign> implements IMeetingUserSignService {

    private final IMeetingService meetingService;
    private final IMeetingJoinerService meetingJoinerService;
    private final ISysUserService sysUserService;
    private final ISysOrgService sysOrgService;
    private final HrApiFacade hrApiFacade;


    /**
     * 查询会议签名列表
     */
    @Override
    public Page<MeetingUserSign> pageMeetingUserSign(MeetingUserSignBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MeetingUserSign> lqw = buildQueryWrapper(bo);
        Page<MeetingUserSign> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询会议签名列表
     */
    @Override
    public List<MeetingUserSign> listMeetingUserSign(MeetingUserSignBo bo) {
        LambdaQueryWrapper<MeetingUserSign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建会议签名列表查询条件
     */
    private LambdaQueryWrapper<MeetingUserSign> buildQueryWrapper(MeetingUserSignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeetingUserSign> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, MeetingUserSign::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getSignPic()), MeetingUserSign::getSignPic, bo.getSignPic());
        lqw.eq(bo.getMeetingId() != null, MeetingUserSign::getMeetingId, bo.getMeetingId());
        return lqw;
    }


    /**
     * 批量删除会议签名
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 生成会议签到表PDF
     */
    @Override
    public byte[] generateSignTablePdf(Long meetingId) {
        // 1. 获取会议信息
        Meeting meeting = meetingService.getById(meetingId);
        if (meeting == null) {
            throw new ServiceException("会议不存在");
        }

        // 2. 获取已签到的用户签名信息
        List<MeetingUserSign> signList = listMeetingUserSign(
                new MeetingUserSignBo() {{
                    setMeetingId(meetingId);
                }}
        );

        // 如果没有签到记录，返回空PDF
        if (signList.isEmpty()) {
            return PdfSignTableUtils.generateSignTablePdf(
                    meeting.getMeetingTitle(),
                    meeting.getMeetingBeginTime(),
                    new ArrayList<>(),
                    new ArrayList<>()
            );
        }

        // 3. 获取已签到用户的ID集合
        Set<Long> signedUserIds = signList.stream()
                .map(MeetingUserSign::getUserId)
                .collect(Collectors.toSet());

        // 4. 获取参会人员和列席人员，只保留已签到的
        List<MeetingJoiner> joiners = meetingJoinerService.list(
                Wrappers.<MeetingJoiner>lambdaQuery()
                        .eq(MeetingJoiner::getMeetingId, meetingId)
                        .in(MeetingJoiner::getUserId, signedUserIds)
        );

        // 5. 分离参会人员和列席人员
        List<MeetingJoiner> attendees = joiners.stream()
                .filter(joiner -> JoinUserType.ATTEND.getKey().equals(joiner.getUserType()))
                .sorted(Comparator.comparing(MeetingJoiner::getJoinerOrder))
                .collect(Collectors.toList());

        List<MeetingJoiner> observers = joiners.stream()
                .filter(joiner -> JoinUserType.READ.getKey().equals(joiner.getUserType()))
                .sorted(Comparator.comparing(MeetingJoiner::getJoinerOrder))
                .collect(Collectors.toList());

        // 6. 构建签名信息映射
        Map<Long, MeetingUserSign> signMap = signList.stream()
                .collect(Collectors.toMap(MeetingUserSign::getUserId, Function.identity()));

        // 7. 构建参会人员签到信息
        List<PdfSignTableUtils.SignTableAttendee> attendeeList = buildAttendeeList(attendees, signMap);

        // 8. 构建列席人员签到信息
        List<PdfSignTableUtils.SignTableAttendee> observerList = buildAttendeeList(observers, signMap);

        // 9. 生成PDF
        return PdfSignTableUtils.generateSignTablePdf(
                meeting.getMeetingTitle(),
                meeting.getMeetingBeginTime(),
                attendeeList,
                observerList
        );
    }

    /**
     * 构建参与者列表（只包含已签到的用户）
     */
    private List<PdfSignTableUtils.SignTableAttendee> buildAttendeeList(
            List<MeetingJoiner> joiners, Map<Long, MeetingUserSign> signMap) {

        List<PdfSignTableUtils.SignTableAttendee> result = new ArrayList<>();

        for (MeetingJoiner joiner : joiners) {
            // 只处理已签到的用户
            MeetingUserSign sign = signMap.get(joiner.getUserId());
            if (sign == null) {
                continue; // 跳过未签到的用户
            }

            // 获取用户信息
            SysUser user = sysUserService.getById(joiner.getUserId());
            if (user == null) {
                continue;
            }

            // 获取用户组织信息作为职务
            String title = "";
            if (user.isAdmin()) {
                title = "管理员";
            } else {
                List<CrccOrg> positions = hrApiFacade.listPositionByUserId(LoginHelper.getProviderId(), String.valueOf(user.getUserId()));
                title = positions.stream().map(CrccOrg::getName).collect(Collectors.joining("、"));
            }

            // 获取签名信息
            String signatureBase64 = sign.getSignPic();

            PdfSignTableUtils.SignTableAttendee attendee = new PdfSignTableUtils.SignTableAttendee(
                    user.getNickName(),
                    title,
                    "",
                    signatureBase64
            );

            result.add(attendee);
        }

        return result;
    }

}
