package com.tenint.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.constant.CacheNames;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.utils.JsonUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.convert.MessageConfigConvert;
import com.tenint.meeting.domain.MessageConfig;
import com.tenint.meeting.domain.bo.MessageConfigBo;
import com.tenint.meeting.domain.dto.MessageParamsDTO;
import com.tenint.meeting.domain.dto.MessageTypeDTO;
import com.tenint.meeting.enums.MessageComponentTypeEnum;
import com.tenint.meeting.mapper.MessageConfigMapper;
import com.tenint.meeting.service.IMessageConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 待办配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
public class MessageConfigServiceImpl extends ServiceImpl<MessageConfigMapper, MessageConfig> implements IMessageConfigService {

    @Override
    @Cacheable(cacheNames = CacheNames.MESSAGE_CONFIG, key = "#bo.cacheKey")
    public int saveBo(MessageConfigBo bo) {
        MessageConfig db = baseMapper.selectByClientTypeAndMessageType(bo.getClientType(), bo.getMessageType());
        validTableAndColumns(bo);
        MessageConfig messageConfig = MessageConfigConvert.INSTANCE.convert(bo);
        if (ObjectUtil.isNotNull(db)) {
            messageConfig.setId(db.getId());
            return baseMapper.updateById(messageConfig);
        }

        return baseMapper.insert(messageConfig);
    }

    @Override
    @CacheEvict(cacheNames = CacheNames.MESSAGE_CONFIG, key = "#bo.cacheKey")
    public int updateBoById(MessageConfigBo bo) {
        validTableAndColumns(bo);
        MessageConfig todoConfig = MessageConfigConvert.INSTANCE.convert(bo);
        return baseMapper.updateById(todoConfig);
    }

    @Override
    @CacheEvict(cacheNames = CacheNames.MESSAGE_CONFIG, key = "#dto.cacheKey")
    public MessageConfig selectByClientTypeAndMessageType(MessageTypeDTO dto) {
        return baseMapper.selectByClientTypeAndMessageType(dto.getClientType(), dto.getMessageType());
    }

    @Override
    public MessageConfig selectByMessageType(String messageType) {
        return baseMapper.selectByType(messageType);
    }


    private void validTableAndColumns(MessageConfigBo bo) {
        if (MessageComponentTypeEnum.NONE.key.equals(bo.getComponentType())) {
            return;
        }

        int i = baseMapper.existsTable(bo.getBusinessTable());
        if (i < 1) {
            throw new ServiceException("输入的表不存在");
        }

        MessageParamsDTO paramsDTO = parseParams(bo.getComponentParams());
        HashMap<String, String> sqlFields = paramsDTO.getSqlFields();
        if (ObjectUtil.isEmpty(sqlFields)) {
            return;
        }
        List<String> columns = baseMapper.selectDbTableColumnsByName(bo.getBusinessTable());
        Collection<String> sqlField = sqlFields.values();

        boolean hasDuplicates = sqlField.stream()
                .distinct()
                .count() < sqlField.size();
        if (hasDuplicates) {
            throw new ServiceException("不能设置重复的sql()字段");

        }
        for (String column : sqlField) {
            if (!columns.contains(column)) {
                throw new ServiceException("存在不在表中的列: " + column);
            }
        }

    }

    public MessageParamsDTO parseParams(String params) {

        if (StringUtils.isBlank(params)) {
            throw new ServiceException("组件参数不能为空");
        }
        MessageParamsDTO paramsDTO = new MessageParamsDTO();
        HashMap<String, Object> dict = JsonUtils.parseMap(params);
        Set<Map.Entry<String, Object>> entries = dict.entrySet();

        HashMap<String,String> sqlMap = new HashMap<>();
        HashMap<String, String> delayMap = new HashMap<>();
        HashMap<String, Object> normalMap = new HashMap<>();
        HashMap<String, Object> basicMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : entries) {
            Object value = entry.getValue();
            String key = entry.getKey();
            String string = StrUtil.toString(value);
            String sqlContents = extractSqlContents(string);
            String dollarContents = extractDollarContents(string);
            if (ObjectUtil.isNotEmpty(sqlContents)) {
                sqlMap.put(key,sqlContents);
            } else if (ObjectUtil.isNotEmpty(dollarContents)) {
                delayMap.put(key, dollarContents);
            } else {
                normalMap.put(key, value);
            }
            basicMap.put(key, value);
        }

        paramsDTO.setSqlFields(sqlMap);
        paramsDTO.setDelayFields(delayMap);
        paramsDTO.setNormalFields(normalMap);
        paramsDTO.setBasicMap(basicMap);
        return paramsDTO;
    }

    @Override
    public Page<MessageConfig> pageConfig(MessageConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MessageConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectPage(pageQuery.build(), lqw);
    }

    @Override
    public List<MessageConfig> selectByClientType(String clientType) {
        return baseMapper.selectByClientType(clientType);
    }

    private LambdaQueryWrapper<MessageConfig> buildQueryWrapper(MessageConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MessageConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getMessageType()), MessageConfig::getMessageType, bo.getMessageType());
        lqw.like(StringUtils.isNotBlank(bo.getMessageModule()), MessageConfig::getMessageModule, bo.getMessageModule());
        lqw.like(StringUtils.isNotBlank(bo.getClientType()), MessageConfig::getClientType, bo.getClientType());
        return lqw;
    }

    public String extractContents(String input, String patternStr) {
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * @param input
     * @return {@link List}<{@link String}>
     */
    public String extractSqlContents(String input) {
        return extractContents(input, "sql\\(([^)]+)\\)");
    }

    /**
     * @param input
     * @return {@link List}<{@link String}>
     */
    public String extractDollarContents(String input) {
        return extractContents(input, "\\$\\(([^)]+)\\)");
    }


}
