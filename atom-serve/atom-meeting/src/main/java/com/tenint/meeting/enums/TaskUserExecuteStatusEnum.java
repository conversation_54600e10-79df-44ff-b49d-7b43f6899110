package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: myz
 * @date: 2024/10/23
 */
@DictEnum(key = "TASK_USER_EXECUTE_STATUS",label = "督查督办执行办结状态",style = true)
@Getter
@AllArgsConstructor
public enum TaskUserExecuteStatusEnum implements KeyLabelStyleEnum {
    
    NOT_COMPLETED("0", "未办结", "primary"),

    COMPLETED("1", "已办结", "primary");

    private final String key,label,eleStyle;
}
