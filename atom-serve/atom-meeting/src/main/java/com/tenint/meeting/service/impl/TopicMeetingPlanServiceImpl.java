package com.tenint.meeting.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.Task;
import com.tenint.meeting.domain.TopicMeetingPlan;
import com.tenint.meeting.domain.TopicPlanLink;
import com.tenint.meeting.domain.bo.TopicMeetingPlanBo;
import com.tenint.meeting.mapper.TopicMeetingPlanMapper;
import com.tenint.meeting.service.ITopicMeetingPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 预约Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingPlanServiceImpl extends MPJBaseServiceImpl<TopicMeetingPlanMapper, TopicMeetingPlan> implements ITopicMeetingPlanService {


    /**
     * 查询预约列表
     */
    @Override
    public Page<TopicMeetingPlan> pageTopicMeetingPlan(TopicMeetingPlanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopicMeetingPlan> lqw = buildQueryWrapper(bo);
        Page<TopicMeetingPlan> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询预约列表
     */
    @Override
    public List<TopicMeetingPlan> listTopicMeetingPlan(TopicMeetingPlanBo bo) {
        LambdaQueryWrapper<TopicMeetingPlan> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    @Override
    public List<TopicMeetingPlan> getListByTopicId(Long topicId){
        MPJLambdaWrapper<TopicMeetingPlan> lqw = JoinWrappers.lambda();
        lqw.selectAll(TopicMeetingPlan.class)
                .innerJoin(TopicPlanLink.class, TopicPlanLink::getPlanId, TopicMeetingPlan::getId)
                .eq(TopicPlanLink::getTopicId, topicId);
        return this.selectJoinList(TopicMeetingPlan.class, lqw);
    }

    /**
     * 构建预约列表查询条件
     */
    private LambdaQueryWrapper<TopicMeetingPlan> buildQueryWrapper(TopicMeetingPlanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopicMeetingPlan> lqw = Wrappers.lambdaQuery();
            lqw.like(StringUtils.isNotBlank(bo.getTitle()), TopicMeetingPlan::getTitle, bo.getTitle());
            lqw.eq(StringUtils.isNotBlank(bo.getType()), TopicMeetingPlan::getType, bo.getType());
            lqw.orderByDesc(TopicMeetingPlan::getCreateTime);
            if (ObjectUtil.isNotEmpty(bo.getDateRange())){
                lqw.between(TopicMeetingPlan::getLastTime,bo.getDateRange()[0],bo.getDateRange()[1]);
            }
        return lqw;
    }


    /**
     * 批量删除预约
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
