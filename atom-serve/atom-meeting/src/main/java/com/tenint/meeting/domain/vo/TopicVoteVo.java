package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 议题表决视图对象 t_topic_vote
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ExcelIgnoreUnannotated
public class TopicVoteVo {

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 会议id
     */
    @ExcelProperty(value = "会议id")
    private Long meetingId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 议题id
     */
    @ExcelProperty(value = "议题id")
    private Long topicId;

    /**
     * 表决意见，1 同意；2 原则同意；3 不同意；4 保留意见
     */
    @ExcelProperty(value = "表决意见，1 同意；2 原则同意；3 不同意；4 保留意见")
    private String status;

    private String userName;

    /**
     *  用户类型
     */
    private String userType;

    /**
     * 表决意见
     */
    private String remark;


}

