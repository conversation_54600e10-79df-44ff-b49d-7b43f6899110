package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 议题收集审批人类型
 * <AUTHOR>
 */
@DictEnum(key = "topic_audit_role", label = "议题收集附件类型", style = true)
@Getter
@AllArgsConstructor
public enum TopicAuditRoleEnum implements KeyLabelStyleEnum {

    DEPART("1", "部门领导", "default"),
    LEADER("2", "分管领导", "default"),
    ;

    private final String key, label, eleStyle;
}