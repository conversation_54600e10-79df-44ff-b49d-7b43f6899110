package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicMeetingFlow;
import com.tenint.meeting.domain.bo.TopicMeetingFlowBo;

import java.util.Collection;
import java.util.List;
/**
 * 议题收集流转记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface ITopicMeetingFlowService extends IService<TopicMeetingFlow> {

    /**
     * 查询议题收集流转记录列表
     */
    Page<TopicMeetingFlow> pageTopicMeetingFlow(TopicMeetingFlowBo bo, PageQuery pageQuery);

    /**
     * 查询议题收集流转记录列表
     */
    List<TopicMeetingFlow> listTopicMeetingFlow(TopicMeetingFlowBo bo);

    /**
     * 校验并批量删除议题收集流转记录信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据议题id查询关联的议题审核记录
     * @param topicId
     * @return
     */
    List<TopicMeetingFlow> listFlowByTopicId(Long topicId);
}
