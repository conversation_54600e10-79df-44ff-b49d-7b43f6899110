package com.tenint.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.constant.CommonConstant;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.convert.MessageConvert;
import com.tenint.meeting.domain.Message;
import com.tenint.meeting.domain.MessageConfig;
import com.tenint.meeting.domain.bo.MessageBo;
import com.tenint.meeting.domain.vo.MessagePushVo;
import com.tenint.meeting.domain.vo.MessageRowVo;
import com.tenint.meeting.enums.MessagePushTypeEnun;
import com.tenint.meeting.enums.MessageTypeEnum;
import com.tenint.meeting.mapper.MessageMapper;
import com.tenint.meeting.service.IMessageConfigService;
import com.tenint.meeting.service.IMessageService;
import com.tenint.meeting.service.MessagePushService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息推送主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Service
@RequiredArgsConstructor
public class MessageServiceImpl implements IMessageService {

    private final MessageMapper baseMapper;

    private final IMessageConfigService messageConfigService;

    private final MessagePushService messagePushService;


    /**
     * 查询消息推送主体列表
     */
    @Override
    public Page<Message> pageMessage(MessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Message> lqw = buildQueryWrapper(bo);
        return baseMapper.selectPage(pageQuery.build(), lqw);
    }

    /**
     * 查询消息推送主体列表
     */
    @Override
    public List<Message> listMessage(MessageBo bo) {
        LambdaQueryWrapper<Message> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建消息推送主体列表查询条件
     */
    private LambdaQueryWrapper<Message> buildQueryWrapper(MessageBo bo) {
        Map<String, Object> params = bo.getParams();
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<Message> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, Message::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getIsRead()), Message::getIsRead, bo.getIsRead());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), Message::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getModule()), Message::getModule, bo.getModule());
        lqw.eq(Message::getIsActive, true);
        return lqw;
    }


    /**
     * 批量删除消息推送主体
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        boolean delete = baseMapper.deleteBatchIds(ids) > 0;
        return delete;
    }

    @Override
    public void createByMessageAndUserId(MessageBo message, Collection<Long> userIds, Long linkId) {
        if (userIds.isEmpty()) {
            return;
        }
        List<Message> entityList = new ArrayList<>();
        for (Long id : userIds) {
            Message entity = MessageConvert.INSTANCE.convert(message);
            entity.setUserId(id);
            entity.setModule(message.getType().split("_")[0]);
            entity.setBusinessId(linkId);
            entityList.add(entity);
        }
        baseMapper.saveBatch(entityList);

        Map<Long, List<Message>> map = entityList.stream().collect(Collectors.groupingBy(Message::getUserId));
        for (Map.Entry<Long, List<Message>> entry : map.entrySet()) {
            Long userId = entry.getKey();
            List<Message> userMessageList = entry.getValue();
            List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(userMessageList);
            messagePushService.pushMessage(userId, MessagePushVo.insert(messageRowVos));
        }

        // List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(entityList);
        // for (Long id : userId) {
        //     messagePushService.pushMessage(id, MessagePushVo.insert(messageRowVos));
        // }

    }

    @Override
    public void createByMessageAndUserId(String message, String type, Collection<Long> userIds, Long linkId) {
        if (ObjectUtil.isEmpty(userIds)) {
            return;
        }
        List<Message> entityList = new ArrayList<>();
        for (Long id : userIds) {
            Message entity = new Message();
            entity.setDescription(message);
            entity.setIsRead(CommonConstant.YesOrNo.No.value);
            entity.setIsActive(UserConstants.ACTIVE);
            entity.setModule(type.split("_")[0]);
            entity.setUserId(id);
            entity.setBusinessId(linkId);
            entity.setType(type);
            entityList.add(entity);
        }

        baseMapper.saveBatch(entityList);

        Map<Long, List<Message>> map = entityList.stream().collect(Collectors.groupingBy(Message::getUserId));
        for (Map.Entry<Long, List<Message>> entry : map.entrySet()) {
            Long userId = entry.getKey();
            List<Message> userMessageList = entry.getValue();
            List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(userMessageList);
            messagePushService.pushMessage(userId, MessagePushVo.of(MessagePushTypeEnun.INSERT.key, messageRowVos));
        }

        // for (Long id : userId) {
        //     List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(entityList.stream().filter(m -> m.getUserId().equals(id)).collect(Collectors.toList()));
        //     messagePushService.pushMessage(id, MessagePushVo.of(MessagePushTypeEnun.INSERT.key, messageRowVos));
        // }

    }

    @Override
    public void completeById(Long id) {
        Message message = baseMapper.selectById(id);
        message.setIsRead(CommonConstant.YesOrNo.Yes.value);
        message.setCompleted(true);
        baseMapper.updateById(message);

        MessageRowVo messageRowVo = MessageConvert.INSTANCE.convertRow(message);
        messagePushService.pushMessage(message.getUserId(), MessagePushVo.of(MessagePushTypeEnun.UPDATE.key, messageRowVo));
    }

    @Override
    public void completeByTypeAndBusinessId(MessageTypeEnum messageTypeEnum, Long businessId) {
        MessageConfig messageConfig = messageConfigService.selectByMessageType(messageTypeEnum.key);
        if (ObjectUtil.isNull(messageConfig)) {
            return;
        }
        List<Message> messageList = baseMapper.updateCompleteByBusinessId(messageTypeEnum, businessId, LoginHelper.getUserId(), messageConfig.getAssociated());

        if (ObjectUtil.isNotEmpty(messageList)) {
            List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(messageList);
            // 执行完成推送前端删除这条待办
            messagePushService.pushMessage(LoginHelper.getUserId(), MessagePushVo.delete(messageRowVos));
        }
    }

    @Override
    public void completeAllByTypeAndBusinessId(MessageTypeEnum messageTypeEnum, Long businessId) {
        MessageConfig messageConfig = messageConfigService.selectByMessageType(messageTypeEnum.key);
        if (ObjectUtil.isNull(messageConfig)) {
            return;
        }
        List<Message> messageList = baseMapper.updateCompleteByBusinessId(messageTypeEnum, businessId, null, false);

        if (ObjectUtil.isNotEmpty(messageList)) {
            // 根据用户id分组
            Map<Long, List<Message>> map = messageList.stream().collect(Collectors.groupingBy(Message::getUserId));
            for (Map.Entry<Long, List<Message>> entry : map.entrySet()) {
                Long userId = entry.getKey();
                if(userId == null){
                    continue;
                }
                List<Message> userMessageList = entry.getValue();
                // 执行完成推送前端删除这条待办
                List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(userMessageList);
                messagePushService.pushMessage(userId, MessagePushVo.delete(messageRowVos));
            }
        }
    }
    

    /**
     * 限制下个数
     *
     * @param userId
     * @return
     */
    public List<MessageRowVo> listPendingHandleMessageByUserId(Long userId) {
        LambdaQueryWrapper<Message> lqw = Wrappers.lambdaQuery();
        lqw.eq(Message::getUserId, userId);
        lqw.eq(Message::getIsRead, CommonConstant.YesOrNo.No.value);
        lqw.orderByDesc(Message::getCreateTime);
        Page<Message> page = Page.of(1, 500);
        Page<Message> messageList = baseMapper.selectPage(page, lqw);
        return MessageConvert.INSTANCE.convertRow(messageList.getRecords());
    }

    @Override
    public void updateAllReadStatusByUserIdAndNotInType(Long userId, Set<String> typeList) {
        List<Message> messageList = baseMapper.updateAllReadStatusByUserIdAndNotInType(userId, typeList);
        if (ObjectUtil.isNotEmpty(messageList)) {
            List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(messageList);
            messagePushService.pushMessage(userId, MessagePushVo.of(MessagePushTypeEnun.DELETE.key, messageRowVos));
        }
    }

    @Override
    public int removeByBusinessId(List<Long> businessIdList) {
        return removeByBusinessId(null, businessIdList);
    }

    @Override
    public int removeByBusinessId(MessageTypeEnum messageTypeEnum, List<Long> businessIdList) {
        if (ObjectUtil.isEmpty(businessIdList)) {
            return 0;
        }
        LambdaQueryWrapper<Message> wrapper = Wrappers.<Message>lambdaQuery()
                .select(Message::getId)
                .in(Message::getBusinessId, businessIdList);
        if(messageTypeEnum != null){
            wrapper.eq(Message::getType, messageTypeEnum.key);
        }
        List<Message> messageList = baseMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(messageList)) {
            return 0;
        }
        List<Long> ids = new ArrayList<>();
        messageList.forEach(message -> ids.add(message.getId()));
        int i = baseMapper.deleteBatchIds(ids);
        if (i > 0) {
            List<MessageRowVo> messageRowVos = MessageConvert.INSTANCE.convertRow(messageList);
            messagePushService.pushMessage(LoginHelper.getUserId(), MessagePushVo.delete(messageRowVos));
        }
        return i;
    }

    @Override
    public Message getById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public int updateReadStatusById(Message message) {
        int i = baseMapper.updateById(message);
        if (i > 0) {
            MessagePushVo data = MessagePushVo.of(MessagePushTypeEnun.DELETE.key, MessageConvert.INSTANCE.convertRow(message));
            messagePushService
                    .pushMessage(message.getUserId(), data);
        }
        return i;

    }

    @Override
    public boolean removeById(Long id) {
        boolean status = baseMapper.deleteById(id) > 0;
        if (status) {
            Message message = baseMapper.selectById(id);
            MessagePushVo data = MessagePushVo.of(MessagePushTypeEnun.DELETE.key, MessageConvert.INSTANCE.convertRow(message));
            messagePushService
                    .pushMessage(message.getUserId(), data);
        }
        return status;
    }

    @Override
    public long countByUserIdAndIsRead(Long userId, String value) {
        long count = baseMapper.selectCount(Wrappers.<Message>lambdaQuery()
                .eq(Message::getIsRead, value)
                .eq(Message::getUserId, userId));
        return count;
    }

    @Override
    public HashMap<String, Object> dynamicBusinessTable(String columns, String businessTableName, Long businessId) {
        return baseMapper.dynamicBusinessTable(columns, businessTableName, businessId);
    }

}
