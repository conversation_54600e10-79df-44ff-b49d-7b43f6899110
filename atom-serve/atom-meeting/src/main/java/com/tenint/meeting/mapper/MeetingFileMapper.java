package com.tenint.meeting.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.MeetingFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tenint.meeting.domain.vo.MeetingFileVo;

import java.util.List;

/**
 * 附件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface MeetingFileMapper extends BaseMapper<MeetingFile> {

    /**
     * 根据会议编号查询附件列表
     * @param meetingId
     * @return
     */
    default List<MeetingFile> selectListByMeetingId(Long meetingId) {
        return selectList(new LambdaQueryWrapper<MeetingFile>().eq(MeetingFile::getMeetingId, meetingId));
    }

    default Page<MeetingFile> selectPageByMeetingIds(Page<MeetingFile> page, List<Long> meetings) {
        return selectPage(page, new LambdaQueryWrapper<MeetingFile>().in(MeetingFile::getMeetingId, meetings));
    }
}
