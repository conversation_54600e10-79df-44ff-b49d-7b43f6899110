package com.tenint.meeting.manager;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;


import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.spring.SpringUtils;
import com.tenint.crcc.domain.CrccUser;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.meeting.convert.TopicMeetingConvert;
import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.TopicMeetingLink;
import com.tenint.meeting.domain.TopicPlanLink;
import com.tenint.meeting.domain.bo.TopicMeetingBo;
import com.tenint.meeting.domain.bo.TopicMeetingFileBo;
import com.tenint.meeting.domain.bo.TopicMeetingLinkEditBo;
import com.tenint.meeting.domain.vo.TopicMeetingFileVo;
import com.tenint.meeting.domain.vo.TopicMeetingPlanVo;
import com.tenint.meeting.domain.vo.TopicMeetingVo;
import com.tenint.meeting.enums.*;
import com.tenint.meeting.service.*;
import com.tenint.system.manager.SysUserManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.tenint.common.constant.ConfigConstants.TOPIC_AUDIT;

/**
 * 议题收集综合Service层
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingManager {

    private final ITopicMeetingService topicMeetingService;
    private final ITopicMeetingPlanService meetingPlanService;
    private final ITopicPlanLinkService topicPlanLinkService;
    private final TopicMeetingAuditManager auditManager;
    private final TopicMeetingFileManager fileManager;
    private final HrApiFacade hrApiFacade;
    private final SysUserManager userManager;
    private final TopicMeetingFlowManager flowManager;
    private final TopicMeetingPlanManager planManager;
    private final IMessageService messageService;
    private final ITopicMeetingLinkService topicMeetingLinkService;

    /**
     * 查询我创建的议题收集列表（分页）
     */
    public TableDataInfo<TopicMeetingVo> pageMyTopicMeetingVo(TopicMeetingBo bo, PageQuery pageQuery) {
        // 非管理员，只能查询自己创建的
        if(!LoginHelper.isAdmin()){
            bo.setCreatorId(LoginHelper.getUserId());
        }
        pageQuery.setOrderByColumn("createTime");
        pageQuery.setIsAsc("desc");
        Page<TopicMeeting> page = topicMeetingService.pageTopicMeeting(bo, pageQuery);
        Page<TopicMeetingVo> pageVo = TopicMeetingConvert.INSTANCE.convertPage(page);
        pageVo.getRecords().forEach(this::fillTopicMeetingPageVoInfo);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    // 填充 page vo数据
    private void fillTopicMeetingPageVoInfo(TopicMeetingVo vo){
        List<TopicMeetingPlanVo> plans = planManager.getVoListByTopicId(vo.getId());
        vo.setPlanList(plans);
    }

    /**
     * 新增/更新
     *
     * @param topicMeetingBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateTopicMeeting(TopicMeetingBo topicMeetingBo, String status) {

        TopicMeeting topicMeeting = TopicMeetingConvert.INSTANCE.convert(topicMeetingBo);

        if (!Objects.isNull(LoginHelper.getUserId())) {
            String dept = userManager.getUserDeptByUserId(LoginHelper.getUserId());
            topicMeeting.setCreatorDept(dept);
        }

        // 汇报人
        Long userId = topicMeetingBo.getReportUserId();
        CrccUser user = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(userId));
        topicMeeting.setReportUserId(userId);
        topicMeeting.setReportUserName(user.getName());

        // 主表状态
        topicMeeting.setStatus(status);

        topicMeetingService.saveOrUpdate(topicMeeting);
        Long topicId = topicMeeting.getId();

        Long departUserId = topicMeetingBo.getDepartUserId();
        Long leaderUserId = topicMeetingBo.getLeaderUserId();

        List<TopicMeetingFileBo> mainFile = topicMeetingBo.getMainFile();
        List<TopicMeetingFileBo> topicFileList = topicMeetingBo.getTopicFileList();

        auditManager.saveOrUpdateAuditUser(topicId, Collections.singletonList(departUserId), Collections.singletonList(leaderUserId));
        fileManager.saveOrUpdateTopicFile(topicId, mainFile, topicFileList);

        //  选择的预会议逾期校验
        List<Long> planIds = topicMeetingBo.getPlanIds();
        planManager.getVoListByIds(planIds).forEach(plan -> {
            if (plan.getDelay()) {
                throw new ServiceException("您选择的预会议 " + plan.getTitle() + " 已逾期 请重新选择！");
            }
        });
        // 创建议题预会议关联
        topicPlanLinkService.linkTopicMeetingAndPlans(topicId, planIds);
    }

    /**
     * 校验并批量删除议题收集信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeWithValidByIds(List<Long> ids) {
        // 删除议题
        topicMeetingService.removeBatchByIds(ids);
        // 删除审核相关人员信息
        auditManager.removeAuditUserByTopicId(ids.stream().toList());
        // 删除附件
        fileManager.removeTopicFileByTopicId(ids.stream().toList());
        // 删除消息
        messageService.removeByBusinessId(ids);
        // 删除link
        topicMeetingLinkService.deleteByTopicId(ids);
        return true;
    }

    /**
     * 获取议题收集详情
     *
     * @param id
     * @return
     */
    public TopicMeetingVo getTopicMeeting(Long id) {
        // 1. 查询议题表
        TopicMeeting topicMeeting = topicMeetingService.getById(id);
        TopicMeetingVo topicMeetingVo = TopicMeetingConvert.INSTANCE.convert(topicMeeting);
        if (ObjectUtil.isNull(topicMeeting)) {
            topicMeetingVo = new TopicMeetingVo();
            // 议题表没有查询到可能为特殊议题
            TopicMeetingLink link = topicMeetingLinkService.getById(id);
            // 填充特殊议题的信息
            if(link != null){
                topicMeetingVo.setTopicTitle(link.getTopicTitle());
                topicMeetingVo.setReportUserId(link.getReportUserId());
                topicMeetingVo.setReportUserName(link.getReportUserName());
                topicMeetingVo.setCreatorDept(link.getCreatorDept());
                topicMeetingVo.setCreateTime(link.getCreateTime());
            }
        } else {
            // 关联的多个预会议
            List<TopicMeetingPlanVo> plans = planManager.getVoListByTopicId(id);
            topicMeetingVo.setPlanList(plans);
            // 审核人
            Map<String, Long> userMap = auditManager.collectAuditUserByTopicId(id);
            Long deptUserId = userMap.get(TopicAuditRoleEnum.DEPART.getKey());
            Long leadUserId = userMap.get(TopicAuditRoleEnum.LEADER.getKey());
            topicMeetingVo.setDepartUserId(deptUserId);
            topicMeetingVo.setLeaderUserId(leadUserId);
            String deptName = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(deptUserId)).getName();
            String leadName = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(leadUserId)).getName();
            topicMeetingVo.setDepartUserName(deptName);
            topicMeetingVo.setLeaderUserName(leadName);

        }
        // 附件
        Map<String, List<TopicMeetingFileVo>> fileMap = fileManager.collectFileMapByTopicId(id);
        topicMeetingVo.setMainFile(fileMap.get(TopicFileTypeEnum.MAIN_FILE.getKey()));
        topicMeetingVo.setTopicFileList(fileMap.get(TopicFileTypeEnum.TOPIC_FILE.getKey()));

        return topicMeetingVo;
    }

    /**
     * 根据预会议获得审核通过的议题
     * @param planId 预会议id
     * @return 结果
     */
    public List<TopicMeetingVo> getAuditedTopicByPlanId(Long planId) {
        TopicMeetingBo bo = new TopicMeetingBo();
        // 设置 planId 同时按照 topic_order 排序
        bo.setPlanId(planId);
        bo.setStatus(TopicMeetingAuditEnum.LEAD_PASS.getKey());
        List<TopicMeeting> meetingList = topicMeetingService.listTopicMeeting(bo);
        return TopicMeetingConvert.INSTANCE.convertList(meetingList);
    }

    /**
     * 提交议题收集
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitTopicMeeting(Long id) {
        topicMeetingService.lambdaUpdate()
                .eq(TopicMeeting::getId, id)
                .set(TopicMeeting::getStatus, TopicMeetingAuditEnum.SUBMIT.getKey())
                .set(TopicMeeting::getUpdaterId, LoginHelper.getUserId())
                .set(TopicMeeting::getUpdateTime, new Date()).update();
        handleAfterSubmitted(id);
        return true;
    }

    /**
     * 退回后保存并提交议题收集
     *
     * @param topicMeetingBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void reSubmitTopicMeeting(TopicMeetingBo topicMeetingBo) {
        // 保存并提交
        SpringUtils.getAopProxy(this).saveOrUpdateTopicMeeting(topicMeetingBo, TopicMeetingAuditEnum.SUBMIT.getKey());
        handleAfterSubmitted(topicMeetingBo.getId());
    }

    /**
     * 议题提交后续处理
     * 添加审核人权限；添加审核记录；推送审核待办；完成退回待办（如果需要）
     *
     * @param id 议题id
     */
    private void handleAfterSubmitted(Long id) {
        TopicMeeting topicMeeting = topicMeetingService.getById(id);
        // 记录日志
        flowManager.saveFlowWithTopicSubmitted(topicMeeting);
        Map<String, Long> user = auditManager.collectAuditUserByTopicId(id);
        List<Long> userIds = user.values().stream().toList();
        userManager.saveAuthRoleBackGround(userIds, TOPIC_AUDIT);
        // 已选预会议逾期校验
        planManager.getVoListByTopicId(id).forEach(plan -> {
            if (plan.getDelay()) {
                throw new ServiceException("您选择的预会议 " + plan.getTitle() + " 已逾期 请重新选择！");
            }
        });

        // 推送待办 - 部门领导审核
        Long departUserId = user.get(TopicAuditRoleEnum.DEPART.getKey());
        messageService.createByMessageAndUserId("【议题收集】: 待审核'" + topicMeeting.getTopicTitle() + "'",
                MessageTypeEnum.TOPIC_AUDIT.getKey(), Collections.singletonList(departUserId), id);
        // 尝试完成退回待办（退回后再次提交的）
        messageService.completeByTypeAndBusinessId(MessageTypeEnum.TOPIC_BACK, id);
    }

    /**
     * 根据预会议id查询议题-分页
     * 审核通过的议题
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    public TableDataInfo<TopicMeetingVo> listAuditedTopicByPlanId(TopicMeetingBo bo, PageQuery pageQuery) {
        // 前端设置了 planId 同时按照 topic_order 排序
        bo.setStatus(TopicMeetingAuditEnum.LEAD_PASS.getKey());
        Page<TopicMeeting> page = topicMeetingService.pageTopicMeeting(bo, pageQuery);
        Page<TopicMeetingVo> pageVo = TopicMeetingConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 更新议题信息 集团管理员审核通过的议题
     *
     * @param editBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTopicAfterGroupPass(TopicMeetingLinkEditBo editBo) {
        CrccUser user = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(editBo.getReportUserId()));
        topicMeetingService.lambdaUpdate()
                .eq(TopicMeeting::getId, editBo.getId())
                .set(TopicMeeting::getTopicTitle, editBo.getTopicTitle())
                .set(TopicMeeting::getReportUserId, editBo.getReportUserId())
                .set(TopicMeeting::getReportUserName, user.getName())
                .set(TopicMeeting::getUpdaterId, LoginHelper.getUserId())
                .set(TopicMeeting::getUpdateTime, new Date()).update();
        // 同步更新 link 的标题和汇报人
        topicMeetingLinkService.lambdaUpdate().eq(TopicMeetingLink::getTopicId, editBo.getId())
                .set(TopicMeetingLink::getTopicTitle, editBo.getTopicTitle())
                .set(TopicMeetingLink::getReportUserId, editBo.getReportUserId())
                .set(TopicMeetingLink::getReportUserName, user.getName())
                .update();
    }

    /**
     * 更新议题排序（审核通过的）
     *
     * @param planId
     * @param sortedTopicIdsInPage
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAuditedTopicOrder(Long planId, List<Long> sortedTopicIdsInPage, int pageNo, int pageSize) {
        // 1. 获取完整的已审核 topic 列表
        List<TopicMeetingVo> allAudited = getAuditedTopicByPlanId(planId);

        // 2. 构建当前页的 TopicId 到新顺序的映射
        int startIndex = (pageNo - 1) * pageSize;

        Map<Long, Integer> pageOrderMap = new HashMap<>();
        for (int i = 0; i < sortedTopicIdsInPage.size(); i++) {
            pageOrderMap.put(sortedTopicIdsInPage.get(i), startIndex + i);
        }

        // 3. 重新构建全量排序：当前页用新的顺序，其他页保持原有顺序（先剔除当前页的 topicId）
        List<Long> fullSortedTopicIds = allAudited.stream()
                .map(TopicMeetingVo::getId)
                .filter(id -> !pageOrderMap.containsKey(id)) // 非当前页的保留原顺序
                .collect(Collectors.toList());

        // 插入当前页排序后的 topicId 到合适位置（即 startIndex 开始）
        fullSortedTopicIds.addAll(startIndex, sortedTopicIdsInPage);

        // 4. 为 fullSortedTopicIds 重新分配 TopicOrder
        for (int i = 0; i < fullSortedTopicIds.size(); i++) {
            Long topicId = fullSortedTopicIds.get(i);
            topicPlanLinkService.lambdaUpdate()
                    .eq(TopicPlanLink::getPlanId, planId)
                    .eq(TopicPlanLink::getTopicId, topicId)
                    .set(TopicPlanLink::getTopicOrder, i)
                    .update();
        }
    }
}
