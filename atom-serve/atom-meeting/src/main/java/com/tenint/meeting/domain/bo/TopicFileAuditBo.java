package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 议题文件审核业务对象 t_topic_file_audit
 *
 * <AUTHOR>
 * @date 2024-07-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicFileAuditBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 审核用户编号
     */
    @NotNull(message = "审核用户编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 审核状态
     */
    @NotBlank(message = "审核状态不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String status;


    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String remarks;


    /**
     * 主题编号
     */
    @NotNull(message = "主题编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long topicId;


    /**
     * 文件编号
     */
    @NotNull(message = "文件编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long fileId;


}
