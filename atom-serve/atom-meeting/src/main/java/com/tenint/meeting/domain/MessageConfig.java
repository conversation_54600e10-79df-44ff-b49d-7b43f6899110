package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.tenint.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 待办配置对象 sys_todo_config
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@TableName("t_message_config")
public class MessageConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID")
    private Long id;

    @TableLogic
    @TableField(value = "IS_ACTIVE", fill = FieldFill.INSERT)
    private String isActive;

    @TableField("MESSAGE_MODULE")
    private String messageModule;

    @TableField("MESSAGE_TYPE")
    private String messageType;

    @TableField("COMPONENT_URL")
    private String componentUrl;

    @TableField("COMPONENT_TYPE")
    private String componentType;

    /**
     *  弹窗或者路由跳转所需要的参数，构建一个map如: '{id: sql(id),
     *  username: sql(username), type: view, title: 审核$(username)}'等。
     *  <br />
     *  获取待办详细时会将当前map构建在params参数中。
     *  <br />
     *  sql()包含的字段将搜索对应业务表的值插入。如businesstable为sys_user,sql(user_id)则会搜索用户表的user_id字段插入到map的id字段中
     *  <br />
     *  没有添加sql()的字段将以字符串形式直接渲染。
     *  包含$()字符的字段将最后渲染，只能使用构建map中存在的数据。注意$(fliedName)中fliedName为map中的key
     * 参数 跳转或者对话框弹出需要的参数信息
     */
    @TableField("COMPONENT_PARAMS")
    private String componentParams;

    @TableField("BUSINESS_TABLE")
    private String businessTable;

    @TableField("DIALOG_FIELD")
    private String dialogField;

    @TableField("CLIENT_TYPE")
    private String clientType;

    @TableField("IS_ASSOCIATED")
    private Boolean associated;

    @TableField("REMARKS")
    private String remarks;


}
