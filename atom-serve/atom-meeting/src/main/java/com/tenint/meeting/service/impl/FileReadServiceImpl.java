package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.FileRead;
import com.tenint.meeting.domain.bo.FileReadBo;
import com.tenint.meeting.mapper.FileReadMapper;
import com.tenint.meeting.service.IFileReadService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 文件阅读-发布后会议的阅读人员才会进此Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
@RequiredArgsConstructor
@Service
public class FileReadServiceImpl extends ServiceImpl<FileReadMapper, FileRead> implements IFileReadService {


    /**
     * 查询文件阅读-发布后会议的阅读人员才会进此列表
     */
    @Override
    public Page<FileRead> pageFileRead(FileReadBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FileRead> lqw = buildQueryWrapper(bo);
        Page<FileRead> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询文件阅读-发布后会议的阅读人员才会进此列表
     */
    @Override
    public List<FileRead> listFileRead(FileReadBo bo) {
        LambdaQueryWrapper<FileRead> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建文件阅读-发布后会议的阅读人员才会进此列表查询条件
     */
    private LambdaQueryWrapper<FileRead> buildQueryWrapper(FileReadBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FileRead> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getMeetingId() != null, FileRead::getMeetingId, bo.getMeetingId());
            lqw.eq(bo.getMeetingType() != null, FileRead::getMeetingType, bo.getMeetingType());
            lqw.eq(bo.getFileId() != null, FileRead::getFileId, bo.getFileId());
            lqw.eq(bo.getUserId() != null, FileRead::getUserId, bo.getUserId());
            lqw.eq(bo.getIsRead() != null, FileRead::getIsRead, bo.getIsRead());
        return lqw;
    }


    /**
     * 批量删除文件阅读-发布后会议的阅读人员才会进此
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public int countByMeetingId(Long meetingId) {
        LambdaQueryWrapper<FileRead> lqw = Wrappers.lambdaQuery();
        lqw.eq(FileRead::getMeetingId, meetingId)
                .eq(FileRead::getIsActive, UserConstants.ACTIVE);
        return (int) count(lqw);
    }

    @Override
    public List<FileRead> listFileReadsByMeetingId(Long meetingId) {
        LambdaQueryWrapper<FileRead> lqw = Wrappers.lambdaQuery();
        lqw.eq(FileRead::getMeetingId, meetingId).eq(FileRead::getIsActive, UserConstants.ACTIVE);

        return list(lqw);
    }

    @Override
    public List<FileRead> listFileReadsByMeetingIdAndUserIds(Long meetingId, List<Long> userIds) {
        LambdaQueryWrapper<FileRead> lqw = Wrappers.lambdaQuery();
        lqw.select(FileRead::getId)
                .eq(FileRead::getMeetingId, meetingId).in(FileRead::getUserId, userIds);

        return list(lqw);
    }

    @Override
    public void removeByMeetingId(Long meetingId) {
        LambdaQueryWrapper<FileRead> lqw = Wrappers.lambdaQuery();
        lqw.eq(FileRead::getMeetingId, meetingId);
        remove(lqw);
    }

}
