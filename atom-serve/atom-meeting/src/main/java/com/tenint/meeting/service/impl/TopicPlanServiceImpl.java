package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.utils.DateUtils;
import com.tenint.meeting.domain.TopicPlan;
import com.tenint.meeting.domain.vo.TopicPlanRowVo;
import com.tenint.meeting.domain.vo.TopicPlanVo;
import com.tenint.meeting.mapper.TopicPlanDetailMapper;
import com.tenint.meeting.mapper.TopicPlanMapper;
import com.tenint.meeting.service.ITopicPlanService;
import com.tenint.meeting.domain.bo.TopicPlanBo;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 议题计划时间Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@RequiredArgsConstructor
@Service
public class TopicPlanServiceImpl extends ServiceImpl<TopicPlanMapper, TopicPlan> implements ITopicPlanService {

    private final TopicPlanDetailMapper topicPlanDetailMapper;

    /**
     * 查询议题计划时间列表
     */
    @Override
    public Page<TopicPlan> pageTopicPlan(TopicPlanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopicPlan> lqw = buildQueryWrapper(bo);
        Page<TopicPlan> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题计划时间列表
     */
    @Override
    public List<TopicPlan> listTopicPlan(TopicPlanBo bo) {
        LambdaQueryWrapper<TopicPlan> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题计划时间列表查询条件
     */
    private LambdaQueryWrapper<TopicPlan> buildQueryWrapper(TopicPlanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopicPlan> lqw = Wrappers.lambdaQuery();
            lqw.eq(StringUtils.isNotBlank(bo.getTitle()), TopicPlan::getTitle, bo.getTitle());
        return lqw;
    }


    /**
     * 批量删除议题计划时间
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<TopicPlanRowVo> listByAvailable() {
        return topicPlanDetailMapper.selectListByAvailable(DateUtils.getNowDate());
    }

}
