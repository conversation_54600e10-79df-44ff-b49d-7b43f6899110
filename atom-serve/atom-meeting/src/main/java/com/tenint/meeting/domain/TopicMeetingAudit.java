package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 会议收集审核人员对象 t_topic_meeting_audit
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
@TableName("t_topic_meeting_audit")
public class TopicMeetingAudit extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 用户类型 1 部门领导 2 分管领导
    */
    private String userType;

   /**
    * 用户id
    */
    private Long userId;

   /**
    * 单人审批状态
    */
    private String status;

   /**
    * 备注
    */
    private String remark;

   /**
    * 关联的议题收集id
    */
    private Long topicMeetId;

   /**
    * 有效位
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
