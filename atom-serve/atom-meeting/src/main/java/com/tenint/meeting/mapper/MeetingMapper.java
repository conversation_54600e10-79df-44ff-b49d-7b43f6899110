package com.tenint.meeting.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.domain.Meeting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 会议信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface MeetingMapper extends BaseMapper<Meeting> {

}
