package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 会议收集审核人员视图对象 t_topic_meeting_audit
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
@ExcelIgnoreUnannotated
public class TopicMeetingAuditVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 议题收集id
     */
    @ExcelProperty(value = "议题收集id")
    private Long topicId;

    /**
     * 议题标题
     */
    @ExcelProperty(value = "议题标题")
    private String topicTitle;

    /**
     * 会议类型
     */
    @ExcelProperty(value = "会议类型")
    private String meetType;

    /**
     * 议题上传时间
     */
    @ExcelProperty(value = "议题上传时间")
    private Date createTime;

    /**
     * 议题审批状态
     */
    @ExcelProperty(value = "议题审批状态")
    private String status;

    /**
     * 上传单位
     */
    @ExcelProperty(value = "上传单位")
    private String creatorDept;

    /**
     * 汇报人姓名
     */
    @ExcelProperty(value = "汇报人姓名")
    private String reportUserName;

    /**
     * 关联的多个预会议
     */
    private List<TopicMeetingPlanVo> planList;

}

