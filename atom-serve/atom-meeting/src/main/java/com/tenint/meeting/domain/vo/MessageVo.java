package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import com.tenint.meeting.enums.MessageTypeEnum;
import lombok.Data;


/**
 * 消息推送主体视图对象 t_message
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Data
@ExcelIgnoreUnannotated
public class MessageVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 是否已读
     */
    @ExcelProperty(value = "是否已读")
    private String isRead;


    /**
     * 完成的
     */
    private Boolean completed;


    /**
     * 消息推送内容
     */
    @ExcelProperty(value = "消息推送内容")
    private String description;

    /**
     * @see MessageTypeEnum
     */
    @ExcelProperty
    private String module;

    private String type;

    private Long businessId;
}

