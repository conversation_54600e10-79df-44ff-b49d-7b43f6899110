package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.Meeting;
import com.tenint.meeting.domain.vo.JoinerMeetingVo;
import com.tenint.meeting.domain.vo.MeetingTreeNodeVo;
import com.tenint.meeting.domain.vo.MeetingVo;
import com.tenint.meeting.domain.bo.MeetingBo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Mapper
public interface MeetingConvert {

    MeetingConvert INSTANCE = Mappers.getMapper( MeetingConvert.class );


    @Mapping(target = "meetingBeginTime", source = "meetingBo", qualifiedByName = "toBeginDateTime")
    @Mapping(target = "meetingEndTime", source = "meetingBo", qualifiedByName = "toEndDateTime")
    Meeting convert(MeetingBo meetingBo);

    @Mappings({
            @Mapping(target = "meetingDate", source = "meetingBeginTime", qualifiedByName = "dateToLocalDate"),
            @Mapping(target = "meetingTime", ignore = true) // 暂时忽略，稍后在 @AfterMapping 中处理
    })
    MeetingVo convert(Meeting bean);

    List<MeetingVo> convertList(List<Meeting> list);

    Page<MeetingVo> convertPage(Page<Meeting> page);


    @Named("toBeginDateTime")
    default Date toBeginDateTime(MeetingBo meetingBo) {
        return toDateTime(meetingBo.getMeetingDate(), meetingBo.getMeetingTime(), true);
    }

    @Named("toEndDateTime")
    default Date toEndDateTime(MeetingBo meetingBo) {
        return toDateTime(meetingBo.getMeetingDate(), meetingBo.getMeetingTime(), false);
    }

    default Date toDateTime(LocalDate date, String[] time, boolean isStartTime) {
        if (date != null && time != null && time.length == 2) {
            LocalTime localTime = LocalTime.parse(isStartTime ? time[0] : time[1]);
            return Date.from(date.atTime(localTime).atZone(ZoneId.systemDefault()).toInstant());
        }
        return null;
    }

    @Named("dateToLocalDate")
    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    @AfterMapping
    default void setMeetingTime(Meeting meeting, @MappingTarget MeetingVo meetingVo) {
        if (meeting.getMeetingBeginTime() != null && meeting.getMeetingEndTime() != null) {
            LocalTime beginTime = LocalDateTime.ofInstant(meeting.getMeetingBeginTime().toInstant(), ZoneId.systemDefault()).toLocalTime();
            LocalTime endTime = LocalDateTime.ofInstant(meeting.getMeetingEndTime().toInstant(), ZoneId.systemDefault()).toLocalTime();

            // 假设 meetingTime 需要的格式是 "HH:mm:ss"
            meetingVo.setMeetingTime(new String[] {beginTime.toString(), endTime.toString()});
        }
    }

    List<MeetingTreeNodeVo> convertJoinerMeetingToTreeNode(List<JoinerMeetingVo> joinerMeetingVos);
}
