package com.tenint.meeting.manager;

import com.tenint.common.helper.LoginHelper;
import com.tenint.meeting.convert.MeetingUserFileConvert;
import com.tenint.meeting.domain.FileEntity;
import com.tenint.meeting.domain.MeetingUserFile;
import com.tenint.meeting.domain.vo.MeetingUserFileVo;
import com.tenint.meeting.service.IFileEntityService;
import com.tenint.meeting.service.IMeetingUserFileService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 会议用户文件信息综合Service层
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RequiredArgsConstructor
@Service
public class MeetingUserFileManager {

    private final IMeetingUserFileService meetingUserFileService;

    private final IFileEntityService fileEntityService;

    /**
     * 查询会议用户文件信息
     */
    public MeetingUserFileVo getVoById(Long id) {
        MeetingUserFile meetingUserFile = meetingUserFileService.getById(id);
        MeetingUserFileVo meetingUserFileVo = MeetingUserFileConvert.INSTANCE.convert(meetingUserFile);
        return meetingUserFileVo;
    }

    /**
     * 根据文件id查找关联人员
     * @param fileId
     * @return
     */
    public List<Long> getUserFileVoListByFileId(Long fileId) {
        List<MeetingUserFile> userFileList = meetingUserFileService.listUserFileByFileId(Collections.singletonList(fileId));
        List<Long> userList = userFileList.stream().map(MeetingUserFile::getUserId).toList();
        return userList;
    }

    /**
     * 校验并批量删除会议用户文件信息信息
     */
    public Boolean removeWithValidByIds(Collection<Long> fileIds) {
        return meetingUserFileService.removeWithValidByIds(fileIds, CollectionUtils.isNotEmpty(fileIds));
    }

    public void saveOrUpdateByBo(Long fileId, List<Long> userList) {
        meetingUserFileService.saveOrUpdateBatch(fileId, userList);
    }

    /**
     * 获取自己创建的该会议类型的关联人员
     * @param meetingType
     * @return
     */
    public Set<Long> listUserFileByMeetingType(Long meetingType) {

        // 1、根据会议类型 即parentid 找到所有关联的文件
        List<FileEntity> fileEntities = fileEntityService.listByParentId(meetingType, LoginHelper.getUserId());
        // 2、根据文件id 找到关联的人员
        List<Long> fileIds = fileEntities.stream().map(FileEntity::getId).toList();
        List<MeetingUserFile> userFileList = meetingUserFileService.listUserFileByFileId(fileIds);
        return userFileList.stream().map(MeetingUserFile::getUserId)
                .collect(Collectors.toSet());
    }

}
