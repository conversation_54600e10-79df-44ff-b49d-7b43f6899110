package com.tenint.meeting.enums;
import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务附件类型
 * <AUTHOR>
 */

@DictEnum(key = "TASK_FILE_TYPE",label = "任务附件类型")
@Getter
@AllArgsConstructor
public enum TaskFileTypeEnum implements KeyLabelEnum {

    ORIGIN("1", "任务附件"),

    QUOTE("2", "引用文件"),

    ;
    private final String key,label;
}
