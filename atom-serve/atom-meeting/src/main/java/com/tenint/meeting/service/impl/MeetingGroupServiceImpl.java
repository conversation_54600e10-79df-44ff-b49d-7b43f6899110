package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.meeting.domain.MeetingGroup;
import com.tenint.meeting.mapper.MeetingGroupMapper;
import com.tenint.meeting.service.IMeetingGroupService;
import com.tenint.meeting.domain.bo.MeetingGroupBo;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 用户组Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@RequiredArgsConstructor
@Service
public class MeetingGroupServiceImpl extends ServiceImpl<MeetingGroupMapper, MeetingGroup> implements IMeetingGroupService {


    /**
     * 查询用户组列表
     */
    @Override
    public Page<MeetingGroup> pageMeetingGroup(MeetingGroupBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MeetingGroup> lqw = buildQueryWrapper(bo);
        Page<MeetingGroup> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询用户组列表
     */
    @Override
    public List<MeetingGroup> listMeetingGroup(MeetingGroupBo bo) {
        LambdaQueryWrapper<MeetingGroup> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建用户组列表查询条件
     */
    private LambdaQueryWrapper<MeetingGroup> buildQueryWrapper(MeetingGroupBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeetingGroup> lqw = Wrappers.lambdaQuery();
            lqw.like(StringUtils.isNotBlank(bo.getName()), MeetingGroup::getName, bo.getName());
            lqw.eq(bo.getUserId() != null, MeetingGroup::getUserId, bo.getUserId());
        return lqw;
    }


    /**
     * 批量删除用户组
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
