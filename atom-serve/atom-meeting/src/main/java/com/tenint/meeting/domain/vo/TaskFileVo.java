package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.annotation.Translation;
import com.tenint.common.constant.TransConstant;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 督查督办附件视图对象 t_task_file
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@ExcelIgnoreUnannotated
public class TaskFileVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 督查督办ID
     */
    @ExcelProperty(value = "督查督办ID")
    private Long taskId;

    /**
     * 附件类型 1 任务附件 2 新建任务-引用文件
     */
    @ExcelProperty(value = "附件类型 1 任务附件 2 新建任务-引用文件")
    private String fileType;

    private Long ossId;

    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "ossId")
    private String url;

    /**
     * 附件名称
     */
    @ExcelProperty(value = "附件名称")
    private String name;


}

