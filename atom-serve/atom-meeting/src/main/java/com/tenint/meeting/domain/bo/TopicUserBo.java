package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 议题用户关联业务对象 t_topic_user
 *
 * <AUTHOR>
 * @date 2024-07-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicUserBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 目标名称
     */
    @NotBlank(message = "目标名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String typeName;


    /**
     * 目标编号
     */
    @NotNull(message = "目标编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long typeId;


    /**
     * 议题编号
     */
    @NotNull(message = "计划编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long planId;


    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;
}
