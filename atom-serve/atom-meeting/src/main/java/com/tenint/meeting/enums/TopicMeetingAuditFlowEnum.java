package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "TOPIC_MEET_AUDIT_FLOW_TYPE",label = "议题收集审核流程类型",style = true)
@Getter
@AllArgsConstructor
public enum TopicMeetingAuditFlowEnum implements KeyLabelStyleEnum {

    //todo 暂时不更新审核人表的状态
    CREATE("10", "创建", "info"),
    AUDIT("20", "审核", "info"),
    ;
//    EDIT_POINT("0", "编辑指定", "info"),
//    SUBMIT_POINT("1", "提交", "info"),
//    PASS_POINT("20", "通过", "success"),
//    RECALL_POINT("22", "退回", "danger"),
    ;

    private final String key, label, eleStyle;
}
