package com.tenint.meeting.domain.bo;

import com.tenint.common.constant.CommonConstant;
import com.tenint.common.core.domain.BaseEntity;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 消息推送主体业务对象 t_message
 *
 * <AUTHOR>
 * @date 2023-05-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MessageBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;


    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;


    /**
     * 是否已读
     */
    @NotBlank(message = "是否已读不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isRead;


    /**
     * 完成的
     */
    private Boolean completed;


    /**
     * 消息推送内容
     */
    @NotBlank(message = "消息推送内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String description;


    /**
     * 消息模块
     */
    @NotBlank(message = "模块不能为空", groups = {AddGroup.class, EditGroup.class})
    private String module;

    /**
     * 子类型
     */
    private String type;

    private Long businessId;


    public static MessageBo of(String type, String description) {
        MessageBo bo = new MessageBo();
        bo.setType(type);
        bo.setDescription(description);
        bo.setIsRead(CommonConstant.YesOrNo.No.value);
        return bo;
    }
}
