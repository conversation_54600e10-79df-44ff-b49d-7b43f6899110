package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.FileEntity;
import com.tenint.meeting.domain.MeetingFile;
import com.tenint.meeting.domain.vo.MeetingFileVo;
import com.tenint.meeting.domain.bo.MeetingFileBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MeetingFileConvert {

    MeetingFileConvert INSTANCE = Mappers.getMapper( MeetingFileConvert.class );


    MeetingFile convert(MeetingFileBo bean);

    MeetingFileVo convert(MeetingFile bean);

    List<MeetingFileVo> convertList(List<MeetingFile> list);

    List<MeetingFileVo> convertFileEntityList(List<FileEntity> list);

    Page<MeetingFileVo> convertPage(Page<MeetingFile> page);

}
