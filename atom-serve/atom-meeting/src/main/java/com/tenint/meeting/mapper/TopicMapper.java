package com.tenint.meeting.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.tenint.meeting.domain.Topic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tenint.meeting.domain.dto.TopicUploadDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 议题Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface TopicMapper extends BaseMapper<Topic> {

    /**
     * 获取当前开放收集时间并且关联当前用户的议题
     * @param wrapper
     * @return
     */
     List<Topic> getRelatedTopicList(@Param("ew") QueryWrapper<Topic> wrapper);

    /**
     * 分页查询所有关联有过操作记录的议题
     * @param page
     * @param wrapper
     * @return
     */
    Page<TopicUploadDTO> pageRelatedTopic(Page<Topic> page, @Param("ew") QueryWrapper<Topic> wrapper);
}
