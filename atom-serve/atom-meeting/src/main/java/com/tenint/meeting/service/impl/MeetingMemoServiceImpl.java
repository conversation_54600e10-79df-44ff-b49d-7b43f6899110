package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.MeetingMemo;
import com.tenint.meeting.domain.bo.MeetingMemoBo;
import com.tenint.meeting.mapper.MeetingMemoMapper;
import com.tenint.meeting.service.IMeetingMemoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 会议签名Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@RequiredArgsConstructor
@Service
public class MeetingMemoServiceImpl extends ServiceImpl<MeetingMemoMapper, MeetingMemo> implements IMeetingMemoService {


    /**
     * 查询会议签名列表
     */
    @Override
    public Page<MeetingMemo> pageMeetingMemo(MeetingMemoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MeetingMemo> lqw = buildQueryWrapper(bo);
        Page<MeetingMemo> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询会议签名列表
     */
    @Override
    public List<MeetingMemo> listMeetingMemo(MeetingMemoBo bo) {
        LambdaQueryWrapper<MeetingMemo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建会议签名列表查询条件
     */
    private LambdaQueryWrapper<MeetingMemo> buildQueryWrapper(MeetingMemoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeetingMemo> lqw = Wrappers.lambdaQuery();
            lqw.eq(StringUtils.isNotBlank(bo.getMemoWord()), MeetingMemo::getMemoWord, bo.getMemoWord());
            lqw.eq(bo.getMeetingId() != null, MeetingMemo::getMeetingId, bo.getMeetingId());
            lqw.eq(bo.getCreatorId() != null, MeetingMemo::getCreatorId,bo.getCreatorId());
            lqw.orderByDesc(MeetingMemo::getCreateTime);
        return lqw;
    }


    /**
     * 批量删除会议签名
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
