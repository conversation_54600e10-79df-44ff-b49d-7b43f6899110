package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 会议签名对象 t_meeting_user_sign
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
@TableName("t_meeting_user_sign")
public class MeetingUserSign extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 用户id
    */
    private Long userId;

   /**
    * 签名base64码
    */
    private String signPic;

   /**
    * 会议id
    */
    private Long meetingId;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
