package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题文件审核对象 t_topic_file_audit
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
@TableName("t_topic_file_audit")
public class TopicFileAudit extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    @TableId(value = "id")
    private Long id;

   /**
    * 是否有效
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;

   /**
    * 审核用户编号
    */
    private Long userId;

   /**
    * 审核状态
    */
    private String status;

   /**
    * 备注
    */
    private String remarks;

   /**
    * 主题编号
    */
    private Long topicId;

   /**
    * 文件编号
    */
    private Long fileId;


}
