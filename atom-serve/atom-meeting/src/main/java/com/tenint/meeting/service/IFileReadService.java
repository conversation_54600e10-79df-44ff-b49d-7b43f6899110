package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.FileRead;
import com.tenint.meeting.domain.bo.FileReadBo;

import java.util.Collection;
import java.util.List;
/**
 * 文件阅读-发布后会议的阅读人员才会进此Service接口
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
public interface IFileReadService extends IService<FileRead> {

    /**
     * 查询文件阅读-发布后会议的阅读人员才会进此列表
     */
    Page<FileRead> pageFileRead(FileReadBo bo, PageQuery pageQuery);

    /**
     * 查询文件阅读-发布后会议的阅读人员才会进此列表
     */
    List<FileRead> listFileRead(FileReadBo bo);

    /**
     * 校验并批量删除文件阅读-发布后会议的阅读人员才会进此信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);



    /**
     * 根据会议id查询关联参会人员记录的数量
     * @param meetingId
     * @return
     */
    int countByMeetingId(Long meetingId);

    /**
     * 根据会议id找到关联的有效阅读记录
     * @param meetingId
     * @return
     */
    List<FileRead> listFileReadsByMeetingId(Long meetingId);

    /**
     * 根据会议id和用户id找到关联的有效阅读记录
     * @param meetingId
     * @param userIds
     * @return
     */
    List<FileRead> listFileReadsByMeetingIdAndUserIds(Long meetingId, List<Long> userIds);

    /**
     * 根据会议id删除关联的所有阅读记录
     * @param meetingId
     */
    void removeByMeetingId(Long meetingId);
}
