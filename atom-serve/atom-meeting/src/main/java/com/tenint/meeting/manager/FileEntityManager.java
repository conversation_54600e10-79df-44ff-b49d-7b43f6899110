package com.tenint.meeting.manager;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysDictData;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StreamUtils;
import com.tenint.common.utils.TreeBuildUtils;
import com.tenint.common.utils.redis.OssDelayedUtils;
import com.tenint.meeting.convert.MeetingFileConvert;
import com.tenint.meeting.convert.TopicMeetingFileConvert;
import com.tenint.meeting.domain.FileEntity;
import com.tenint.meeting.domain.Meeting;
import com.tenint.meeting.domain.MeetingJoiner;
import com.tenint.meeting.domain.TopicMeetingLink;
import com.tenint.meeting.domain.bo.FileTopicUnionQueryBo;
import com.tenint.meeting.domain.bo.MeetingJoinerBo;
import com.tenint.meeting.domain.bo.TopicMeetingLinkBo;
import com.tenint.meeting.domain.vo.*;
import com.tenint.meeting.enums.*;
import com.tenint.meeting.service.*;
import com.tenint.meeting.domain.bo.FileEntityBo;
import com.tenint.meeting.convert.FileEntityConvert;

import com.tenint.system.domain.SysOss;
import com.tenint.system.mapper.SysOssMapper;
import com.tenint.system.service.ISysConfigService;
import com.tenint.system.service.ISysDictDataService;
import com.tenint.system.service.ISysUserService;
import graphql.com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tenint.common.utils.TreeBuildUtils.DEFAULT_CONFIG;

/**
 * 文件综合Service层
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@RequiredArgsConstructor
@Service
public class FileEntityManager {

    private final IFileEntityService fileEntityService;

    private final MeetingUserFileManager userFileManager;

    private final SysOssMapper ossMapper;

    private final IMeetingJoinerService meetingJoinerService;

    private final IMeetingService meetingService;

    private final ISysUserService sysUserService;

    private final IFileEntityService entityService;

    private final ITopicMeetingLinkService meetingLinkService;

    private final TopicMeetingFileManager topicFileManager;

    private final ISysDictDataService dictDataService;

    private final ISysConfigService sysConfigService;

    private final IFileTopicUnionService fileTopicUnionService;

    /**
     * 查询文件
     */
    public FileEntityVo getVoById(Long id) {
        FileEntity fileEntity = fileEntityService.getById(id);
        FileEntityVo fileEntityVo = FileEntityConvert.INSTANCE.convert(fileEntity);
        // 查询关联人员
        List<Long> userFileVos = userFileManager.getUserFileVoListByFileId(id);
        fileEntityVo.setUserList(userFileVos);
        return fileEntityVo;
    }

    /**
     * 查询文件列表
     */
    public TableDataInfo<FileEntityVo> pageFileEntityVo(FileEntityBo bo, PageQuery pageQuery) {
        bo.setEntityType(FileEntityType.FILE.getKey());
        Page<FileEntity> page = fileEntityService.pageFileEntity(bo, pageQuery);
        Page<FileEntityVo> pageVo = FileEntityConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询自己上传的文件列表
     */
    public TableDataInfo<FileEntityVo> pageSelfFileEntityVo(FileEntityBo bo, PageQuery pageQuery) {
        bo.setEntityType(FileEntityType.FILE.getKey());
        Page<FileEntity> page = fileEntityService.pageSelfFileEntity(bo, pageQuery);
        Page<FileEntityVo> pageVo = FileEntityConvert.INSTANCE.convertPage(page);
        Set<Long> meetTypes = page.getRecords().stream().map(FileEntity::getParentId).collect(Collectors.toSet());
        // 根据meetType查询会议id 目前是一对一的
        Map<Long, Long> meetingMap = meetingService.getMeetingMap(meetTypes);
        pageVo.getRecords().forEach(item -> item.setMeetingId(meetingMap.getOrDefault(item.getParentId(), 0L)));
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    public TableDataInfo<FileEntityVo> pageSelfFileEntityQueryVo(FileEntityBo bo, PageQuery pageQuery) {
        // 1、根据userId查询参会人员为自己的有效状态/已发布的会议meetType
        Set<Long> meetTypes = listSelfFolder(JoinUserType.ATTEND.getKey(), MeetingStatus.PUBLISH.getKey());
        // 2、根据meetType查询关联的文件
        bo.setParentIds(meetTypes.stream().toList());
        Page<FileEntity> page = fileEntityService.pageFileEntity(bo, pageQuery);
        Page<FileEntityVo> pageVo = FileEntityConvert.INSTANCE.convertPage(page);
        // 3、根据meetType查询会议id 目前是一对一的
        Map<Long, Long> meetingMap = meetingService.getMeetingMap(meetTypes);
        pageVo.getRecords().forEach(item -> item.setMeetingId(meetingMap.getOrDefault(item.getParentId(), 0L)));
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询文件列表
     */
    public List<FileEntityVo> listFileEntityVo(FileEntityBo bo) {
        List<FileEntity> list = fileEntityService.listFileEntity(bo);
        List<FileEntityVo> listVo = FileEntityConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增文件
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveByBo(FileEntityBo bo) {
        FileEntity fileEntity = FileEntityConvert.INSTANCE.convert(bo);
        updateAncestors(fileEntity);
        fileEntity.setOrgId(LoginHelper.getOrgId());
        // 上传文件编辑状态
        if (FileEntityType.FILE.getKey().equals(fileEntity.getEntityType())) {
            fileEntity.setFileStatus(FileStatus.EDIT.getKey());
            OssDelayedUtils.closeDelete(fileEntity.getOssId().toString());
        }
        boolean state = fileEntityService.save(fileEntity);
        // todo 添加根节点时同步字典项数据
        if (fileEntity.getParentId() == 0) {
            SysDictData sysDictData = new SysDictData();
            sysDictData.setDictLabel(fileEntity.getName());
            sysDictData.setDictValue(String.valueOf(fileEntity.getId()));
            sysDictData.setDictType("meeting_type");
            sysDictData.setStatus("0");
            dictDataService.saveDictData(sysDictData);
            // 刷新缓存
            sysConfigService.resetConfigCache();
        }
        userFileManager.saveOrUpdateByBo(fileEntity.getId(), bo.getUserList());
        return state ? fileEntity.getId() : null;
    }

    /**
     * 修改文件
     */
    @Transactional(rollbackFor = Exception.class)
    public Long updateByBo(FileEntityBo bo) {
        validateFileEntityExists(bo.getId());
        FileEntity fileEntity = FileEntityConvert.INSTANCE.convert(bo);
        updateAncestors(fileEntity);
        // 上传文件编辑状态
        if (FileEntityType.FILE.getKey().equals(fileEntity.getEntityType())) {
            fileEntity.setFileStatus(FileStatus.EDIT.getKey());
            OssDelayedUtils.closeDelete(fileEntity.getOssId().toString());
        }
        boolean state = fileEntityService.updateById(fileEntity);
        if (fileEntity.getParentId() == 0) {
            dictDataService.updateByDictValue(fileEntity.getName(), String.valueOf(fileEntity.getId()), "meeting_type");
        }
        userFileManager.saveOrUpdateByBo(fileEntity.getId(), bo.getUserList());
        return state ? fileEntity.getId() : null;
    }

    /**
     * 提交文件
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitFile(Long id) {
        validateFileEntityExists(id);
        FileEntity fileEntity = fileEntityService.getById(id);
        // 提交状态
        fileEntity.setFileStatus(FileStatus.SUBMIT.getKey());
        return fileEntityService.updateById(fileEntity);
    }


    /**
     * 校验并批量删除文件信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        userFileManager.removeWithValidByIds(ids);
        return fileEntityService.removeWithValidByIds(ids, isValid);
    }


    private void validateFileEntityExists(Long id) {
        if (fileEntityService.getById(id) == null) {
            throw new ServiceException("文件数据不存在");
        }
    }


    // 更新祖先节点信息
    public void updateAncestors(FileEntity fileEntity) {
        Long parentId = fileEntity.getParentId();
        if (parentId == null || parentId == 0L) {
            fileEntity.setAncestors("0");
            return;
        }
        FileEntity entity = fileEntityService.getById(parentId);
        String ancestors = entity.getAncestors() + "/" + parentId;
        fileEntity.setAncestors(ancestors);
    }

    public List<Tree<Long>> folderTree() {
        List<FileEntity> list = fileEntityService.listByEntityType(FileEntityType.FOLDER.getKey());
        Long root = 0L;
        return TreeBuildUtils.build(list, root, DEFAULT_CONFIG, (entity, tree) -> {
            tree.setId(entity.getId());
            tree.setParentId(entity.getParentId());
            tree.setName(entity.getName());
            tree.setWeight(entity.getFileOrder());
        });
    }

    /**
     * 获取全部文件夹
     *
     * @return
     */
    public List<MeetingFolderVo> listAllFolder() {
        List<FileEntity> list = fileEntityService.listByEntityType(FileEntityType.FOLDER.getKey());
        return FileEntityConvert.INSTANCE.convertFolder(list);
    }

    /**
     * 关联当前会议待审核的所有附件
     *
     * @param meetingType
     * @return
     */
    public List<MeetingFileVo> listAllSubmitFile(Long meetingType) {
        List<FileEntity> fileEntities = getFileEntities(meetingType, FileStatus.SUBMIT.getKey());
        return handleFileEntities(fileEntities);
    }

    public List<MeetingFileVo> listAllPassFile(Long meetingType) {
        List<FileEntity> fileEntities = getFileEntities(meetingType, FileStatus.PASS.getKey());
        return handleFileEntities(fileEntities);
    }

    /**
     * 关联当前会议的资料（不含议题）
     *
     * @param meetingId 会议id
     * @return 结果
     */
    public List<MeetingFileVo> listAllMeetingFile(Long meetingId) {
        FileEntityBo entityBo = new FileEntityBo();
        entityBo.setEntityType(FileEntityType.FILE.getKey());
        entityBo.setMeetingId(meetingId);
        List<FileEntity> fileEntities = fileEntityService.listFileEntity(entityBo);
        return handleFileEntities(fileEntities);
    }

    private List<FileEntity> getFileEntities(Long meetingType, String fileStatus) {
        FileEntityBo entityBo = new FileEntityBo();
        entityBo.setEntityType(FileEntityType.FILE.getKey());
        entityBo.setParentId(meetingType);
        entityBo.setFileStatus(fileStatus);
        return fileEntityService.listFileEntity(entityBo);
    }

    private List<MeetingFileVo> handleFileEntities(List<FileEntity> fileEntities) {
        List<Long> userIds = StreamUtils.toList(fileEntities, FileEntity::getCreatorId);
        Map<Long, SysUser> userMap = sysUserService.getMapByUserIds(userIds);

        List<Long> ossIds = StreamUtils.toList(fileEntities, FileEntity::getOssId);
        if (ossIds.isEmpty()) {
            return MeetingFileConvert.INSTANCE.convertFileEntityList(fileEntities);
        }
        List<SysOss> sysOsses = ossMapper.selectBatchIds(ossIds);
        Map<Long, SysOss> ossMap = StreamUtils.toMap(sysOsses, SysOss::getId, e -> e);
        List<MeetingFileVo> list = MeetingFileConvert.INSTANCE.convertFileEntityList(fileEntities);
        list.forEach(file -> {
            SysOss sysOss = ossMap.get(file.getOssId());
            if (sysOss != null) {
                file.setUrl(sysOss.getUrl());
                file.setCreateTime(sysOss.getCreateTime());
            }
            SysUser user = userMap.get(file.getCreatorId());
            if (user != null) {
                file.setCreateName(user.getNickName());
            }
        });
        return list;
    }

    /**
     * 从会议信息批量新增/更新附件
     *
     * @param meetingType
     * @param fileEntityBos
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateFileEntity(Long meetingType, List<FileEntityBo> fileEntityBos) {

        List<FileEntity> fileEntities = FileEntityConvert.INSTANCE.convertBoList(fileEntityBos);

        List<FileEntity> updateFiles = fileEntities.stream().filter(item -> !Objects.isNull(item.getId())).toList();
        List<FileEntity> newFiles = fileEntities.stream().filter(item -> Objects.isNull(item.getId())).toList();
        newFiles.forEach(item -> {
            item.setParentId(meetingType);
            item.setEntityType(FileEntityType.FILE.getKey());
            item.setOrgId(LoginHelper.getOrgId());
            item.setFileStatus(FileStatus.PASS.getKey());
        });

        List<FileEntity> oldEntities = getFileEntities(meetingType, FileStatus.PASS.getKey());
        List<Long> updateIds = StreamUtils.toList(updateFiles, FileEntity::getId);
        List<FileEntity> removeFiles = oldEntities.stream().filter(item -> !updateIds.contains(item.getId())).toList();

        fileEntityService.saveBatch(newFiles);
        List<String> ossIds = newFiles.stream().map(FileEntity::getOssId).map(String::valueOf).toList();
        OssDelayedUtils.closeDelete(ossIds);

        fileEntityService.updateBatchById(updateFiles);
        fileEntityService.removeBatchByIds(removeFiles);
    }


    /**
     * 这里的folder类型的数据name为会议类型
     *
     * @return
     */
    public Map<Long, String> getMeetTypeMap() {
        FileEntityBo entityBo = new FileEntityBo();
        entityBo.setEntityType(FileEntityType.FOLDER.getKey());

        List<FileEntity> folders = fileEntityService.listFileEntity(entityBo);
        Map<Long, String> map = folders.stream().collect(Collectors.toMap(FileEntity::getId, FileEntity::getName, (a, b) -> a));

        Map<Long, String> nameMap = new HashMap<>();
        folders.forEach(item -> {
            String ancestors = item.getAncestors();
            if (ancestors.contains("/")) {
                ancestors = ancestors.substring(ancestors.indexOf("/") + 1);
                List<String> parents = Lists.newArrayList(ancestors.split("/"));
                parents.add(String.valueOf(item.getId()));

                StringBuilder sb = new StringBuilder();
                parents.forEach(parent -> {
                    String name = map.get(Long.valueOf(parent));
                    sb.append(name).append("/");
                });

                nameMap.put(item.getId(), sb.deleteCharAt(sb.length() - 1).toString());
            } else {
                nameMap.put(item.getId(), item.getName());
            }
        });

        return nameMap;
    }

    /**
     * 查询到关联自己的资源目录
     *
     * @return
     */
    public Set<Long> listSelfFolder(String userType, String meetStatus) {

        // 1、查询参会人员表里userId为本人的会议id
        MeetingJoinerBo meetingJoinerBo = new MeetingJoinerBo();
        meetingJoinerBo.setUserId(LoginHelper.getUserId());
        meetingJoinerBo.setUserType(userType);
        List<MeetingJoiner> joiners = meetingJoinerService.listMeetingJoiner(meetingJoinerBo);

        if (CollectionUtils.isEmpty(joiners)) {
            return Collections.emptySet();
        }
        Set<Long> meetingIds = joiners.stream().map(MeetingJoiner::getMeetingId).collect(Collectors.toSet());
        // 2、根据会议id查询会议表里的会议类型 去重后返回
        List<Meeting> meetings = meetingService.listMeetingByIds(meetingIds, meetStatus, JoinUserType.JOIN.getKey());
        if (CollectionUtils.isEmpty(meetings)) {
            return Collections.emptySet();
        }
        Set<Long> meetingTypes = meetings.stream().map(Meeting::getMeetingType).collect(Collectors.toSet());

        return meetingTypes;
    }

    /**
     * 审核文件
     *
     * @param id
     * @param status
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditFile(Long id, String status) {

        FileEntity fileEntity = fileEntityService.getById(id);
        fileEntity.setFileStatus(status);
        return fileEntityService.updateById(fileEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void fileAdd(UploadFileVo uploadFileVo) {
        if (uploadFileVo.getId() == null) {
            throw new ServiceException("文件夹不能为空");
        }
        if (uploadFileVo.getUid() != null) {
            fileEntityService.removeById(uploadFileVo.getUid());
        }
        FileEntity entity = fileEntityService.getById(uploadFileVo.getId());
        if (!(FileType.TOPIC.getKey()).equals(uploadFileVo.getFileType())) {
            uploadFileVo.setMeetingId(entity.getMeetingId());
            entityService.fileInsertBatch(entity, uploadFileVo);
        } else { // 添加议题
            entity.setFileType(FileType.TOPIC.getKey());
            fileEntityService.updateById(entity); // 文件夹类型为topic
            TopicMeetingLink link = meetingLinkService.insertSpecialTopicMeetingLink(uploadFileVo.getId(), entity.getMeetingId(), uploadFileVo.getTopicTitle(), uploadFileVo.getReportUserId(), uploadFileVo.getFileOrder());
            topicFileManager.saveOrUpdateTopicFile(link.getId(), uploadFileVo.getMainFile(), uploadFileVo.getTopicFileList());

        }
    }

    public List<FileEntityVo> listFileAndTopicList(FileEntityBo bo) {
        bo.setEntityType(FileEntityType.FILE.getKey());
        List<FileEntity> entityList = entityService.listFileEntity(bo);
        // 普通附件
        List<FileEntityVo> entityVos = FileEntityConvert.INSTANCE.convertList(entityList);
        List<FileEntityVo> addList = new ArrayList<>();
        // 尝试获得 topic_meeting_link 关联的议题数据
        if (ObjectUtil.isNotEmpty(bo.getParentId())) {
            FileEntity entity = entityService.getById(bo.getParentId());
            // 关联会议的目录
            if (ObjectUtil.isNotEmpty(entity.getMeetingId())) {
                TopicMeetingLinkBo linkBo = new TopicMeetingLinkBo();
                linkBo.setMeetingId(entity.getMeetingId());
                linkBo.setTopicTitle(bo.getName());
                linkBo.setDateRange(bo.getDateRange());
                List<TopicMeetingLink> topicMeetingLinks = meetingLinkService.listTopicMeetingLink(linkBo);
                topicMeetingLinks.forEach(item -> {
                    FileEntityVo fileEntityVo = new FileEntityVo();
                    // 有预会议 id 说明来自议题收集
                    if (item.getPlanId() != null) {
                        fileEntityVo.setId(item.getTopicId());
                        fileEntityVo.setFileStatus(UserConstants.NORMAL_TOPIC);
                    } else {
                        // 否则为会议资料直接上传的特殊议题
                        fileEntityVo.setId(item.getId());
                        fileEntityVo.setFileStatus(UserConstants.SPECIAL_TOPIC);
                    }
                    fileEntityVo.setName(item.getTopicTitle());
                    fileEntityVo.setCreateTime(item.getCreateTime());
                    fileEntityVo.setFileOrder(item.getTopicOrder());
                    fileEntityVo.setMeetingId(item.getMeetingId());
                    addList.add(fileEntityVo);
                });
                // 不关联会议的目录 只有可能存在特殊议题
            } else if (FileType.TOPIC.getKey().equals(entity.getFileType())) {
                TopicMeetingLinkBo linkBo = new TopicMeetingLinkBo();
                linkBo.setTopicId(entity.getId());
                linkBo.setTopicTitle(bo.getName());
                linkBo.setDateRange(bo.getDateRange());
                List<TopicMeetingLink> topicMeetingLinks = meetingLinkService.listTopicMeetingLink(linkBo);
                topicMeetingLinks.forEach(item -> {
                    FileEntityVo fileEntityVo = new FileEntityVo();
                    fileEntityVo.setId(item.getId());
                    fileEntityVo.setName(item.getTopicTitle());
                    fileEntityVo.setCreateTime(item.getCreateTime());
                    fileEntityVo.setFileStatus(UserConstants.SPECIAL_TOPIC);
                    fileEntityVo.setFileOrder(item.getTopicOrder());
                    fileEntityVo.setMeetingId(item.getMeetingId());
                    addList.add(fileEntityVo);
                });
            }
            addList.sort(Comparator.comparingLong(FileEntityVo::getFileOrder));
            entityVos.addAll(0, addList);
        }
        return entityVos;
    }

    public UploadFileVo getAddFile(Long id, Long linkId) {
        UploadFileVo uploadFileVo = new UploadFileVo();
        FileEntity entity = fileEntityService.getById(id);
        if (!FileType.TOPIC.getKey().equals(entity.getFileType())) {
            FileEntityBo fileList = FileEntityConvert.INSTANCE.convertBo(entity);
            uploadFileVo.setFileList(Stream.of(fileList).collect(Collectors.toList()));
            uploadFileVo.setId(entity.getParentId());
            uploadFileVo.setFileType(entity.getFileType());
            uploadFileVo.setMeetingId(entity.getMeetingId());
            uploadFileVo.setUid(entity.getId());
        } else {
            TopicMeetingLink link = meetingLinkService.getById(linkId);
            Map<String, List<TopicMeetingFileVo>> fileMap = topicFileManager.collectFileMapByTopicId(linkId);
            uploadFileVo.setMainFile(TopicMeetingFileConvert.INSTANCE.convertBoListBo(fileMap.get(TopicFileTypeEnum.MAIN_FILE.getKey())));
            uploadFileVo.setTopicFileList(TopicMeetingFileConvert.INSTANCE.convertBoListBo(fileMap.get(TopicFileTypeEnum.TOPIC_FILE.getKey())));
            uploadFileVo.setFileType(entity.getFileType());
            uploadFileVo.setMeetingId(entity.getMeetingId());
            uploadFileVo.setReportUserId(link.getReportUserId());
            uploadFileVo.setUid(linkId);
            uploadFileVo.setId(entity.getId());
        }

        return uploadFileVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSpecialTopic(UploadFileVo uploadFileVo) {
        Long linkId = uploadFileVo.getUid();
        // 更新特殊议题
        TopicMeetingLink link = meetingLinkService.updateSpecialTopicMeetingLink(linkId, uploadFileVo.getId(), uploadFileVo.getMeetingId(), uploadFileVo.getTopicTitle(), uploadFileVo.getReportUserId(), uploadFileVo.getFileOrder());
        // 更新议题附件
        topicFileManager.saveOrUpdateTopicFile(link.getId(), uploadFileVo.getMainFile(), uploadFileVo.getTopicFileList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void delSpecialTopic(Long[] ids) {
        for (Long id : ids) {
            // 先删除特殊议题
            meetingLinkService.removeById(id);
            // 删除议题附件
            topicFileManager.deleteByTopicId(id);
        }
    }

    public void updateOrder(List<Long> ids, String type) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("id不能为空！");
        }
        if (UserConstants.NORMAL_TOPIC.equals(type)) {
            List<TopicMeetingLink> meetingLinks = meetingLinkService.lambdaQuery().in(TopicMeetingLink::getTopicId, ids).list();
            meetingLinks.forEach(item -> {
                item.setTopicOrder((long) (ids.indexOf(item.getTopicId()) + 1));
            });
            meetingLinkService.updateBatchById(meetingLinks);
        } else if (UserConstants.SPECIAL_TOPIC.equals(type)) {
            List<TopicMeetingLink> meetingLinks = meetingLinkService.lambdaQuery().in(TopicMeetingLink::getId, ids).list();
            meetingLinks.forEach(item -> {
                item.setTopicOrder((long) (ids.indexOf(item.getId()) + 1));
            });
            meetingLinkService.updateBatchById(meetingLinks);
        } else {
            List<FileEntity> entities = fileEntityService.lambdaQuery().in(FileEntity::getId, ids).list();
            entities.forEach(item -> {
                item.setFileOrder((long) (ids.indexOf(item.getId()) + 1));
            });
            fileEntityService.updateBatchById(entities);
        }
    }

    /**
     * 分页查询会议资料议题列表
     *
     * @param bo        条件
     * @param pageQuery 分页
     * @return 结果
     */
    public TableDataInfo<FileTopicUnionVo> pageFileTopicUnionVo(FileTopicUnionQueryBo bo, PageQuery pageQuery) {
        Page<FileTopicUnionVo> voPage = fileTopicUnionService.selectFileTopicUnionVoPage(bo, pageQuery);
        return TableDataInfo.build(voPage);
    }
}
