package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 议题附件
 * <AUTHOR>
 * @date 2024/07/16
 */

@Data
@TableName("t_topic_file")
public class TopicFile {

    private Long id;

    /**
     * 议题 id
     */
    private Long topicId;

    /**
     * oss 附件Id
     */
    private Long ossId;

    /**
     * 议题附件名称
     */
    private String name;

    /**
     * 议题文件状态
     */
    private String status;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 是否活动的
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 议题类型id
     */
    private Long planTypeId;

    /**
     * 议题类型名称
     */
    private String planTypeName;

}
