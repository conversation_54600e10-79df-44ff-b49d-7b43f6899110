package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.MeetingJoiner;
import com.tenint.meeting.domain.bo.MeetingJoinerBo;
import com.tenint.meeting.domain.bo.MeetingUserSignBo;
import com.tenint.meeting.domain.vo.JoinerMeetingVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 参会人员Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IMeetingJoinerService extends IService<MeetingJoiner> {

    /**
     * 查询参会人员列表
     */
    Page<MeetingJoiner> pageMeetingJoiner(MeetingJoinerBo bo, PageQuery pageQuery);

    /**
     * 查询参会人员列表
     */
    List<MeetingJoiner> listMeetingJoiner(MeetingJoinerBo bo);

    /**
     * 校验并批量删除参会人员信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 保存关联人员信息
     * @param meetingId 会议编号
     */
    void saveBatchByMeetingId(Long meetingId, Map<String ,List<Long>> totalMap);

    /**
     * 根据会议id删除关联人员人员信息列表 by meetingId
     * @param meetingId
     */
    void removeJoinerByMeetingId(List<Long> meetingId);

    /**
     * 根据人员类型分组参会列席人员
     *
     * @param meetingId 会议id
     * @return 结果
     */
    Map<String, List<MeetingJoiner>> groupMeetingJoinerByType(Long meetingId);

//    /**
//     * 根据关联人id和人员类型 找到关联的
//     * @param userId
//     * @param userType
//     * @return
//     */
//    List<Long> listMeetingIdByUserIdWithType(Long userId, String userType);

    /**
     * 根据用户编号和会议状态查询会议信息列表
     * @param userId 用户编号
     * @param status  会议状态
     * @return
     */
    List<JoinerMeetingVo> listMeetingByUserIdAndStatusAndBeginTime(Long userId, List<String> status, String beginTime);

    /**
     * 签到，改变状态
     */
    void changeAttendanceStatus(MeetingUserSignBo bo);

    /**
     * 根据用户id查询会议id
     */
    List<Long> selectByUserId(Long userId);

    Long getCountNum(Long meetingId,String type);
}
