package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 文件阅读-发布后会议的阅读人员才会进此视图对象 t_file_read
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
@Data
@ExcelIgnoreUnannotated
public class FileReadVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联会议id
     */
    @ExcelProperty(value = "关联会议id")
    private Long meetingId;

    /**
     * 会议资源目录
     */
    @ExcelProperty(value = "会议资源目录")
    private Long meetingType;

    /**
     * 附件id
     */
    @ExcelProperty(value = "附件id")
    private Long fileId;

    /**
     * 阅读人员id
     */
    @ExcelProperty(value = "阅读人员id")
    private Long userId;

    /**
     * 阅读次数
     */
    @ExcelProperty(value = "阅读次数")
    private Long isRead;


}

