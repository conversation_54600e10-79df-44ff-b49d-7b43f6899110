package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 文件视图对象 t_file_entity
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@ExcelIgnoreUnannotated
public class FileEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 父节点
     */
    @ExcelProperty(value = "父节点")
    private Long parentId;

    /**
     *  文件/文件夹
     */
    @ExcelProperty(value = " 文件/文件夹")
    private String entityType;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 祖级列表
     */
    @ExcelProperty(value = "祖级列表")
    private String ancestors;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 文件标记, 什么重点文件啥的
     */
    @ExcelProperty(value = "文件标记, 什么重点文件啥的")
    private String flag;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 管理的文件信息
     */
    @ExcelProperty(value = "管理的文件信息")
    private Long ossId;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long fileOrder;

    /**
     * 文件类型文件类型（如.txt, .jpg等）。对于目录，此字段可以为NULL
     */
    @ExcelProperty(value = "文件类型文件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=.txt,,.=jpg等")
    private String fileType;


    private Date createTime;

    /**
     * 文件状态 0 提交 2通过 9拒绝
     */
    private String fileStatus;

    /**
     * 关联人员
     */
    private List<Long> userList;

    /**
     * 会议id
     */
    private Long meetingId;
}

