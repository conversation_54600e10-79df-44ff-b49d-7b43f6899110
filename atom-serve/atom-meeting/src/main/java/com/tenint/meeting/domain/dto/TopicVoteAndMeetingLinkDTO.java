package com.tenint.meeting.domain.dto;

import com.tenint.meeting.domain.TopicMeetingLink;
import com.tenint.meeting.domain.TopicVote;
import com.tenint.meeting.domain.vo.TopicVoteVo;
import lombok.Data;
import org.bouncycastle.est.LimitedSource;

import java.util.List;

/**
 * @author: myz
 * @date: 2025/01/14
 */
@Data
public class TopicVoteAndMeetingLinkDTO {

    private List<TopicVoteVo> votes;

    private TopicMeetingLink link;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 会议标题
     */
    private String meetingTitle;

    /**
     * 会议类型
     */
    private Long meetingType;

    /**
     * 预会议id
     */
    private Long planId;

    /**
     * 预会议名称
     */
    private String planTitle;
}
