package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicMeetingFile;
import com.tenint.meeting.domain.bo.TopicMeetingFileBo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 议题收集附件Service接口
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface ITopicMeetingFileService extends IService<TopicMeetingFile> {

    /**
     * 查询议题收集附件列表
     */
    Page<TopicMeetingFile> pageTopicMeetingFile(TopicMeetingFileBo bo, PageQuery pageQuery);

    /**
     * 查询议题收集附件列表
     */
    List<TopicMeetingFile> listTopicMeetingFile(TopicMeetingFileBo bo);

    /**
     * 校验并批量删除议题收集附件信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据议题id查询关联的文件
     * @param topicId
     * @return
     */
    Map<String, List<TopicMeetingFile>> collectFileMapByTopicId(Long topicId);

    /**
     * 新增/更新议题关联的附件
     * @param topicId
     * @param fileList
     */
    Boolean saveOrUpdateTopicFile(Long topicId, List<TopicMeetingFile> fileList);

    /**
     * 根据议题id删除关联的附件
     * @param topicIds
     */
    void removeTopicFileByTopicId(List<Long> topicIds);
}
