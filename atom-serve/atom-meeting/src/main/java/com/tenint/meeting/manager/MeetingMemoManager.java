package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.meeting.convert.MeetingMemoConvert;
import com.tenint.meeting.domain.MeetingMemo;
import com.tenint.meeting.domain.bo.MeetingMemoBo;
import com.tenint.meeting.domain.vo.MeetingMemoVo;
import com.tenint.meeting.service.IMeetingMemoService;
import com.tenint.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;
import java.util.Map;

/**
 * 会议签名综合Service层
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@RequiredArgsConstructor
@Service
public class MeetingMemoManager {

    private final IMeetingMemoService meetingMemoService;

    private final ISysUserService sysUserService;

    /**
     * 查询会议签名
     */
    public MeetingMemoVo getVoById(Long id) {
        MeetingMemo meetingMemo = meetingMemoService.getById(id);
        MeetingMemoVo meetingMemoVo = MeetingMemoConvert.INSTANCE.convert(meetingMemo);
        return meetingMemoVo;
    }


    /**
     * 查询会议签名列表
     */
    public TableDataInfo<MeetingMemoVo> pageMeetingMemoVo(MeetingMemoBo bo, PageQuery pageQuery) {
        Page<MeetingMemo> page = meetingMemoService.pageMeetingMemo(bo, pageQuery);
        Page<MeetingMemoVo> pageVo = MeetingMemoConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询会议签名列表
     */
    public List<MeetingMemoVo> listMeetingMemoVo(MeetingMemoBo bo) {
        List<MeetingMemo> list = meetingMemoService.listMeetingMemo(bo);
        List<MeetingMemoVo> listVo = MeetingMemoConvert.INSTANCE.convertList(list);
        List<Long> uids = listVo.stream().map(MeetingMemoVo::getCreatorId).distinct().toList();
        Map<Long, SysUser> userMap = sysUserService.getMapByUserIds(uids);
        listVo.forEach(item -> {
            item.setUserName(userMap.get(item.getCreatorId()).getNickName());
        });
        return listVo;
    }

    /**
     * 新增会议签名
     */
    public Boolean saveByBo(MeetingMemoBo bo) {
        MeetingMemo meetingMemo = MeetingMemoConvert.INSTANCE.convert(bo);
        return meetingMemoService.save(meetingMemo);
    }

    /**
     * 修改会议签名
     */
    public Boolean updateByBo(MeetingMemoBo bo) {
        validateMeetingMemoExists(bo.getId());
        MeetingMemo meetingMemo = MeetingMemoConvert.INSTANCE.convert(bo);
        return meetingMemoService.updateById(meetingMemo);
    }


    /**
     * 校验并批量删除会议签名信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return meetingMemoService.removeWithValidByIds(ids, isValid);
    }


    private void validateMeetingMemoExists(Long id) {
        if (meetingMemoService.getById(id) == null) {
            throw new ServiceException("会议签名数据不存在");
        }
    }

}
