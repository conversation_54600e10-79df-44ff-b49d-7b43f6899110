package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.meeting.domain.TopicFile;
import com.tenint.meeting.mapper.TopicFileMapper;
import com.tenint.meeting.service.ITopicFileService;
import com.tenint.meeting.domain.bo.TopicFileBo;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 议题附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@RequiredArgsConstructor
@Service
public class TopicFileServiceImpl extends ServiceImpl<TopicFileMapper, TopicFile> implements ITopicFileService {


    /**
     * 查询议题附件列表
     */
    @Override
    public Page<TopicFile> pageTopicFile(TopicFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopicFile> lqw = buildQueryWrapper(bo);
        Page<TopicFile> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题附件列表
     */
    @Override
    public List<TopicFile> listTopicFile(TopicFileBo bo) {
        LambdaQueryWrapper<TopicFile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题附件列表查询条件
     */
    private LambdaQueryWrapper<TopicFile> buildQueryWrapper(TopicFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopicFile> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getTopicId() != null, TopicFile::getTopicId, bo.getTopicId());
            lqw.eq(bo.getOssId() != null, TopicFile::getOssId, bo.getOssId());
            lqw.like(StringUtils.isNotBlank(bo.getName()), TopicFile::getName, bo.getName());
            lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TopicFile::getStatus, bo.getStatus());
            lqw.eq(bo.getUploadTime() != null, TopicFile::getUploadTime, bo.getUploadTime());
        return lqw;
    }


    /**
     * 批量删除议题附件
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
