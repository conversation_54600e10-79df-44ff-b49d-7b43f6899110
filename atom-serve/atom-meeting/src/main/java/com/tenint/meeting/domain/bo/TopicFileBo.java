package com.tenint.meeting.domain.bo;

import com.tenint.common.core.domain.BaseEntity;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 议题附件业务对象 t_topic_file
 *
 * <AUTHOR>
 * @date 2024-07-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicFileBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 主题编号
     */
    @NotNull(message = "主题编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long topicId;


    /**
     * oss附件编号
     */
    @NotNull(message = "oss附件编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long ossId;


    /**
     * 附件名称
     */
    @NotBlank(message = "附件名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String name;


    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String status;


    /**
     * 上传时间
     */
    private Date uploadTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 用户id
     */
    private Long userId;
}
