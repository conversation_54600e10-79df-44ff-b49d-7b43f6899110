package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serial;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.core.domain.BaseEntity;

/**
 * 督查督办主对象 t_task
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@TableName("t_task")
public class Task extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 任务类型 1来自会议 2新建任务
     */
    private String taskType;

    /**
     * 督办事项名称
     */
    private String taskTitle;

    /**
     * 主责部门id
     */
    private Long deptId;

    /**
     * 主责部门名称
     */
    private String deptName;

    /**
     * 分管领导id
     */
    private Long majorUserId;

    /**
     * 部门负责人id
     */
    private Long userId;

    /**
     * 协助部室id
     */
    private Long coDeptId;

    /**
     * 协助部室名称
     */
    private String coDeptName;

    /**
     * 协助机构code
     */
    private String coOrgCode;

    /**
     * 协助分管领导id
     */
    private Long coMajorUserId;

    /**
     * 协助部门负责人id
     */
    private Long coUserId;

    /**
     * 办结时间
     */
    private Date completeTime;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 机构code
     */
    private String orgCode;

    /**
     * 立项时间
     */
    private Date approvalTime;

    /**
     * 立项依据
     */
    private String taskGist;

    /**
     * 分管领导补充意见时效（天）
     */
    private Integer majorFillExpDays;

    /**
     * 关联的部门领导ids
     */
    private String otherMajorIds;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * $column.columnComment
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
