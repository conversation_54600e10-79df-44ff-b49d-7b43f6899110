package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题用户关联对象 t_topic_user
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@TableName("t_topic_user")
public class TopicUser extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    @TableId(value = "id")
    private Long id;

   /**
    * 目标名称
    */
    private String typeName;

   /**
    * 目标编号
    */
    private Long typeId;

   /**
    * 议题编号
    */
    private Long planId;

    /**
     * 用户id
     */
    private Long userId;

   /**
    * 是否有效
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
