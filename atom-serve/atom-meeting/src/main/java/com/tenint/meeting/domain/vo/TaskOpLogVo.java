package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;


/**
 * 督查督办操作流程日志视图对象 t_task_op_log
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
public class TaskOpLogVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 督办主表id
     */
    @ExcelProperty(value = "督办主表id")
    private Long taskId;

    /**
     * 操作人id
     */
    @ExcelProperty(value = "操作人id")
    private Long userId;

    /**
     * 操作人姓名
     */
    @ExcelProperty(value = "操作人姓名")
    private String userName;

    /**
     * 操作情况
     */
    @ExcelProperty(value = "操作情况")
    private String opName;

    /**
     * 操作类型
     */
    private String opType;

    /**
     * 创建时间
     */
    private Date createTime;


}

