package com.tenint.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.helper.LoginHelper;
import com.tenint.meeting.domain.Meeting;
import com.tenint.meeting.enums.JoinUserType;
import com.tenint.meeting.enums.MeetingStatus;
import com.tenint.meeting.mapper.MeetingMapper;
import com.tenint.meeting.service.IMeetingJoinerService;
import com.tenint.meeting.service.IMeetingServeService;
import com.tenint.meeting.service.IMeetingService;
import com.tenint.meeting.domain.bo.MeetingBo;

import java.util.*;
import java.util.stream.Collectors;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 会议信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@RequiredArgsConstructor
@Service
public class MeetingServiceImpl extends ServiceImpl<MeetingMapper, Meeting> implements IMeetingService {

    private final IMeetingJoinerService meetingJoinerService;

    private final IMeetingServeService meetingServeService;

    /**
     * 查询会议信息列表
     */
    @Override
    public Page<Meeting> pageMeeting(MeetingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Meeting> lqw = buildQueryWrapper(bo);
        Page<Meeting> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }


    @Override
    public Page<Meeting> pageMyMeeting(MeetingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Meeting> lqw = buildQueryWrapper(bo);
        // 非管理员
        if (!LoginHelper.isAdmin()) {
            Long currentUserId = LoginHelper.getUserId();
            // 追加我的会议查询条件
            appendMyMeetingCondition(lqw, currentUserId);
        }
        Page<Meeting> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    @Override
    public List<Meeting> listMyMeeting(MeetingBo bo) {
        LambdaQueryWrapper<Meeting> lqw = buildQueryWrapper(bo);
        // 非管理员
        if (!LoginHelper.isAdmin()) {
            Long currentUserId = LoginHelper.getUserId();
            // 追加我的会议查询条件
            appendMyMeetingCondition(lqw, currentUserId);
        }
        List<Meeting> result = baseMapper.selectList(lqw);
        return result;
    }

    /**
     * 追加我的会议查询条件
     *
     * @param lqw    条件
     * @param userId 用户id
     */
    private void appendMyMeetingCondition(LambdaQueryWrapper<Meeting> lqw, Long userId) {
        lqw.and(w -> {
            // 发布人
            w.and(w1 -> w1.eq(Meeting::getCreatorId, userId));
            // 主持人
            w.or(w2 -> w2.eq(Meeting::getUserId, userId));
            // 记录人
            w.or(w3 -> w3.eq(Meeting::getRegisterId, userId));

            List<Long> filterMeetingIds = new ArrayList<>();
            // 参会或列席人员
            List<Long> joinMeetingIds = meetingJoinerService.selectByUserId(userId);
            // 会议服务
            List<Long> serveMeetingIds = meetingServeService.getListByUserId(userId);
            filterMeetingIds.addAll(joinMeetingIds);
            filterMeetingIds.addAll(serveMeetingIds);
            w.or(!filterMeetingIds.isEmpty(), w4 -> w4.in(Meeting::getId, filterMeetingIds));
        });
    }

    /**
     * 查询会议信息列表
     */
    @Override
    public List<Meeting> listMeeting(MeetingBo bo) {
        LambdaQueryWrapper<Meeting> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建会议信息列表查询条件
     */
    private LambdaQueryWrapper<Meeting> buildQueryWrapper(MeetingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Meeting> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getMeetingTitle()), Meeting::getMeetingTitle, bo.getMeetingTitle());
        lqw.like(StringUtils.isNotBlank(bo.getMeetingBrief()), Meeting::getMeetingBrief, bo.getMeetingBrief());
        lqw.eq(bo.getMeetingType() != null, Meeting::getMeetingType, bo.getMeetingType());
        lqw.eq(bo.getRegisterId() != null, Meeting::getRegisterId, bo.getRegisterId());
        // 会议发布状态
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Meeting::getStatus, bo.getStatus());
        // 会议召开状态
        lqw.in(ObjectUtil.isNotEmpty(bo.getMeetingStatusIn()), Meeting::getMeetingStatus, bo.getMeetingStatusIn());
        // 创建人查看数据范围，管理员能看所有
        lqw.eq(!LoginHelper.isAdmin() && bo.getCreatorId() != null, Meeting::getCreatorId, bo.getCreatorId());
        lqw.apply(bo.getMeetingDate() != null, "MEETING_BEGIN_TIME::DATE = {0}", bo.getMeetingDate());
        // 日期范围查询
        lqw.apply(bo.getMeetingQueryStartDate() != null, "MEETING_BEGIN_TIME::DATE >= {0}", bo.getMeetingQueryStartDate());
        lqw.apply(bo.getMeetingQueryEndDate() != null, "MEETING_END_TIME::DATE <= {0}", bo.getMeetingQueryEndDate());
        return lqw;
    }


    /**
     * 批量删除会议信息
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public int updateStatusById(Long id, String status) {
        Meeting meeting = new Meeting();
        meeting.setId(id);
        meeting.setStatus(status);
        return baseMapper.updateById(meeting);
    }

    @Override
    public int updateMeetingStatusById(Long id, String meetingStatus) {
        Meeting meeting = new Meeting();
        meeting.setId(id);
        meeting.setMeetingStatus(meetingStatus);
        return baseMapper.updateById(meeting);
    }

    @Override
    public List<Meeting> listMeetingByIds(Collection<Long> ids, String meetStatus, String userType) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<Meeting> lqw = Wrappers.lambdaQuery();
        lqw.in(CollectionUtils.isNotEmpty(ids), Meeting::getId, ids)
                .ge(StringUtils.isNotBlank(meetStatus) && JoinUserType.JOIN.getKey().equals(userType), Meeting::getStatus, meetStatus)
                .eq(StringUtils.isNotBlank(meetStatus) && !JoinUserType.JOIN.getKey().equals(userType), Meeting::getStatus, meetStatus);
        List<Meeting> meetings = list(lqw);
        return meetings;
    }

    @Override
    public Meeting getMeetingByType(Long meetingType) {

        Optional<Meeting> first = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(Meeting::getMeetingType, meetingType)
                .eq(Meeting::getIsActive, UserConstants.ACTIVE)
                .list().stream().findFirst();
        return first.orElse(null);
    }

    @Override
    public Map<Long, Long> getMeetingMap(Set<Long> meetTypes) {
        if (CollectionUtils.isEmpty(meetTypes)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<Meeting> lqw = Wrappers.lambdaQuery();
        lqw.in(CollectionUtils.isNotEmpty(meetTypes), Meeting::getMeetingType, meetTypes);
        List<Meeting> meetings = list(lqw);
        Map<Long, Long> map = meetings.stream().collect(Collectors.toMap(Meeting::getMeetingType, Meeting::getId, (a, b) -> a));
        return map;
    }

}
