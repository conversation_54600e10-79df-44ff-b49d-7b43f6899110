package com.tenint.meeting.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * 议题上传对象
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class TopicUploadBo {

    /**
     * 议题id
     */
    private Long topicId;

    /**
     * 类型id
     */
    private Long typeId;

    /**
     * 文件列表
     */
    private List<TopicFileBo> fileList;

    /**
     * 审核人员
     */
    private Long userId;
}
