package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.FileComment;
import com.tenint.meeting.domain.bo.FileCommentBo;
import com.tenint.meeting.domain.vo.FileCommentVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface FileCommentConvert {

    FileCommentConvert INSTANCE = Mappers.getMapper( FileCommentConvert.class );


    FileComment convert(FileCommentBo bean);

    FileCommentVo convert(FileComment bean);

    List<FileCommentVo> convertList(List<FileComment> list);

    Page<FileCommentVo> convertPage(Page<FileComment> page);

}
