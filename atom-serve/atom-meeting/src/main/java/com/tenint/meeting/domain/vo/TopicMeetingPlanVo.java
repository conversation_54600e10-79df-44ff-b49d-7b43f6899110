package com.tenint.meeting.domain.vo;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 预约视图对象 t_topic_meeting_plan
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ExcelIgnoreUnannotated
public class TopicMeetingPlanVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 截至时间
     */
    @ExcelProperty(value = "截至时间")
    private Date lastTime;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    private Boolean delay = false;
}

