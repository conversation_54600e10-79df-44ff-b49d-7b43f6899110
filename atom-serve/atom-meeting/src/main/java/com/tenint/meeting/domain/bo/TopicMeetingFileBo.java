package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 议题收集附件业务对象 t_topic_meeting_file
 *
 * <AUTHOR>
 * @date 2024-08-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicMeetingFileBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 关联的议题收集id
     */
    @NotNull(message = "关联的议题收集id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long topicMeetId;


    /**
     * 文件类型 1 正文附件 2 普通附件
     */
    @NotBlank(message = "文件类型 1 正文附件 2 普通附件不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String fileType;


    /**
     * $column.columnComment
     */
    @NotNull(message = "$column.columnComment不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long ossId;


    /**
     * 附件名称
     */
    @NotBlank(message = "附件名称不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String name;


}
