package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.MeetingServe;
import com.tenint.meeting.domain.bo.MeetingServeBo;

import java.util.Collection;
import java.util.List;
/**
 * 会议服务Service接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface IMeetingServeService extends IService<MeetingServe> {

    /**
     * 查询会议服务列表
     */
    Page<MeetingServe> pageMeetingServe(MeetingServeBo bo, PageQuery pageQuery);

    /**
     * 查询会议服务列表
     */
    List<MeetingServe> listMeetingServe(MeetingServeBo bo);

    /**
     * 校验并批量删除会议服务信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 根据登陆信息查询会议id
     */
    List<Long> getListByUserId(Long userId);
}
