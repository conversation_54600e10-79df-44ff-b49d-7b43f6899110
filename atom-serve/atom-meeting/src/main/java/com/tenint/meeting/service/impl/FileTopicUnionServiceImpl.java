package com.tenint.meeting.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.domain.bo.FileTopicUnionQueryBo;
import com.tenint.meeting.domain.vo.FileTopicUnionVo;
import com.tenint.meeting.mapper.FileTopicUnionMapper;
import com.tenint.meeting.service.IFileTopicUnionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FileTopicUnionServiceImpl implements IFileTopicUnionService {

    private final FileTopicUnionMapper fileTopicUnionMapper;

    @Override
    public Page<FileTopicUnionVo> selectFileTopicUnionVoPage(FileTopicUnionQueryBo bo, PageQuery pageQuery) {
        MPJLambdaWrapper<FileTopicUnionVo> wrapper = buildWrapper(bo);
        return fileTopicUnionMapper.selectFileTopicUnionVoPage(pageQuery.build(), wrapper);
    }

    private MPJLambdaWrapper<FileTopicUnionVo> buildWrapper(FileTopicUnionQueryBo bo) {
        MPJLambdaWrapper<FileTopicUnionVo> wrapper = JoinWrappers.lambda(FileTopicUnionVo.class);
        wrapper.eqIfExists(FileTopicUnionVo::getParentId, bo.getParentId());
        wrapper.likeIfExists(FileTopicUnionVo::getName, bo.getName());
        wrapper.eqIfExists(FileTopicUnionVo::getFileType, bo.getFileType());
        wrapper.geIfExists(FileTopicUnionVo::getCreateTime, bo.getStartTime()).leIfExists(FileTopicUnionVo::getCreateTime, bo.getEndTime());
        return wrapper;
    }
}
