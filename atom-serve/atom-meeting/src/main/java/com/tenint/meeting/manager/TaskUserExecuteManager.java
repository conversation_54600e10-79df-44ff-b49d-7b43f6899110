package com.tenint.meeting.manager;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;


import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.meeting.convert.TaskUserExecuteConvert;
import com.tenint.meeting.domain.TaskOpLog;
import com.tenint.meeting.domain.TaskUser;
import com.tenint.meeting.domain.TaskUserExecute;
import com.tenint.meeting.domain.bo.TaskOpLogBo;
import com.tenint.meeting.domain.bo.TaskUserExecuteBo;
import com.tenint.meeting.domain.vo.TaskUserExecuteVo;
import com.tenint.meeting.domain.vo.TaskVo;
import com.tenint.meeting.enums.MessageTypeEnum;
import com.tenint.meeting.enums.TaskOpLogTypeEnum;
import com.tenint.meeting.enums.TaskStatusEnum;
import com.tenint.meeting.enums.TaskUserExecuteStatusEnum;
import com.tenint.meeting.service.IMessageService;
import com.tenint.meeting.service.ITaskUserExecuteService;
import com.tenint.meeting.service.ITaskUserService;
import com.tenint.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 督查督办执行综合Service层
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RequiredArgsConstructor
@Service
public class TaskUserExecuteManager {

    private final ITaskUserExecuteService taskUserExecuteService;

    private final TaskManager taskManager;

    private final TaskOpLogManager taskOpLogManager;

    private final ITaskUserService taskUserService;

    private final IMessageService messageService;

    private final ISysUserService userService;

    /**
     * 查询督查督办执行
     */
    public TaskUserExecuteVo getVoById(Long id) {
        TaskUserExecute taskUserExecute = taskUserExecuteService.getById(id);
        TaskUserExecuteVo taskUserExecuteVo = TaskUserExecuteConvert.INSTANCE.convert(taskUserExecute);
        return taskUserExecuteVo;
    }

    /**
     * 查询督查督办执行列表
     */
    public TableDataInfo<TaskUserExecuteVo> pageTaskUserExecuteVo(TaskUserExecuteBo bo, PageQuery pageQuery) {
        Page<TaskUserExecute> page = taskUserExecuteService.pageTaskUserExecute(bo, pageQuery);
        Page<TaskUserExecuteVo> pageVo = TaskUserExecuteConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询督查督办执行列表
     */
    public List<TaskUserExecuteVo> listTaskUserExecuteVo(TaskUserExecuteBo bo) {
        List<TaskUserExecute> list = taskUserExecuteService.listTaskUserExecute(bo);
        List<TaskUserExecuteVo> listVo = TaskUserExecuteConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 督查督办执行
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean taskExecute(TaskUserExecuteBo bo) {
        TaskUserExecute taskUserExecute = TaskUserExecuteConvert.INSTANCE.convert(bo);
        TaskVo taskVo = taskManager.getVoById(taskUserExecute.getTaskId());
        // 是否直接执行
        boolean noAssignExecute = bo.getNoAssignExecute();
        // 能执行任务的状态
        List<String> canUserExecuteStatus = noAssignExecute ? List.of(
                TaskStatusEnum.PUBLISH.getKey()
        ) : List.of(
                TaskStatusEnum.PUSH_TO_CLERK.getKey(),
                TaskStatusEnum.DEPT_REVOKE.getKey(),
                TaskStatusEnum.NOT_COMPLETED.getKey()
        );
        if (!canUserExecuteStatus.contains(taskVo.getTaskStatus())) {
            throw new ServiceException("当前任务状态无法执行");
        }
        // 直接执行
        if (noAssignExecute) {
            if (!TaskUserExecuteStatusEnum.COMPLETED.getKey().equals(taskUserExecute.getStatus())) {
                throw new ServiceException("直接执行必需办结");
            }
        }
        // 保存执行情况
        taskUserExecuteService.saveOrUpdate(taskUserExecute);
        // 更新任务状态
        String taskStatus = TaskUserExecuteStatusEnum.COMPLETED.getKey().equals(bo.getStatus()) ?
                noAssignExecute ? TaskStatusEnum.DEPT_PASS.getKey() : TaskStatusEnum.COMPLETED.getKey() :
                TaskStatusEnum.NOT_COMPLETED.getKey();
        taskManager.setStatusById(taskUserExecute.getTaskId(), taskStatus);
        // 直接执行办结
        if (noAssignExecute) {
            // 删除可能已经存在的数据
            taskUserService.removeTaskUserByTaskId(Collections.singletonList(taskVo.getId()));
            // 删除执行待办防止多次指派的问题
            messageService.removeByBusinessId(MessageTypeEnum.TASK_USER_EXECUTE, Collections.singletonList(taskVo.getId()));
            // 添加直接执行人关联
            TaskUser taskUser = new TaskUser();
            taskUser.setTaskName(taskVo.getTaskTitle());
            taskUser.setTaskId(taskUserExecute.getTaskId());
            taskUser.setUserId(LoginHelper.getUserId());
            taskUser.setUserName(LoginHelper.getRealName());
            taskUserService.save(taskUser);
            // 直接执行 完成指派待办
            messageService.completeByTypeAndBusinessId(MessageTypeEnum.TASK_ASSIGN, taskVo.getId());

            // 督查督办办结后，创建提醒（待阅、领导补充意见）
            createNoticeMessageAfterTaskCompleted(taskVo);
        } else {
            // 办结：完成执行待办，推送审核待办
            if (bo.getStatus().equals(TaskUserExecuteStatusEnum.COMPLETED.getKey())) {
                // 完成执行待办
                messageService.completeByTypeAndBusinessId(MessageTypeEnum.TASK_USER_EXECUTE, taskVo.getId());
                // 推送审核待办
                messageService.createByMessageAndUserId("【督查督办】: 待审核'" + taskVo.getTaskTitle() + "'",
                        MessageTypeEnum.TASK_AUDIT.getKey(), Collections.singletonList(taskVo.getUserId()), taskVo.getId());
            }
        }
        // 记录流程
        TaskOpLogTypeEnum opTypeEnum = TaskUserExecuteStatusEnum.COMPLETED.getKey().equals(bo.getStatus()) ?
                noAssignExecute ? TaskOpLogTypeEnum.DEPT_PASS : TaskOpLogTypeEnum.COMPLETED :
                TaskOpLogTypeEnum.NOT_COMPLETED;
        TaskOpLogBo taskOpLogBo = TaskOpLogBo.builder()
                .taskId(taskVo.getId())
                .userId(LoginHelper.getUserId())
                .userName(LoginHelper.getRealName())
                .opName(opTypeEnum.getLabel())
                .opType(opTypeEnum.getKey())
                .build();
        taskOpLogManager.saveByBo(taskOpLogBo);
        return true;
    }

    /**
     * 督查督办审核
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean taskAudit(TaskUserExecuteBo bo) {
        TaskUserExecute taskUserExecute = TaskUserExecuteConvert.INSTANCE.convert(bo);
        TaskVo taskVo = taskManager.getVoById(taskUserExecute.getTaskId());
        // 办结才能审核
        if (!TaskStatusEnum.COMPLETED.getKey().equals(taskVo.getTaskStatus())) {
            throw new ServiceException("当前任务状态无法审核");
        }
        // 保存审核记录
        taskUserExecuteService.saveOrUpdate(taskUserExecute);
        // 更新任务状态
        Boolean auditPass = bo.getAuditPass();
        String taskStatus = auditPass ? TaskStatusEnum.DEPT_PASS.getKey() : TaskStatusEnum.DEPT_REVOKE.getKey();
        taskManager.setStatusById(taskVo.getId(), taskStatus);
        // 完成审核待办
        messageService.completeByTypeAndBusinessId(MessageTypeEnum.TASK_AUDIT, taskVo.getId());
        // 推送退回执行待办
        if (!auditPass) {
            String messageComment = StringUtils.isNotBlank(taskUserExecute.getAuditRemark()) ? taskUserExecute.getAuditRemark() : "无";
            String recallMessage = "【督查督办】: 审核退回，待执行'" + taskVo.getTaskTitle() + "'，审核意见：" + messageComment;
            messageService.createByMessageAndUserId(recallMessage, MessageTypeEnum.TASK_USER_EXECUTE.getKey(),
                    Collections.singletonList(taskVo.getExecuteUserId()), taskVo.getId());
        } else {
            // 督查督办办结后，创建提醒（待阅、领导补充意见）
            createNoticeMessageAfterTaskCompleted(taskVo);
        }
        // 记录流程
        TaskOpLogTypeEnum opTypeEnum = auditPass ? TaskOpLogTypeEnum.DEPT_PASS : TaskOpLogTypeEnum.DEPT_REVOKE;
        TaskOpLogBo taskOpLogBo = TaskOpLogBo.builder()
                .taskId(taskVo.getId())
                .userId(LoginHelper.getUserId())
                .userName(LoginHelper.getRealName())
                .opName(taskUserExecute.getAuditRemark())
                .opType(opTypeEnum.getKey())
                .build();
        taskOpLogManager.saveByBo(taskOpLogBo);
        return true;
    }

    /**
     * 督查督办分管领导补充意见
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean taskMajorFill(TaskUserExecuteBo bo) {
        TaskUserExecute taskUserExecute = TaskUserExecuteConvert.INSTANCE.convert(bo);
        TaskVo taskVo = taskManager.getVoById(taskUserExecute.getTaskId());
        // 能维护补充意见的任务状态
        // 1. 部门审核通过，新增内容
        // 2. 已经补充填报了，修改内容
        List<String> canMajorFillStatus = List.of(
                TaskStatusEnum.DEPT_PASS.getKey(),
                TaskStatusEnum.MAJOR_FILL.getKey()
        );
        if (!canMajorFillStatus.contains(taskVo.getTaskStatus())) {
            throw new ServiceException("当前任务状态无法补充意见");
        }
        // 判断补充意见时效
        DateTime expireDate = DateUtil.offsetDay(taskVo.getSuccessTime(), taskVo.getMajorFillExpDays());
        DateTime today = DateUtil.beginOfDay(new Date());
        if(expireDate.isBefore(today)) {
            throw new ServiceException("当前任务已超出补充意见时效");
        }
        // 保存审核记录
        taskUserExecuteService.saveOrUpdate(taskUserExecute);
        // 更新任务状态
        String taskStatus = TaskStatusEnum.MAJOR_FILL.getKey();
        taskManager.setStatusById(taskVo.getId(), taskStatus);
        // 记录流程
        TaskOpLogTypeEnum opTypeEnum = TaskOpLogTypeEnum.MAJOR_FILL;
        TaskOpLogBo taskOpLogBo = TaskOpLogBo.builder()
                .taskId(taskVo.getId())
                .userId(LoginHelper.getUserId())
                .userName(LoginHelper.getRealName())
                .opName(taskUserExecute.getMajorRemark())
                .opType(opTypeEnum.getKey())
                .build();
        taskOpLogManager.saveByBo(taskOpLogBo);

        return true;
    }


    /**
     * 校验并批量删除督查督办执行信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return taskUserExecuteService.removeWithValidByIds(ids, isValid);
    }

    /**
     * 根据taskId删除督查督办执行信息
     *
     * @param taskIds taskId集合
     * @param isValid 是否校验
     */
    public void removeTaskExecuteByTaskId(List<Long> taskIds, Boolean isValid) {
        if (isValid) {
            taskUserExecuteService.removeTaskExecuteByTaskId(taskIds);
        }
    }

    private void validateTaskUserExecuteExists(Long id) {
        if (taskUserExecuteService.getById(id) == null) {
            throw new ServiceException("督查督办执行数据不存在");
        }
    }

    public TaskUserExecuteVo getVoByTaskId(Long taskId) {
        TaskUserExecute execute = taskUserExecuteService.getOne(Wrappers.<TaskUserExecute>lambdaQuery()
                .eq(TaskUserExecute::getTaskId, taskId));
        TaskUserExecuteVo executeVo = TaskUserExecuteConvert.INSTANCE.convert(execute);
        if (execute != null) {
            Long id = execute.getCreatorId();
            SysUser user = userService.getUserById(id);
            executeVo.setUserName(user.getNickName());
        }
        return executeVo;
    }

    /**
     * 督查督办办结后，创建提醒（待阅、领导补充意见）
     *
     * @param taskVo 任务vo
     */
    public void createNoticeMessageAfterTaskCompleted(TaskVo taskVo) {
        // 推送 相关人督查督办待阅提醒
        List<Long> taskViewUserIds = new ArrayList<>();
        // 协助部门领导（可选）
        if(taskVo.getCoMajorUserId() != null){
            taskViewUserIds.add(taskVo.getCoMajorUserId());
        }
        // 协助部门负责人（可选）
        if(taskVo.getCoUserId() != null){
            taskViewUserIds.add(taskVo.getCoUserId());
        }
        // 关联其他领导人（可选）
        if(StringUtils.isNotBlank(taskVo.getOtherMajorIds())){
            List<Long> otherMajorIdList = Arrays.stream(taskVo.getOtherMajorIds().split(",")).map(Long::parseLong).toList();
            taskViewUserIds.addAll(otherMajorIdList);
        }
        if(!taskViewUserIds.isEmpty()){
            messageService.createByMessageAndUserId("【督查督办】: 已办结'" + taskVo.getTaskTitle() + "'",
                    MessageTypeEnum.TASK_VIEW_NOTICE.getKey(), taskViewUserIds, taskVo.getId());
        }
        // 推送 部门领导补充意见提醒
        messageService.createByMessageAndUserId("【督查督办】: 可补充意见'" + taskVo.getTaskTitle() + "'",
                MessageTypeEnum.TASK_MAJOR_FILL_NOTICE.getKey(), Collections.singletonList(taskVo.getMajorUserId()), taskVo.getId());
    }
}
