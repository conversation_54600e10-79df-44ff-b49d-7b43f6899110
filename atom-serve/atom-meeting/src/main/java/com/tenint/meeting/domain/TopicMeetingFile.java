package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题收集附件对象 t_topic_meeting_file
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@TableName("t_topic_meeting_file")
public class TopicMeetingFile extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 关联的议题收集id
    */
    private Long topicMeetId;

   /**
    * 文件类型 1 正文附件 2 普通附件
    */
    private String fileType;

   /**
    * $column.columnComment
    */
    private Long ossId;

   /**
    * 附件名称
    */
    private String name;

   /**
    * 有效位
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
