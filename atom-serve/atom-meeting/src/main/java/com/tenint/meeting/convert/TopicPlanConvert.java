package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.TopicPlan;
import com.tenint.meeting.domain.TopicPlanDetail;
import com.tenint.meeting.domain.bo.TopicPlanBo;
import com.tenint.meeting.domain.bo.TopicPlanDetailBo;
import com.tenint.meeting.domain.vo.TopicPlanDetailVo;
import com.tenint.meeting.domain.vo.TopicPlanVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TopicPlanConvert {

    TopicPlanConvert INSTANCE = Mappers.getMapper( TopicPlanConvert.class );

    TopicPlan convert(TopicPlanBo bean);

    TopicPlanVo convert(TopicPlan bean);

    List<TopicPlanVo> convertList(List<TopicPlan> list);

    Page<TopicPlanVo> convertPage(Page<TopicPlan> page);

}
