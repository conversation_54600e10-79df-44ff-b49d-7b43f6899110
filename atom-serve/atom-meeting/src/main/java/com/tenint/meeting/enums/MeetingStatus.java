package com.tenint.meeting.enums;


import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "MEETING_STATUS",label = "会议状态",style = true)
@Getter
@AllArgsConstructor
public enum MeetingStatus implements KeyLabelStyleEnum {

    /**
     * 编辑
     */
    EDIT("0", "编辑", "primary"),

    /**
     * 提交
     */
    SUBMIT("1", "提交", "primary"),

    /**
     * 审核通过
     */
    AUDIT_PASS("2", "审核通过", "success"),

    /**
     * 审核不通过
     */
    AUDIT_FAIL("8", "审核不通过", "danger"),

    /**
     * 发布
     */
    PUBLISH("9", "发布", "success");


    private final String key,label,eleStyle;
}
