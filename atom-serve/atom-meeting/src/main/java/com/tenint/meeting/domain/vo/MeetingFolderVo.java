package com.tenint.meeting.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

@Data
public class MeetingFolderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 父节点
     */
    @ExcelProperty(value = "父节点")
    private Long parentId;


    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;


    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 文件标记, 什么重点文件啥的
     */
    @ExcelProperty(value = "文件标记, 什么重点文件啥的")
    private String flag;


    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long fileOrder;

}
