package com.tenint.meeting.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.system.domain.SysOss;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.tenint.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 议题业务对象 t_topic
 *
 * <AUTHOR>
 * @date 2024-07-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopicBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;


    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String title;


    /**
     * 状态
     */
    private String status;


    /**
     * 内容
     */
    private String content;


    /**
     * 文件列表
     */
    private List<SysOss> fileList;


    /**
     * 议题计划列表
     */
    private List<TopicPlanDetailBo> planList;



    /**
     * 汇报人
     */
    private Long reporterUserId;

    /**
     * 汇报单位
     */
    private String reportingOrg;


    /**
     * 议题层级
     */
    private Integer topicLevel;

    /**
     * 主题排序
     */
    private Integer topicSort;


}
