package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.spring.SpringUtils;
import com.tenint.crcc.domain.CrccUser;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.meeting.convert.TopicMeetingAuditConvert;
import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.TopicMeetingAudit;
import com.tenint.meeting.domain.TopicMeetingFlow;
import com.tenint.meeting.domain.bo.TopicMeetingAuditBo;
import com.tenint.meeting.domain.bo.TopicMeetingAuditInfoBo;
import com.tenint.meeting.domain.bo.TopicMeetingLinkRecallBo;
import com.tenint.meeting.domain.dto.TopicMeetingAuditDTO;
import com.tenint.meeting.domain.vo.TopicMeetingAuditVo;
import com.tenint.meeting.domain.vo.TopicMeetingPlanVo;
import com.tenint.meeting.domain.vo.TopicMeetingVo;
import com.tenint.meeting.enums.*;
import com.tenint.meeting.service.IMessageService;
import com.tenint.meeting.service.ITopicMeetingAuditService;
import com.tenint.meeting.service.ITopicMeetingFlowService;
import com.tenint.meeting.service.ITopicMeetingService;
import com.tenint.system.service.ISysRoleService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.tenint.common.constant.ConfigConstants.MEETING_ORGANIZATION;

/**
 * 会议收集审核综合Service层
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingAuditManager {

    private final ITopicMeetingAuditService topicMeetingAuditService;
    private final ITopicMeetingService topicMeetingService;
    private final ITopicMeetingFlowService flowService;
    private final TopicMeetingPlanManager planManager;
    private final HrApiFacade hrApiFacade;
    private final IMessageService messageService;

    /**
     * 查询会议收集审核
     */
    public TopicMeetingAuditVo getVoById(Long id) {
        TopicMeetingAudit topicMeetingAudit = topicMeetingAuditService.getById(id);
        TopicMeetingAuditVo topicMeetingAuditVo = TopicMeetingAuditConvert.INSTANCE.convert(topicMeetingAudit);
        return topicMeetingAuditVo;
    }

    /**
     * 查询会议收集待审 部门/分管领导审核页面
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    public TableDataInfo<TopicMeetingAuditVo>  pageTopicMeetingAuditWithLeaderShip(TopicMeetingAuditBo bo, PageQuery pageQuery) {

        Page<TopicMeetingAuditDTO> page = new Page<>();

        if (LoginHelper.isAdmin()) {
            page = topicMeetingAuditService.pageTopicMeetingAuditWithAdmin(bo, pageQuery);
            Page<TopicMeetingAuditVo> voPage = TopicMeetingAuditConvert.INSTANCE.convertDTOPage(page);
            return new TableDataInfo<>(voPage.getRecords(), voPage.getTotal());
        }
        page = topicMeetingAuditService.pageTopicMeetingAuditWithLeaderShip(bo, pageQuery);
        Page<TopicMeetingAuditVo> voPage = TopicMeetingAuditConvert.INSTANCE.convertDTOPage(page);
        voPage.getRecords().forEach(this::fillTopicMeetingAuditPageVoInfo);
        return new TableDataInfo<>(voPage.getRecords(), voPage.getTotal());
    }

    /**
     * 查询会议收集已审 审核页面
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    public TableDataInfo<TopicMeetingAuditVo> pageTopicMeetingAuditedWithLeaderShip(TopicMeetingAuditBo bo, PageQuery pageQuery) {

        Page<TopicMeetingAuditDTO> page = new Page<>();
        if (LoginHelper.isAdmin()) {
            page = topicMeetingAuditService.pageTopicMeetingAuditedWithAdmin(bo, pageQuery);
        } else {
            page = topicMeetingAuditService.pageTopicMeetingAudited(bo, pageQuery);
        }
        Page<TopicMeetingAuditVo> voPage = TopicMeetingAuditConvert.INSTANCE.convertDTOPage(page);
        voPage.getRecords().forEach(this::fillTopicMeetingAuditPageVoInfo);
        return new TableDataInfo<>(voPage.getRecords(), voPage.getTotal());
    }

    // 填充 page vo数据
    private void fillTopicMeetingAuditPageVoInfo(TopicMeetingAuditVo vo){
        List<TopicMeetingPlanVo> plans = planManager.getVoListByTopicId(vo.getTopicId());
        vo.setPlanList(plans);
    }

    /**
     * 新增会议收集审核
     */
    public Boolean saveByBo(TopicMeetingAuditBo bo) {
        TopicMeetingAudit topicMeetingAudit = TopicMeetingAuditConvert.INSTANCE.convert(bo);
        return topicMeetingAuditService.save(topicMeetingAudit);
    }

    /**
     * 修改会议收集审核
     */
    public Boolean updateByBo(TopicMeetingAuditBo bo) {
        validateTopicMeetingAuditExists(bo.getId());
        TopicMeetingAudit topicMeetingAudit = TopicMeetingAuditConvert.INSTANCE.convert(bo);
        return topicMeetingAuditService.updateById(topicMeetingAudit);
    }


    /**
     * 校验并批量删除会议收集审核信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return topicMeetingAuditService.removeWithValidByIds(ids, isValid);
    }


    private void validateTopicMeetingAuditExists(Long id) {
        if (topicMeetingAuditService.getById(id) == null) {
            throw new ServiceException("会议收集审核数据不存在");
        }
    }

    /**
     * 根据议题id查询关联的审核人
     *
     * @param topicId
     * @return
     */
    Map<String, Long> collectAuditUserByTopicId(Long topicId) {

        Map<String, TopicMeetingAudit> auditMap = topicMeetingAuditService.collectAuditUserByTopicId(topicId);
        return auditMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getUserId()));
    }

    /**
     * 根据议题id查询关联的审核人 value 姓名
     *
     * @param topicId
     * @return
     */
    Map<String, String> collectAuditUserNameByTopicId(Long topicId) {

        Map<String, Long> user = collectAuditUserByTopicId(topicId);
        return user.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                entry -> hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(entry.getValue())).getName()));
    }

    /**
     * 新增/更新议题关联的审核人
     *
     * @param topicId
     * @param departUserIds
     * @param leaderUserIds
     */
    void saveOrUpdateAuditUser(Long topicId, List<Long> departUserIds, List<Long> leaderUserIds) {

        Map<String, List<Long>> userMap = new HashMap<>();
        userMap.put(TopicAuditRoleEnum.DEPART.getKey(), departUserIds);
        userMap.put(TopicAuditRoleEnum.LEADER.getKey(), leaderUserIds);
        topicMeetingAuditService.saveOrUpdateAuditUser(topicId, userMap);
    }

    /**
     * 根据议题id删除关联的审核用户
     *
     * @param topicIds
     */
    void removeAuditUserByTopicId(List<Long> topicIds) {
        topicMeetingAuditService.removeAuditUserByTopicId(topicIds);
    }

    /**
     * 审批议题收集
     *
     * @param auditInfoBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditTopicMeeting(TopicMeetingAuditInfoBo auditInfoBo) {

        Long userId = LoginHelper.getUserId();
        Long topicId = auditInfoBo.getTopicId();
        String status = auditInfoBo.getStatus();

        // 判断当前是部门 分管领导
        TopicMeeting topic = topicMeetingService.getById(topicId);
        String auditStatus = getAuditStatus(topic, status);

        Date date = new Date();
        // 更新主表
        topicMeetingService.lambdaUpdate()
                .set(TopicMeeting::getStatus, auditStatus)
                .set(TopicMeeting::getUpdaterId, userId)
                .set(TopicMeeting::getUpdateTime, date)
                .eq(TopicMeeting::getId, topicId).update();

        CrccUser user = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(userId));
        TopicMeetingFlow flow = new TopicMeetingFlow();
        flow.setTopicMeetId(topicId);
        flow.setUserId(userId);
        flow.setUserName(LoginHelper.isAdmin() ? "系统管理员" : user.getName());
        flow.setStatus(auditStatus);
        flow.setRemark(auditInfoBo.getRemark());
        flow.setOperateTime(date);
        flow.setFlowType(TopicMeetingAuditFlowEnum.AUDIT.getKey());
        flowService.save(flow);

        // 推送待办
        if (TopicMeetingAuditEnum.DEPT_PASS.getKey().equals(auditStatus)) {
            // 推送分管领导 待审核
            Map<String, Long> userMap = SpringUtils.getAopProxy(this).collectAuditUserByTopicId(topicId);
            Long leaderUserId = userMap.get(TopicAuditRoleEnum.LEADER.getKey());

            messageService.completeByTypeAndBusinessId(MessageTypeEnum.TOPIC_AUDIT, topicId);
            messageService.createByMessageAndUserId("【议题收集】: 待审核'" + topic.getTopicTitle() + "'",
                    MessageTypeEnum.TOPIC_AUDIT.getKey(), Collections.singletonList(leaderUserId), topicId);

        } else if (TopicMeetingAuditEnum.DEPT_RECALL.getKey().equals(auditStatus)) {
            String deptRecallComment = StringUtils.isNotBlank(auditInfoBo.getRemark()) ? auditInfoBo.getRemark() : "无";
            String deptRecallMessage = "【议题收集】: 部门领导审核退回，请重新修改'" + topic.getTopicTitle() + "'，审核意见：" + deptRecallComment;
            // 退回提交人
            messageService.completeByTypeAndBusinessId(MessageTypeEnum.TOPIC_AUDIT, topicId);
            messageService.createByMessageAndUserId(deptRecallMessage,
                    MessageTypeEnum.TOPIC_BACK.getKey(), Collections.singletonList(topic.getCreatorId()), topicId);

        } else if (TopicMeetingAuditEnum.LEAD_RECALL.getKey().equals(auditStatus)) {
            String leadRecallComment = StringUtils.isNotBlank(auditInfoBo.getRemark()) ? auditInfoBo.getRemark() : "无";
            String leadRecallMessage = "【议题收集】: 分管领导审核退回，请重新修改'" + topic.getTopicTitle() + "'，审核意见：" + leadRecallComment;
            // 退回提交人
            messageService.completeByTypeAndBusinessId(MessageTypeEnum.TOPIC_AUDIT, topicId);
            messageService.createByMessageAndUserId(leadRecallMessage,
                    MessageTypeEnum.TOPIC_BACK.getKey(), Collections.singletonList(topic.getCreatorId()), topicId);
        } else if (TopicMeetingAuditEnum.LEAD_PASS.getKey().equals(auditStatus)) {
            messageService.completeByTypeAndBusinessId(MessageTypeEnum.TOPIC_AUDIT, topicId);
        }
    }

    private static String getAuditStatus(TopicMeeting topic, String status) {
        String lastStatus = topic.getStatus();
        String auditStatus = "";
        if (TopicMeetingAuditEnum.SUBMIT.getKey().equals(lastStatus)) {
            auditStatus = TopicMeetingAuditInfoEnum.PASS.getKey().equals(status) ? TopicMeetingAuditEnum.DEPT_PASS.getKey() :
                    TopicMeetingAuditEnum.DEPT_RECALL.getKey();
        } else if (TopicMeetingAuditEnum.DEPT_PASS.getKey().equals(lastStatus)) {
            auditStatus = TopicMeetingAuditInfoEnum.PASS.getKey().equals(status) ? TopicMeetingAuditEnum.LEAD_PASS.getKey() :
                    TopicMeetingAuditEnum.LEAD_RECALL.getKey();
        }
        return auditStatus;
    }

    /**
     * 退回议题信息 分管领导审核通过的议题
     *
     * @param linkRecallBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void recallTopicMeetingAfterGroupPass(TopicMeetingLinkRecallBo linkRecallBo) {

        Long topicId = linkRecallBo.getId();
        Long userId = LoginHelper.getUserId();
        Date date = new Date();

        TopicMeeting topic = topicMeetingService.getById(topicId);
        // 更新主表
        topicMeetingService.lambdaUpdate()
                .set(TopicMeeting::getStatus, TopicMeetingAuditEnum.ORGANIZATION_RECALL.getKey())
                .set(TopicMeeting::getUpdaterId, userId)
                .set(TopicMeeting::getUpdateTime, date)
                .eq(TopicMeeting::getId, topicId).update();

        CrccUser user = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(userId));
        TopicMeetingFlow flow = new TopicMeetingFlow();
        flow.setTopicMeetId(topicId);
        flow.setUserId(userId);
        flow.setUserName(user.getName());
        flow.setStatus(TopicMeetingAuditEnum.ORGANIZATION_RECALL.getKey());
        flow.setRemark(linkRecallBo.getRemark());
        flow.setOperateTime(date);
        flow.setFlowType(TopicMeetingAuditFlowEnum.AUDIT.getKey());
        flowService.save(flow);

        String recallComment = StringUtils.isNotBlank(linkRecallBo.getRemark()) ? linkRecallBo.getRemark() : "无";
        String recallMessage = "【议题收集】: 预会议组织者退回，请重新修改'" + topic.getTopicTitle() + "'，审核意见：" + recallComment;
        // 推送待办 - 退回至提交人
        messageService.completeByTypeAndBusinessId(MessageTypeEnum.TOPIC_AUDIT, topicId);
        messageService.createByMessageAndUserId(recallMessage,
                MessageTypeEnum.TOPIC_BACK.getKey(), Collections.singletonList(topic.getCreatorId()), topicId);
    }
}
