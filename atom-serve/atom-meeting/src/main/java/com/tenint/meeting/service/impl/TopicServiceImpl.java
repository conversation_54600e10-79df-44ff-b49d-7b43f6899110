package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.helper.LoginHelper;
import com.tenint.meeting.domain.Topic;
import com.tenint.meeting.domain.dto.TopicUploadDTO;
import com.tenint.meeting.domain.query.TopicQuery;
import com.tenint.meeting.mapper.TopicMapper;
import com.tenint.meeting.service.ITopicService;
import com.tenint.meeting.domain.bo.TopicBo;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 议题Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@RequiredArgsConstructor
@Service
public class TopicServiceImpl extends ServiceImpl<TopicMapper, Topic> implements ITopicService {


    /**
     * 查询议题列表
     */
    @Override
    public Page<Topic> pageTopic(TopicBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Topic> lqw = buildQueryWrapper(bo);
        Page<Topic> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题列表
     */
    @Override
    public List<Topic> listTopic(TopicBo bo) {
        LambdaQueryWrapper<Topic> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题列表查询条件
     */
    private LambdaQueryWrapper<Topic> buildQueryWrapper(TopicBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Topic> lqw = Wrappers.lambdaQuery();
            lqw.eq(StringUtils.isNotBlank(bo.getTitle()), Topic::getTitle, bo.getTitle());
            lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Topic::getStatus, bo.getStatus());
            lqw.eq(StringUtils.isNotBlank(bo.getContent()), Topic::getContent, bo.getContent());
        return lqw;
    }


    /**
     * 批量删除议题
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<Topic> getRelatedTopic() {
        Long userId = LoginHelper.getUserId();
        Date date = new Date();
        QueryWrapper<Topic> wrapper = new QueryWrapper<>();

//        if (!LoginHelper.isAdmin()) {
//            wrapper.eq("TTU.USER_ID", userId);
//            wrapper.le("TTP.BEGIN_TIME", date);
//            wrapper.ge("TTP.END_TIME", date);
//        }
        return baseMapper.getRelatedTopicList(wrapper);
    }

    @Override
    public Page<TopicUploadDTO> pageRelatedTopic(TopicQuery query, PageQuery pageQuery) {

        Long userId = LoginHelper.getUserId();
        Page<Topic> build = pageQuery.build();
        QueryWrapper<Topic> wrapper = new QueryWrapper<>();
        if (!LoginHelper.isAdmin()) {
            wrapper.eq("TC.CREATOR_ID", userId);
        }
        wrapper.like(StringUtils.isNotBlank(query.getTitle()), "TC.TITLE", query.getTitle());
        return baseMapper.pageRelatedTopic(build, wrapper);
    }


}
