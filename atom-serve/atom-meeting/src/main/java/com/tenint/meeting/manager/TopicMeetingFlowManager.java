package com.tenint.meeting.manager;

import com.tenint.common.helper.LoginHelper;
import com.tenint.crcc.domain.CrccUser;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.meeting.convert.TopicMeetingFlowConvert;
import com.tenint.meeting.domain.TopicMeeting;
import com.tenint.meeting.domain.TopicMeetingFlow;
import com.tenint.meeting.domain.vo.TopicMeetingFlowRecordVo;
import com.tenint.meeting.domain.vo.TopicMeetingFlowVo;
import com.tenint.meeting.domain.vo.TopicMeetingPlanVo;
import com.tenint.meeting.enums.TopicAuditRoleEnum;
import com.tenint.meeting.enums.TopicMeetingAuditFlowEnum;
import com.tenint.meeting.service.ITopicMeetingFlowService;
import com.tenint.meeting.service.ITopicMeetingPlanService;
import com.tenint.meeting.service.ITopicMeetingService;
import com.tenint.system.service.IUserSignService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 议题收集流转记录综合Service层
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@RequiredArgsConstructor
@Service
public class TopicMeetingFlowManager {

    private final ITopicMeetingFlowService topicMeetingFlowService;
    private final ITopicMeetingService topicMeetingService;
    private final ITopicMeetingPlanService topicMeetingPlanService;
    private final IUserSignService userSignService;
    private final TopicMeetingAuditManager topicMeetingAuditManager;
    private final TopicMeetingPlanManager topicMeetingPlanManager;
    private final HrApiFacade hrApiFacade;

    /**
     * 提交议题审核后记录操作日志
     *
     * @param topicMeeting 议题
     */
    public void saveFlowWithTopicSubmitted(TopicMeeting topicMeeting) {
        Long userId = LoginHelper.getUserId();
        CrccUser user = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(userId));
        TopicMeetingFlow flow = new TopicMeetingFlow();
        flow.setTopicMeetId(topicMeeting.getId());
        flow.setUserId(userId);
        flow.setUserName(LoginHelper.isAdmin() ? "系统管理员" : user.getName());
        flow.setStatus(topicMeeting.getStatus());
        flow.setRemark(topicMeeting.getRemark());
        flow.setOperateTime(new Date());
        flow.setFlowType(TopicMeetingAuditFlowEnum.CREATE.getKey());
        topicMeetingFlowService.save(flow);
    }

    /**
     * 根据议题id获取议题审批流程
     * @param topicId
     * @return
     */
    public TopicMeetingFlowRecordVo getFlowRecordByTopicId(Long topicId) {
        TopicMeeting topic = topicMeetingService.getById(topicId);
        if(topic == null) {
            return null;
        }
        TopicMeetingFlowRecordVo flowRecordVo = TopicMeetingFlowConvert.INSTANCE.convert(topic);//
//        flowRecordVo.setPlanMeetName(plan.getTitle());
        // 关联的预会议
        List<TopicMeetingPlanVo> planList= topicMeetingPlanManager.getVoListByTopicId(topicId);
        flowRecordVo.setPlanList(planList);

        Map<String, String> userMap = topicMeetingAuditManager.collectAuditUserNameByTopicId(topicId);
        flowRecordVo.setDepartUserName(userMap.get(TopicAuditRoleEnum.DEPART.getKey()));
        flowRecordVo.setLeaderUserName(userMap.get(TopicAuditRoleEnum.LEADER.getKey()));

        List<TopicMeetingFlow> flowList = topicMeetingFlowService.listFlowByTopicId(topicId);
        List<TopicMeetingFlowVo> flowVoList = TopicMeetingFlowConvert.INSTANCE.convertList(flowList);

        List<Long> userIds = flowList.stream().map(TopicMeetingFlow::getUserId).toList();
        Map<Long, String> signMap = userSignService.getSignMapByUserId(userIds);
        flowVoList.forEach(item -> {
            String sign = signMap.getOrDefault(item.getUserId(), "暂无签名");
            item.setSign(sign);
        });
        return flowRecordVo.setFlowList(flowVoList);
    }


}
