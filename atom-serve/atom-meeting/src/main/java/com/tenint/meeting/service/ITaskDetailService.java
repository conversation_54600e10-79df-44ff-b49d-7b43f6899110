package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TaskDetail;
import com.tenint.meeting.domain.bo.TaskDetailBo;

import java.util.Collection;
import java.util.List;
/**
 * 督查督办明细子Service接口
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
public interface ITaskDetailService extends IService<TaskDetail> {

    /**
     * 查询督查督办明细子列表
     */
    Page<TaskDetail> pageTaskDetail(TaskDetailBo bo, PageQuery pageQuery);

    /**
     * 查询督查督办明细子列表
     */
    List<TaskDetail> listTaskDetail(TaskDetailBo bo);

    /**
     * 校验并批量删除督查督办明细子信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据任务id删除关联的明细记录
     * @param taskId
     */
    void removeDetailByTaskId(List<Long> taskId);
}
