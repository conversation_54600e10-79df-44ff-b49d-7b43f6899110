package com.tenint.meeting.enums;

import com.tenint.common.annotation.DictEnum;
import com.tenint.common.core.dict.KeyLabelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@DictEnum(key = "TOPIC_AUDIT_STATUS",label = "议题审核状态",style = true)
@Getter
@AllArgsConstructor
public enum TopicAuditEnum implements KeyLabelStyleEnum {

    EDIT("0", "暂存", "primary"),

    SUBMIT("1", "提交", "primary"),

    PASS("5", "通过", "primary"),

    RETURN("9", "不通过", "primary");


    private final String key,label,eleStyle;
}
