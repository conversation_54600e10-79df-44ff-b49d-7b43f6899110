package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicVote;
import com.tenint.meeting.domain.bo.TopicVoteBo;
import com.tenint.meeting.domain.dto.TopicVoteAndMeetingLinkDTO;

import java.util.Collection;
import java.util.List;
/**
 * 议题表决Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ITopicVoteService extends IService<TopicVote> {

    /**
     * 查询议题表决列表
     */
    Page<TopicVote> pageTopicVote(TopicVoteBo bo, PageQuery pageQuery);

    /**
     * 查询议题表决列表
     */
    List<TopicVote> listTopicVote(TopicVoteBo bo);

    /**
     * 校验并批量删除议题表决信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 新增/修改议题状态
     * @param linkDTO 对象
     * @return
     */
    void addOrUpdate(TopicVoteAndMeetingLinkDTO linkDTO);

    List<TopicVote> getVotesByMeetingIdAndTopicId(Long meetingId, Long topicId);
}
