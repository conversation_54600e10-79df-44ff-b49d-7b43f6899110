package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 议题计划时间对象 t_topic_plan
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
@TableName("t_topic_plan_detail")
public class TopicPlanDetail extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    @TableId(value = "id")
    private Long id;

   /**
    * 计划编号
    */
    private Long planId;

   /**
    * 开始时间
    */
    private Date beginTime;

   /**
    * 结束时间
    */
    private Date endTime;

   /**
    * 当前计划的类型名称
    */
    private String typeName;

   /**
    * 关联编号
    */
    private Long typeId;

    /**
     * 是否有效
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;
}
