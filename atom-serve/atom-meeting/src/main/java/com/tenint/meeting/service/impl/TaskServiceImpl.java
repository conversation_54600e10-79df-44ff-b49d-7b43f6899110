package com.tenint.meeting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.Task;
import com.tenint.meeting.domain.TaskUser;
import com.tenint.meeting.domain.TaskUserExecute;
import com.tenint.meeting.domain.query.TaskQuery;
import com.tenint.meeting.domain.vo.TaskVo;
import com.tenint.meeting.enums.TaskOpLogTypeEnum;
import com.tenint.meeting.enums.TaskStatusEnum;
import com.tenint.meeting.mapper.TaskMapper;
import com.tenint.meeting.service.ITaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 督查督办主Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RequiredArgsConstructor
@Service
public class TaskServiceImpl extends MPJBaseServiceImpl<TaskMapper, Task> implements ITaskService {


    @Override
    public TaskVo getVoById(Long id) {
        TaskQuery query = new TaskQuery();
        query.setId(id);
        MPJLambdaWrapper<Task> wrapper = buildJoinWrapper(query);
        TaskVo taskVo = baseMapper.selectJoinOne(TaskVo.class, wrapper);
        return taskVo;
    }

    @Override
    public Page<TaskVo> pageTaskPublish(TaskQuery query, PageQuery pageQuery) {
        MPJLambdaWrapper<Task> lqw = buildJoinWrapper(query);
        lqw.eq(!LoginHelper.isAdmin(), Task::getCreatorId, LoginHelper.getUserId())
                .orderByDesc(Task::getCreateTime);
        Page<TaskVo> result = baseMapper.selectJoinPage(pageQuery.build(), TaskVo.class, lqw);
        return result;
    }

    @Override
    public Page<TaskVo> pageTaskVoAssign(TaskQuery query, PageQuery pageQuery) {
        MPJLambdaWrapper<Task> wrapper = buildJoinWrapper(query);
        wrapper
                .ne(Task::getTaskStatus, TaskStatusEnum.EDIT.getKey())
                .eq(!LoginHelper.isAdmin(), Task::getUserId, LoginHelper.getUserId())
                .orderByDesc(Task::getCreateTime);
        TaskQuery.Tab tab = query.getTab();
        if (tab != null) {
            switch (tab) {
                case ASSIGN:
                    wrapper.eq(Task::getTaskStatus, TaskStatusEnum.PUBLISH.getKey());
                    break;
                case AUDITING:
                    wrapper.eq(Task::getTaskStatus, TaskStatusEnum.COMPLETED.getKey());
                    break;
                case AUDITED:
                    wrapper.exists("select 1 from T_TASK_OP_LOG a where a.task_id = t.id " +
                                    "and a.user_id = {0} and a.op_type in ({1},{2})",
                            LoginHelper.getUserId(), TaskOpLogTypeEnum.DEPT_PASS.getKey(), TaskOpLogTypeEnum.DEPT_REVOKE.getKey());
                    break;
                default:
                    break;
            }
        }
        Page<TaskVo> result = baseMapper.selectJoinPage(pageQuery.build(), TaskVo.class, wrapper);
        return result;
    }

    @Override
    public Page<TaskVo> pageTaskVoExecute(TaskQuery query, PageQuery pageQuery) {
        MPJLambdaWrapper<Task> wrapper = buildJoinWrapper(query);
        wrapper
                .notIn(Task::getTaskStatus, TaskStatusEnum.EDIT.getKey(), TaskStatusEnum.PUBLISH.getKey())
                .eq(!LoginHelper.isAdmin(), TaskUser::getUserId, LoginHelper.getUserId())
                .orderByDesc(Task::getCreateTime);
        TaskQuery.Tab tab = query.getTab();
        if (tab != null) {
            switch (tab) {
                // 待执行 未办结 审核不通过
                case EXECUTING:
                    wrapper.in(Task::getTaskStatus, TaskStatusEnum.PUSH_TO_CLERK.getKey(), TaskStatusEnum.DEPT_REVOKE.getKey(), TaskStatusEnum.NOT_COMPLETED.getKey());
                    break;
                // 已办结 审核通过 领导补充
                case EXECUTED:
                    wrapper.in(Task::getTaskStatus, TaskStatusEnum.COMPLETED.getKey(), TaskStatusEnum.DEPT_PASS.getKey(), TaskStatusEnum.MAJOR_FILL.getKey());
                    break;
                default:
                    break;
            }
        }
        Page<TaskVo> result = baseMapper.selectJoinPage(pageQuery.build(), TaskVo.class, wrapper);
        return result;
    }

    @Override
    public Page<TaskVo> pageTaskVoMajor(TaskQuery query, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        MPJLambdaWrapper<Task> wrapper = buildJoinWrapper(query);
        wrapper
                .ne(Task::getTaskStatus, TaskStatusEnum.EDIT.getKey())
                .and(!LoginHelper.isAdmin(), w -> {
                    w
                            // 发布人
                            .eq(Task::getCreatorId, userId)
                            .or()
                            // 分管领导
                            .eq(Task::getMajorUserId, userId)
                            .or()
                            // 负责人
                            .eq(Task::getUserId, userId)
                            .or()
                            // 协助部门分管领导
                            .eq(Task::getCoMajorUserId, userId)
                            .or()
                            // 协助部门负责人
                            .eq(Task::getCoUserId, userId)
                            .or()
                            // 执行人
                            .eq(TaskUser::getUserId, userId)
                            .or()
                            // 关联其他领导
                            .apply("REGEXP_LIKE(t.other_major_ids, '(^|,)' || {0} || '(,|$)')", userId)
                    ;
                });
        Page<TaskVo> result = baseMapper.selectJoinPage(pageQuery.build(), TaskVo.class, wrapper);
        return result;
    }

    @Override
    public Page<Task> pageTaskByOrgCode(TaskQuery query, PageQuery pageQuery) {
        LambdaQueryWrapper<Task> lqw = buildQueryWrapper(query);
        List<Task> tasks = baseMapper.selectList(lqw);
        if (!LoginHelper.isAdmin()) {
            tasks.removeIf(item -> {
                String orgCode = LoginHelper.getOrgCode();
                return !orgCode.startsWith(item.getOrgCode());
            });
        }
        Page<Task> page = pageQuery.build();
        this.setPageData(page, tasks);
        return page;
    }

    /**
     * 查询督查督办主列表
     */
    @Override
    public List<Task> listTask(TaskQuery query) {
        LambdaQueryWrapper<Task> lqw = buildQueryWrapper(query);
        return baseMapper.selectList(lqw);
    }

    @Override
    public Page<TaskVo> pageTaskVoUnion(TaskQuery query, PageQuery pageQuery) {
        MPJLambdaWrapper<Task> wrapper = buildJoinWrapper(query);
        TaskQuery.Tab tab = query.getTab();
        wrapper.and(w ->
                w.and(w1 ->
                {
                    // 督查督办指派（指派或者审核）
                    // 负责人是当前用户
                    w1.eq(!LoginHelper.isAdmin(), Task::getUserId, LoginHelper.getUserId())
                            // 非编辑
                            .ne(Task::getTaskStatus, TaskStatusEnum.EDIT.getKey());
                    // 待处理
                    if (TaskQuery.Tab.PENDING.equals(tab)) {
                        // 发布需要指派
                        // 办结需要审核
                        w1.in(Task::getTaskStatus, TaskStatusEnum.PUBLISH.getKey(), TaskStatusEnum.COMPLETED.getKey());
                    }
                }).or(w2 -> {
                    // 督查督办执行
                    // 执行人是当前用户
                    w2.eq(!LoginHelper.isAdmin(), TaskUser::getUserId, LoginHelper.getUserId())
                            // 非编辑
                            .ne(Task::getTaskStatus, TaskStatusEnum.EDIT.getKey())
                            // 不是发布的状态
                            .ne(Task::getTaskStatus, TaskStatusEnum.PUBLISH.getKey());
                    // 待处理
                    if (TaskQuery.Tab.PENDING.equals(tab)) {
                        // 待执行 负责人退回 未办结
                        w2.in(Task::getTaskStatus, TaskStatusEnum.PUSH_TO_CLERK.getKey(), TaskStatusEnum.DEPT_REVOKE.getKey(), TaskStatusEnum.NOT_COMPLETED.getKey());
                    }
                }).or(w3 -> {
                    // 督查督办查阅 分管领导查看
                    // 分管领导是当前用户
                    w3.eq(!LoginHelper.isAdmin(), Task::getMajorUserId, LoginHelper.getUserId())
                            // 非编辑
                            .ne(Task::getTaskStatus, TaskStatusEnum.EDIT.getKey());
                    // 待处理 需要补充意见
                    if (TaskQuery.Tab.PENDING.equals(tab)) {
                        // 部门负责人审核通过的
                        w3.in(Task::getTaskStatus, TaskStatusEnum.DEPT_PASS.getKey());
                        // 补充时效范围内的
                        w3.apply("t3.success_time + INTERVAL '1' DAY * t.major_fill_exp_days >= TRUNC(sysdate)");
                    }
                })
        );
        Page<TaskVo> result = baseMapper.selectJoinPage(pageQuery.build(), TaskVo.class, wrapper);
        return result;
    }

    /**
     * 构建督查督办主列表查询条件
     */
    private LambdaQueryWrapper<Task> buildQueryWrapper(TaskQuery query) {
        LambdaQueryWrapper<Task> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(query.getTaskType()), Task::getTaskType, query.getTaskType());
        lqw.like(StringUtils.isNotBlank(query.getTaskTitle()), Task::getTaskTitle, query.getTaskTitle());
        lqw.eq(query.getDeptId() != null, Task::getDeptId, query.getDeptId());
        lqw.eq(query.getUserId() != null, Task::getUserId, query.getUserId());
        lqw.eq(query.getCompleteTime() != null, Task::getCompleteTime, query.getCompleteTime());
        lqw.eq(StringUtils.isNotBlank(query.getTaskContent()), Task::getTaskContent, query.getTaskContent());
        lqw.ne(query.getTaskStatus() != null, Task::getTaskStatus, query.getTaskStatus());
        return lqw;
    }

    private MPJLambdaWrapper<Task> buildJoinWrapper(TaskQuery query) {
        MPJLambdaWrapper<Task> lqw = JoinWrappers.lambda(Task.class);
        lqw.selectAll(Task.class)
                .selectAs(SysUser::getNickName, TaskVo::getCreateName)
                .selectAs(TaskUser::getUserId, TaskVo::getExecuteUserId)
                .selectAs(TaskUser::getUserName, TaskVo::getExecuteUserName)
                .selectAs(TaskUserExecute::getId, TaskVo::getTaskExecuteId)
                .selectAs(TaskUserExecute::getStatus, TaskVo::getExecuteStatus)
                .selectAs(TaskUserExecute::getExecuteWorkable, TaskVo::getExecuteWorkable)
                .selectAs(TaskUserExecute::getExecuteDelay, TaskVo::getExecuteDelay)
                .selectAs(TaskUserExecute::getExecutePlan, TaskVo::getExecutePlan)
                .selectAs(TaskUserExecute::getPlanTime, TaskVo::getPlanTime)
                .selectAs(TaskUserExecute::getSuccessTime, TaskVo::getSuccessTime)
                .selectAs(TaskUserExecute::getRemark, TaskVo::getRemark)
                .selectAs(TaskUserExecute::getAuditRemark, TaskVo::getAuditRemark)
                .selectAs(TaskUserExecute::getMajorRemark, TaskVo::getMajorRemark)
                .leftJoin(SysUser.class, SysUser::getUserId, Task::getCreatorId)
                .leftJoin(TaskUser.class, TaskUser::getTaskId, Task::getId)
                .leftJoin(TaskUserExecute.class, TaskUserExecute::getTaskId, Task::getId);
        lqw.eq(StringUtils.isNotBlank(query.getTaskType()), Task::getTaskType, query.getTaskType());
        lqw.like(StringUtils.isNotBlank(query.getTaskTitle()), Task::getTaskTitle, query.getTaskTitle());
        lqw.ge(query.getPublishTimeStart() != null, Task::getPublishTime, query.getPublishTimeStart());
        lqw.le(query.getPublishTimeEnd() != null, Task::getPublishTime, query.getPublishTimeEnd());
        lqw.eq(StringUtils.isNotBlank(query.getTaskStatus()), Task::getTaskStatus, query.getTaskStatus());
        lqw.eq(query.getId() != null, Task::getId, query.getId());
        lqw.eq(query.getDeptId() != null, Task::getDeptId, query.getDeptId());
        lqw.eq(query.getUserId() != null, Task::getUserId, query.getUserId());
        lqw.eq(query.getCompleteTime() != null, Task::getCompleteTime, query.getCompleteTime());
        lqw.eq(StringUtils.isNotBlank(query.getTaskContent()), Task::getTaskContent, query.getTaskContent());
        return lqw;
    }


    /**
     * 批量删除督查督办主
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    private <T> void setPageData(Page<T> page, List<T> list) {
        long pageNum = page.getCurrent();
        long pageSize = page.getSize();
        List<T> pageList = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        page.setRecords(pageList);
        page.setTotal(list.size());
        page.setPages(list.size() % pageSize == 0 ? list.size() / pageSize : list.size() / pageSize + 1);
    }

}
