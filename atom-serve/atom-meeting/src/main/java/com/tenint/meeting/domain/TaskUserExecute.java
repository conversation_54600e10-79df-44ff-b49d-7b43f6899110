package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 督查督办执行对象 t_task_user_execute
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
@TableName("t_task_user_execute")
public class TaskUserExecute extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 督办主表id
    */
    private Long taskId;

   /**
    * 落实情况
    */
    private String executeWorkable;

   /**
    * 延迟原因
    */
    private String executeDelay;

   /**
    * 当前进度，后续计划
    */
    private String executePlan;

   /**
    * 计划完成时间
    */
    private Date planTime;

   /**
    * 实际完成时间
    */
    private Date successTime;

   /**
    * 备注
    */
    private String remark;

    /**
     * 部门负责人审核意见
     */
    private String auditRemark;

    /**
     * 分管领导补充意见
     */
    private String majorRemark;

   /**
    * 办结状态
    */
    private String status;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
