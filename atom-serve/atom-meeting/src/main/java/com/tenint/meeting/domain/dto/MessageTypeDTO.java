package com.tenint.meeting.domain.dto;

import com.tenint.common.constant.CacheNames;
import lombok.Data;

@Data
public class MessageTypeDTO {

    private String clientType;

    private String messageType;

    public static MessageTypeDTO of(String clientType, String messageType) {
        MessageTypeDTO dto = new MessageTypeDTO();
        dto.setClientType(clientType);
        dto.setMessageType(messageType);
        return dto;
    }

    public String getCacheKey() {
        return CacheNames.MESSAGE_CONFIG + ":" + clientType + ":" + messageType;
    }
}
