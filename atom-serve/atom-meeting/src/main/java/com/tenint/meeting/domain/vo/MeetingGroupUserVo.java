package com.tenint.meeting.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 用户组用户视图对象 t_meeting_group_user
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
@ExcelIgnoreUnannotated
public class MeetingGroupUserVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名字
     */
    @ExcelProperty(value = "用户名字")
    private String userName;


    /**
     * 主表id
     */
    private Long groupId;


}

