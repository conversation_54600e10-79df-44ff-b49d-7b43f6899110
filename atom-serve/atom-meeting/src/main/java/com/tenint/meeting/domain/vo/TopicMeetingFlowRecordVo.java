package com.tenint.meeting.domain.vo;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 议题收集流转记录视图对象 t_topic_meeting_flow
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@Data
@ExcelIgnoreUnannotated
public class TopicMeetingFlowRecordVo {

    private static final long serialVersionUID = 1L;

    /**
     * 议题主键
     */
    @ExcelProperty(value = "议题主键")
    private Long topicId;

    /**
     * 议题名称
     */
    @ExcelProperty(value = "议题名称")
    private String topicTitle;

    /**
     * 会议类型
     */
    @ExcelProperty(value = "会议类型")
    @Deprecated
    private String meetType;

    /**
     * 预召开会议id
     */
    @ExcelProperty(value = "预召开会议id")
    @Deprecated
    private Long planId;

    /**
     * 预召开会议标题
     */
    @ExcelProperty(value = "预召开会议标题")
    @Deprecated
    private String planMeetName;

    /**
     * 事项编码
     */
    @ExcelProperty(value = "事项编码")
    private String noticeCode;

    /**
     * 补充说明
     */
    @ExcelProperty(value = "补充说明")
    private String remark;

    /**
     * 部门领导
     */
    @ExcelProperty(value = "部门领导")
    private String departUserName;

    /**
     * 分管领导
     */
    @ExcelProperty(value = "分管领导")
    private String leaderUserName;

    /**
     * 审批流程
     */
    private List<TopicMeetingFlowVo> flowList;

    /**
     * 预会议列表
     */
    private List<TopicMeetingPlanVo> planList;
}

