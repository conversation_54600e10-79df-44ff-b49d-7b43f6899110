package com.tenint.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.meeting.domain.TopicMeetingAudit;
import com.tenint.meeting.domain.bo.TopicMeetingAuditBo;
import com.tenint.meeting.domain.dto.TopicMeetingAuditDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 会议收集审核人员Service接口
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
public interface ITopicMeetingAuditService extends IService<TopicMeetingAudit> {


    /**
     * 校验并批量删除会议收集审核人员信息
     */
    Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据议题id查询关联的审核人
     * @param topicId
     * @return
     */
    Map<String, TopicMeetingAudit> collectAuditUserByTopicId(Long topicId);

    /**
     * 新增/更新议题关联的审核人
     * @param topicId
     * @param userList
     * @return
     */
    Boolean saveOrUpdateAuditUser(Long topicId, Map<String, List<Long>> userList);

    /**
     * 根据议题id删除关联的审核用户
     * @param topicIds
     */
    void removeAuditUserByTopicId(List<Long> topicIds);

    /**
     * 查询会议收集-待审 部门/分管领导审核页面
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<TopicMeetingAuditDTO> pageTopicMeetingAuditWithLeaderShip(TopicMeetingAuditBo bo, PageQuery pageQuery);

    /**
     * 查询会议收集-待审 集团领导审核页面
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<TopicMeetingAuditDTO> pageTopicMeetingAuditWithGroup(TopicMeetingAuditBo bo, PageQuery pageQuery);

    /**
     * 查询会议收集-已审
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<TopicMeetingAuditDTO> pageTopicMeetingAudited(TopicMeetingAuditBo bo, PageQuery pageQuery);

    /**
     * 查询会议收集-全部待审 超级管理员
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<TopicMeetingAuditDTO> pageTopicMeetingAuditWithAdmin(TopicMeetingAuditBo bo, PageQuery pageQuery);

    /**
     * 查询会议收集-全部已审 状态大于提交 超级管理员
     * @param bo
     * @param pageQuery
     * @return
     */
    Page<TopicMeetingAuditDTO> pageTopicMeetingAuditedWithAdmin(TopicMeetingAuditBo bo, PageQuery pageQuery);
}
