package com.tenint.meeting.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.constant.CacheNames;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;

import com.tenint.meeting.domain.MeetingGroupUser;
import com.tenint.meeting.service.IMeetingGroupUserService;
import com.tenint.meeting.domain.bo.MeetingGroupUserBo;
import com.tenint.meeting.domain.vo.MeetingGroupUserVo;
import com.tenint.meeting.convert.MeetingGroupUserConvert;

import com.tenint.common.utils.StringUtils;
import com.tenint.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;

/**
 * 用户组用户综合Service层
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@RequiredArgsConstructor
@Service
public class MeetingGroupUserManager {

    private final IMeetingGroupUserService meetingGroupUserService;


    /**
     * 查询用户组用户
     */
    public MeetingGroupUserVo getVoById(Long id) {
        MeetingGroupUser meetingGroupUser = meetingGroupUserService.getById(id);
        MeetingGroupUserVo meetingGroupUserVo = MeetingGroupUserConvert.INSTANCE.convert(meetingGroupUser);

        return meetingGroupUserVo;
    }

    /**
     * 查询用户组用户列表
     */
    public TableDataInfo<MeetingGroupUserVo> pageMeetingGroupUserVo(MeetingGroupUserBo bo, PageQuery pageQuery) {
        Page<MeetingGroupUser> page = meetingGroupUserService.pageMeetingGroupUser(bo, pageQuery);
        Page<MeetingGroupUserVo> pageVo = MeetingGroupUserConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询用户组用户列表
     */
    public List<MeetingGroupUserVo> listMeetingGroupUserVo(MeetingGroupUserBo bo) {
        List<MeetingGroupUser> list = meetingGroupUserService.listMeetingGroupUser(bo);
        List<MeetingGroupUserVo> listVo = MeetingGroupUserConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增用户组用户
     */
    public Boolean saveByBo(MeetingGroupUserBo bo) {
        MeetingGroupUser meetingGroupUser = MeetingGroupUserConvert.INSTANCE.convert(bo);
        return meetingGroupUserService.save(meetingGroupUser);
    }

    /**
     * 修改用户组用户
     */
    public Boolean updateByBo(MeetingGroupUserBo bo) {
        validateMeetingGroupUserExists(bo.getId());
        MeetingGroupUser meetingGroupUser = MeetingGroupUserConvert.INSTANCE.convert(bo);
        return meetingGroupUserService.updateById(meetingGroupUser);
    }


    /**
     * 校验并批量删除用户组用户信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return meetingGroupUserService.removeWithValidByIds(ids, isValid);
    }


    private void validateMeetingGroupUserExists(Long id) {
        if (meetingGroupUserService.getById(id) == null) {
            throw new ServiceException("用户组用户数据不存在");
        }
    }

}
