package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.MeetingJoiner;
import com.tenint.meeting.domain.vo.MeetingJoinerVo;
import com.tenint.meeting.domain.bo.MeetingJoinerBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MeetingJoinerConvert {

    MeetingJoinerConvert INSTANCE = Mappers.getMapper( MeetingJoinerConvert.class );


    MeetingJoiner convert(MeetingJoinerBo bean);

    MeetingJoinerVo convert(MeetingJoiner bean);

    List<MeetingJoinerVo> convertList(List<MeetingJoiner> list);

    Page<MeetingJoinerVo> convertPage(Page<MeetingJoiner> page);

}
