package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.FileComment;
import com.tenint.meeting.domain.bo.FileCommentBo;
import com.tenint.meeting.mapper.FileCommentMapper;
import com.tenint.meeting.service.IFileCommentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 议题批注文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@RequiredArgsConstructor
@Service
public class FileCommentServiceImpl extends ServiceImpl<FileCommentMapper, FileComment> implements IFileCommentService {


    /**
     * 查询议题批注文件列表
     */
    @Override
    public Page<FileComment> pageFileComment(FileCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FileComment> lqw = buildQueryWrapper(bo);
        Page<FileComment> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询议题批注文件列表
     */
    @Override
    public List<FileComment> listFileComment(FileCommentBo bo) {
        LambdaQueryWrapper<FileComment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建议题批注文件列表查询条件
     */
    private LambdaQueryWrapper<FileComment> buildQueryWrapper(FileCommentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FileComment> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getLinkId() != null, FileComment::getLinkId, bo.getLinkId());
            lqw.eq(bo.getLinkFileId() != null, FileComment::getLinkFileId, bo.getLinkFileId());
            lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), FileComment::getOriginalName, bo.getOriginalName());
        return lqw;
    }


    /**
     * 批量删除议题批注文件
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public int selectCommentCount(Long linkId, Long linkFileId) {
        Long count = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(FileComment::getLinkId, linkId).eq(FileComment::getLinkFileId, linkFileId).count();
        return count.intValue();
    }

}
