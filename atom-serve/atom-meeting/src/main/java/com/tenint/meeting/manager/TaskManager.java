package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.domain.CrccUser;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.meeting.convert.TaskConvert;
import com.tenint.meeting.domain.Task;
import com.tenint.meeting.domain.TaskDeptMajor;
import com.tenint.meeting.domain.TaskDetail;
import com.tenint.meeting.domain.TaskUser;
import com.tenint.meeting.domain.bo.TaskBo;
import com.tenint.meeting.domain.bo.TaskMajorFillExpDaysUpdateBo;
import com.tenint.meeting.domain.bo.TaskOpLogBo;
import com.tenint.meeting.domain.query.TaskQuery;
import com.tenint.meeting.domain.vo.TaskFileVo;
import com.tenint.meeting.domain.vo.TaskVo;
import com.tenint.meeting.enums.*;
import com.tenint.meeting.mapper.TaskUserMapper;
import com.tenint.meeting.service.IMessageService;
import com.tenint.meeting.service.ITaskService;
import com.tenint.system.manager.SysUserManager;
import com.tenint.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 督查督办主综合Service层
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RequiredArgsConstructor(onConstructor_ = @Lazy)
@Service
public class TaskManager {

    private final ITaskService taskService;
    private final TaskDetailManager taskDetailManager;
    private final TaskFileManager taskFileManager;
    private final ISysUserService sysUserService;
    private final HrApiFacade hrApiFacade;
    private final SysUserManager userManager;
    private final TaskDeptMajorManager taskDeptMajorManager;
    private final TaskUserMapper taskUserMapper;
    private final TaskUserManager taskUserManager;
    private final TaskUserExecuteManager taskUserExecuteManager;
    private final IMessageService messageService;
    private final TaskOpLogManager taskOpLogManager;

    /**
     * 查询督查督办视图，包含所有子表明细
     */
    public TaskVo getVoById(Long id) {
        TaskVo taskVo = taskService.getVoById(id);
        // 查询明细表
        TaskDetail detail = taskDetailManager.getDetailByTaskId(id);
        taskVo.setMeetingId(detail.getMeetingId());
        taskVo.setSourceType(detail.getSourceType());
        taskVo.setSourceContent(detail.getSourceContent());
        // 查询附件表
        List<TaskFileVo> quoteFileList = taskFileManager.listFileByTaskId(id, TaskFileTypeEnum.QUOTE.getKey());
        List<TaskFileVo> originFileList = taskFileManager.listFileByTaskId(id, TaskFileTypeEnum.ORIGIN.getKey());

        taskVo.setSourceFileList(quoteFileList);
        taskVo.setFileList(originFileList);
        return taskVo;
    }

    /**
     * 查询督查督办主列表(发布)
     */
    public TableDataInfo<TaskVo> pageTaskVoPublish(TaskQuery query, PageQuery pageQuery) {
        Page<TaskVo> pageVo = taskService.pageTaskPublish(query, pageQuery);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询督查督办主列表(指派、审核)
     */
    public TableDataInfo<TaskVo> pageTaskVoAssign(TaskQuery query, PageQuery pageQuery) {
        Page<TaskVo> pageVo = taskService.pageTaskVoAssign(query, pageQuery);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询督查督办主列表(执行)
     */
    public TableDataInfo<TaskVo> pageTaskVoExecute(TaskQuery query, PageQuery pageQuery) {
        Page<TaskVo> pageVo = taskService.pageTaskVoExecute(query, pageQuery);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询督查督办主列表(督查督办查阅、涉及人员均可查看)
     */
    public TableDataInfo<TaskVo> pageTaskVoMajor(TaskQuery query, PageQuery pageQuery) {
        Page<TaskVo> pageVo = taskService.pageTaskVoMajor(query, pageQuery);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询当前用户涉及的督查督办列表
     *
     * @param query 条件
     * @param pageQuery 分页
     * @return 结果
     * @apiNote 包含业务 指派、审核、执行、补充意见
     */
    public TableDataInfo<TaskVo> pageTaskVoUnion(TaskQuery query, PageQuery pageQuery) {
        Page<TaskVo> pageVo = taskService.pageTaskVoUnion(query, pageQuery);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }


    /**
     * 新增督查督办主
     */
    public void saveOrUpdateByBo(TaskBo bo) {
        bo.setTaskStatus(TaskStatusEnum.EDIT.getKey());
        Task task = TaskConvert.INSTANCE.convert(bo);

        // 责任部门
        List<CrccOrg> deptOrgs = hrApiFacade.listOrgPathByOrgId(LoginHelper.getProviderId(), bo.getDeptId());
//        CrccOrg deptOrg = hrApiFacade.getCompanyByOrgId(LoginHelper.getProviderId(), bo.getDeptId());
        CrccOrg deptOrg = deptOrgs.get(0);
        String deptName = userManager.getOrgName(deptOrgs);
        task.setDeptName(deptName);
        task.setOrgCode(deptOrg.getCode());

        // 协助部门
        Long coDeptId = bo.getCoDeptId();
        if(coDeptId != null){
            List<CrccOrg> coDeptOrgs = hrApiFacade.listOrgPathByOrgId(LoginHelper.getProviderId(), coDeptId);
//            CrccOrg coDeptOrg = hrApiFacade.getCompanyByOrgId(LoginHelper.getProviderId(), coDeptId);
            CrccOrg coDeptOrg = coDeptOrgs.get(0);
            String coDeptName = userManager.getOrgName(coDeptOrgs);
            task.setCoDeptName(coDeptName);
            task.setCoOrgCode(coDeptOrg.getCode());
        } else {
            taskService.lambdaUpdate()
                    .eq(Task::getId, task.getId())
                    .set(Task::getCoDeptName, null)
                    .set(Task::getCoOrgCode, null)
                    .set(Task::getCoDeptId, null)
                    .set(Task::getCoMajorUserId, null)
                    .set(Task::getCoUserId, null)
                    .update();
        }
        taskService.saveOrUpdate(task);

        // 处理任务类型
        String taskType = bo.getTaskType();
        if (TaskTypeEnum.MEET.getKey().equals(taskType)) {
            // 保存到明细子表
            taskDetailManager.saveOrUpdateDetailByTaskBo(bo, task.getId());
        }else if (TaskTypeEnum.TASK.getKey().equals(taskType)) {
            String sourceType = bo.getSourceType();
            if (TaskSourceTypeEnum.FILE.getKey().equals(sourceType)) {
                taskDetailManager.saveOrUpdateDetailByTaskBo(bo, task.getId());
                // 保存到附件表
                taskFileManager.saveOrUpdateFileByTask(bo.getSourceFileList(), task.getId(), TaskFileTypeEnum.QUOTE.getKey());
            }else if (TaskSourceTypeEnum.INPUT.getKey().equals(sourceType)){
                // 保存到明细子表
                taskDetailManager.saveOrUpdateDetailByTaskBo(bo, task.getId());
            }
        }

        // 处理附件
        taskFileManager.saveOrUpdateFileByTask(bo.getFileList(), task.getId(), TaskFileTypeEnum.ORIGIN.getKey());

        // 记录日志
        boolean isNew = bo.getId() == null;
        if (isNew) {
            TaskOpLogBo taskOpLogBo = TaskOpLogBo.builder()
                    .taskId(task.getId())
                    .userId(LoginHelper.getUserId())
                    .userName(LoginHelper.getRealName())
                    .opName(TaskOpLogTypeEnum.EDIT.getLabel())
                    .opType(TaskOpLogTypeEnum.EDIT.getKey())
                    .build();
            taskOpLogManager.saveByBo(taskOpLogBo);
        }
    }

    /**
     * 更新领导补充意见时效
     * @param bo 实体类
     */
    public void updateMajorFillExpDays(TaskMajorFillExpDaysUpdateBo bo) {
        taskService.lambdaUpdate()
                .eq(Task::getId, bo.getId())
                .set(Task::getMajorFillExpDays, bo.getMajorFillExpDays()).update();
    }

    /**
     * 强制退回执行人
     */
    @Transactional(rollbackFor = Exception.class)
    public void forceRevoke(Long id){
        TaskVo taskVo = getVoById(id);
        if (taskVo == null) {
            throw new ServiceException("当前任务不存在");
        }
        // 强制退回后，需要完成（隐藏）所有督查督办提醒（待阅、领导补充意见）防止出现问题
        completeAllTaskNoticeMessage(id);
        List<String> canRevokeStatus = List.of(
                TaskStatusEnum.DEPT_PASS.getKey(),
                TaskStatusEnum.MAJOR_FILL.getKey()
        );
        if (!canRevokeStatus.contains(taskVo.getTaskStatus())) {
            throw new ServiceException("当前任务状态无法退回执行人");
        }
        // 是否是部门负责人直接执行，判断执行人是否是他自己
        boolean isNoAssignExecuted = taskVo.getUserId().equals(taskVo.getExecuteUserId());
        if(isNoAssignExecuted){
            // 直接执行的 退回刚发布状态
            setStatusById(taskVo.getId(), TaskStatusEnum.PUBLISH.getKey());
            messageService.createByMessageAndUserId("【督查督办】: 发布人强制退回，待指派'" + taskVo.getTaskTitle() + "'",
                    MessageTypeEnum.TASK_ASSIGN.getKey(), Collections.singletonList(taskVo.getUserId()), id);
        } else {
            // 退回待执行状态
            setStatusById(taskVo.getId(), TaskStatusEnum.PUSH_TO_CLERK.getKey());
            messageService.createByMessageAndUserId("【督查督办】: 发布人强制退回，待执行'" + taskVo.getTaskTitle() + "'",
                    MessageTypeEnum.TASK_USER_EXECUTE.getKey(), Collections.singletonList(taskVo.getExecuteUserId()), id);
        }
        // 记录日志
        TaskOpLogBo taskOpLogBo = TaskOpLogBo.builder()
                .taskId(taskVo.getId())
                .userId(LoginHelper.getUserId())
                .userName(LoginHelper.getRealName())
                .opName(TaskOpLogTypeEnum.FORCE_REVOKE.getLabel())
                .opType(TaskOpLogTypeEnum.FORCE_REVOKE.getKey())
                .build();
        taskOpLogManager.saveByBo(taskOpLogBo);
    }


    /**
     * 校验并批量删除督查督办信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeWithValidByIds(List<Long> ids, Boolean isValid) {
        taskService.removeWithValidByIds(ids, isValid);
        // 删除明细表
        taskDetailManager.removeDetailByTaskId(ids, isValid);
        // 删除附件表
        taskFileManager.removeFileByTaskId(ids, isValid);
        // 删除执行用户表
        taskUserManager.removeTaskUserByTaskId(ids, isValid);
        // 删除执行用户执行表
        taskUserExecuteManager.removeTaskExecuteByTaskId(ids, isValid);
        // 删除消息
        messageService.removeByBusinessId(ids);
        // 删除日志
        taskOpLogManager.removeWithValidByTaskIds(ids, isValid);
    }

    /**
     * 集团管理员发布督查督办
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void publishTask(Long id) {
        taskService.lambdaUpdate().eq(Task::getId, id)
                .set(Task::getTaskStatus, TaskStatusEnum.PUBLISH.getKey())
                .set(Task::getPublishTime, new Date())
                .set(Task::getUpdaterId, LoginHelper.getUserId())
                .set(Task::getUpdateTime, new Date()).update();

        Task task = taskService.getById(id);
        // 1、保存所选的部门领导负责人信息用于以后默认选中
        // 2、赋予相关人员权限
        taskDeptMajorManager.handleTaskAuth(task);

        // 推送待办 推送部门负责人
        messageService.createByMessageAndUserId("【督查督办】: 待指派'" + task.getTaskTitle() + "'",
                MessageTypeEnum.TASK_ASSIGN.getKey(), Collections.singletonList(task.getUserId()), id);

        // 推送 督查督办待阅提醒
        List<Long> taskViewUserIds = new ArrayList<>();
        // 部门领导
        taskViewUserIds.add(task.getMajorUserId());
        // 协助部门领导（可选）
        if(task.getCoMajorUserId() != null){
            taskViewUserIds.add(task.getCoMajorUserId());
        }
        // 协助部门负责人（可选）
        if(task.getCoUserId() != null){
            taskViewUserIds.add(task.getCoUserId());
        }
        // 关联其他领导人（可选）
        if(StringUtils.isNotBlank(task.getOtherMajorIds())){
            List<Long> otherMajorIdList = Arrays.stream(task.getOtherMajorIds().split(",")).map(Long::parseLong).toList();
            taskViewUserIds.addAll(otherMajorIdList);
        }
        messageService.createByMessageAndUserId("【督查督办】: 已发布'" + task.getTaskTitle() + "'",
                MessageTypeEnum.TASK_VIEW_NOTICE.getKey(), taskViewUserIds, id);

        // 记录日志
        TaskOpLogBo taskOpLogBo = TaskOpLogBo.builder()
                .taskId(task.getId())
                .userId(LoginHelper.getUserId())
                .userName(LoginHelper.getRealName())
                .opName(TaskOpLogTypeEnum.PUBLISH.getLabel())
                .opType(TaskOpLogTypeEnum.PUBLISH.getKey())
                .build();
        taskOpLogManager.saveByBo(taskOpLogBo);
    }

    public void setStatusById(Long id, String status) {
        taskService.lambdaUpdate().eq(Task::getId, id)
                .set(Task::getTaskStatus, status)
                .update();
    }

    public List<TaskVo> taskVoList(TaskQuery query){
        query.setTaskStatus(TaskStatusEnum.EDIT.getKey());
        query.setUserId(LoginHelper.getUserId());
        List<Task> tasks = taskService.listTask(query);
        List<TaskVo> taskVos = TaskConvert.INSTANCE.convertList(tasks);
        taskVos.forEach(item -> {
            CrccUser crccUser = hrApiFacade.getUserByUserId(LoginHelper.getProviderId(), String.valueOf(item.getUserId()));
            item.setUserName(crccUser.getName());
        });
        return taskVos;
    }


    public TableDataInfo<TaskVo> pageTaskByOrgCode(TaskQuery query, PageQuery pageQuery) {
        query.setTaskStatus(TaskStatusEnum.EDIT.getKey());
        Page<Task> page = taskService.pageTaskByOrgCode(query, pageQuery);
        Page<TaskVo> pageVo = TaskConvert.INSTANCE.convertPage(page);

        List<Long> creatorIds = page.getRecords().stream().map(Task::getCreatorId).toList();
        Map<Long, SysUser> userMap = sysUserService.getMapByUserIds(creatorIds);
        pageVo.getRecords().forEach(item -> {
            String nickName = userMap.get(item.getCreatorId()).getNickName();
            item.setCreateName(nickName);
            TaskUser task = taskUserMapper.getTaskByUserIdOrTaskId(LoginHelper.getUserId(), item.getId());
            if (LoginHelper.isAdmin()){
                item.setFlag(true);
            }else {
                boolean flag = Objects.equals(item.getUserId(), LoginHelper.getUserId());
                item.setFlag(task != null || flag);
            }
        });
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 完成当前用户督查督办提醒
     * 1、督查督办待阅提醒
     * 2、督查督办领导补充意见提醒
     * 督查督办提醒：查看即完成的消息
     * @param id 任务id
     */
    public void completeTaskNoticeMessage(Long id){
        messageService.completeByTypeAndBusinessId(MessageTypeEnum.TASK_VIEW_NOTICE, id);
        messageService.completeByTypeAndBusinessId(MessageTypeEnum.TASK_MAJOR_FILL_NOTICE, id);
    }

    /**
     * 完成所有督查督办提醒
     * 1、督查督办待阅提醒
     * 2、督查督办领导补充意见提醒
     * 督查督办提醒：查看即完成的消息
     * @param id 任务id
     */
    public void completeAllTaskNoticeMessage(Long id){
        messageService.completeAllByTypeAndBusinessId(MessageTypeEnum.TASK_VIEW_NOTICE, id);
        messageService.completeAllByTypeAndBusinessId(MessageTypeEnum.TASK_MAJOR_FILL_NOTICE, id);
    }


}
