package com.tenint.meeting.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import com.tenint.meeting.domain.TaskFile;
import com.tenint.meeting.domain.bo.TaskFileBo;
import com.tenint.meeting.mapper.TaskFileMapper;
import com.tenint.meeting.service.ITaskFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 督查督办附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RequiredArgsConstructor
@Service
public class TaskFileServiceImpl extends ServiceImpl<TaskFileMapper, TaskFile> implements ITaskFileService {


    /**
     * 查询督查督办附件列表
     */
    @Override
    public Page<TaskFile> pageTaskFile(TaskFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TaskFile> lqw = buildQueryWrapper(bo);
        Page<TaskFile> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }

    /**
     * 查询督查督办附件列表
     */
    @Override
    public List<TaskFile> listTaskFile(TaskFileBo bo) {
        LambdaQueryWrapper<TaskFile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建督查督办附件列表查询条件
     */
    private LambdaQueryWrapper<TaskFile> buildQueryWrapper(TaskFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TaskFile> lqw = Wrappers.lambdaQuery();
            lqw.eq(bo.getTaskId() != null, TaskFile::getTaskId, bo.getTaskId());
            lqw.eq(StringUtils.isNotBlank(bo.getFileType()), TaskFile::getFileType, bo.getFileType());
            lqw.eq(bo.getOssId() != null, TaskFile::getOssId, bo.getOssId());
            lqw.like(StringUtils.isNotBlank(bo.getName()), TaskFile::getName, bo.getName());
        return lqw;
    }


    /**
     * 批量删除督查督办附件
     */
    @Override
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<TaskFile> listFileByTaskIdWithType(Long taskId, String fileType) {

        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TaskFile::getTaskId, taskId).eq(TaskFile::getFileType, fileType).list();
    }

    @Override
    public void removeFileByTaskId(List<Long> taskIds) {
        LambdaQueryChainWrapper<TaskFile> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .in(TaskFile::getTaskId, taskIds);
        remove(wrapper.getWrapper());
    }

    @Override
    public void removeFileByTaskIdWithType(Long taskId, String fileType) {
        LambdaQueryChainWrapper<TaskFile> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TaskFile::getTaskId, taskId).eq(TaskFile::getFileType, fileType);
        remove(wrapper.getWrapper());
    }

}
