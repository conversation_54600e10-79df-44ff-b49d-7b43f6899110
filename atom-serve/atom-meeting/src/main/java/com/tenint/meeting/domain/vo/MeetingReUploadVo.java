package com.tenint.meeting.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class MeetingReUploadVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 截止时间
     */
    private String deadTime;

    /**
     * 当前是否超时
     */
    private boolean timeOut;

    /**
     * 是否打开重新上传
     */
    private boolean openReUpload;
}
