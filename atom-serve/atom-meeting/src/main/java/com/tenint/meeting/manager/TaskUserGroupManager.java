package com.tenint.meeting.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;

import com.tenint.common.exception.ServiceException;


import com.tenint.meeting.convert.TaskUserGroupConvert;
import com.tenint.meeting.domain.TaskUserGroup;
import com.tenint.meeting.domain.bo.TaskUserGroupBo;
import com.tenint.meeting.domain.vo.TaskUserGroupVo;
import com.tenint.meeting.service.ITaskUserGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;

/**
 * 执行人员详情综合Service层
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RequiredArgsConstructor
@Service
public class TaskUserGroupManager {

    private final ITaskUserGroupService taskUserGroupService;

    /**
     * 查询执行人员详情
     */
    public TaskUserGroupVo getVoById(Long id) {
        TaskUserGroup taskUserGroup = taskUserGroupService.getById(id);
        TaskUserGroupVo taskUserGroupVo = TaskUserGroupConvert.INSTANCE.convert(taskUserGroup);
        return taskUserGroupVo;
    }

    /**
     * 查询执行人员详情列表
     */
    public TableDataInfo<TaskUserGroupVo> pageTaskUserGroupVo(TaskUserGroupBo bo, PageQuery pageQuery) {
        Page<TaskUserGroup> page = taskUserGroupService.pageTaskUserGroup(bo, pageQuery);
        Page<TaskUserGroupVo> pageVo = TaskUserGroupConvert.INSTANCE.convertPage(page);
        return new TableDataInfo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    /**
     * 查询执行人员详情列表
     */
    public List<TaskUserGroupVo> listTaskUserGroupVo(TaskUserGroupBo bo) {
        List<TaskUserGroup> list = taskUserGroupService.listTaskUserGroup(bo);
        List<TaskUserGroupVo> listVo = TaskUserGroupConvert.INSTANCE.convertList(list);
        return listVo;
    }

    /**
     * 新增执行人员详情
     */
    public Boolean saveByBo(TaskUserGroupBo bo) {
        TaskUserGroup taskUserGroup = TaskUserGroupConvert.INSTANCE.convert(bo);
        return taskUserGroupService.save(taskUserGroup);
    }

    /**
     * 修改执行人员详情
     */
    public Boolean updateByBo(TaskUserGroupBo bo) {
        validateTaskUserGroupExists(bo.getId());
        TaskUserGroup taskUserGroup = TaskUserGroupConvert.INSTANCE.convert(bo);
        return taskUserGroupService.updateById(taskUserGroup);
    }


    /**
     * 校验并批量删除执行人员详情信息
     */
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return taskUserGroupService.removeWithValidByIds(ids, isValid);
    }


    private void validateTaskUserGroupExists(Long id) {
        if (taskUserGroupService.getById(id) == null) {
            throw new ServiceException("执行人员详情数据不存在");
        }
    }

}
