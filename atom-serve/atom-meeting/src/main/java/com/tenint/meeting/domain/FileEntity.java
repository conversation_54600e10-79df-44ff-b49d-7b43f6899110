package com.tenint.meeting.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import java.math.BigDecimal;

import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 文件对象 t_file_entity
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@TableName("t_file_entity")
public class FileEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    @TableId(value = "id")
    private Long id;

   /**
    * 父节点
    */
    private Long parentId;

   /**
    *  文件/文件夹
    */
    private String entityType;

   /**
    * 名称
    */
    private String name;

   /**
    * 文件大小
    */
    private BigDecimal fileSize;

   /**
    * 祖级列表
    */
    private String ancestors;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;

   /**
    * 状态 0 公开 1 指定用户 2 群组 9 私有
    */
    private String status;

   /**
    * 文件标记, 什么重点文件啥的
    */
    private String flag;

   /**
    * 备注
    */
    private String remark;

   /**
    * 管理的文件信息
    */
    private Long ossId;

   /**
    * 排序
    */
    private Long fileOrder;

   /**
    * 文件类型文件类型（如.txt, .jpg等）。对于目录，此字段可以为NULL
    */
    private String fileType;

    /**
     * 所属机构
     */
    private Long orgId;

    /**
     * 文件状态 0 提交 2通过 9拒绝
     */
    private String fileStatus;

    /**
     * 会议id
     */
    private Long meetingId;
}
