package com.tenint.meeting.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 会议用户文件信息业务对象 t_meeting_user_file
 *
 * <AUTHOR>
 * @date 2024-04-01
 */

@Data
public class MeetingUserFileBo {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 用户编号
     */
    @NotNull(message = "用户编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 文件编号 非0ss编号
     */
    @NotNull(message = "文件编号 非0ss编号不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long fileId;

    /**
     * 是否分发  1：是  0：否
     */
    @NotBlank(message = "是否分发  1：是  0：否不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String isDistribute;

    /**
     * 请求参数
     */
    @ExcelIgnore
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();
}
