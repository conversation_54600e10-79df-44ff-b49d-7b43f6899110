package com.tenint.meeting.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import com.tenint.meeting.domain.vo.TopicMeetingVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 会议信息业务对象 t_meeting
 *
 * <AUTHOR>
 * @date 2024-03-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;


    /**
     * 会议标题
     */
    @NotBlank(message = "会议标题不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String meetingTitle;


    /**
     * 会议主要内容
     */
    private String meetingBrief;


    /**
     * 会议开始时间
     */
    @NotNull(message = "会议开始时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private LocalDate meetingDate;


    /**
     * 会议结束时间
     */
    @NotNull(message = "会议结束时间不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String[] meetingTime;


    /**
     * 会议类型
     */
    @NotNull(message = "会议类型不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long meetingType;


    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议上传截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadTime;

    /**
     * 协作人员
     */
    private List<Long> joinList;

    /**
     * 阅读人员
     */
    private List<Long> readList;

    /**
     * 参会人员
     */
    private List<Long> attendList;

    /**
     * 主持人姓名
     */
    private String userName;

    /**
     * 是否开启投票
     */
    private String isVote;

    private List<FileEntityBo> fileList;

    /**
     * 会议发布状态
     */
    private String status;

    /**
     * 会议进行状态
     */
    private String meetingStatus;

    /**
     * 预会议id
     */
    private Long meetingPlanId;

    private List<TopicMeetingVo> topicMeeting;

    private String meetingNotify;

    private Long userId;

    private Long registerId;

    /**
     * 日期范围查询开始
     */
    private LocalDate meetingQueryStartDate;

    /**
     * 日期范围查询结束
     */
    private LocalDate meetingQueryEndDate;

    /**
     * 会议召开状态in
     */
    private List<String> meetingStatusIn;
}
