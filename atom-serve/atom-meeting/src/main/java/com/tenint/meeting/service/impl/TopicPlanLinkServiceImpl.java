package com.tenint.meeting.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.meeting.domain.TopicPlanLink;
import com.tenint.meeting.mapper.TopicPlanLinkMapper;
import com.tenint.meeting.service.ITopicPlanLinkService;
import java.util.Collection;
import java.util.List;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 议题预会议关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@RequiredArgsConstructor
@Service
public class TopicPlanLinkServiceImpl extends ServiceImpl<TopicPlanLinkMapper, TopicPlanLink> implements ITopicPlanLinkService {


    /**
     * 批量删除议题预会议关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void linkTopicMeetingAndPlans(Long topicId, List<Long> planIds){
        if(ObjectUtil.isNull(planIds) || planIds.isEmpty()){
            return;
        }
        // 先删除所有关联
        this.lambdaUpdate().eq(TopicPlanLink::getTopicId, topicId).remove();
        int order = 0;
        List<TopicPlanLink> links = planIds.stream().map(planId -> {
            TopicPlanLink topicPlanLink = new TopicPlanLink();
            topicPlanLink.setTopicId(topicId);
            topicPlanLink.setPlanId(planId);
            return topicPlanLink;
        }).toList();
        this.saveBatch(links);
    }

}
