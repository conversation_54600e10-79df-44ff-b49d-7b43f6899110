package com.tenint.meeting.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tenint.common.annotation.Translation;
import com.tenint.common.constant.TransConstant;
import com.tenint.meeting.enums.FileType;
import lombok.Data;

import java.util.Date;

/**
 * 会议资料及议题查询对象
 */
@Data
public class FileTopicUnionVo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 所在目录id
     */
    private Long parentId;

    /**
     * 资料、议题名称
     */
    private String name;

    /**
     * ossId
     */
    private String ossId;

    /**
     * 资料类型
     */
    private FileType fileType;

    /**
     * 排序
     */
    private Long fileOrder;

    /**
     * 附件url
     */
    @TableField(exist = false)
    @Translation(type= TransConstant.OSS_ID_TO_URL, mapper = "ossId")
    private String url;

    /**
     * 创建时间
     */
    private Date createTime;
}
