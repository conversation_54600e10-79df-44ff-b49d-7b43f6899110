package com.tenint.meeting.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.meeting.domain.FileRead;
import com.tenint.meeting.domain.bo.FileReadBo;
import com.tenint.meeting.domain.vo.FileReadVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface FileReadConvert {

    FileReadConvert INSTANCE = Mappers.getMapper( FileReadConvert.class );


    FileRead convert(FileReadBo bean);

    FileReadVo convert(FileRead bean);

    List<FileReadVo> convertList(List<FileRead> list);

    Page<FileReadVo> convertPage(Page<FileRead> page);

}
