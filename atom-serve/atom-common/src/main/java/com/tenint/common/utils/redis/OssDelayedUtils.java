package com.tenint.common.utils.redis;

import com.tenint.common.config.properties.OssExpireProperties;
import com.tenint.common.utils.spring.SpringUtils;
import lombok.NoArgsConstructor;
import org.redisson.api.RDelayedQueue;
import org.springframework.context.annotation.Lazy;

import java.time.Duration;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * oss延时队列
 *
 * <AUTHOR>
 * @date 2023/05/13
 */
@NoArgsConstructor
public class OssDelayedUtils {
    
    private static final String QUEUE_NAME = "ossDelayedQueue";

    private static final OssExpireProperties OSS_PROPERTIES = SpringUtils.getBean(OssExpireProperties.class);

    /**
     * 添加
     *
     * @param key 关键
     */
    public static void addToDeletionList(String key) {
        addToDeletionList(key, Duration.ofHours(12));
    }

    /**
     * 添加
     *
     * @param key  关键
     * @param duration 持续时间
     */
    public static void addToDeletionList(String key, Duration duration) {
        long seconds = duration.getSeconds();
        boolean enable = OSS_PROPERTIES.isEnable();
        if (!enable) {
            return;
        }
        RDelayedQueue<String> bean = SpringUtils.getBean(QUEUE_NAME);
        boolean contains = bean.contains(key);
        if (contains) {
            return;
        }
        bean.offer(key, seconds, TimeUnit.SECONDS);
    }


    /**
     * 删除
     *
     * @param key 关键
     */
    public static void closeDelete(String key) {
        boolean enable = OSS_PROPERTIES.isEnable();
        if (!enable) {
            return;
        }
        RDelayedQueue<String> bean = SpringUtils.getBean(QUEUE_NAME);
        bean.remove(key);
    }


    /**
     * 删除
     *
     * @param keys 关键
     */
    public static void closeDelete(Collection<String> keys) {
        boolean enable = OSS_PROPERTIES.isEnable();
        if (!enable) {
            return;
        }
        RDelayedQueue<String> bean = SpringUtils.getBean(QUEUE_NAME);
        bean.removeAll(keys);
    }
}
