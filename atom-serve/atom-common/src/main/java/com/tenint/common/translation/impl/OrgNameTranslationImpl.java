package com.tenint.common.translation.impl;

import com.tenint.common.annotation.TranslationType;
import com.tenint.common.constant.TransConstant;
import com.tenint.common.core.service.OrgService;
import com.tenint.common.translation.TranslationInterface;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 机构翻译实现
 *
 * <AUTHOR> <PERSON>
 */
@Component
@AllArgsConstructor
@TranslationType(type = TransConstant.ORG_CODE_TO_NAME)
public class OrgNameTranslationImpl implements TranslationInterface<String> {

    private final OrgService orgService;

    public String translation(Object key, String other) {
        return orgService.selectOrgNameByIds(key.toString());
    }
}
