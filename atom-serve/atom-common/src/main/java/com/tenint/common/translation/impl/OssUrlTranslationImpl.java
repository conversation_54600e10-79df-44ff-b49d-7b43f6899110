package com.tenint.common.translation.impl;


import com.tenint.common.annotation.TranslationType;
import com.tenint.common.constant.TransConstant;
import com.tenint.common.core.service.OssService;
import com.tenint.common.translation.TranslationInterface;
import com.tenint.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * OSS翻译实现
 *
 * <AUTHOR> Li
 */
@Component
@AllArgsConstructor
@TranslationType(type = TransConstant.OSS_ID_TO_URL)
public class OssUrlTranslationImpl implements TranslationInterface<String> {

    private final OssService ossService;

    public String translation(Object key, String other) {
        if ("thumb".equals(other)) {
            return ossService.getThumbUrlByIds(key.toString());
        }
        return ossService.getUrlByIds(key.toString());
    }
}
