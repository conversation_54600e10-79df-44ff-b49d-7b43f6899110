package com.tenint.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 常量 类
 */
public class CommonConstant {

    /**
     * 集团总部code
     */

    /**
     * 公司组织代码 数据库维护
     *
     * <AUTHOR>
     * @date 2022/12/19
     */
    @AllArgsConstructor
    @Getter
    public enum CompanyOrgCode{
        jituan("集团",  "0000100024"),
        zong<PERSON>("集团","000010002404000"),
        anhui("安徽公司",   "000010002402001"),
        ji<PERSON><PERSON>("江苏公司", "000010002402002"),
        shanghai("上海公司","000010002402003"),
        zhe<PERSON>("浙江公司","000010002402004"),
        fuji<PERSON>("福建公司",  "000010002402005"),
        nanch<PERSON>("南昌公司","000010002402006"),
        di<PERSON><PERSON>("电务公司",  "000010002402008"),
        lu<PERSON><PERSON>("路桥公司",  "000010002402012"),
        g<PERSON><PERSON>("轨道公司",  "000010002402013"),
        beiji<PERSON>("北京公司", "000010002402014"),
        qiaojian("桥建公司","000010002402015"),
        xinan("西南公司",   "000010002402016"),
        jiance("检测公司",  "000010002402018"),
        jiantou("建投公司", "000010002402019"),
        zichan("资产公司",  "000010002402020");
        public String name,value;
    }

    /**
     * 是否
     */
    @AllArgsConstructor
    @Getter
    public enum YesOrNo {
        Yes("是", "1"),
        No("否", "0");
        public String name, value;
    }

    /**
     * 会议审核状态
     */
    @AllArgsConstructor
    @Getter
    public enum MeetingAuditStatus {
        EDIT("编辑中", "0"),
        SUBMIT("提交审核", "1"),
        BACK("审核退回", "8"),
        PUBLISH("发布", "9");
        public String name, value;
    }

    /**
     * 会议进行状态
     */
    @AllArgsConstructor
    @Getter
    public enum MeetingProgressStatus {
        UNSTART("未开始", "0"),
        INPROGRESS("进行中", "1"),
        OVER("会议结束", "9");
        public String name, value;
    }

}
