package com.tenint.common.config;

import io.undertow.server.handlers.DisallowedMethodsHandler;
import io.undertow.server.handlers.PredicateHandler;
import io.undertow.servlet.api.DeploymentInfo;
import io.undertow.util.HttpString;
import org.springframework.boot.web.embedded.undertow.UndertowDeploymentInfoCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.undertow.predicate.Predicates;

@Configuration
public class UndertowConfig {

    @Bean
    public UndertowDeploymentInfoCustomizer undertowDeploymentInfoCustomizer() {
        HttpString trace = HttpString.tryFromString("TRACE");
        HttpString trac = HttpString.tryFromString("TRAC");
        return deploymentInfo -> deploymentInfo.addInitialHandlerChainWrapper(handler -> new DisallowedMethodsHandler(handler, trace, trac));
    }
}
