package com.tenint.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * @since 2022-06-04
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SysOrg {

    private static final long serialVersionUID = 1L;

    /**
     * 组织机构类型 单位：1，部门：2，岗位：3
     */
    private Integer type;

    /**
     * 单位/部门/岗位id
     */
    private Long id;

    /**
     * 单位简称/部门名称/岗位名称
     */
    private String name;

    /**
     * 单位全称
     */
    private String fullname;

    /**
     * 单位/部门/岗位code
     */
    private String code;

    /**
     * 单位/部门/岗位排序号
     */
    private Integer order;

    /**
     * 是否在组织机构路径中显示
     */
    private Boolean show;

    /**
     * 是否虚拟组织机构
     */
    private Boolean virtual;

    /**
     * 下级组织机构集合，可以为单位、部门、岗位。
     */
    private List<SysOrg> children;

    /**
     * 用户集合 (仅岗位有)
     */
    private List<SysUser> users;

    private Long parentId;
}
