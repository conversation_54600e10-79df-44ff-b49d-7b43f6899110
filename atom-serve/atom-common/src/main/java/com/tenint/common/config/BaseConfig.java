package com.tenint.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 系统基础 base 信息配置
 *
 * <AUTHOR>
 * @date 2023/04/04
 */
@Component
@ConfigurationProperties(prefix = "system")
@Getter
@Setter
public class BaseConfig {

    /**
     * 是否允许超级管理员
     */
    private String isRootAllow;

    /**
     * root 的默认id
     */
    private Long defaultRootId;

    /**
     * 是否开启登录加密
     */
    private boolean loginEncrypt;

    private boolean cacheLazy;

    /**
     * 上传路径
     */
    private static String localResource;

    public static String getLocalResource() {
        return localResource;
    }

    public void setLocalResource(String localResource)
    {
        BaseConfig.localResource = localResource;
    }
    /**
     * 获取下载路径
     */
    public static String getDownloadPath()
    {
        return getLocalResource() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath()
    {
        return getLocalResource() + "/upload";
    }

    /**
     * 是否开启定时任务（项目启动时自动开启）
     */
    private Boolean quartzAutoStartup;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    public static boolean getAddressEnabled()
    {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        BaseConfig.addressEnabled = addressEnabled;
    }
}
