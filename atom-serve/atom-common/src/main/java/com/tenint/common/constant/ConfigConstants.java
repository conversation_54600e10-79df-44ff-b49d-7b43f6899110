package com.tenint.common.constant;

public interface ConfigConstants {

    /**
     * 系统通用角色键
     */
    String SYS_COMMON_ROLE_KEY = "sys.role.common";

    /**
     * 启用验证码
     */
    String ENABLE_CAPTCHA = "sys.account.captchaEnabled";

    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "sys.user.initPassword";

    /**
     * 首页优秀报道显示数量
     */
    String HOME_EXCELLENT_NUM = "excellent_num";

    /**
     * 重点项目推送角色
     */
    String PROJECT_PUSH_ROLE = "project_receiver";

    /**
     * 集团文化部角色
     */
    String GROUP_POWER_ROLE = "group_special";

    /**
     * 公司审核角色
     */
    String COMPANY_POWER_ROLE = "company_special";

    /**
     * 机构是否挂载在公司下
     */
    String ORG_MOUNT_COMPANY = "org_mount_company";

    /**
     * 首页日历是否为领导
     */
    String HOME_CALENDAR_LEADER = "main_isLeader";

    /**
     * 媒体资料上传重复检测
     */
    String MEDIA_REPEAT_CHECK = "media_repeat_check";

    /**
     * 宣传考核-项目级申报人员
     */
    String PROPAGANDA_PROJECT_ROLE = "project-jbr";

    /**
     * 宣传考核-公司级申报人员
     */
    String PROPAGANDA_COMPANY_ROLE = "company-jbr";

    /**
     * 宣传考核-集团认定归类字典-新媒体key
     */
    String PROPAGANDA_MEDIA_TYPE = "new_media_key";

    /**
     * 非新媒体的宣传审核菜单
     */
    String OTHER_AUDIT_MENU = "other_audit_menu";

    /**
     * 新媒体的宣传审核菜单
     */
    String MEDIA_AUDIT_MENU = "media_audit_menu";

    /**
     * 素材资源排名位数
     */
    String MEDIA_RESOURCE_RANK_NUM = "media_rank_num";

    /**
     * 宣传考核-新闻线索时间浮动范围
     */
    String PROPAGANDA_CLUE_FLOAT_TIME = "propaganda_clue_float_time";

    /**
     * 会议协作者
     */
    String MEETING_JOIN = "meeting_join";

    /**
     * 会议参会人员
     */
    String MEETING_ATTEND = "meeting_attend";

    /**
     * 列席人员
     */
    String MEETING_INVITATION = "meeting_invitation";

    /**
     * 会议组织者
     */
    String MEETING_ORGANIZATION = "organization";

    /**
     * 议题审核人
     */
    String TOPIC_AUDIT = "topic_audit";

    /**
     * 督查督办查阅人 (已由督查督办部门领导扩展为该督查督办查阅人：某个督查督办所涉及人员均可查看)
     */
    String TASK_MAJOR_VIEW = "task_dept_major";

    /**
     * 督查督办部门负责人(指派审核)
     */
    String TASK_ASSIGN = "task_assign";

    /**
     * 督查督办执行人
     */
    String TASK_EXECUTE = "task_execute";

    /**
     * 记录人员
     */
    String MEETING_RECORDER = "meeting_recorder";

    /**
     * 服务人员
     */
    String MEETING_SERVER = "meeting_serve";

    /**
     * 督查督办-分管领导补充意见默认时效（天）
     */
    String SYS_TASK_MAJOR_FILL_EXP_DAYS_KEY = "sys.task.majorFillExpDays";
}
