package com.tenint.common.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 标记当前枚举为字典
 *
 * <AUTHOR>
 * @date 2023/05/15
 */
@Target(ElementType.TYPE) // 表示这个注解可以被用在类/接口上
@Retention(RetentionPolicy.RUNTIME) // 表示这个注解在运行时可用
public @interface DictEnum {

    /**
     * 标题
     *
     * @return {@link String}
     */
    String label() default "";

    /**
     * 类型
     *
     * @return {@link String}
     */
    String key();

    boolean style() default false;
}