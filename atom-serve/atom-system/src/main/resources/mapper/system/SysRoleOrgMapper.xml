<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.system.mapper.SysRoleOrgMapper">

    <resultMap type="com.tenint.system.domain.SysRoleOrg" id="BRoleOrgResult">
        <result property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="orgId" column="org_id"/>
    </resultMap>
    <select id="selectOrgListByRoleId" resultType="java.lang.String">
        select o.id
        from sys_org o
                 left join sys_role_org ro on o.id = ro.org_id
        where ro.role_id = #{roleId}
        order by o.parent_id, o.org_sort
    </select>


</mapper>
