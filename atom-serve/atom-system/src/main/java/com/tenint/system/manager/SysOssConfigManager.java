package com.tenint.system.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.system.domain.SysOssConfig;
import com.tenint.system.service.ISysOssConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * oss配置综合服务
 *
 * <AUTHOR>
 * @date 2023/05/14
 */
@RequiredArgsConstructor
@Service
public class SysOssConfigManager {

    private final ISysOssConfigService ossConfigService;

    /**
     * 插入操作系统配置
     *
     * @param config 配置
     * @return {@link Boolean}
     */
    public Boolean saveOssConfig(SysOssConfig config) {
      return  ossConfigService.saveOssConfig(config);
    }

    /**
     * 更新操作系统配置
     *
     * @param config 配置
     * @return {@link Boolean}
     */
    public Boolean updateOssConfig(SysOssConfig config) {
       return ossConfigService.updateOssConfig(config);
    }


    /**
     * 批量删除
     *
     * @param ids     id
     * @param isValid 是有效
     * @return {@link Boolean}
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
     return ossConfigService.deleteWithValidByIds(ids, isValid);
    }


    /**
     * 启用禁用状态
     */
    public int updateOssConfigStatus(SysOssConfig sysOssConfig) {
        return  ossConfigService.updateOssConfigStatus(sysOssConfig);
    }

    /**
     * 分页查询配置信息
     *
     * @param ossConfig 操作系统配置
     * @param pageQuery 页面查询
     * @return {@link Page}<{@link SysOssConfig}>
     */
    public Page<SysOssConfig> pageConfig(SysOssConfig ossConfig, PageQuery pageQuery) {
        return ossConfigService.pageConfig(ossConfig, pageQuery);
    }


    /**
     * 获取详细
     *
     * @param ossConfigId oss配置id
     * @return {@link SysOssConfig}
     */
    public SysOssConfig getById(String ossConfigId) {
        return ossConfigService.getById(ossConfigId);
    }
}
