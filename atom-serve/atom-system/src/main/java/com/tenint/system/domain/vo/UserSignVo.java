package com.tenint.system.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tenint.common.annotation.ExcelDictFormat;
import com.tenint.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 签名视图对象 sys_user_sign
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
@ExcelIgnoreUnannotated
public class UserSignVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 签名base64码
     */
    @ExcelProperty(value = "签名base64码")
    private String signPic;


}

