package com.tenint.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 角色管理范围对象 sys_role_scope
 *
 * <AUTHOR>
 * @date 2023-07-02
 */
@Data
@TableName("sys_role_scope")
public class SysRoleScope implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 管理角色编号
    */
    private Long managerRoleId;

   /**
    * 被管角色编号
    */
    private Long managedRoleId;


}
