package com.tenint.system.service;

import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.vo.SysOrgTreeVo;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.system.domain.vo.SysOrgSearchVo;
import com.tenint.system.domain.vo.SysUserSearchVo;

import java.util.List;

/**
 * 机构管理 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysOrgService {


    /**
     * 查询机构树结构信息
     *
     * @param org 机构信息
     * @return 机构树信息集合
     */
    SysOrgTreeVo getOrgTreeList(SysOrg org, Integer removeTag);


    /**
     * 根据角色ID查询机构树信息
     *
     * @param roleId 角色ID
     * @return 选中机构列表
     */
    List<Long> listOrgByRoleId(Long roleId, SysOrgTreeVo tree);

    /**
     * 根据机构ID查询信息
     *
     * @param orgId 机构ID
     * @return 机构信息
     */
    SysOrg getOrgById(Long orgId);


    /**
     * @param parentId   id
     * @return {@link List}<{@link SysOrg}>
     */
    List<SysOrgTreeVo> listOrgByParentId(Long parentId);

    /**
     * @param parentId   id
     * @return {@link List}<{@link SysOrg}>
     */
    List<SysOrgTreeVo> listOrgByParentIdExcludePosition(Long parentId);

    /**
     * 公司通过org id
     *
     * @param providerId 提供者id
     * @param orgId      org id
     * @return {@link CrccOrg}
     */
    CrccOrg getCompanyByOrgId(String providerId, Long orgId);

    /**
     * orgid列表
     *
     * @param array 数组
     * @return {@link List}<{@link SysOrg}>
     */
    List<SysOrg> listOrgByIds(List<Long> array);


    /**
     * 组织树转为组织列表
     * @param treeVo 原本树结构
     * @return 组织列表
     */
    List<SysOrg> treeToList(CrccOrg treeVo);

    /**
     * 用户通过org id列表
     *
     * @param orgId org id
     * @return {@link List}<{@link SysUser}>
     */
    List<SysUser> listUserByOrgId(Long orgId);

    /**
     * 所有组织信息
     * @return
     */
    List<SysOrg> listCompany();

    /**
     * 搜索用户
     *
     * @param orgId    org id
     * @param username 用户名
     * @return {@link List}<{@link SysOrgTreeVo}>
     */
    List<SysUserSearchVo> searchUser(Long orgId, String username);

    /**
     * 本组织及以下的公司、部门(type = 1)
     */
    List<SysOrg> listOrg();

    List<SysOrgSearchVo> searchOrgName(String orgName);


    /**
     * 根据机构编号获取子机构信息
     *
     * @param orgId org id
     * @return {@link List}<{@link CrccOrg}>
     */
    List<CrccOrg> listChildrenCrccOrgByOrgId(Long orgId);

    /**
     * 机构代码与机构编号的映射关系
     */
    void loadingOrgCodeIdMapper();
}
