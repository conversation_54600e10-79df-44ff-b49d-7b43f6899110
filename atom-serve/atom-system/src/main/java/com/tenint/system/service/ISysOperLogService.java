package com.tenint.system.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.system.domain.SysOperLog;

import java.util.List;

/**
 * 操作日志 服务层
 *
 */
public interface ISysOperLogService extends IService<SysOperLog> {

    /**
     * 分页查询
     *
     * @param operLog   打开日志
     * @param pageQuery 页面查询
     * @return {@link Page}<{@link SysOperLog}>
     */
    Page<SysOperLog> pageOperlog(SysOperLog operLog, PageQuery pageQuery);

    /**
     * 日志列表
     *
     * @param operLog 查询实体
     * @return {@link List}<{@link SysOperLog}>
     */
    List<SysOperLog> listOperLog(SysOperLog operLog);
}
