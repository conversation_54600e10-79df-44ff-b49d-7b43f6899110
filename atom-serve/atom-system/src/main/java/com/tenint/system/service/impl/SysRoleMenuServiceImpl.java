package com.tenint.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.core.domain.entity.SysRole;
import com.tenint.common.utils.spring.SpringUtils;
import com.tenint.system.domain.SysRoleMenu;
import com.tenint.system.mapper.SysRoleMenuMapper;
import com.tenint.system.service.ISysRoleMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements ISysRoleMenuService {
    @Override
    public void deleteRoleMenuByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleMenu> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        baseMapper.delete(wrapper);
    }

    @Override
    public void deleteRoleMenuByRoleIds(List<Long> ids) {
        LambdaQueryWrapper<SysRoleMenu> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SysRoleMenu::getRoleId, ids);
        baseMapper.delete(wrapper);
    }

    @Override
    public int saveRoleMenu(SysRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        for (Long menuId : role.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0) {
            rows = SpringUtils.getAopProxy(this).saveBatch(list) ? list.size() : 0;
        }
        return rows;
    }
}
