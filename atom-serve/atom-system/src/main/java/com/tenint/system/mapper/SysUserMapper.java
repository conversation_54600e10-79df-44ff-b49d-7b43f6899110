package com.tenint.system.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.annotation.DataColumn;
import com.tenint.common.annotation.DataPermission;
import com.tenint.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    @DataPermission({
            @DataColumn(key = "orgField", value = "u.org_code"),
            @DataColumn(key = "userField", value = "u.user_id")
    })
    Page<SysUser> selectPageUserList(@Param("page") Page<SysUser> page, @Param(Constants.WRAPPER) Wrapper<SysUser> queryWrapper);

    /**
     * 根据条件分页查询用户列表
     *
     * @param queryWrapper 查询条件
     * @return 用户信息集合信息
     */
    @DataPermission({
            @DataColumn(key = "orgField", value = "u.org_code"),
            @DataColumn(key = "userField", value = "u.user_id")
    })
    List<SysUser> selectUserList(@Param(Constants.WRAPPER) Wrapper<SysUser> queryWrapper);

    /**
     * 根据条件分页查询已配用户角色列表
     *
     * @param queryWrapper 查询条件
     * @return 用户信息集合信息
     */
    @DataPermission({
            @DataColumn(key = "orgField", value = "u.org_code"),
            @DataColumn(key = "userField", value = "u.user_id")
    })
    Page<SysUser> selectAllocatedList(@Param("page") Page<SysUser> page, @Param(Constants.WRAPPER) Wrapper<SysUser> queryWrapper);


    List<SysUser> getServerList();
    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param queryWrapper 查询条件
     * @return 用户信息集合信息
     */
    @DataPermission({
            @DataColumn(key = "orgField", value = "u.org_code"),
            @DataColumn(key = "userField", value = "u.user_id")
    })
    Page<SysUser> selectUnallocatedList(@Param("page") Page<SysUser> page, @Param(Constants.WRAPPER) Wrapper<SysUser> queryWrapper);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName);

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    SysUser selectUserByMobile(String mobile);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 获取用户名和昵称的映射
     *
     * @param userNames 用户名
     * @return {@link Map}<{@link String}, {@link String}>
     */
    default Map<String, String> getNickNameMapByUsername(Collection<String> userNames, Integer overSelectAllCount) {
        Map<String, String> nickNameMap;
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery();
        wrapper.select(SysUser::getUserName, SysUser::getNickName);

        if (userNames.size() < overSelectAllCount) {
            wrapper.select(SysUser::getUserName, SysUser::getNickName).in(SysUser::getUserName, userNames);
        }
        List<SysUser> userList = selectList(wrapper);
        nickNameMap = userList.stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));
        return nickNameMap;
    }
}
