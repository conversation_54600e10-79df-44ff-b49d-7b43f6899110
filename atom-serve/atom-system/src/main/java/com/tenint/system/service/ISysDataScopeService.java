package com.tenint.system.service;

/**
 * 通用 数据权限 服务
 *
 * <AUTHOR>
 */
public interface ISysDataScopeService {


    /**
     * 获取角色自定义权限
     *
     * @param roleId 角色id
     * @return 机构id组
     */
    String getRoleCustom(Long roleId);

    /**
     * 获取机构及以下权限
     *
     * @param orgId 机构id
     * @return 机构id组
     */
    String getOrgCode(Long orgId);


    String getCompanyCode(String orgCode);

}
