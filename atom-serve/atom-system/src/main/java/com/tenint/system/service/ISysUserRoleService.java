package com.tenint.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tenint.system.domain.SysUserRole;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户角色关联
 *
 * <AUTHOR>
 * @date 2023/05/14
 */
public interface ISysUserRoleService extends IService<SysUserRole> {
    /**
     * 查询当前角色使用数量
     *
     * @param roleId 角色id
     * @return int
     */
    long countUserRoleByRoleId(Long roleId);

    /**
     * 删除用户角色
     *
     * @param userRole 用户角色
     */
    boolean removeUserRole(SysUserRole userRole);

    /**
     * 删除用户角色
     *
     * @param roleId  角色id
     * @param userIds 用户id
     */
    boolean removeUserRoles(Long roleId, Long[] userIds);

    /**
     * 保存用户id和用户id所批角色作用
     *
     * @param roleId  角色id
     * @param userIds 用户id
     * @return int
     */
    int saveAuthUsers(Long roleId, Long[] userIds);

    /**
     * 通过角色id获取用户id列表
     *
     * @param roleId 角色id
     * @return {@link List}<{@link Long}>
     */
    List<Long> listUserIdsByRoleId(Long roleId);

    /**
     * key 角色id value 用户ids
     * @param roleIds
     * @return
     */
    Map<Long, List<Long>> mapUserIdsByRoleId(Long[] roleIds);

    /**
     * 删除用户角色用户id
     *
     * @param userId 用户id
     */
    boolean removeUserRoleByUserId(Long userId);

    /**
     * 保存角色身份验证
     *
     * @param userId  用户id
     * @param roleIds 角色id
     */
    void saveAuthRoles(Long userId, Long[] roleIds);

    /**
     * 保存角色身份验证
     *
     * @param userIds  用户id
     * @param roleIds 角色id
     */
    void saveAuthRoles(List<Long> userIds, List<Long> roleIds);

    /**
     * 删除用户角色用户id
     *
     * @param ids id
     */
    boolean removeUserRoleByUserIds(List<Long> ids);

    /**
     * 通过角色id和组织id查询对应用户id集合
     * @param roleIds 角色id
     * @param orgCodes 组织code
     * @return
     */
    List<Long> listUserIdsByRoleIdAndOrgCode(List<Long> roleIds, List<String> orgCodes);

    /**
     * 通过角色id和组织id查询对应用户id集合
     * @param roleIds 角色id
     * @param orgCodes 组织code
     * @return
     */
    List<Long> listUserIdsByRoleIdAndOrgCode(Long roleIds, String orgCodes);

    /**
     * 通过角色id和组织id查询对应用户id集合 - 不额外做权限限制
     * @param roleIds
     * @param orgCodes
     * @return
     */
    List<Long> listUserIdsByRoleIdWithOrgCode(Long roleIds, String orgCodes);

    /**
     * @param roleKeys
     * @param companyList
     * @return
     */
    Set<Long> listUserIdsByRoleKeyAndOrgCode(String[] roleKeys, Set<String> companyList);
}
