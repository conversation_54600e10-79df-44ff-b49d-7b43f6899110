package com.tenint.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.helper.DataBaseHelper;
import com.tenint.common.utils.StreamUtils;
import com.tenint.system.domain.SysRoleOrg;
import com.tenint.system.mapper.SysOrgMapper;
import com.tenint.system.mapper.SysRoleOrgMapper;
import com.tenint.system.service.ISysDataScopeService;
import com.tenint.system.service.ISysOrgService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据权限 实现
 * <p>
 * 注意: 此Service内不允许调用标注`数据权限`注解的方法
 * 例如: orgMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题
 *
 * <AUTHOR> Li
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service("sdss")
public class SysDataScopeServiceImpl implements ISysDataScopeService {


    private final SysRoleOrgMapper roleOrgMapper;
    private final ISysOrgService orgService;

    @Override
    public String getRoleCustom(Long roleId) {
        List<SysRoleOrg> list = roleOrgMapper.selectList(
                new LambdaQueryWrapper<SysRoleOrg>()
                        .select(SysRoleOrg::getOrgId)
                        .eq(SysRoleOrg::getRoleId, roleId));
        if (CollUtil.isNotEmpty(list)) {
            return StreamUtils.join(list, rd -> Convert.toStr(rd.getOrgId()));
        }
        return null;
    }

    @Override
    public String getOrgCode(Long orgId) {
        SysOrg sysOrg = orgService.getOrgById(orgId);
        return sysOrg.getCode();
    }

    @Override
    public String getCompanyCode(String orgCode) {
        String substring = StringUtil.substring(orgCode, 0, 15);
        return substring;
    }

}
