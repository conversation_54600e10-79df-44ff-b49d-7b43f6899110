package com.tenint.system.domain.bo;

import com.tenint.common.core.validate.AddGroup;
import com.tenint.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.tenint.common.core.domain.BaseEntity;

/**
 * 签名业务对象 sys_user_sign
 *
 * <AUTHOR>
 * @date 2024-08-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class UserSignBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id ;

    /**
     * 用户id
     */
//    @NotNull(message = "用户id不能为空" , groups = { AddGroup.class, EditGroup.class })
    private Long userId;


    /**
     * 签名base64码
     */
    @NotBlank(message = "签名base64码不能为空" , groups = { AddGroup.class, EditGroup.class })
    private String signPic;


}
