package com.tenint.system.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.utils.StringUtils;
import com.tenint.system.domain.vo.SysUserTreeVo;
import com.tenint.system.domain.vo.SysUserVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SysUserConvert {

    SysUserConvert INSTANCE = Mappers.getMapper(SysUserConvert.class);

    SysUserVo convert(SysUser sysUser);

    Page<SysUserVo> convert(Page<SysUser> sysUser);

    List<SysUserVo> convert(List<SysUser> sysUser);



    @Mapping(source = "userId", target = "id")
    @Mapping(source = "nickName", target = "name")
    @Mapping(source = "orgPath", target = "orgName")
    SysUserTreeVo convertTreeVo(SysUser sysUser);


    List<SysUserTreeVo> convertTreeVoList(List<SysUser> sysUserList);


//    @Named("extractOrgName")
//    default String extractOrgName(String orgPath) {
//        String[] split = StringUtils.split(orgPath, "/");
//        if (split.length == 1) {
//            return orgPath;
//        }
//        if (split.length == 2 && "中铁二十四局/总部".equals(orgPath)) {
//            return orgPath;
//        }
//        return orgPath
//                .replace("中铁二十四局/", "")
//                .replace("子、分公司/", "");
//    }

}
