package com.tenint.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.system.domain.SysOssConfig;

import java.util.Collection;

/**
 * 对象存储配置Service接口
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * @date 2021-08-13
 */
public interface ISysOssConfigService extends IService<SysOssConfig> {

    /**
     * 初始化OSS配置
     */
    void init();


    /**
     * 根据新增业务对象插入对象存储配置
     *
     * @param bo 对象存储配置新增业务对象
     * @return
     */
    Boolean saveOssConfig(SysOssConfig bo);

    /**
     * 根据编辑业务对象修改对象存储配置
     *
     * @param bo 对象存储配置编辑业务对象
     * @return
     */
    Boolean updateOssConfig(SysOssConfig bo);

    /**
     * 校验并删除数据
     *
     * @param ids     主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 启用停用状态
     */
    int updateOssConfigStatus(SysOssConfig bo);

    /**
     * 分页查询对象存储配置列表
     *
     * @param ossConfig
     * @param pageQuery 页面查询
     * @return {@link Page}<{@link SysOssConfig}>
     */
    Page<SysOssConfig> pageConfig(SysOssConfig ossConfig, PageQuery pageQuery);
}
