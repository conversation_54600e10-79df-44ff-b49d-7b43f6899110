package com.tenint.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serial;
import lombok.Data;

import com.tenint.common.core.domain.BaseEntity;
/**
 * 签名对象 sys_user_sign
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
@TableName("sys_user_sign")
public class UserSign extends BaseEntity {

    @Serial
    private static final long serialVersionUID=1L;

   /**
    * 主键
    */
    private Long id;

   /**
    * 用户id
    */
    private Long userId;

   /**
    * 签名base64码
    */
    private String signPic;

   /**
    * 是否删除
    */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String isActive;


}
