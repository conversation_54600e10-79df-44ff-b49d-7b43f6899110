package com.tenint.system.domain.vo;

import com.tenint.common.utils.StringUtils;
import lombok.Data;

/**
 * 菜单过渡动画
 *
 * <AUTHOR>
 * @date 2023/05/03
 */
@Data
public class MetaTransitionVo {
    /**
     * 当前路由动画效果
     */
    private String name;
    /**
     * 进场动画
     */
    private String enterTransition;

    /**
     * 离场动画
     */
    private String leaveTransition;

    public MetaTransitionVo(String transition) {
        if (StringUtils.isNotBlank(transition)) {
            if (transition.contains("|")) {
                String[] split = transition.split("|");
                this.enterTransition = split[0];
                this.leaveTransition = split[1];
            } else {
                this.name = transition;
            }
        }
    }
}
