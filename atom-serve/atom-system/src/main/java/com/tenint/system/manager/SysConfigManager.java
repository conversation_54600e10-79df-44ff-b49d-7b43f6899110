package com.tenint.system.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.system.domain.SysConfig;
import com.tenint.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 配置管理综合服务
 *
 * <AUTHOR>
 * @date 2023/05/10
 */
@RequiredArgsConstructor
@Service
public class SysConfigManager {

    private final ISysConfigService sysConfigService;

    /**
     * 分页查询
     *
     * @param config    配置
     * @param pageQuery 页面查询
     * @return {@link TableDataInfo}<{@link SysConfig}>
     */
    public TableDataInfo<SysConfig> pageConfig(SysConfig config, PageQuery pageQuery) {
        Page<SysConfig> page = sysConfigService.pageConfig(config, pageQuery);
        return TableDataInfo.build(page);
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    public SysConfig getConfigById(String configId) {
        return sysConfigService.getById(configId);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    public boolean saveConfig(SysConfig config) {
        return sysConfigService.saveConfig(config);
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    public String updateConfig(SysConfig config) {
        return sysConfigService.updateConfig(config);
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    public void deleteConfigByIds(String[] configIds) {
        sysConfigService.deleteConfigByIds(configIds);
    }


    /**
     * 重置参数缓存数据
     */
    public void resetConfigCache() {
        sysConfigService.resetConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数信息
     * @return 结果
     */
    public boolean checkConfigKeyUnique(SysConfig config) {
        return sysConfigService.checkConfigKeyUnique(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数键名
     * @return 参数键值
     */
    public String getConfigValueByKey(String configKey) {
        return sysConfigService.getConfigValueByKey(configKey);
    }

}
