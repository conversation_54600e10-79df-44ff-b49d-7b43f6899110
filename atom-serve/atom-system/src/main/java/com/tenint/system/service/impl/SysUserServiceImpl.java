package com.tenint.system.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.constant.CacheNames;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.service.UserService;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.system.mapper.SysUserMapper;
import com.tenint.system.mapper.SysUserRoleMapper;
import com.tenint.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户表 service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService, UserService {

    private final SysUserRoleMapper userRoleMapper;

    @Override
    public Page<SysUser> pageUser(Wrapper<SysUser> wrapper, PageQuery pageQuery) {
        return baseMapper.selectPageUserList(pageQuery.build(), wrapper);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param wrapper 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> listUser(Wrapper<SysUser> wrapper) {
        return baseMapper.selectUserList(wrapper);
    }


    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public Page<SysUser> listAllocatedUser(SysUser user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.is_active", UserConstants.ACTIVE)
                .eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId())
                .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
                .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
                .like(StringUtils.isNotBlank(user.getMobile()), "u.mobile", user.getMobile());
        return baseMapper.selectAllocatedList(pageQuery.build(), wrapper);
    }

    @Override
    public List<SysUser> ListAllServer() {
        return baseMapper.getServerList();
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param page 分页
     * @param wrapper 查询条件
     * @return 用户信息集合信息
     */
    public Page<SysUser> pageUnallocatedUser(Page<SysUser> page, Wrapper<SysUser> wrapper) {
        return baseMapper.selectUnallocatedList(page, wrapper);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser getUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    /**
     * 通过手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUser getUserByMobile(String mobile) {
        return baseMapper.selectUserByMobile(mobile);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser getUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }



    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, user.getUserName())
                .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkMobileUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getMobile, user.getMobile())
                .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getEmail, user.getEmail())
                .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (ObjectUtil.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!LoginHelper.isAdmin()) {
            QueryWrapper<SysUser> wrapper = Wrappers.query();
            List<SysUser> users = this.listUser(wrapper.lambda().eq(SysUser::getUserId, userId));
            if (CollUtil.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }


    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        user.setCreatorId(user.getUserId());
        user.setUpdaterId(user.getUserId());
        return baseMapper.insert(user) > 0;
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return baseMapper.updateById(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return baseMapper.updateById(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return baseMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                        .set(SysUser::getAvatar, avatar)
                        .eq(SysUser::getUserName, userName)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return baseMapper.updateById(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return baseMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                        .set(SysUser::getPassword, password)
                        .eq(SysUser::getUserName, userName));
    }


    @Override
    public boolean hasUserByOrgId(Long orgId) {
       return baseMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getOrgId, orgId));
    }

    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String getUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getUserName();
    }

    @Cacheable(cacheNames = CacheNames.SYS_USER_NICK, key = "#userId")
    @Override
    public String getUserNick(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getNickName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getNickName();
    }

    @Override
    public Map<Long, SysUser> getMapByUserIds(List<Long> userIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        Map<Long, SysUser> userMap = new LambdaQueryChainWrapper<>(baseMapper)
                .in(SysUser::getUserId, userIds)
                .list().stream().collect(Collectors.toMap(SysUser::getUserId, user -> user));

        return userMap;
    }

    @Override
    public List<Long> listUserIds() {
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery();
        wrapper.select(SysUser::getUserId);
        return baseMapper.selectList(wrapper).stream().map(SysUser::getUserId).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getUserNickMapByUserName(List<String> userNameList) {
        return baseMapper.getNickNameMapByUsername(userNameList, 100);
    }

    @Override
    public List<String> getNamesByUserIds(List<Long> userIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        List<String> userName = new LambdaQueryChainWrapper<>(baseMapper)
                .select(SysUser::getNickName)
                .in(SysUser::getUserId, userIds)
                .list().stream().map(SysUser::getNickName)
                .toList();

        return userName;
    }

    @Override
    public List<Long> listByOrgCodeAndRoleKey(String orgCode, String roleKey) {
        if (StringUtils.isBlank(roleKey) || StringUtils.isBlank(orgCode)) {
            return Collections.emptyList();
        }
        String[] split = roleKey.split(",");
        // 去除文字里的空格
        for (int i = 0; i < split.length; i++) {
            split[i] = split[i].trim();
        }
        return userRoleMapper.selectListUserIdsByOrgCodeAndRoleKey(orgCode, split);
    }

}
