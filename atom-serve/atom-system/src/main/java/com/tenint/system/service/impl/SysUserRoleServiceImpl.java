package com.tenint.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.entity.SysRole;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.utils.StreamUtils;
import com.tenint.common.utils.spring.SpringUtils;
import com.tenint.system.domain.SysUserRole;
import com.tenint.system.mapper.SysUserMapper;
import com.tenint.system.mapper.SysUserRoleMapper;
import com.tenint.system.service.ISysUserRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统用户角色服务impl
 *
 * <AUTHOR>
 * @date 2023/05/14
 */
@Service
@RequiredArgsConstructor
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleService {

    private final SysUserMapper userMapper;

    @Override
    public long countUserRoleByRoleId(Long roleId) {
        LambdaQueryWrapper<SysUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUserRole::getRoleId, roleId);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public boolean removeUserRole(SysUserRole userRole) {
        LambdaQueryWrapper<SysUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUserRole::getUserId, userRole.getUserId()).eq(SysUserRole::getRoleId, userRole.getRoleId());
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    public boolean removeUserRoles(Long roleId, Long[] userIds) {
        LambdaQueryWrapper<SysUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUserRole::getRoleId, roleId).in(SysUserRole::getUserId, Arrays.asList(userIds));
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAuthUsers(Long roleId, Long[] userIds) {
        // 新增用户与角色管理
        int rows = 1;
        List<SysUserRole> list = StreamUtils.toList(Arrays.asList(userIds), userId -> {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            return ur;
        });
        if (CollUtil.isNotEmpty(list)) {
            rows = SpringUtils.getAopProxy(this).saveBatch(list) ? list.size() : 0;
        }
        return rows;
    }

    @Override
    public List<Long> listUserIdsByRoleId(Long roleId) {
        return lambdaQuery().select(SysUserRole::getUserId)
                .eq(SysUserRole::getRoleId, roleId)
                .list().stream().map(SysUserRole::getUserId)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<Long>> mapUserIdsByRoleId(Long[] roleIds) {
        if(roleIds == null){
            return Collections.emptyMap();
        }
        List<SysUserRole> userRoles = lambdaQuery().select(SysUserRole::getUserId)
                .in(SysUserRole::getRoleId, Arrays.stream(roleIds).toList())
                .list();

        Map<Long, List<Long>> userMao = userRoles.stream().collect(Collectors.groupingBy(SysUserRole::getRoleId,
                Collectors.mapping(SysUserRole::getUserId, Collectors.toList())));

        return userMao;
    }

    @Override
    public boolean removeUserRoleByUserId(Long userId) {
        LambdaQueryWrapper<SysUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUserRole::getUserId, userId);
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAuthRoles(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = StreamUtils.toList(Arrays.asList(roleIds), roleId -> {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                return ur;
            });
            SpringUtils.getAopProxy(this).saveBatch(list);
        }
    }

    @Override
    public void saveAuthRoles(List<Long> userIds, List<Long> roleIds) {
        if (ObjectUtil.isNotEmpty(roleIds) && ObjectUtil.isNotEmpty(userIds)) {
            List<SysUserRole> list = new ArrayList<>();
            userIds.forEach( userId -> {
                for (Long roleId : roleIds) {
                    SysUserRole ur = new SysUserRole();
                    ur.setUserId(userId);
                    ur.setRoleId(roleId);
                    list.add(ur);
                }
            });
            SpringUtils.getAopProxy(this).saveBatch(list);
        }
    }

    @Override
    public boolean removeUserRoleByUserIds(List<Long> ids) {
        LambdaQueryWrapper<SysUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SysUserRole::getUserId, ids);
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    public List<Long> listUserIdsByRoleIdAndOrgCode(List<Long> roleIds, List<String> orgCodes) {
        if(CollectionUtil.isEmpty(roleIds) || CollectionUtil.isEmpty(orgCodes)){
            return Collections.emptyList();
        }
        List<Long> userIdsByRole = lambdaQuery().select(SysUserRole::getUserId)
                .in(SysUserRole::getRoleId, roleIds)
                .list().stream()
                .distinct()
                .map(SysUserRole::getUserId)
                .toList();
        if(CollectionUtil.isEmpty(userIdsByRole)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SysUser> wrappers = Wrappers.lambdaQuery();
        wrappers.in(SysUser::getUserId, userIdsByRole);
        wrappers.in(SysUser::getOrgCode, orgCodes);
        List<SysUser> users = userMapper.selectUserList(wrappers);
        return users.stream().map(SysUser::getUserId).toList();
    }

    @Override
    public List<Long> listUserIdsByRoleIdAndOrgCode(Long roleId, String orgCode) {

        List<Long> userIdsByRole = baseMapper.selectUserIdsByRoleId(roleId);
        if(CollectionUtil.isEmpty(userIdsByRole)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SysUser> wrappers = Wrappers.lambdaQuery();
        wrappers.in(SysUser::getUserId, userIdsByRole);
        wrappers.eq(SysUser::getOrgCode, orgCode);
        List<SysUser> users = userMapper.selectUserList(wrappers);
        return users.stream().map(SysUser::getUserId).toList();
    }

    @Override
    public List<Long> listUserIdsByRoleIdWithOrgCode(Long roleId, String orgCode) {
        List<Long> userIdsByRole = baseMapper.selectUserIdsByRoleId(roleId);
        if(CollectionUtil.isEmpty(userIdsByRole)){
            return Collections.emptyList();
        }

        List<SysUser> users = new LambdaQueryChainWrapper<>(userMapper)
                .in(SysUser::getUserId, userIdsByRole)
                .eq(SysUser::getOrgCode, orgCode).list();

        return users.stream().map(SysUser::getUserId).toList();
    }

    @Override
    public Set<Long> listUserIdsByRoleKeyAndOrgCode(String[] roleKeys, Set<String> companyList) {
        if (ArrayUtil.isEmpty(roleKeys) || CollectionUtil.isEmpty(companyList)) {
            return Collections.emptySet();
        }
        Set<Long> result = new HashSet<>();
        for (String orgCode : companyList) {
            List<Long> userIds = baseMapper.selectListUserIdsByOrgCodeAndRoleKey(orgCode, roleKeys);
            result.addAll(userIds);
        }
       return result;
    }
}
