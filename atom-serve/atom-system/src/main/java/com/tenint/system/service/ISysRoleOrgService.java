package com.tenint.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tenint.system.domain.SysRoleOrg;

import java.util.List;

/**
 * 角色机构接口
 *
 * <AUTHOR>
 * @date 2023/05/14
 */
public interface ISysRoleOrgService extends IService<SysRoleOrg> {
    /**
     * 根据角色id 删除角色组织关联
     *
     * @param roleId 角色id
     */
    void deleteRoleOrgByRoleId(Long roleId);

    /**
     * 删除角色组织关联
     *
     * @param ids id
     */
    void deleteRoleOrgByRoleIds(List<Long> ids);
}
