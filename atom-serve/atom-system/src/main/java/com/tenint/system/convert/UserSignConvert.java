package com.tenint.system.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.system.domain.UserSign;
import com.tenint.system.domain.vo.UserSignVo;
import com.tenint.system.domain.bo.UserSignBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserSignConvert {

    UserSignConvert INSTANCE = Mappers.getMapper( UserSignConvert.class );


    UserSign convert(UserSignBo bean);

    UserSignVo convert(UserSign bean);

    List<UserSignVo> convertList(List<UserSign> list);

    Page<UserSignVo> convertPage(Page<UserSign> page);

}
