package com.tenint.system.manager;

import cn.dev33.satoken.context.SaHolder;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tenint.common.constant.CacheConstants;
import com.tenint.common.constant.CacheNames;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysDictData;
import com.tenint.common.core.domain.entity.SysDictType;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.common.core.service.DictService;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.utils.StreamUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.redis.CacheUtils;
import com.tenint.system.service.ISysDictDataService;
import com.tenint.system.service.ISysDictTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典类型综合服务
 *
 * <AUTHOR>
 * @date 2023/05/13
 */
@RequiredArgsConstructor
@Service
public class SysDictTypeManager implements DictService {

    private final ISysDictTypeService dictTypeService;

    private final ISysDictDataService dictDataService;

    /**
     * 分页查询字典类型
     *
     * @param dictType  dict类型
     * @param pageQuery 页面查询
     * @return {@link TableDataInfo}<{@link SysDictType}>
     */
    public TableDataInfo<SysDictType> pageDictType(SysDictType dictType, PageQuery pageQuery) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SysDictType> page = dictTypeService.pageDictType(dictType, pageQuery);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询字典类型
     *
     * @param dictType 字典类型信息
     * @return 字典类型集合信息
     */
    public List<SysDictType> listDictType(SysDictType dictType) {
        return dictTypeService.listDictType(dictType);
    }

    /**
     * 根据所有字典类型
     *
     * @return 字典类型集合信息
     */
    public List<SysDictType> selectDictTypeAll() {
        return dictTypeService.list();
    }


    /**
     * 根据字典类型ID查询信息
     *
     * @param dictId 字典类型ID
     * @return 字典类型
     */

    public SysDictType selectDictTypeById(Long dictId) {
        return dictTypeService.getById(dictId);
    }

    /**
     * 根据字典类型查询信息
     *
     * @param dictType 字典类型
     * @return 字典类型
     */
    public SysDictType getDictTypeByType(String dictType) {
        return dictTypeService.getDictTypeByType(dictType);
    }


    /**
     * 批量删除字典类型信息
     *
     * @param dictIds 需要删除的字典ID
     */

    @Transactional(rollbackFor = Exception.class)
    public void deleteDictTypeByIds(Long[] dictIds, boolean deep) {
        for (Long dictId : dictIds) {
            SysDictType dictType = dictTypeService.getById(dictId);
            if (!deep && dictDataService.count(new LambdaQueryWrapper<SysDictData>()
                    .eq(SysDictData::getDictType, dictType.getDictType())) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
            }
            CacheUtils.evict(CacheNames.SYS_DICT, dictType.getDictType());
            dictDataService.deleteByType(dictType.getDictType());
        }
        dictTypeService.removeBatchByIds(Arrays.asList(dictIds));
    }

    /**
     * 重置字典缓存数据
     */
    public void resetDictCache() {
        dictTypeService.clearDictCache();
        dictDataService.loadingDictCache();
    }


    /**
     * 新增保存字典类型信息
     *
     * @param dict 字典类型信息
     * @return 结果
     */
    public List<SysDictData> saveDictType(SysDictType dict) {
        return dictTypeService.saveDictType(dict);
    }

    /**
     * 修改保存字典类型信息
     *
     * @param dict 字典类型信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SysDictData> updateDictType(SysDictType dict) {
        SysDictType oldDict = dictTypeService.getById(dict.getDictId());
        dictDataService.update(null, new LambdaUpdateWrapper<SysDictData>()
                .set(SysDictData::getDictType, dict.getDictType())
                .eq(SysDictData::getDictType, oldDict.getDictType()));
        boolean updated = dictTypeService.updateById(dict);
        if (updated) {
            CacheUtils.evict(CacheNames.SYS_DICT, oldDict.getDictType());
            return dictDataService.listDictDataByType(dict.getDictType());
        }
        throw new ServiceException("操作失败");
    }

    /**
     * 校验字典类型称是否唯一
     *
     * @param dict 字典类型
     * @return 结果
     */

    public boolean checkDictTypeUnique(SysDictType dict) {
        return dictTypeService.checkDictTypeUnique(dict);
    }

    /**
     * 根据字典类型和字典值获取字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @param separator 分隔符
     * @return 字典标签
     */
    @SuppressWarnings("unchecked cast")
    public String getDictLabel(String dictType, String dictValue, String separator) {
        // 优先从本地缓存获取
        List<SysDictData> datas = (List<SysDictData>) SaHolder.getStorage().get(CacheConstants.SYS_DICT_KEY + dictType);
        if (ObjectUtil.isNull(datas)) {
            datas = dictDataService.listDictDataByType(dictType);
            SaHolder.getStorage().set(CacheConstants.SYS_DICT_KEY + dictType, datas);
        }

        Map<String, String> map = StreamUtils.toMap(datas, SysDictData::getDictValue, SysDictData::getDictLabel);
        if (StringUtils.containsAny(dictValue, separator)) {
            return Arrays.stream(dictValue.split(separator))
                    .map(v -> map.getOrDefault(v, StringUtils.EMPTY))
                    .collect(Collectors.joining(separator));
        } else {
            return map.getOrDefault(dictValue, StringUtils.EMPTY);
        }
    }

    /**
     * 根据字典类型和字典标签获取字典值
     *
     * @param dictType  字典类型
     * @param dictLabel 字典标签
     * @param separator 分隔符
     * @return 字典值
     */
    @SuppressWarnings("unchecked cast")
    public String getDictValue(String dictType, String dictLabel, String separator) {
        // 优先从本地缓存获取
        List<SysDictData> datas = (List<SysDictData>) SaHolder.getStorage().get(CacheConstants.SYS_DICT_KEY + dictType);
        if (ObjectUtil.isNull(datas)) {
            datas =  dictDataService.listDictDataByType(dictType);
            SaHolder.getStorage().set(CacheConstants.SYS_DICT_KEY + dictType, datas);
        }

        Map<String, String> map = StreamUtils.toMap(datas, SysDictData::getDictLabel, SysDictData::getDictValue);
        if (StringUtils.containsAny(dictLabel, separator)) {
            return Arrays.stream(dictLabel.split(separator))
                    .map(l -> map.getOrDefault(l, StringUtils.EMPTY))
                    .collect(Collectors.joining(separator));
        } else {
            return map.getOrDefault(dictLabel, StringUtils.EMPTY);
        }
    }

}
