package com.tenint.system.manager;

import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.vo.SysOrgTreeVo;
import com.tenint.crcc.constant.CrccConstant;
import com.tenint.crcc.convert.SysUserConvert;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.system.domain.vo.SysOrgSearchVo;
import com.tenint.system.domain.vo.SysUserSearchVo;
import com.tenint.system.service.ISysOrgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class SysOrgManager {

    private final ISysOrgService  sysOrgService;


    /**
     * 查询机构树结构信息
     *
     * @param org 机构信息
     * @return 机构树信息集合
     */
    public SysOrgTreeVo selectOrgTreeList(SysOrg org) {
        return sysOrgService.getOrgTreeList(org, 3);
    }




    /**
     * 根据角色ID查询机构树信息
     *
     * @param roleId 角色ID
     * @return 选中机构列表
     */
    public List<Long> selectOrgListByRoleId(Long roleId, SysOrgTreeVo treeVo) {
      return sysOrgService.listOrgByRoleId(roleId, treeVo);
    }


    /**
     * 根据机构ID查询信息
     *
     * @param orgId 机构ID
     * @return 机构信息
     */
    public SysOrg selectOrgById(Long orgId) {
        return sysOrgService.getOrgById(orgId);
    }


    /**
     * 根据父节点
     *
     * @param parentId 父id
     * @return {@link List}<{@link SysOrgTreeVo}>
     */
    public List<SysOrgTreeVo> listOrgByParentId(Long parentId) {
        return sysOrgService.listOrgByParentId(parentId);
    }

    /**
     * 用户通过org id列表
     *
     * @param orgId org id
     * @return {@link List}<{@link SysUser}>
     */
    public List<SysUserSearchVo> listUserByOrgId(Long orgId) {
        List<SysUser> sysUsers = sysOrgService.listUserByOrgId(orgId);
        List<SysUserSearchVo> voList = new ArrayList<>();
        sysUsers.forEach(sysUser -> {
            SysUserSearchVo vo = new SysUserSearchVo();
            vo.setId(sysUser.getUserId());
            vo.setName(sysUser.getNickName());
            vo.setOrgId(sysUser.getOrgId());
            vo.setOrgName(sysUser.getOrgName());
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 搜索用户
     *
     * @param orgId    org id
     * @param username 用户名
     * @return {@link List}<{@link SysOrgTreeVo}>
     */
    public List<SysUserSearchVo> searchUser(Long orgId, String username) {
        return sysOrgService.searchUser(orgId, username);
    }

    /**
     * 搜索组织机构
     *
     * @param orgName 组织名字
     * @return {@link List}<{@link SysUserSearchVo}>
     */
    public List<SysOrgSearchVo> searchOrgName(String orgName) {
        return sysOrgService.searchOrgName(orgName);
    }

    /**
     * 获取子分公司信息
     * @return
     */
    public List<CrccOrg> listOrg() {
        return sysOrgService.listChildrenCrccOrgByOrgId(CrccConstant.COMPANY_ORG_ID);
    }

    /**
     * 获取子分公司信息map
     * key code value name
     * @return
     */
    public Map<String, String> orgMap(){
        List<CrccOrg> crccOrgs = listOrg();
        Map<String, String> map = crccOrgs.stream().collect(Collectors.toMap(CrccOrg::getCode, CrccOrg::getName, (a, b) -> a));

        return map;
    }
}
