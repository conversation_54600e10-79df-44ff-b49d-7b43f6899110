package com.tenint.system.domain.vo;

import com.tenint.common.utils.StringUtils;
import lombok.Data;

import java.util.HashMap;

/**
 * 路由显示信息
 *
 * <AUTHOR>
 */
@Data
public class MetaVo {

    /**
     * 设置该路由在侧边栏和面包屑中展示的名字
     */
    private String title;

    /**
     * 设置该路由的图标，对应路径src/assets/icons/svg
     */
    private String icon;

    /**
     * 菜单名称右侧的额外图标
     */
//    private String extraIcon;

    /**
     * 设置为true，则不会被 <keep-alive>缓存
     */
    private boolean keepAlive;

    /**
     * 内嵌的`iframe`链接 `可选`
     */
    private String frameSrc;

    /**
     * `iframe`页是否开启首次加载动画（默认`true`）`可选
     */
    private boolean frameLoading;

    private boolean visible;

    /**
     * @description 当前路由动画效果
     * @see {@link https://next.router.vuejs.org/guide/advanced/transitions.html#transitions}
     * @see animate.css {@link https://animate.style}
     * name?: string;
     * 进场动画
     * enterTransition?:string;
     * 离场动画
     * leaveTransition?:string;
     */
    private HashMap<String,String> transition = new HashMap<>();

    /**
     * 是否不添加信息到标签页，（默认`false`）
     */
    private boolean hiddenTag;

    /**
     * 动态路由可打开的最大数量 `可选`
     */
   private Integer dynamicLevel;

    /**
     * 显示父菜单
     */
    private boolean showParent;

    /**
     * 内链地址（http(s)://开头）
     */
    private String link;

    public MetaVo(String title, String icon) {
        this.title = title;
        this.icon = icon;
    }

    public MetaVo(String title, String icon, boolean keepAlive) {
        this.title = title;
        this.icon = icon;
        this.keepAlive = keepAlive;
    }

    public MetaVo(String title, String icon, String link) {
        this.title = title;
        this.icon = icon;
        this.link = link;
    }

    public MetaVo(String title, String icon, boolean keepAlive, String link,boolean visible, boolean showParent) {
        this.title = title;
        this.icon = icon;
        this.keepAlive = keepAlive;
        this.showParent = showParent;
        this.visible = visible;
        if (StringUtils.ishttp(link)) {
            this.link = link;
        }
    }
}
