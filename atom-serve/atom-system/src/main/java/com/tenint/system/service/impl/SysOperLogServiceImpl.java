package com.tenint.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.event.OperLogEvent;
import com.tenint.common.utils.AddressUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.system.domain.SysLogininfor;
import com.tenint.system.domain.SysOperLog;
import com.tenint.system.mapper.SysOperLogMapper;
import com.tenint.system.mapper.SysUserMapper;
import com.tenint.system.service.ISysOperLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLog> implements ISysOperLogService {

    private final SysUserMapper userMapper;
    /**
     * 操作日志记录
     *
     * @param operLogEvent 操作日志事件
     */
    @Async
    @EventListener
    public void recordOper(OperLogEvent operLogEvent) {
        SysOperLog operLog = BeanUtil.toBean(operLogEvent, SysOperLog.class);
        // 远程查询操作地点
        operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
        operLog.setOperTime(new Date());
        save(operLog);
    }

    @Override
    public Page<SysOperLog> pageOperlog(SysOperLog operLog, PageQuery query) {
        Page<SysOperLog> page = page(query.build(), Wrappers.<SysOperLog>lambdaQuery()
                .like(StringUtils.isNotBlank(operLog.getTitle()), SysOperLog::getTitle, operLog.getTitle())
                .eq(ObjectUtil.isNotNull(operLog.getBusinessType()), SysOperLog::getBusinessType, operLog.getBusinessType())
                .eq(StringUtils.isNotBlank(operLog.getOperName()), SysOperLog::getOperName, operLog.getOperName())
                .eq(ObjectUtil.isNotNull(operLog.getStatus()), SysOperLog::getStatus, operLog.getStatus())
                .eq(StringUtils.isNotBlank(operLog.getOperIp()), SysOperLog::getOperIp, operLog.getOperIp())
                .ge(hasParams(operLog ,"beginTime"), SysOperLog::getOperTime, operLog.getParams().get("beginTime"))
                .le(hasParams(operLog ,"beginTime"), SysOperLog::getOperTime, operLog.getParams().get("endTime"))
                .orderByDesc(SysOperLog::getOperId)
        );
        setUserNames(page.getRecords());
        return page;
    }

    public boolean hasParams(SysOperLog operLog, String key) {
        if (operLog.getParams() == null) {
            return false;
        }
        return operLog.getParams().containsKey(key);
    }

    @Override
    public List<SysOperLog> listOperLog(SysOperLog sysOperLog) {
        LambdaQueryWrapper<SysOperLog> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(sysOperLog.getTitle()), SysOperLog::getTitle, sysOperLog.getTitle())
                .eq(sysOperLog.getBusinessType() != null, SysOperLog::getBusinessType, sysOperLog.getBusinessType())
                .eq(StringUtils.isNotBlank(sysOperLog.getOperName()), SysOperLog::getOperName, sysOperLog.getOperName())
                .eq(sysOperLog.getStatus() != null, SysOperLog::getStatus, sysOperLog.getStatus())
                .eq(StringUtils.isNotBlank(sysOperLog.getOperIp()), SysOperLog::getOperIp, sysOperLog.getOperIp())
                .ge(sysOperLog.getParams() != null, SysOperLog::getOperTime, sysOperLog.getParams().get("beginTime"))
                .le(sysOperLog.getParams() != null, SysOperLog::getOperTime, sysOperLog.getParams().get("endTime"))
                .orderByDesc(SysOperLog::getOperId);
        return list(wrapper);
    }

    public void setUserNames(List<SysOperLog> inforList) {
        if (ObjectUtil.isEmpty(inforList)) {
            return;
        }
        Set<String> userNames = inforList.stream().map(SysOperLog::getOperName).collect(Collectors.toSet());

        Map<String, String> nickNameMap =  userMapper.getNickNameMapByUsername(userNames, 100);

        inforList.forEach(infor -> infor.setOperName(nickNameMap.get(infor.getOperName())));
    }
}
