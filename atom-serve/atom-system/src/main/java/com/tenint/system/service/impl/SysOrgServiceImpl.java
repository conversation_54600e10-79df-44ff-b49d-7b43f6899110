package com.tenint.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.constant.CacheConstants;
import com.tenint.common.core.domain.dto.RoleDTO;
import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysRole;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.vo.SysOrgTreeVo;
import com.tenint.common.core.service.OrgService;
import com.tenint.common.enums.DataScopeType;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.redis.RedisUtils;
import com.tenint.common.utils.spring.SpringUtils;
import com.tenint.crcc.convert.SysOrgConvert;
import com.tenint.crcc.convert.SysUserConvert;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.domain.CrccPositionSimple;
import com.tenint.crcc.domain.CrccUser;
import com.tenint.crcc.domain.CrccUserSearch;
import com.tenint.crcc.facade.HrApiFacade;
import com.tenint.system.domain.SysRoleOrg;
import com.tenint.system.domain.bo.SearchUserOrgBo;
import com.tenint.system.domain.bo.SysOrgNodeParentPair;
import com.tenint.system.domain.vo.SysOrgSearchVo;
import com.tenint.system.domain.vo.SysUserSearchVo;
import com.tenint.system.mapper.SysRoleMapper;
import com.tenint.system.mapper.SysRoleOrgMapper;
import com.tenint.system.service.ISysOrgService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

import static com.tenint.common.constant.UserConstants.ORG_ROOT_ID;

/**
 * 机构管理 服务实现
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysOrgServiceImpl implements ISysOrgService, OrgService {

    private final HrApiFacade hrApiFacade;

    private final SysRoleMapper roleMapper;

    private final SysRoleOrgMapper roleOrgMapper;



    public CrccOrg getCrccOrgTreeList(SysOrg org) {
        String providerId = LoginHelper.getProviderId();
        Long rootOrgId = getRootOrgId(LoginHelper.getOrgId());


//
//        if (LoginHelper.isAdmin()) {
//            rootOrgId = 1L;
//        } else {
//            // 全部数据权限
//            boolean allScope = false;
//            // 当前机构数据权限
//            boolean onlyOrgScope = false;
//            // 当前机构及下级数据权限
//            boolean orgAndChildScope = false;
//            // 自定义数据权限
//            boolean customScope = false;
//            // 仅本人数据权限
//            boolean selfScope = false;
//
//            boolean companyScope = false;
//
//            // 权限范围验证
//            List<RoleDTO> roles = LoginHelper.getLoginUser().getRoles();
//            for (RoleDTO role : roles) {
//                DataScopeType code = DataScopeType.findCode(role.getDataScope());
//                if (code == DataScopeType.ALL) {
//                    allScope = true;
//                    break;
//                } else if (code == DataScopeType.ORG) {
//                    onlyOrgScope = true;
//                } else if (code == DataScopeType.ORG_AND_CHILD) {
//                    orgAndChildScope = true;
//                } else if (code == DataScopeType.COMPANY) {
//                    companyScope = true;
//                } else if (code == DataScopeType.CUSTOM) {
//                    customScope = true;
//                } else if (code == DataScopeType.SELF) {
//                    selfScope = true;
//                }
//            }
//
//            if (!allScope) {
//                if (companyScope) {
//
//                }
//
//                if (onlyOrgScope || selfScope) {
//                    return hrApiFacade.getCompanyByOrgId(LoginHelper.getProviderId(),LoginHelper.getOrgId());
//                }
//                rootOrgId = LoginHelper.getOrgId();
//            } else {
//                rootOrgId = 1L;
//            }
//        }


        List<CrccOrg> crccOrgs = hrApiFacade.listChildrenOrgByOrgId(providerId, rootOrgId);
        CrccOrg currenOrg = hrApiFacade.listOrgPathByOrgId(LoginHelper.getProviderId(),rootOrgId).get(0);
        if(currenOrg.getType() == 3) {
            return currenOrg;
        }
        currenOrg.setChildren(new ArrayList<>());
        if (ObjectUtil.isNotEmpty(crccOrgs)) {

            ForkJoinPool customThreadPool = new ForkJoinPool(3);
            try {
                customThreadPool.submit( () ->
                        crccOrgs.parallelStream().forEach((c) -> {
                            if (c.getType() == 1) {
                                CrccOrg crccOrg = hrApiFacade.treeOrgByProviderAndCompanyId(providerId, c.getId());
                                if (ObjectUtil.isNotEmpty(crccOrg)) {
                                    currenOrg.getChildren().add(crccOrg);
                                }
                            }
                        })
                ).get();
            } catch (Exception e) {
                throw new ServiceException("查询异常");
            }
        }
        return currenOrg;
    }
    /**
     * 查询机构树结构信息
     *
     * @param org 机构信息
     * @return 机构树信息集合
     */
    @Override
    public SysOrgTreeVo getOrgTreeList(SysOrg org, Integer removeTag) {
        CrccOrg crccOrgTreeList = getCrccOrgTreeList(org);
        removeTypeNodes(crccOrgTreeList, removeTag);
        return SysOrgConvert.INSTANCE.toTargetTreeVo(crccOrgTreeList);
    }

    public void removeTypeNodes(CrccOrg crccOrg, int removeType) {
        if (crccOrg == null) {
            return;
        }

        List<CrccOrg> children = crccOrg.getChildren();
        if (children == null) {
            return;
        }
        Iterator<CrccOrg> iterator = children.iterator();
        while (iterator.hasNext()) {
            CrccOrg child = iterator.next();
            if(child!=null){
                if (child.getType() == removeType) {
                    iterator.remove();
                } else {
                    removeTypeNodes(child, removeType);
                }
            }
        }
    }


    /**
     * 根据角色ID查询机构树信息
     *
     * @param roleId 角色ID
     * @return 选中机构列表
     */
    @Override
    public List<Long> listOrgByRoleId(Long roleId, SysOrgTreeVo treeVo) {

        List<Long> orgIds = roleOrgMapper.selectList(Wrappers.<SysRoleOrg>lambdaQuery().select(SysRoleOrg::getOrgId)
                .eq(SysRoleOrg::getRoleId, roleId)).stream().map(SysRoleOrg::getOrgId).collect(Collectors.toList());

        SysRole role = roleMapper.selectById(roleId);

        if (role.getOrgCheckStrictly()) {
            // 创建并填充parentIdMap
            Map<Long, SysOrgTreeVo> nodeMap = new HashMap<>();
            setParentNodes(treeVo, nodeMap);

            Set<Long> parentIds = new HashSet<>();
            for (SysOrgTreeVo node : nodeMap.values()) {
                parentIds.addAll(getAncestorIds(node, nodeMap));
            }
            return orgIds.stream().filter( id -> !parentIds.contains(id)).collect(Collectors.toList());
        }
        return orgIds;

    }

    private void setParentNodes(SysOrgTreeVo root, Map<Long, SysOrgTreeVo> nodeMap) {
        Deque<SysOrgTreeVo> stack = new ArrayDeque<>();
        stack.push(root);

        while (!stack.isEmpty()) {
            SysOrgTreeVo node = stack.pop();

            // 将当前节点添加到 nodeMap 中
            nodeMap.put(node.getId(), node);

            List<SysOrgTreeVo> children = node.getChildren();
            if (children != null && !children.isEmpty()) {
                for (SysOrgTreeVo child : children) {
                    child.setParentId(node.getId());
                    stack.push(child);
                }
            }
        }
    }

    public  List<Long> getAncestorIds(SysOrgTreeVo node, Map<Long, SysOrgTreeVo> nodeMap) {
        List<Long> ancestorIds = new ArrayList<>();
        while (node != null) {
            Long parentId = node.getParentId();
            if (parentId != null) {
                ancestorIds.add(parentId);
                node = nodeMap.get(parentId);
            } else {
                node = null;
            }
        }
        return ancestorIds;
    }



    /**
     * 根据机构ID查询信息
     *
     * @param orgId 机构ID
     * @return 机构信息
     */
    @Override
    public SysOrg getOrgById(Long orgId) {
        String providerId = LoginHelper.getProviderId();

        CrccOrg crccOrg = hrApiFacade.getCompanyByOrgId(providerId, orgId);
        if (ObjectUtil.isNull(crccOrg)) {
            List<CrccOrg> crccOrgs = hrApiFacade.listOrgPathByOrgId(providerId, orgId);
            if (ObjectUtil.isNotEmpty(crccOrgs)) {
                crccOrg = crccOrgs.get(0);
            }
        }
        if (ObjectUtil.isNull(crccOrg)) {
            return null;
        }
        return SysOrgConvert.INSTANCE.toTarget(crccOrg);
    }


    @Override
    public List<SysOrgTreeVo> listOrgByParentId(Long parentId) {

        List<CrccOrg> orgPath = hrApiFacade.listOrgPathByOrgId(LoginHelper.getProviderId(), LoginHelper.getOrgId());
        if (CollectionUtils.isEmpty(orgPath)) {
            return Collections.emptyList();
        }
        CrccOrg currentUserOrg = orgPath.get(0);
        if (!LoginHelper.isAdmin()) {
            // 全部数据权限
            boolean allScope = false;
            // 当前机构数据权限
            boolean onlyOrgScope = false;
            // 当前机构及下级数据权限
            boolean orgAndChildScope = false;
            // 自定义数据权限
            boolean customScope = false;
            // 仅本人数据权限
            boolean selfScope = false;

            // 公司级权限
            boolean companyScope = false;

            // 权限范围验证
            List<RoleDTO> roles = LoginHelper.getLoginUser().getRoles();
            for (RoleDTO role : roles) {
                DataScopeType code = DataScopeType.findCode(role.getDataScope());
                if (code == DataScopeType.ALL) {
                    allScope = true;
                    break;
                } else if (code == DataScopeType.ORG) {
                    onlyOrgScope = true;
                } else if (code == DataScopeType.ORG_AND_CHILD) {
                    orgAndChildScope = true;
                } else if (code == DataScopeType.COMPANY) {
                    companyScope = true;
                }
                else if (code == DataScopeType.CUSTOM) {
                    customScope = true;
                } else if (code == DataScopeType.SELF) {
                    selfScope = true;
                }
            }

            if (!allScope) {

                if (parentId == 0) {
                    if (companyScope) {
                        String currentUserCompanyCode = StringUtils.substring(LoginHelper.getOrgCode(), 0, 15);
                        Map<String, Long> orgCodeIdMap = getOrgCodeIdMap();
                        Long id = orgCodeIdMap.get(currentUserCompanyCode);
                        currentUserOrg = hrApiFacade.getCompanyByOrgId(LoginHelper.getProviderId(), id);
                    }
                    return Collections.singletonList(SysOrgConvert.INSTANCE.toTargetTreeVo(currentUserOrg));
                }
                // 获取当前选择的机构信息
                List<CrccOrg> selectedCompanyByOrgIdPath = hrApiFacade.listOrgPathByOrgId(LoginHelper.getProviderId(), parentId);
                CrccOrg selectedCompanyByOrgId = selectedCompanyByOrgIdPath.get(0);
                boolean isChildNode = selectedCompanyByOrgId.getCode().startsWith(LoginHelper.getOrgCode());
                if (orgAndChildScope) {
                    if (isChildNode) {
                        List<CrccOrg> childrenList = hrApiFacade.listChildrenOrgByOrgId(LoginHelper.getProviderId(), parentId);
                        return SysOrgConvert.INSTANCE.toTargetTreeVo(childrenList);
                    }
                }
                // 自定义数据权限，需要查询角色关联的菜单
                if (customScope) {
                    List<Long> orgIds = roleOrgMapper.selectList(Wrappers.<SysRoleOrg>lambdaQuery().eq(SysRoleOrg::getRoleId, LoginHelper.getLoginUser().getRoleId())).stream()
                            .map(SysRoleOrg::getOrgId)
                            .toList();
                    if (orgIds.contains(parentId)) {
                        List<CrccOrg> childrenList = hrApiFacade.listChildrenOrgByOrgId(LoginHelper.getProviderId(), parentId);
                        return SysOrgConvert.INSTANCE.toTargetTreeVo(childrenList);
                    }
                }

                // 公司级权限
                if (companyScope) {
                    String currentUserCompanyCode = StringUtils.substring(LoginHelper.getOrgCode(), 0, 15);
                    String selectCompanyCode = StringUtils.substring(selectedCompanyByOrgId.getCode(), 0, 15);
                    if (currentUserCompanyCode.equals(selectCompanyCode)) {
                        List<CrccOrg> childrenList = hrApiFacade.listChildrenOrgByOrgId(LoginHelper.getProviderId(), parentId);
                        return SysOrgConvert.INSTANCE.toTargetTreeVo(childrenList);
                    }
                }

                if (onlyOrgScope || selfScope) {
                    List<CrccOrg> crccOrgs = hrApiFacade.listOrgPathByOrgId(LoginHelper.getProviderId(), LoginHelper.getOrgId());
                    return Collections.singletonList(SysOrgConvert.INSTANCE.toTargetTreeVo(crccOrgs.get(0))) ;
                }

            }
        }

        if (parentId == 0) {
            CrccOrg rootOrg = hrApiFacade.getCompanyByOrgId(LoginHelper.getProviderId(), ORG_ROOT_ID);
            return Collections.singletonList(SysOrgConvert.INSTANCE.toTargetTreeVo(rootOrg));
        }

        List<CrccOrg> childrenList = hrApiFacade.listChildrenOrgByOrgId(LoginHelper.getProviderId(), parentId);
        if (childrenList == null || childrenList.isEmpty()) {
            return Collections.emptyList();
        }

        return SysOrgConvert.INSTANCE.toTargetTreeVo(childrenList);
    }

    @Override
    public List<SysOrgTreeVo> listOrgByParentIdExcludePosition(Long parentId) {
        List<SysOrgTreeVo> sysOrgTreeVos = listOrgByParentId(parentId);
        sysOrgTreeVos =  sysOrgTreeVos.stream().filter(org -> org.getType() != 3).collect(Collectors.toList());
        return sysOrgTreeVos;
    }

    @Override
    public CrccOrg getCompanyByOrgId(String providerId, Long orgId) {
        return hrApiFacade.getCompanyByOrgId(providerId, orgId);
    }

    @Override
    public List<SysOrg> listOrgByIds(List<Long> list) {

        if (ObjectUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        list.removeIf(Objects::isNull);

        if (list.size() == 0) {
            return Collections.emptyList();
        }
        list = list.stream().distinct().toList();

        List<SysOrg> orgList = new  ArrayList<>();
        String providerId = LoginHelper.getProviderId();
        if (list.size() > 5) {
            ForkJoinPool customThreadPool = new ForkJoinPool(5);
            try {
                List<Long> finalList = list;
                customThreadPool.submit( () ->
                        finalList.parallelStream().forEach((id) -> {
                            CrccOrg crccOrg = hrApiFacade.getCompanyByOrgId(providerId, id);
                            if (ObjectUtil.isNotNull(crccOrg)) {
                                SysOrg org = SysOrgConvert.INSTANCE.toTarget(crccOrg);
                                orgList.add(org);
                            }
                        })
                ).get();
            } catch (Exception e) {
                throw new ServiceException("查询异常");
            }
        } else {
            for (Long id : list) {
                CrccOrg crccOrg = hrApiFacade.getCompanyByOrgId(providerId, id);
                if (ObjectUtil.isNotNull(crccOrg)) {
                    SysOrg org = SysOrgConvert.INSTANCE.toTarget(crccOrg);
                    orgList.add(org);
                }
            }
        }

        return orgList;
    }

    @Override
    public List<SysOrg> treeToList(CrccOrg treeVo) {
        if (treeVo == null) {
            return Collections.emptyList();
        }
        List<SysOrg> result = new ArrayList<>();
        ArrayDeque<CrccOrg> deque = new ArrayDeque<>();
        //根节点
        deque.offer(treeVo);
        SysOrg orgNode = SysOrgConvert.INSTANCE.toTarget(treeVo);
        result.add(orgNode);
        while (!deque.isEmpty()){
            CrccOrg node = deque.pop();
            if(node.getChildren() == null){
                continue;
            }
            node.getChildren().forEach(tree->{
                deque.offer(tree);
                SysOrg org = SysOrgConvert.INSTANCE.toTarget(tree);
                org.setParentId(node.getId());
                result.add(org);
            });
        }
        return result;
    }

    @Override
    public List<SysUser> listUserByOrgId(Long orgId) {
        List<SysUser> userList = new ArrayList<>();
        String providerId = LoginHelper.getProviderId();
        List<CrccUser> crccUserList = hrApiFacade.listUserByPositionId(providerId, orgId.toString());
        if (CollectionUtil.isEmpty(crccUserList)) {
            return userList;
        }
        crccUserList.forEach(crccUser -> {
            SysUser user = SysUserConvert.INSTANCE.convert(crccUser);
            userList.add(user);
        });
        return userList;
    }

    @Override
    public List<SysOrg> listCompany() {
        SysUser admin = LoginHelper.getAdminUser();
        CrccOrg rootOrg = hrApiFacade.getCompanyByOrgId(admin.getProviderId(),admin.getOrgId());
        if(rootOrg == null) {
            throw new ServiceException("系统获取组织机构失败");
        }
        CrccOrg crccOrg = hrApiFacade.treeOrgByProviderAndCompanyId(admin.getProviderId(),rootOrg.getId());
        removeTypeNodes(crccOrg, 3);
        if(crccOrg == null) {
            return Collections.emptyList();
        }
        return treeToList(crccOrg);
    }

    @Override
    public List<SysOrg> listOrg() {
        CrccOrg crccOrg = hrApiFacade.treeOrgByProviderAndCompanyId(LoginHelper.getProviderId(),LoginHelper.getOrgId());
        removeTypeNodes(crccOrg, 2);
        if(crccOrg == null) {
            return Collections.emptyList();
        }
        return treeToList(crccOrg);
    }

    @Override
    public List<SysOrgSearchVo> searchOrgName(String orgName) {
        String key = "SYS_ORG:SEARCH:" + LoginHelper.getOrgCode();
        List<SysOrgSearchVo> cacheList = RedisUtils.getCacheList(key);
        if (CollectionUtil.isNotEmpty(cacheList)) {
            return cacheList.stream().filter(org -> org.getName().contains(orgName)).collect(Collectors.toList());
        }
        CrccOrg crccOrgTreeList = getCrccOrgTreeList(new SysOrg());
        if (ObjectUtil.isEmpty(crccOrgTreeList)) {
            return Collections.emptyList();
        }
        List<SysOrg> orgList = treeToList(crccOrgTreeList);
        List<SysOrgSearchVo> searchVo = orgList.stream().map(org -> {
            SysOrgSearchVo sysOrgSearchVo = new SysOrgSearchVo();
            sysOrgSearchVo.setCode(org.getCode());
            sysOrgSearchVo.setName(org.getName());
            return sysOrgSearchVo;
        }).toList();

        RedisUtils.setCacheList(key, searchVo);
        RedisUtils.expire(key, 60);
        return searchVo.stream().filter(org -> org.getName().contains(orgName)).collect(Collectors.toList());
    }

    public void removeEmptyUserNodesNonRecursive(CrccOrg root) {

        Deque<SysOrgNodeParentPair> stack = new ArrayDeque<>();
        Deque<SysOrgNodeParentPair> postOrder = new ArrayDeque<>();
        stack.push(new SysOrgNodeParentPair(root, null));

        while (!stack.isEmpty()) {
            SysOrgNodeParentPair nodeParentPair = stack.pop();
            CrccOrg node = nodeParentPair.getNode();
            postOrder.push(nodeParentPair);

            if (node.getChildren() != null) {
                for (CrccOrg child : node.getChildren()) {
                    stack.push(new SysOrgNodeParentPair(child, node));
                }
            }
        }

        while (!postOrder.isEmpty()) {
            SysOrgNodeParentPair nodeParentPair = postOrder.pop();
            CrccOrg node = nodeParentPair.getNode();
            CrccOrg parent = nodeParentPair.getParent();

            if ((node.getUsers() == null || node.getUsers().isEmpty())
                    && (node.getChildren() == null || node.getChildren().isEmpty())) {
                if (parent != null) {
                    parent.getChildren().remove(node);
                }
            }
        }
    }

    @Override
    public List<SysUserSearchVo> searchUser(Long orgId, String username) {

        Long rootOrgId = getRootOrgId(orgId);
        List<SysUserSearchVo> list = new ArrayList<>();

        String key = "org_search_user";

        Map<String, SearchUserOrgBo> cacheObject = RedisUtils.getCacheObject(key);
        if (ObjectUtil.isEmpty(cacheObject)) {
            CrccOrg crccOrg = hrApiFacade.treeOrgAndUserByCompanyId("crcc24", 1L);
            if (Objects.isNull(crccOrg)) {
                return Collections.emptyList();
            }
            crccOrg.setParentId(rootOrgId);
            List<SysOrg> orgList = treeToList(crccOrg);
            if (CollectionUtil.isEmpty(orgList)) {
                return Collections.emptyList();
            }

            removeEmptyUserNodesNonRecursive(crccOrg);
            List<SysOrg> filterOrgList = treeToList(crccOrg);
            cacheObject = filterOrgList.stream().map( item -> new SearchUserOrgBo(item.getId().toString(),item.getParentId().toString(), item.getName()))
                    .collect(Collectors.toMap(SearchUserOrgBo::getId, org -> org, (k1, k2) -> k1));
            RedisUtils.setCacheObject(key, cacheObject);
            RedisUtils.expire(key, Duration.ofMinutes(2));
        }


        List<CrccUserSearch> users = hrApiFacade.searchUser(LoginHelper.getProviderId(), rootOrgId, username, "true");
        Map<String, SearchUserOrgBo> finalCacheObject = cacheObject;
        // 101406为退休人员 可以参考hr api文档
        users.stream().filter(item -> !item.getUser().getPositionStatus().equals(101406)).forEach(user -> {
            CrccPositionSimple crccPositionSimple = user.getPositions().get(0);
            Long id = crccPositionSimple.getId();
            SearchUserOrgBo currentOrg = finalCacheObject.get(id.toString());
            StringBuilder orgName = new StringBuilder(currentOrg.getName());
            // 如果父节点编号不等于根节点，那么向orgName前面拼接父节点名称
            while (!Objects.equals(currentOrg.getParentId(), rootOrgId.toString())) {
                currentOrg = finalCacheObject.get(currentOrg.getParentId());
                orgName.insert(0, currentOrg.getName() + "/");
            }
            SysUserSearchVo userSearchVo = new SysUserSearchVo();
            userSearchVo.setOrgId(crccPositionSimple.getId());
            userSearchVo.setId(user.getUser().getId());
            userSearchVo.setName(user.getUser().getName());
            userSearchVo.setOrgName(orgName.toString());
            list.add(userSearchVo);
        });

        return list;
    }

    /**
     * 通过机构ID查询机构名称
     *
     * @param orgIds 机构ID串逗号分隔
     * @return 机构名称串逗号分隔
     */
    @Override
    public String selectOrgNameByIds(String orgIds) {
        List<String> list = new ArrayList<>();
        for (Long id : StringUtils.splitTo(orgIds, Convert::toLong)) {
            SysOrg org = SpringUtils.getAopProxy(this).getOrgById(id);
            if (ObjectUtil.isNotNull(org)) {
                list.add(org.getName());
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    @Override
    public Long selectOrgIdByCode(String orgCode) {

        return null;
    }


    private Long getRootOrgId(Long orgId) {

        Long rootOrgId;

        if (!LoginHelper.isAdmin()) {
            // 全部数据权限
            boolean allScope = false;
            // 当前机构数据权限
            boolean onlyOrgScope = false;
            // 当前机构及下级数据权限
            boolean orgAndChildScope = false;
            // 自定义数据权限
            boolean customScope = false;
            // 仅本人数据权限
            boolean selfScope = false;

            boolean companyScope = false;

            // 权限范围验证
            List<RoleDTO> roles = LoginHelper.getLoginUser().getRoles();
            for (RoleDTO role : roles) {
                DataScopeType code = DataScopeType.findCode(role.getDataScope());
                if (code == DataScopeType.ALL) {
                    allScope = true;
                    break;
                } else if (code == DataScopeType.ORG) {
                    onlyOrgScope = true;
                } else if (code == DataScopeType.ORG_AND_CHILD) {
                    orgAndChildScope = true;
                } else if (code == DataScopeType.CUSTOM) {
                    customScope = true;
                } else if (code == DataScopeType.SELF) {
                    selfScope = true;
                } else if (code == DataScopeType.COMPANY) {
                    companyScope = true;
                }
            }

            if (!allScope) {
                if (companyScope) {
                    // 判断那个机构的权限大
                    String orgCode = StringUtils.substring(LoginHelper.getOrgCode(), 0, 15);
                    rootOrgId = getOrgCodeIdMap().get(orgCode);
                    return rootOrgId;
                } else if (onlyOrgScope || selfScope) {
                    if (orgId == 0) {
                        return LoginHelper.getOrgId();
                    }
                    return orgId;
                }

                rootOrgId = LoginHelper.getOrgId();
            } else {
                rootOrgId = 1L;
            }
        } else {
            rootOrgId = 1L;
        }

        return rootOrgId;
    }


    /**
     * 根据机构编号获取子机构信息
     *
     * @param orgId org id
     * @return {@link List}<{@link CrccOrg}>
     */
    public List<CrccOrg> listChildrenCrccOrgByOrgId(Long orgId) {
       return hrApiFacade.listChildrenOrgByOrgId("crcc24", orgId).stream().sorted(Comparator.comparing(CrccOrg::getOrder)).collect(Collectors.toList());
    }

    @Override
    public void loadingOrgCodeIdMapper() {
        RedisUtils.deleteObject(CacheConstants.ORG_CODE_ID_MAPPER);
        CrccOrg crccOrg = hrApiFacade.treeOrgByProviderAndCompanyId("crcc24", 1L);
        List<SysOrg> sysOrgs = treeToList(crccOrg);
        Map<String, Long> orgCodeIdMapper = sysOrgs.stream().collect(Collectors.toMap(SysOrg::getCode, SysOrg::getId));
        RedisUtils.setCacheObject(CacheConstants.ORG_CODE_ID_MAPPER, orgCodeIdMapper);
    }

    private Map<String, Long> getOrgCodeIdMap() {
        Map<String, Long> map = RedisUtils.getCacheObject(CacheConstants.ORG_CODE_ID_MAPPER);
        if (ObjectUtil.isEmpty(map)) {
            return Collections.emptyMap();
        }
        return map;
    }
}
