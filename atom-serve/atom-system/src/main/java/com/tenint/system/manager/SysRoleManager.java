package com.tenint.system.manager;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.constant.CacheConstants;
import com.tenint.common.constant.ConfigConstants;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.R;
import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysRole;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.model.LoginUser;
import com.tenint.common.core.domain.vo.SysOrgTreeVo;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StringUtils;
import com.tenint.system.domain.SysRoleOrg;
import com.tenint.system.domain.SysUserRole;
import com.tenint.system.domain.bo.SysRoleManagedBo;
import com.tenint.system.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 系统角色 综合服务处理
 *
 * <AUTHOR>
 * @date 2023/05/14
 */
@RequiredArgsConstructor
@Service
public class SysRoleManager {

    private final ISysRoleService roleService;

    private final ISysUserService userService;

    private final ISysOrgService orgService;

    private final ISysRoleMenuService roleMenuService;

    private final ISysUserRoleService userRoleService;

    private final ISysConfigService configService;

    private final ISysRoleOrgService roleOrgService;

    /**
     * 获取角色信息列表
     */
    public Page<SysRole> pageRole(SysRole role, PageQuery pageQuery) {
        return roleService.pageRole(role, pageQuery);
    }


    /**
     * 获取角色信息列表
     */
    public List<SysRole> listRole(SysRole role) {
        return roleService.listRole(role);
    }

    /**
     * 根据角色编号获取详细信息
     *
     * @param roleId 角色ID
     */
    public SysRole getRoleById(Long roleId) {
        return roleService.getRoleById(roleId);
    }

    public void checkRoleDataScope(Long roleId) {
        roleService.checkRoleDataScope(roleId);
    }

    /**
     * 新增角色
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveRole(SysRole role) {
        if (!roleService.checkRoleNameUnique(role)) {
            throw new ServiceException("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (!roleService.checkRoleKeyUnique(role)) {
            throw new ServiceException("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        roleService.save(role);
        return saveRoleMenu(role);

    }

    /**
     * 修改保存角色
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(SysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        if (!roleService.checkRoleNameUnique(role)) {
            throw new ServiceException("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (!roleService.checkRoleKeyUnique(role)) {
            throw new ServiceException("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }

        roleService.updateById(role);
        roleMenuService.deleteRoleMenuByRoleId(role.getRoleId());
        if (saveRoleMenu(role) > 0) {
            List<String> keys = StpUtil.searchTokenValue("", 0, -1, false);
            if (CollUtil.isEmpty(keys)) {
                return;
            }
            // 角色关联的在线用户量过大会导致redis阻塞卡顿 谨慎操作
            keys.parallelStream().forEach(key -> {
                String token = key.replace(CacheConstants.LOGIN_TOKEN_KEY, "");
                // 如果已经过期则跳过
                if (StpUtil.stpLogic.getTokenActivityTimeoutByToken(token) < -1) {
                    return;
                }
                LoginUser loginUser = LoginHelper.getLoginUser(token);
                if (loginUser.getRoles() != null && loginUser.getRoles().stream().anyMatch(r -> r.getRoleId().equals(role.getRoleId()))) {
                    try {
                        StpUtil.logoutByTokenValue(token);
                    } catch (NotLoginException ignored) {
                    }
                }
            });
            return;
        }
        throw new ServiceException("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
    }

    /**
     * 新增角色机构信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleOrg(SysRole role) {
        int rows = 1;
        // 新增角色与机构（数据权限）管理
        List<SysRoleOrg> list = new ArrayList<SysRoleOrg>();
        for (Long orgId : role.getOrgIds()) {
            SysRoleOrg rd = new SysRoleOrg();
            rd.setRoleId(role.getRoleId());
            rd.setOrgId(orgId);
            list.add(rd);
        }
        if (list.size() > 0) {
            rows = roleOrgService.saveBatch(list) ? list.size() : 0;
        }
        return rows;
    }


    /**
     * 修改保存数据权限
     */

    public int dataScope(SysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        return updateAuthDataScope(role);
    }

    /**
     * 状态修改
     */
    public int changeStatus(SysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        return roleService.updateRoleStatus(role);
    }

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateAuthDataScope(SysRole role) {
        // 修改角色信息
        roleService.updateById(role);
        // 删除角色与机构关联
        roleOrgService.deleteRoleOrgByRoleId(role.getRoleId());
        // 新增角色和机构信息（数据权限）
        return insertRoleOrg(role);
    }


    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleById(Long roleId) {
        // 删除角色与菜单关联
        roleMenuService.deleteRoleMenuByRoleId(roleId);
        // 删除角色与机构关联
        roleOrgService.deleteRoleOrgByRoleId(roleId);
        return roleService.removeById(roleId);
    }


    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleByIds(Long[] roleIds) {
        for (Long roleId : roleIds) {
            roleService.checkRoleAllowed(new SysRole(roleId));
            roleService.checkRoleDataScope(roleId);
            SysRole role = roleService.getById(roleId);
            if (userRoleService.countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        List<Long> ids = Arrays.asList(roleIds);
        // 删除角色与菜单关联
        roleMenuService.deleteRoleMenuByRoleIds(ids);
        // 删除角色与机构关联
        roleOrgService.deleteRoleOrgByRoleIds(ids);
        return roleService.removeBatchByIds(ids);

    }


    /**
     * 获取角色选择框列表
     */
    public R<List<SysRole>> optionselect() {
        return R.ok(roleService.list());
    }

    /**
     * 查询已分配用户角色列表
     */
    public Page<SysUser> listUserByAllocated(SysUser user, PageQuery pageQuery) {
        return userService.listAllocatedUser(user, pageQuery);
    }

    /**
     * 查询未分配用户角色列表
     */
    public Page<SysUser> pageUnallocatedUser(SysUser user, PageQuery pageQuery) {
        List<Long> userIds = userRoleService.listUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.is_active", UserConstants.ACTIVE)
                .and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id"))
                .notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds)
                .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
                .like(StringUtils.isNotBlank(user.getMobile()), "u.mobile", user.getMobile());
        return userService.pageUnallocatedUser(pageQuery.build(), wrapper);
    }

    /**
     * 取消授权用户
     *
     * @param userRole 用户和角色关联信息
     */
    public boolean deleteAuthUser(SysUserRole userRole) {
        return userRoleService.removeUserRole(userRole);
    }

    /**
     * 批量取消授权用户
     *
     * @param roleId  角色ID
     * @param userIds 用户ID串
     */
    public boolean deleteAuthUsers(Long roleId, Long[] userIds) {
        return userRoleService.removeUserRoles(roleId, userIds);
    }

    /**
     * 批量选择用户授权
     *
     * @param roleId  角色ID
     * @param userIds 用户ID串
     */
    public void updateRolesByUsers(Long roleId, Long[] userIds) {
        roleService.checkRoleDataScope(roleId);
        saveAuthUsers(roleId, userIds);
    }

    /**
     * 获取对应角色机构树列表
     *
     * @param roleId 角色ID
     */
    public Map<String, Object> getOrgTreeMapByRoleId(Long roleId) {
        Map<String, Object> ajax = new HashMap<>();
        SysOrgTreeVo orgTree = orgService.getOrgTreeList(new SysOrg(), 3);
        ajax.put("checkedKeys", orgService.listOrgByRoleId(roleId, orgTree));
        ajax.put("orgs", orgTree);
        return ajax;

    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    public int saveAuthUsers(Long roleId, Long[] userIds) {
        return userRoleService.saveAuthUsers(roleId, userIds);
    }


    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int saveRoleMenu(SysRole role) {
        return roleMenuService.saveRoleMenu(role);
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    public void checkRoleAllowed(SysRole role) {
        roleService.checkRoleAllowed(role);
    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    public int updateRoleStatus(SysRole role) {
        return roleService.updateRoleStatus(role);
    }


    public boolean isMainLeader() {
        return roleService.hasRoleWithSelf(ConfigConstants.HOME_CALENDAR_LEADER, "main_leader");
    }

    /**
     * 查询所管理的角色列表
     *
     * @param roleId 角色id
     * @return {@link List}<{@link Long}>
     */
    public List<Long> listManagedRoleIdByRoleId(Long roleId) {
        return roleService.listManagedRoleIdByRoleId(roleId);
    }

    public boolean saveOrUpdateRoleManaged(Long roleId, ArrayList<Long> managedRoleIds) {
       return roleService.saveOrUpdateRoleManaged(roleId, managedRoleIds);
    }
}
