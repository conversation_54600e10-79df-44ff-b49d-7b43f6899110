package com.tenint.system.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysRole;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 角色表 interface
 */
public interface ISysRoleService extends IService<SysRole> {


    Page<SysRole> pageRole(SysRole role, PageQuery pageQuery);

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    List<SysRole> listRole(SysRole role);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> listRoleByUserId(Long userId);

    /**
     * 根据用户ID查询角色权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> listRolePermissionByUserId(Long userId);


    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    SysRole getRoleById(Long roleId);

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    boolean checkRoleNameUnique(SysRole role);

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    boolean checkRoleKeyUnique(SysRole role);

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    void checkRoleAllowed(SysRole role);

    /**
     * 校验角色是否有数据权限
     *
     * @param roleId 角色id
     */
    void checkRoleDataScope(Long roleId);


    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    int updateRoleStatus(SysRole role);

    /**
     * 获取角色名
     *
     * @param userId 用户id
     * @return {@link String}
     */
    String getRoleNamesByUserId(Long userId);

    /**
     * 根据key查询role
     *
     * @param roleKey key
     * @return {@link SysRole}
     */
    SysRole getRoleByKey(String roleKey);

    /**
     * 是否有集团文化部权限
     *
     * @return boolean
     */
    boolean hasGroupPowerRoles();

    /**
     * 是否有公司审核权限
     *
     * @return boolean
     */
    boolean hasCompanyPowerRoles();

    /**
     * 当前用户是否有相应角色， 从配置表获取
     *
     * @param configKey      配置关键
     * @param defaultRoleKey 默认角色关键
     * @return boolean
     */
    boolean hasRoleWithSelf(String configKey, String defaultRoleKey);

    /**
     * 查询所管理的角色列表
     *
     * @param roleId 角色id
     * @return {@link List}<{@link Long}>
     */
    List<Long> listManagedRoleIdByRoleId(Long roleId);

    /**
     * 保存或更新角色管理
     *
     * @param roleId         角色id
     * @param managedRoleIds 托管角色id
     * @return boolean
     */
    boolean saveOrUpdateRoleManaged(Long roleId, ArrayList<Long> managedRoleIds);
}
