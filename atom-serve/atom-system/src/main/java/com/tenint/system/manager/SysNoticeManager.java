package com.tenint.system.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.system.domain.SysNotice;
import com.tenint.system.service.ISysNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@RequiredArgsConstructor
@Service
public class SysNoticeManager {

    private final ISysNoticeService noticeService;

    /**
     * 分页获取通知
     *
     * @param notice    请注意
     * @param pageQuery 页面查询
     * @return {@link TableDataInfo}<{@link SysNotice}>
     */
    public TableDataInfo<SysNotice> pageNotice(SysNotice notice, PageQuery pageQuery) {
        pageQuery.setOrderByColumn("create_time");
        pageQuery.setIsAsc("desc");
        Page<SysNotice> sysNoticePage = noticeService.pageNotice(notice, pageQuery);
        return TableDataInfo.build(sysNoticePage);
    }


    /**
     * 获取通知详细
     *
     * @param noticeId 注意id
     * @return {@link SysNotice}
     */
    public SysNotice getById(Long noticeId) {
       return noticeService.getById(noticeId);
    }

    /**
     * 保存通知
     *
     * @param notice 请注意
     * @return boolean
     */
    public boolean saveNotice(SysNotice notice) {
        return noticeService.save(notice);
    }

    /**
     * 更新通知
     *
     * @param notice 请注意
     * @return boolean
     */
    public boolean updateNotice(SysNotice notice) {
        return noticeService.updateById(notice);
    }

    /**
     * 批量删除通知
     *
     * @param noticeIds 注意id
     * @return boolean
     */
    public boolean removeNoticeByIds(Long[] noticeIds) {
        return noticeService.removeBatchByIds(Arrays.asList(noticeIds) );
    }
}
