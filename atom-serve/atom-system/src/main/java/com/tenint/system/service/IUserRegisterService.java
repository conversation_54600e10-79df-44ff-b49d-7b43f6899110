package com.tenint.system.service;

import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.domain.CrccUser;

import java.util.List;

public interface IUserRegisterService {

    /**
     * 插入用户
     * @param user
     * @param providerId
     */
    void insertUser(CrccUser user, String providerId);

    String getOrgName(String providerId, Long orgId);

    String getOrgName(List<CrccOrg> listOrg, Integer level);

    /**
     * 插入参会人员信息
     * @param userIds
     */
    void insertBatchUser(List<Long> userIds);
}
