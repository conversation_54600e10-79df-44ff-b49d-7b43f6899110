package com.tenint.system.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.page.TableDataInfo;
import com.tenint.system.domain.SysOperLog;
import com.tenint.system.service.ISysOperLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@RequiredArgsConstructor
@Service
public class SysOperLogManager {

    private final ISysOperLogService operLogService;

    public TableDataInfo<SysOperLog> pageOperLog(SysOperLog operLog, PageQuery query) {
        Page<SysOperLog> sysOperLogPage = operLogService.pageOperlog(operLog, query);
        return TableDataInfo.build(sysOperLogPage);
    }


    /**
     * 查询系统操作日志集合
     *
     * @param sysOperLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> listOperlog(SysOperLog sysOperLog) {
        return operLogService.listOperLog(sysOperLog);
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public boolean deleteOperLogByIds(Long[] operIds)
    {
        return operLogService.removeBatchByIds(Arrays.asList(operIds));
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog getById(Long operId) {
        return operLogService.getById(operId);

    }

    /**
     * 清空操作日志
     */
    public void cleanOperLog() {
        operLogService.remove(Wrappers.emptyWrapper());
    }
}
