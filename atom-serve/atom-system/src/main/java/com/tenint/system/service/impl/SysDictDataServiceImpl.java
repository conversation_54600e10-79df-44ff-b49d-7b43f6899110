package com.tenint.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.common.constant.CacheNames;
import com.tenint.common.constant.UserConstants;
import com.tenint.common.core.domain.PageQuery;
import com.tenint.common.core.domain.entity.SysDictData;
import com.tenint.common.exception.ServiceException;
import com.tenint.common.helper.LoginHelper;
import com.tenint.common.utils.StreamUtils;
import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.redis.CacheUtils;
import com.tenint.system.mapper.SysDictDataMapper;
import com.tenint.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.tenint.crcc.constant.CrccConstant.GROUP_CODE;

/**
 * 字典 业务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysDictDataServiceImpl extends ServiceImpl<SysDictDataMapper, SysDictData> implements ISysDictDataService {

    @Override
    public Page<SysDictData> pageDictData(SysDictData dictData, PageQuery pageQuery) {
        LambdaQueryWrapper<SysDictData> lqw = new LambdaQueryWrapper<SysDictData>()
                .eq(StringUtils.isNotBlank(dictData.getDictType()), SysDictData::getDictType, dictData.getDictType())
                .like(StringUtils.isNotBlank(dictData.getDictLabel()), SysDictData::getDictLabel, dictData.getDictLabel())
                .eq(StringUtils.isNotBlank(dictData.getStatus()), SysDictData::getStatus, dictData.getStatus())
                .orderByAsc(SysDictData::getDictSort);
        String orgCode = getOrgCode(dictData.getRemark());
        lqw.likeRight(StringUtils.isNotBlank(dictData.getRemark()),SysDictData::getRemark,orgCode);
        return baseMapper.selectPage(pageQuery.build(), lqw);
    }

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> listDictData(SysDictData dictData) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDictData>()
                .eq(StringUtils.isNotBlank(dictData.getDictType()), SysDictData::getDictType, dictData.getDictType())
                .like(StringUtils.isNotBlank(dictData.getDictLabel()), SysDictData::getDictLabel, dictData.getDictLabel())
                .eq(StringUtils.isNotBlank(dictData.getStatus()), SysDictData::getStatus, dictData.getStatus())
                .orderByAsc(SysDictData::getDictSort));
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String getDictLabel(String dictType, String dictValue) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysDictData>()
                        .select(SysDictData::getDictLabel)
                        .eq(SysDictData::getDictType, dictType)
                        .eq(SysDictData::getDictValue, dictValue))
                .getDictLabel();
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData getDictDataById(Long dictCode) {
        return baseMapper.selectById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = getDictDataById(dictCode);
            baseMapper.deleteById(dictCode);
            CacheUtils.evict(CacheNames.SYS_DICT, data.getDictType());
        }
    }

    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#data.dictType")
    @Override
    public List<SysDictData> saveDictData(SysDictData data) {
        if ("meeting_place".equals(data.getDictType())){
            String orgCode = LoginHelper.getOrgCode();
            String code = getOrgCode(orgCode);
            data.setRemark(code);
            data.setDictValue(IdUtil.fastSimpleUUID());
        }
        boolean saved = save(data);
        if (saved) {
            return baseMapper.selectDictDataByType(data.getDictType());
        }
        throw new ServiceException("操作失败");
    }

    private static String getOrgCode(String orgCode) {
        String code = "";
        if (StringUtils.isEmpty(orgCode)){
            return code;
        }
        if (orgCode.contains(GROUP_CODE) || LoginHelper.isAdmin()){
            code = StringUtils.substring(orgCode, 0, 10);
        }else {
            code = StringUtils.substring(orgCode, 0, 15);
        }
        return code;
    }

    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#data.dictType")
    @Override
    public List<SysDictData> updateDictData(SysDictData data) {
        int row = baseMapper.updateById(data);
        if (row > 0) {
            return baseMapper.selectDictDataByType(data.getDictType());
        }
        throw new ServiceException("操作失败");
    }

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    @Cacheable(cacheNames = CacheNames.SYS_DICT, key = "#dictType")
    @Override
    public List<SysDictData> listDictDataByType(String dictType) {
        List<SysDictData> dictDatas = baseMapper.selectDictDataByType(dictType);
        if (CollUtil.isNotEmpty(dictDatas)) {
            return dictDatas;
        }
        return null;
    }

    @Override
    public void loadingDictCache() {
        List<SysDictData> dictDataList = list(
                new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getStatus, UserConstants.DICT_NORMAL));
        Map<String, List<SysDictData>> dictDataMap = StreamUtils.groupByKey(dictDataList, SysDictData::getDictType);
        dictDataMap.forEach((k, v) -> {
            List<SysDictData> dictList = StreamUtils.sorted(v, Comparator.comparing(SysDictData::getDictSort));
            CacheUtils.put(CacheNames.SYS_DICT, k, dictList);
        });
    }

    @Override
    public boolean deleteByType(String dictType) {
        return remove(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, dictType));
    }

    @Override
    public List<SysDictData> placeListByMeetingPlace() {
        String orgCode = getOrgCode(LoginHelper.getOrgCode());
        return this.list(Wrappers.<SysDictData>lambdaQuery().eq(SysDictData::getDictType,"meeting_place")
                .likeRight(SysDictData::getRemark,orgCode)
                .orderByAsc(SysDictData::getDictSort));
    }

    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#type")
    @Override
    public List<SysDictData> updateByDictValue(String name,String value,String type) {
        this.lambdaUpdate().eq(SysDictData::getDictType,type)
                .eq(SysDictData::getDictValue,value)
                .set(SysDictData::getDictLabel,name).update();
        return baseMapper.selectDictDataByType(type);
    }

}
