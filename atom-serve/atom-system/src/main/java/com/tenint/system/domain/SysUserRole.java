package com.tenint.system.domain;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户角色关联表
 *
 * <AUTHOR>
 * @date 2019/03/30
 */
@Getter
@Setter
@TableName("sys_user_role")
public class SysUserRole {

    /**
     * 主键
     */
    @TableId("id")
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

}
