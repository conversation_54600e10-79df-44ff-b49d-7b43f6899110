package com.tenint.crcc.menhu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/8
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
public class RestInfo<T> {

    
    private String status;

    
    private String msg;

    
    private T data;

    @Data
    
    public static class todoData {

        
        String url;

        
        Integer todoCategory;

        
        Integer todoNumber;

        
        Integer totalNum;

        
        Map<String, Object> reserveExtensions;

    }


}
