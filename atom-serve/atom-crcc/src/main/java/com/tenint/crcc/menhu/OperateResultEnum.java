package com.tenint.crcc.menhu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/12/8
 */
@AllArgsConstructor
@Getter
public enum OperateResultEnum {

    /**
     * 应用聚合API-操作成功
     */
    ACCESSIBLE_OPERATE_SUCCESS("C00000", "操作成功"),
    /**
     * 应用聚合API-操作成功(业务应用未授权企业门户当前登录人访问)
     */
    ACCESSIBLE_T_OPERATE_SUCCESS("C00001", "操作成功(业务应用未授权企业门户当前登录人访问)"),


    /**
     * 待办聚合API
     */
    TODO_OPERATE_SUCCESS("C00000", "操作成功，有待办事项"),
    TODO_NONE_OPERATE_SUCCESS("C00001", "操作成功，无待办事项"),
    TODO_TODO_OPERATE_SUCCESS("C00204", "操作成功，待办事项分类索引不存在"),

    /**
     * 信息聚合API
     */
    PRESSES_OPERATE_SUCCESS("C00000", "操作成功"),
    PRESSES_NONE_OPERATE_SUCCESS("C00001", "操作成功，无聚合信息"),


    /**
     * 请求参数错误
     */
    REQUEST_PARAM_ERROR("C00400", "请求参数错误"),
    /**
     * 系统内部错误
     */
    SYSTEM_INTERNAL_ERROR("C00501", "系统内部错误");

    /**
     * 请求状态码
     */
    private String status;

    /**
     * 请求响应信息
     */
    private String msg;

}


