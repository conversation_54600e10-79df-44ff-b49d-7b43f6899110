package com.tenint.crcc.facade;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tenint.crcc.domain.CrccProject;
import org.springframework.graphql.client.HttpGraphQlClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;

@Service
public class ProjectFacade {


    public List<CrccProject> getProject() {
        WebClient webClient = WebClient.builder()
                .baseUrl("https://api01.crcc.cn/api/project/graphql")
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(100 * 1024 * 1024))
                .build();

        HttpGraphQlClient graphQlClient = HttpGraphQlClient.builder(webClient)
                .headers(headers -> {
                    headers.setBearerAuth(HrApiConfiguration.storeAndGetApiToken());
                    headers.setContentType(MediaType.APPLICATION_JSON);
                })
                .build();


        String document = """
                {
                    providerProjects(provider: "crcc24") {
                        code
                        name
                        shortName
                        totalContractPrice
                        businessType{code,text}
                        abroad
                        city
                        country
                        status{code,text}
                    }
                }
                """;

        Map<String,List<CrccProject>> map=graphQlClient.document(document)
                .execute().block().getData();
        List<CrccProject> projectList= null;
        if (map != null) {
            projectList = map.get("providerProjects");
        }
        String str= JSON.toJSON(projectList).toString();
        projectList= JSONObject.parseArray(str,CrccProject.class);
        return projectList;
    }


}
