package com.tenint.crcc.convert;

import com.tenint.common.core.domain.entity.SysOrg;
import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.common.core.domain.vo.SysOrgTreeVo;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.domain.CrccUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SysOrgConvert {

    SysOrgConvert INSTANCE = Mappers.getMapper( SysOrgConvert.class );

    /**
     * crcc机构信息 转为 系统机构信息
     *
     * @param crccOrg 中铁机构
     * @return {@link CrccOrg}
     */
    SysOrg toTarget(CrccOrg crccOrg);

    /**
     * 列表转换
     *
     * @param crccOrgs 中国铁建组织
     * @return {@link List}<{@link SysOrg}>
     */
    List<SysOrg> toTarget(List<CrccOrg> crccOrgs);

    /**
     * 转为树
     *
     * @param crccOrg 中国铁建组织
     * @return {@link SysOrgTreeVo}
     */
    SysOrgTreeVo toTargetTreeVo(CrccOrg crccOrg);

    /**
     * 转为树列表
     *
     * @param crccOrgs 中国铁建组织
     * @return {@link List}<{@link SysOrgTreeVo}>
     */
    List<SysOrgTreeVo> toTargetTreeVo(List<CrccOrg> crccOrgs);



    @Mappings({
            @Mapping(source = "id", target = "userId"),
            @Mapping(source = "name", target = "nickName")
    })
    SysUser convert(CrccUser crccUser);
}
