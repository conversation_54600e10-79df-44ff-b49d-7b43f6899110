package com.tenint.crcc.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 中铁二十局用户表
 *
 * <AUTHOR>
 * @date 2023/04/22
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CrccProject {

    /**
     * 编号
     */
    private String code;

    /**
     * 全称
     */
    private String name;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 合同金额
     */
    private String totalContractPrice;
    /**
     * 工程类别
     */
    private CrccDict businessType;
    /**
     * 是否境外
     */
    private String abroad;
    /**
     * 城市
     */
    private String city;
    /**
     * 国家
     */
    private String country;
    /**
     * 项目状态
     */
    private CrccDict status;

}