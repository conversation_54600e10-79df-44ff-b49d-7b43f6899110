package com.tenint.crcc.justauth.source;

import com.tenint.crcc.justauth.AuthCrccRequest;
import me.zhyd.oauth.request.AuthDefaultRequest;

/**
 * 身份验证源
 *
 * <AUTHOR>
 * @date 2023/04/21
 */
public enum AuthCrccSource implements me.zhyd.oauth.config.AuthSource {

    CRCC {
        /**
         * 授权的api
         *
         * @return url
         */
        @Override
        public String authorize() {
            return "https://sso.crcc.cn/oauth/authorize";
        }

        /**
         * 获取accessToken的api
         *
         * @return url
         */

        @Override
        public String accessToken() {
            return "https://sso.crcc.cn/oauth/token";
        }

        /**
         * 获取用户信息的api
         *
         * @return url
         */
        @Override
        public String userInfo() {
            return "https://sso.crcc.cn/oauth/userinfo";
        }

        /**
         * 平台对应的 AuthRequest 实现类，必须继承自 {@link AuthDefaultRequest}
         *
         * @return class
         */
        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthCrccRequest.class;
        }
    }
}
