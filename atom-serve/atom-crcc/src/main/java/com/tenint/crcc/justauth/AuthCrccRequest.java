package com.tenint.crcc.justauth;

import com.alibaba.fastjson.JSONObject;
import com.tenint.crcc.domain.CrccAuthUser;
import com.tenint.crcc.justauth.source.AuthCrccSource;
import com.xkcoding.http.support.HttpHeader;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.log.Log;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.AuthChecker;
import me.zhyd.oauth.utils.Base64Utils;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;

/**
 * 身份验证请求中国铁建
 *
 * <AUTHOR>
 * @date 2023/04/22
 */
public class AuthCrccRequest extends AuthDefaultRequest {


    public AuthCrccRequest(AuthConfig config) {
        super(config, AuthCrccSource.CRCC);
    }

    @Override
    public AuthResponse login(AuthCallback authCallback) {
        try {
            checkCode(authCallback);
//            if (!config.isIgnoreCheckState()) {
//                AuthChecker.checkState(authCallback.getState(), source, authStateCache);
//            }
            AuthToken authToken = this.getAccessToken(authCallback);

            AuthUser user = this.getUserInfo(authToken);
            String username = user.getUsername();
            String[] split = username.split("\\|");
            return AuthResponse.builder()
                    .code(AuthResponseStatus.SUCCESS.getCode())
                    .data(new CrccAuthUser(split[0], split[1]))
                    .build();
        } catch (Exception e) {
            Log.error("Failed to login with oauth authorization.", e);
            return AuthResponse.builder().code(AuthResponseStatus.FAILURE.getCode())
                    .msg(e.getMessage()).build();
        }
    }

    @Override
    protected AuthToken getAccessToken(AuthCallback authCallback) {

        String body = doGetAuthorizationCode(authCallback.getCode());
        JSONObject object = JSONObject.parseObject(body);

        this.checkResponse(object);

        return AuthToken.builder()
                .accessToken(object.getString("access_token"))
                .refreshToken(object.getString("refresh_token"))
                .idToken(object.getString("id_token"))
                .tokenType(object.getString("token_type"))
                .scope(object.getString("scope"))
                .build();
    }

    @Override
    protected String doGetAuthorizationCode(String code) {
        // 使用base64加密 client_id和client_secret验证
        String encode = Base64Utils.encode(config.getClientId() + ":" + config.getClientSecret());
        return new HttpUtils(config.getHttpConfig())
                .get(accessTokenUrl(code), null, new HttpHeader()
                        .add("Authorization", "Basic " + encode), false).getBody();
    }

    @Override
    protected AuthUser getUserInfo(AuthToken authToken) {
        String body = doGetUserInfo(authToken);
        JSONObject object = JSONObject.parseObject(body);
        this.checkResponse(object);
        return AuthUser.builder()
                .uuid(object.getString("id"))
                .username(object.getString("name"))
                .gender(AuthUserGender.UNKNOWN)
                .token(authToken)
                .source(source.toString())
                .build();
    }

    @Override
    public String authorize(String state) {
        return UrlBuilder.fromBaseUrl(super.authorize(state))
                .queryParam("scope", "openid+profile")
                .build();
    }

    private void checkResponse(JSONObject object) {
        // oauth/token 验证异常
        if (object.containsKey("error")) {
            throw new AuthException(object.getString("error_description"));
        }
        // user 验证异常
        if (object.containsKey("message")) {
            throw new AuthException(object.getString("message"));
        }
    }
}
