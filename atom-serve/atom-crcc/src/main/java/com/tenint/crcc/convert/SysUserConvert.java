package com.tenint.crcc.convert;

import com.tenint.common.core.domain.entity.SysUser;
import com.tenint.crcc.domain.CrccUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SysUserConvert {
    SysUserConvert INSTANCE = Mappers.getMapper( SysUserConvert.class );

    /**
     * crcc用户转为系统用户信息
     *
     * @param crccUser 年代
     * @return {@link SysUser}
     */

    @Mappings({
            @Mapping(source = "id", target = "userId"),
            @Mapping(source = "name", target = "nickName")
    })
    SysUser convert(CrccUser crccUser);
}
