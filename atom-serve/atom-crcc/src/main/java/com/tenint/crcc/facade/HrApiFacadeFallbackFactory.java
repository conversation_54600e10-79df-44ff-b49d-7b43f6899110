package com.tenint.crcc.facade;

import com.tenint.common.constant.CacheNames;
import com.tenint.common.utils.redis.CacheUtils;
import com.tenint.crcc.domain.CrccOrg;
import com.tenint.crcc.domain.CrccOrgSecondary;
import com.tenint.crcc.domain.CrccUser;
import com.tenint.crcc.domain.CrccUserSearch;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/5/28
 */
@Component
@Slf4j
public class HrApiFacadeFallbackFactory implements FallbackFactory<HrApiFacade> {
    @Override
    public HrApiFacade create(Throwable throwable) {
        log.error(throwable.getMessage());
        return new HrApiFacade() {
            @Override
            public CrccUser getUserByUserId(String providerId, String userId) {
                return CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":getUserByUserId", userId);
            }

            @Override
            public CrccOrg getParentDeptByDeptId(String providerId, String deptId) {
                return null;
            }

            @Override
            public List<CrccOrg> listOrgPathByOrgId(String providerId, Long orgId) {
                List<CrccOrg> result = CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":listOrgPathByOrgId", orgId);
                return result == null ? Collections.emptyList(): result;
            }

            @Override
            public CrccOrg getCompanyByOrgId(String providerId, Long orgId) {
                return CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":getCompanyByOrgId", orgId);
            }

            @Override
            public List<CrccOrg> listPositionByUserId(String providerId, String userId) {
                return Collections.emptyList();
            }

            @Override
            public CrccOrg treeOrgByProviderAndCompanyId(String providerId, Long companyId) {
                return CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":treeOrgByProviderAndCompanyId", companyId);
            }

            @Override
            public CrccOrg treeOrgAndUserByCompanyId(String providerId, Long companyId) {
                return CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":treeOrgAndUserByCompanyId", companyId);
            }

            @Override
            public List<CrccUser> listUserByPositionId(String providerId, String positionId) {
                List<CrccUser> result = CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":listUserByPositionId", positionId);
                return result == null ? Collections.emptyList(): result;
            }

            @Override
            public CrccOrg getDeptByPositionId(String providerId, Long positionId) {
                return CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":getDeptByPositionId", positionId);
            }

            @Override
            public List<CrccOrg> listChildrenOrgByOrgId(String providerId, Long orgId) {
                List<CrccOrg> result = CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":listChildrenOrgByOrgId", orgId);
                return result == null ? Collections.emptyList(): result;
            }


            @Override
            public CrccOrg getMainPositionByUserId(String providerId, String userId) {
                return CacheUtils.get(CacheNames.HR_FEIGN_CACHE + ":getMainPositionByUserId", userId);
            }

            @Override
            public List<CrccOrgSecondary> listOrg() {
                return Collections.emptyList();
            }

            @Override
            public CrccOrg getOrgRoot(String providerId) {
                return null;
            }

            @Override
            public CrccOrg getDeptByDeptId(String providerId, String deptId) {
                return null;
            }

            @Override
            public List<CrccOrg> listDeptByDeptId(String providerId, String deptId) {
                return null;
            }

            @Override
            public CrccOrg getPositionByPositionId(String providerId, String positionId) {
                return null;
            }

            @Override
            public List<CrccUserSearch> searchUser(String providerId, Long orgId, String userName, String mainPositionOnly) {
                return Collections.emptyList();
            }

        };
    }
}
