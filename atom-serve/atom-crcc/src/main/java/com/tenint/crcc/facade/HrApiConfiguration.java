package com.tenint.crcc.facade;

import com.tenint.common.utils.StringUtils;
import com.tenint.common.utils.redis.RedisUtils;
import com.tenint.crcc.justauth.AuthHrRequest;
import com.tenint.crcc.justauth.provider.AuthRequestProvider;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Component
public class HrApiConfiguration  implements RequestInterceptor {

    public final static String HR_TOKEN_KEY = "hr-api";

    private static final long OFFSET_SECONDS = 60;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header("Authorization", String.format("%s %s", "Bearer", storeAndGetApiToken()));
    }


    public static String storeAndGetApiToken() {
        String token = RedisUtils.getCacheObject(HR_TOKEN_KEY);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        AuthHrRequest hr = (AuthHrRequest) AuthRequestProvider.getAuthRequest("hr");
        AuthResponse<AuthUser> response = hr.login(null);
        if (response.ok()) {
            AuthUser authUser = response.getData();
            AuthToken data = authUser.getToken();
            String accessToken = data.getAccessToken();
            int expireIn = data.getExpireIn();
            // 人事接口默认23小时过期，设置一个提前量，临期更新接口
            long seconds = expireIn - OFFSET_SECONDS;
            // 极端情况小于 0 则设置后马上过期
            if (seconds <= 0) {
                seconds = 1;
            }

            RedisUtils.setCacheObject(HR_TOKEN_KEY, accessToken, Duration.ofSeconds(seconds));
            return accessToken;
        }
        return null;
    }

}