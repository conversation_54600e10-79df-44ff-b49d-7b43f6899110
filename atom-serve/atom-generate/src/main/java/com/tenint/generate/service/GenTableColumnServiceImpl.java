package com.tenint.generate.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tenint.generate.domain.GenTableColumn;
import com.tenint.generate.mapper.GenTableColumnMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务 服务层实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GenTableColumnServiceImpl extends ServiceImpl<GenTableColumnMapper, GenTableColumn> implements IGenTableColumnService {

    @Override
    public List<GenTableColumn> selectDbTableColumnsByName(String tableName) {
       return baseMapper.selectDbTableColumnsByName(tableName);
    }
}

