<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tenint.generate.mapper.GenTableMapper">

    <resultMap type="com.tenint.generate.domain.GenTable" id="GenTableResult">
        <id property="id" column="table_id"/>
        <result property="tableName"      column="table_name"        />
        <result property="tableComment"   column="table_comment"     />
        <result property="subTableName"   column="sub_table_name"    />
        <result property="subTableFkName" column="sub_table_fk_name" />
        <result property="className"      column="class_name"        />
        <result property="tplCategory"    column="tpl_category"      />
        <result property="packageName"    column="package_name"      />
        <result property="moduleName"     column="module_name"       />
        <result property="isNeedExcel"    column="is_need_excel"     />
        <result property="isNeedValidator" column="is_need_validator"  />
        <result property="isNeedSatoken"  column="is_need_satoken"   />
        <result property="isSortDelete"  column="is_sort_delete"     />
        <result property="businessName"   column="business_name"     />
        <result property="functionName"   column="function_name"     />
        <result property="functionAuthor" column="function_author"   />
        <result property="genType"        column="gen_type"          />
        <result property="genPath"        column="gen_path"          />
        <result property="options"        column="options"           />
        <result property="creatorId" column="creator_id"/>
        <result property="createTime"     column="create_time"       />
        <result property="updaterId" column="updater_id"/>
        <result property="updateTime"     column="update_time"       />
        <result property="remark"         column="remark"            />
        <result property="tableRows"      column="table_rows"        />
        <collection  property="columns"  javaType="java.util.List"  resultMap="GenTableColumnResult" />
    </resultMap>

    <resultMap type="com.tenint.generate.domain.GenTableColumn" id="GenTableColumnResult">
        <id property="id" column="column_id"/>
        <result property="tableId" column="table_id"/>
        <result property="columnName" column="column_name"/>
        <result property="columnComment" column="column_comment"/>
        <result property="columnType" column="column_type"/>
        <result property="javaType" column="java_type"/>
        <result property="javaField" column="java_field"/>
        <result property="isPk" column="is_pk"/>
        <result property="isIncrement" column="is_increment"/>
        <result property="isRequired" column="is_required"/>
        <result property="isInsert" column="is_insert"/>
        <result property="isEdit" column="is_edit"/>
        <result property="isList"         column="is_list"        />
        <result property="isQuery"        column="is_query"       />
        <result property="queryType"      column="query_type"     />
        <result property="htmlType"       column="html_type"      />
        <result property="dictType"       column="dict_type"      />
        <result property="sort"           column="sort"           />
        <result property="creatorId" column="creator_id"/>
        <result property="createTime"     column="create_time"    />
        <result property="updaterId" column="updater_id"/>
        <result property="updateTime"     column="update_time"    />
    </resultMap>

    <select id="selectPageDbTableList" resultMap="GenTableResult">
        <if test="@com.tenint.common.helper.DataBaseHelper@isMySql()">
            select table_name, table_comment, create_time, update_time
            from information_schema.tables
            where table_schema = (select database())
            AND table_name NOT LIKE 'xxl_job_%' AND  table_name NOT LIKE 'QRTZ_%' AND table_name NOT LIKE 'gen_%'
            AND table_name NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(table_name) like lower(concat('%', #{genTable.tableName}, '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(table_comment) like lower(concat('%', #{genTable.tableComment}, '%'))
            </if>
            order by create_time desc
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isOracle() or @com.tenint.common.helper.DataBaseHelper@isDm()">
            select lower(dt.table_name) as table_name, dtc.comments as table_comment, uo.created as create_time, uo.last_ddl_time as update_time
            from user_tables dt, user_tab_comments dtc, user_objects uo
            where dt.table_name = dtc.table_name
            and dt.table_name = uo.object_name
            and uo.object_type = 'TABLE'
            AND dt.table_name NOT LIKE 'XXL_JOB_%' AND dt.table_name NOT LIKE 'QRTZ_%' AND dt.table_name NOT LIKE 'GEN_%'
            AND lower(dt.table_name) NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(dt.table_name) like lower(concat(concat('%', #{genTable.tableName}), '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(dtc.comments) like lower(concat(concat('%', #{genTable.tableComment}), '%'))
            </if>
            order by create_time desc
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isPostgerSql()">
            select table_name, table_comment, create_time, update_time
            from (
            SELECT c.relname AS table_name,
            obj_description(c.oid) AS table_comment,
            CURRENT_TIMESTAMP AS create_time,
            CURRENT_TIMESTAMP AS update_time
            FROM pg_class c
            LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
            AND c.relname != 'spatial_%'::text
            AND n.nspname = 'public'::name
            AND n.nspname <![CDATA[ <> ]]> ''::name
            ) list_table
            where table_name NOT LIKE 'xxl_job_%' AND table_name NOT LIKE 'QRTZ_%' AND table_name NOT LIKE 'gen_%'
            AND table_name NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(table_name) like lower(concat('%', #{genTable.tableName}, '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(table_comment) like lower(concat('%', #{genTable.tableComment}, '%'))
            </if>
            order by create_time desc
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isSqlServer()">
            SELECT cast(D.NAME as nvarchar) as table_name,
            cast(F.VALUE as nvarchar) as table_comment,
            crdate as create_time,
            refdate as update_time
            FROM SYSOBJECTS D
            INNER JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID
            AND F.MINOR_ID = 0 AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
            AND D.NAME NOT LIKE 'xxl_job_%' AND D.NAME NOT LIKE 'QRTZ_%' AND D.NAME NOT LIKE 'gen_%'
            AND D.NAME NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(D.NAME) like lower(concat(N'%', N'${genTable.tableName}', N'%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(CAST(F.VALUE AS nvarchar)) like lower(concat(N'%', N'${genTable.tableComment}', N'%'))
            </if>
            order by crdate desc
        </if>
    </select>

    <select id="selectDbTableListByNames" resultMap="GenTableResult">
        <if test="@com.tenint.common.helper.DataBaseHelper@isMySql()">
            select table_name, table_comment, create_time, update_time from information_schema.tables
            where table_name NOT LIKE 'xxl_job_%' AND table_name NOT LIKE 'QRTZ_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
            and table_name in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isOracle() or @com.tenint.common.helper.DataBaseHelper@isDm()">
            select lower(dt.table_name) as table_name, dtc.comments as table_comment, uo.created as create_time, uo.last_ddl_time as update_time
            from user_tables dt, user_tab_comments dtc, user_objects uo
            where dt.table_name = dtc.table_name
            and dt.table_name = uo.object_name
            and uo.object_type = 'TABLE'
            AND dt.table_name NOT LIKE 'XXL_JOB_%' AND dt.table_name NOT LIKE 'QRTZ_%' AND dt.table_name NOT LIKE 'GEN_%'
            AND dt.table_name NOT IN (select table_name from gen_table)
            and lower(dt.table_name) in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isPostgerSql()">
            select table_name, table_comment, create_time, update_time
            from (
            SELECT c.relname AS table_name,
            obj_description(c.oid) AS table_comment,
            CURRENT_TIMESTAMP AS create_time,
            CURRENT_TIMESTAMP AS update_time
            FROM pg_class c
            LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
            AND c.relname != 'spatial_%'::text
            AND n.nspname = 'public'::name
            AND n.nspname <![CDATA[ <> ]]> ''::name
            ) list_table
            where table_name NOT LIKE 'xxl_job_%' AND table_name NOT LIKE 'QRTZ_%'  and table_name NOT LIKE 'gen_%'
            and table_name in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isSqlServer()">
            SELECT cast(D.NAME as nvarchar) as table_name,
            cast(F.VALUE as nvarchar) as table_comment,
            crdate as create_time,
            refdate as update_time
            FROM SYSOBJECTS D
            INNER JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID
            AND F.MINOR_ID = 0 AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
            AND D.NAME NOT LIKE 'xxl_job_%' AND d.NAME NOT LIKE 'QRTZ_%' AND D.NAME NOT LIKE 'gen_%'
            AND D.NAME in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
    </select>

    <select id="selectTableByName" parameterType="String" resultMap="GenTableResult">
        <if test="@com.tenint.common.helper.DataBaseHelper@isMySql()">
            select table_name, table_comment, create_time, update_time from information_schema.tables
            where table_name NOT LIKE 'xxl_job_%' AND table_name NOT LIKE 'QRTZ_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
            and table_name = #{tableName}
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isOracle() or @com.tenint.common.helper.DataBaseHelper@isDm() ">
            select lower(dt.table_name) as table_name, dtc.comments as table_comment, uo.created as create_time, uo.last_ddl_time as update_time
            from user_tables dt, user_tab_comments dtc, user_objects uo
            where dt.table_name = dtc.table_name
            and dt.table_name = uo.object_name
            and uo.object_type = 'TABLE'
            AND dt.table_name NOT LIKE 'XXL_JOB_%' AND dt.table_name NOT LIKE 'QRTZ_%' AND dt.table_name NOT LIKE 'GEN_%'
            AND dt.table_name NOT IN (select table_name from gen_table)
            and lower(dt.table_name) = #{tableName}
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isPostgerSql()">
            select table_name, table_comment, create_time, update_time
            from (
            SELECT c.relname AS table_name,
            obj_description(c.oid) AS table_comment,
            CURRENT_TIMESTAMP AS create_time,
            CURRENT_TIMESTAMP AS update_time
            FROM pg_class c
            LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
            AND c.relname != 'spatial_%'::text
            AND n.nspname = 'public'::name
            AND n.nspname <![CDATA[ <> ]]> ''::name
            ) list_table
            where table_name NOT LIKE 'xxl_job_%' AND table_name NOT LIKE 'QRTZ_%' and table_name NOT LIKE 'gen_%'
            and table_name = #{tableName}
        </if>
        <if test="@com.tenint.common.helper.DataBaseHelper@isSqlServer()">
            SELECT cast(D.NAME as nvarchar) as table_name,
            cast(F.VALUE as nvarchar) as table_comment,
            crdate as create_time,
            refdate as update_time
            FROM SYSOBJECTS D
            INNER JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID
            AND F.MINOR_ID = 0 AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
            AND D.NAME NOT LIKE 'xxl_job_%' AND D.NAME NOT LIKE 'QRTZ_%' AND D.NAME NOT LIKE 'gen_%'
            AND D.NAME = #{tableName}
        </if>
    </select>


    <select id="selectGenTableById" parameterType="Long" resultMap="GenTableResult">
        SELECT t.id as table_id,
               t.table_name,
               t.table_comment,
               t.sub_table_name,
               t.sub_table_fk_name,
               t.class_name,
               t.tpl_category,
               t.package_name,
               t.module_name,
               t.is_need_excel,
               t.is_need_validator,
               t.is_need_satoken,
               t.is_sort_delete,
               t.business_name,
               t.function_name,
               t.function_author,
               t.gen_type,
               t.gen_path,
               t.options,
               t.remark,
               c.id as column_id,
               c.column_name,
               c.column_comment,
               c.column_type,
               c.java_type,
               c.java_field,
               c.is_pk,
               c.is_increment,
               c.is_required,
               c.is_insert,
               c.is_edit,
               c.is_list,
               c.is_query,
               c.query_type,
               c.html_type,
               c.dict_type,
               c.sort
        FROM gen_table t
                 LEFT JOIN gen_table_column c ON t.id = c.table_id
        where t.id = #{tableId}
        order by c.sort
    </select>

    <select id="selectGenTableByName" parameterType="String" resultMap="GenTableResult">
        SELECT t.id as table_id,
               t.table_name,
               t.table_comment,
               t.sub_table_name,
               t.sub_table_fk_name,
               t.class_name,
               t.tpl_category,
               t.package_name,
               t.module_name,
               t.is_need_excel,
               t.is_need_validator,
               t.is_need_satoken,
               t.is_sort_delete,
               t.business_name,
               t.function_name,
               t.function_author,
               t.gen_type,
               t.gen_path,
               t.options,
               t.remark,
               c.id as column_id,
               c.column_name,
               c.column_comment,
               c.column_type,
               c.java_type,
               c.java_field,
               c.is_pk,
               c.is_increment,
               c.is_required,
               c.is_insert,
               c.is_edit,
               c.is_list,
               c.is_query,
               c.query_type,
               c.html_type,
               c.dict_type,
               c.sort
        FROM gen_table t
                 LEFT JOIN gen_table_column c ON t.id = c.table_id
        where t.table_name = #{tableName}
        order by c.sort
    </select>

    <select id="selectGenTableAll" parameterType="String" resultMap="GenTableResult">
        SELECT t.id as table_id,
               t.table_name,
               t.table_comment,
               t.sub_table_name,
               t.sub_table_fk_name,
               t.class_name,
               t.tpl_category,
               t.package_name,
               t.module_name,
               t.is_need_excel,
               t.is_need_validator,
               t.is_need_satoken,
               t.is_sort_delete,
               t.business_name,
               t.function_name,
               t.function_author,
               t.options,
               t.remark,
               c.id as column_id,
               c.column_name,
               c.column_comment,
               c.column_type,
               c.java_type,
               c.java_field,
               c.is_pk,
               c.is_increment,
               c.is_required,
               c.is_insert,
               c.is_edit,
               c.is_list,
               c.is_query,
               c.query_type,
               c.html_type,
               c.dict_type,
               c.sort
        FROM gen_table t
                 LEFT JOIN gen_table_column c ON t.id = c.table_id
        order by c.sort
    </select>

</mapper>
