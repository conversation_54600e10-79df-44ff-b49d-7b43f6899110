package ${packageName}.convert.${moduleName};

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${packageName}.domain.${ClassName};
import ${packageName}.domain.vo.${ClassName}Vo;
import ${packageName}.domain.bo.${ClassName}Bo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ${ClassName}Convert {

    ${ClassName}Convert INSTANCE = Mappers.getMapper( ${ClassName}Convert.class );


    ${ClassName} convert(${ClassName}Bo bean);

    ${ClassName}Vo convert(${ClassName} bean);

    List<${ClassName}Vo> convertList(List<${ClassName}> list);

    Page<${ClassName}Vo> convertPage(Page<${ClassName}> page);

}
