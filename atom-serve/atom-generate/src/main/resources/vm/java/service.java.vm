package ${packageName}.service.${moduleName};

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tenint.common.core.domain.PageQuery;
import ${packageName}.domain.${ClassName};
import ${packageName}.domain.bo.${ClassName}Bo;
import java.util.Collection;
import java.util.List;
/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IService<${ClassName}> {

    #if($table.crud || $table.sub)
    /**
     * 查询${functionName}列表
     */
    Page<${ClassName}> page${ClassName}(${ClassName}Bo bo, PageQuery pageQuery);
    #end

    /**
     * 查询${functionName}列表
     */
    List<${ClassName}> list${ClassName}(${ClassName}Bo bo);

    /**
     * 校验并批量删除${functionName}信息
     */
    Boolean removeWithValidByIds(Collection<${pkColumn.javaType}> ids, Boolean isValid);

}
