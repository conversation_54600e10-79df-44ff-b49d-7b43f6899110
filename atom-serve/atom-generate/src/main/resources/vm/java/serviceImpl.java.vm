package ${packageName}.service.${moduleName}.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tenint.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${packageName}.domain.${ClassName};
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.service.I${ClassName}Service;
import ${packageName}.domain.bo.${ClassName}Bo;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.tenint.common.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RequiredArgsConstructor
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {


    #if($table.crud || $table.sub)
    /**
     * 查询${functionName}列表
     */
    @Override
    public Page<${ClassName}> page${ClassName}(${ClassName}Bo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<${ClassName}> lqw = buildQueryWrapper(bo);
        Page<${ClassName}> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return result;
    }
    #end

    /**
     * 查询${functionName}列表
     */
    @Override
    public List<${ClassName}> list${ClassName}(${ClassName}Bo bo) {
        LambdaQueryWrapper<${ClassName}> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 构建${functionName}列表查询条件
     */
    private LambdaQueryWrapper<${ClassName}> buildQueryWrapper(${ClassName}Bo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<${ClassName}> lqw = Wrappers.lambdaQuery();
        #foreach($column in $columns)
        #if($column.query)
            #set($queryType=$column.queryType)
            #set($javaField=$column.javaField)
            #set($javaType=$column.javaType)
            #set($columnName=$column.columnName)
            #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            #set($mpMethod=$column.queryType.toLowerCase())
            #if($queryType != 'BETWEEN')
            #if($javaType == 'String')
                #set($condition='StringUtils.isNotBlank(bo.get'+$AttrName+'())')
            #else
                #set($condition='bo.get'+$AttrName+'() != null')
            #end
            lqw.$mpMethod($condition, ${ClassName}::get$AttrName, bo.get$AttrName());
            #else
            lqw.between(params.get("begin$AttrName") != null && params.get("end$AttrName") != null,
                ${ClassName}::get$AttrName ,params.get("begin$AttrName"), params.get("end$AttrName"));
            #end
        #end
        #end
        return lqw;
    }


    /**
     * 批量删除${functionName}
     */
    @Override
    public Boolean removeWithValidByIds(Collection<${pkColumn.javaType}> ids, Boolean isValid) {
        if( isValid){
           // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
